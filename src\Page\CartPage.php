<?php

namespace App\Page;

use App\Page\Base\BasePage;
use Behat\Mink\Element\NodeElement;
use RuntimeException;

/**
 * CartPage handles actions on the shopping cart page.
 */
class CartPage extends BasePage
{
    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'CART_ITEMS' => '.cart-items .cart-item',
        'CART_TOTAL' => '.cart-summary .cart-total',
        'CHECKOUT_BUTTON' => '.checkout-button',
        'EMPTY_CART_MESSAGE' => '.cart-empty-message',
        'REMOVE_ITEM_BUTTON' => '.remove-item-button',
        'QUANTITY_INPUT' => '.item-quantity input',
        'UPDATE_CART_BUTTON' => '.update-cart-button'
    ];
    /**
     * The path of the cart page.
     *
     * @var string
     */
    protected string $path = '/cart';

    /**
     * Get the cart items
     *
     * @return array List of cart items with product name, quantity, and price
     */
    public function getCartItems(): array
    {
        $items = [];
        $itemElements = $this->browserService->findElements(self::SELECTORS['CART_ITEMS']);

        foreach ($itemElements as $element) {
            $nameElement = $element->find('css', '.item-name');
            $priceElement = $element->find('css', '.item-price');
            $quantityElement = $element->find('css', '.item-quantity input');

            if ($nameElement && $priceElement) {
                $quantity = 1; // Default quantity

                if ($quantityElement) {
                    $quantity = (int)$quantityElement->getAttribute('value');
                } else {
                    // Try alternative quantity selector
                    $quantityTextElement = $element->find('css', '.item-quantity');
                    if ($quantityTextElement) {
                        $quantityText = $quantityTextElement->getText();
                        $quantity = (int)preg_replace('/[^0-9]/', '', $quantityText);
                    }
                }

                $items[] = [
                    'name' => $nameElement->getText(),
                    'price' => $priceElement->getText(),
                    'quantity' => $quantity
                ];
            }
        }

        return $items;
    }

    /**
     * Get number of items in cart
     *
     * @return int Number of items
     */
    public function getItemCount(): int
    {
        $itemElements = $this->browserService->findElements(self::SELECTORS['CART_ITEMS']);
        return count($itemElements);
    }

    /**
     * Get the cart total
     *
     * @return string
     */
    public function getCartTotal(): string
    {
        return $this->getElementText(self::SELECTORS['CART_TOTAL']);
    }

    /**
     * Proceed to checkout
     *
     * @return void
     */
    public function proceedToCheckout(): void
    {
        if ($this->isEmpty()) {
            throw new RuntimeException('Cannot proceed to checkout with empty cart');
        }

        $this->clickElement(self::SELECTORS['CHECKOUT_BUTTON']);
        $this->waitForPageToLoad();
        $this->waitForElementVisible('.checkout-steps');
    }

    /**
     * Check if the cart is empty
     *
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->elementExists(self::SELECTORS['EMPTY_CART_MESSAGE']);
    }

    /**
     * Update the quantity of a cart item
     *
     * @param string $productName Product name
     * @param int $quantity New quantity
     * @return void
     * @throws RuntimeException If the product is not in the cart
     */
    public function updateItemQuantity(string $productName, int $quantity): void
    {
        $itemElement = $this->findCartItemByName($productName);
        if (!$itemElement) {
            throw new RuntimeException(sprintf('Product "%s" not found in cart', $productName));
        }

        $quantityInput = $itemElement->find('css', self::SELECTORS['QUANTITY_INPUT']);
        $quantityInput->setValue((string)$quantity);

        $this->clickElement(self::SELECTORS['UPDATE_CART_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Update the quantity of a cart item by index
     *
     * @param int $index Item index (1-based)
     * @param int $quantity New quantity
     * @return void
     * @throws RuntimeException If the index is invalid
     */
    public function updateItemQuantityByIndex(int $index, int $quantity): void
    {
        $itemElements = $this->browserService->findElements(self::SELECTORS['CART_ITEMS']);

        if ($index < 1 || $index > count($itemElements)) {
            throw new RuntimeException(sprintf('Invalid cart item index: %d', $index));
        }

        $itemElement = $itemElements[$index - 1];
        $quantityInput = $itemElement->find('css', self::SELECTORS['QUANTITY_INPUT']);
        if (!$quantityInput) {
            throw new RuntimeException('Quantity input not found');
        }

        $quantityInput->setValue((string)$quantity);
        $this->clickElement(self::SELECTORS['UPDATE_CART_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Find a cart item by product name
     *
     * @param string $productName Product name
     * @return NodeElement|null Cart item element or null if not found
     */
    private function findCartItemByName(string $productName): ?NodeElement
    {
        $itemElements = $this->browserService->findElements(self::SELECTORS['CART_ITEMS']);

        foreach ($itemElements as $element) {
            $name = $element->find('css', '.item-name')->getText();
            if ($name === $productName) {
                return $element;
            }
        }

        return null;
    }

    /**
     * Remove an item from the cart by product name
     *
     * @param string $productName Product name
     * @return void
     * @throws RuntimeException If the product is not in the cart
     */
    public function removeItem(string $productName): void
    {
        $itemElement = $this->findCartItemByName($productName);
        if (!$itemElement) {
            throw new RuntimeException(sprintf('Product "%s" not found in cart', $productName));
        }

        $removeButton = $itemElement->find('css', self::SELECTORS['REMOVE_ITEM_BUTTON']);
        $removeButton->click();

        $this->waitForAjaxToComplete();
    }

    /**
     * Remove an item from the cart by index
     *
     * @param int $index Item index (1-based)
     * @return void
     * @throws RuntimeException If the index is invalid
     */
    public function removeItemByIndex(int $index): void
    {
        $itemElements = $this->browserService->findElements(self::SELECTORS['CART_ITEMS']);

        if ($index < 1 || $index > count($itemElements)) {
            throw new RuntimeException(sprintf('Invalid cart item index: %d', $index));
        }

        $itemElement = $itemElements[$index - 1];
        $removeButton = $itemElement->find('css', self::SELECTORS['REMOVE_ITEM_BUTTON']);
        if (!$removeButton) {
            throw new RuntimeException('Remove button not found');
        }

        $removeButton->click();
        $this->waitForAjaxToComplete();
    }

    /**
     * Remove the first item from the cart
     *
     * @return void
     * @throws RuntimeException If the cart is empty
     */
    public function removeFirstItem(): void
    {
        $this->removeItemByIndex(1);
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible('.cart-summary');
    }

    // --- Added Stub Methods ---

    /**
     * Selects the supply duration for an item (Not Implemented).
     *
     * @param string|int $duration The duration to select (e.g., \'3 months\', 90).
     * @throws RuntimeException Always throws as not implemented.
     */
    public function selectSupplyDuration($duration): void
    {
        // TODO: Implement actual logic to select supply duration on the cart page.
        throw new RuntimeException(__METHOD__ . ' is not implemented yet.');
    }

    /**
     * Selects the quantity for an item (Not Implemented - Use updateItemQuantity instead?).
     * Note: Consider if updateItemQuantity or updateItemQuantityByIndex already covers this.
     *
     * @param int $quantity The quantity to select.
     * @throws RuntimeException Always throws as not implemented.
     */
    public function selectQuantity($quantity): void
    {
        // TODO: Implement actual logic if different from updateItemQuantity.
        throw new RuntimeException(__METHOD__ . ' is not implemented yet. Consider using updateItemQuantity.');
    }

    /**
     * Handles 3D Secure authentication steps (Not Implemented).
     *
     * @param array $authDetails Optional details for authentication.
     * @throws RuntimeException Always throws as not implemented.
     */
    public function handle3DSecureAuthentication(array $authDetails = []): void
    {
        // TODO: Implement actual logic to handle 3DS iframe/redirect.
        throw new RuntimeException(__METHOD__ . ' is not implemented yet.');
    }

    /**
     * Completes the PayPal checkout process (Not Implemented).
     * This might involve confirming payment on a PayPal page/iframe.
     *
     * @throws RuntimeException Always throws as not implemented.
     */
    public function completePayPalCheckout(): void
    {
        // TODO: Implement actual logic for PayPal checkout confirmation.
        throw new RuntimeException(__METHOD__ . ' is not implemented yet.');
    }

    // NOTE: If the error message\'s \"...\" indicated other missing methods,
    //       you might need to add stubs for those as well.

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // TODO: Implement actual logic to count subscription items
        // For now, return a dummy value
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // TODO: Implement actual logic to count one-time purchase items
        // For now, return a dummy value
        return $this->getItemCount();
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // TODO: Implement actual logic to get subscription frequencies
        // For now, return an empty array
        return [];
    }
}
