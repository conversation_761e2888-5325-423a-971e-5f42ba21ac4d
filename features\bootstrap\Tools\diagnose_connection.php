<?php

require_once __DIR__ . '/../../../vendor/autoload.php';

use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Net\SSH2;

try {
    echo "Running comprehensive connection diagnostics...\n\n";

    // SSH Connection
    echo "1. Testing SSH Connection\n";
    echo str_repeat('-', 50) . "\n";

    $ssh = new SSH2('18.170.243.171');
    $key = PublicKeyLoader::load("**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");

    if (!$ssh->login('ec2-user', $key)) {
        throw new RuntimeException('SSH authentication failed');
    }
    echo "✓ SSH Connection successful\n\n";

    // Check MySQL Configuration
    echo "2. Checking MySQL Configuration\n";
    echo str_repeat('-', 50) . "\n";

    $result = $ssh->exec('sudo cat /etc/my.cnf 2>/dev/null || sudo cat /etc/mysql/my.cnf 2>/dev/null');
    echo "MySQL Configuration:\n$result\n\n";

    // Check MySQL Bind Address
    echo "3. Checking MySQL Bind Address\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec("sudo netstat -tlpn | grep mysql");
    echo "MySQL Listen Status:\n$result\n\n";

    // Check Firewall Status
    echo "4. Checking Firewall Status\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('sudo iptables -L -n -v');
    echo "Firewall Rules:\n$result\n\n";

    // Check SELinux Status
    echo "5. Checking SELinux Status\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('getenforce 2>/dev/null || echo "SELinux not installed"');
    echo "SELinux Status: $result\n\n";

    // Test Direct MySQL Connection
    echo "6. Testing Direct MySQL Connection\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('nc -zv ************* 3306');
    echo "Direct MySQL Connection Test:\n$result\n\n";

    // Check Local Windows Firewall
    echo "7. Checking Local Windows Firewall\n";
    echo str_repeat('-', 50) . "\n";
    $result = shell_exec('netsh advfirewall show currentprofile');
    echo "Windows Firewall Status:\n$result\n\n";

    // Test Local Port Availability
    echo "8. Testing Local Port Availability\n";
    echo str_repeat('-', 50) . "\n";
    $port = 49152;
    $sock = @fsockopen('127.0.0.1', $port, $errno, $errstr, 0.1);
    if ($sock) {
        fclose($sock);
        echo "Port $port is in use\n";
    } else {
        echo "Port $port is available\n";
    }

    echo "\nDiagnostics completed.\n";

} catch (Exception $e) {
    echo "Error during diagnostics: " . $e->getMessage() . "\n";
    exit(1);
} 