<?php

namespace App\Service\Reporting;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;

/**
 * Service for test result reporting
 */
class ReportingService extends AbstractService implements ReportingServiceInterface
{
    private string $reportName;
    private array $reportMetadata = [];
    private array $results = [];
    private array $errors = [];
    private string $projectRoot;

    /**
     * Constructor
     *
     * @param string $projectRoot Project root directory
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(string $projectRoot, ?LoggerInterface $logger = null)
    {
        parent::__construct($logger);
        $this->projectRoot = $projectRoot;
        $this->reportName = 'Test Report';
        $this->logInfo("ReportingService initialized");
    }

    /**
     * {@inheritdoc}
     */
    public function initReport(string $name, array $metadata = []): void
    {
        $this->reportName = $name;
        $this->reportMetadata = $metadata;
        $this->results = [];
        $this->errors = [];

        // Add default metadata
        $this->reportMetadata['date'] = date('Y-m-d H:i:s');
        $this->reportMetadata['environment'] = getenv('TEST_ENV') ?: 'unknown';
        $this->reportMetadata['brand'] = getenv('TEST_BRAND') ?: 'unknown';

        $this->logInfo("Initialized report: $name");
    }

    /**
     * {@inheritdoc}
     */
    public function recordResult(string $name, bool $success, array $metadata = []): void
    {
        $this->results[] = [
            'name' => $name,
            'success' => $success,
            'timestamp' => date('Y-m-d H:i:s'),
            'metadata' => $metadata
        ];

        $status = $success ? 'SUCCESS' : 'FAILURE';
        $this->logInfo("Recorded test result: $name - $status");
    }

    /**
     * {@inheritdoc}
     */
    public function recordError(string $message, \Throwable $exception = null): void
    {
        $error = [
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        if ($exception) {
            $error['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }

        $this->errors[] = $error;
        $this->logError("Recorded error: $message", $exception);
    }

    /**
     * {@inheritdoc}
     */
    public function generateReport(string $format = 'html', string $outputPath = null): string
    {
        $this->logInfo("Generating report in format: $format");

        $content = '';
        switch (strtolower($format)) {
            case 'html':
                $content = $this->generateHtmlReport();
                break;

            case 'json':
                $content = $this->generateJsonReport();
                break;

            case 'xml':
                $content = $this->generateXmlReport();
                break;

            default:
                throw new RuntimeException("Unsupported report format: $format");
        }

        if ($outputPath) {
            $dir = dirname($outputPath);
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }

            file_put_contents($outputPath, $content);
            $this->logInfo("Report saved to: $outputPath");
        }

        return $content;
    }

    /**
     * Generate an HTML report
     *
     * @return string HTML report content
     */
    private function generateHtmlReport(): string
    {
        $successCount = count(array_filter($this->results, function ($result) {
            return $result['success'];
        }));

        $failureCount = count($this->results) - $successCount;

        $html = '<!DOCTYPE html>
<html>
<head>
    <title>' . htmlspecialchars($this->reportName) . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .summary { margin: 20px 0; padding: 10px; background-color: #f5f5f5; border-radius: 5px; }
        .success { color: green; }
        .failure { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .error-section { margin-top: 30px; border-top: 1px solid #ddd; padding-top: 20px; }
        .error { background-color: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid #ff0000; }
    </style>
</head>
<body>
    <h1>' . htmlspecialchars($this->reportName) . '</h1>
    
    <div class="summary">
        <p><strong>Date:</strong> ' . htmlspecialchars($this->reportMetadata['date']) . '</p>
        <p><strong>Environment:</strong> ' . htmlspecialchars($this->reportMetadata['environment']) . '</p>
        <p><strong>Brand:</strong> ' . htmlspecialchars($this->reportMetadata['brand']) . '</p>
        <p><strong>Results:</strong> 
            <span class="success">' . $successCount . ' passed</span>, 
            <span class="failure">' . $failureCount . ' failed</span>, 
            <span>' . count($this->results) . ' total</span>
        </p>
    </div>';

        if (!empty($this->results)) {
            $html .= '
    <h2>Test Results</h2>
    <table>
        <tr>
            <th>Test</th>
            <th>Status</th>
            <th>Timestamp</th>
        </tr>';

            foreach ($this->results as $result) {
                $statusClass = $result['success'] ? 'success' : 'failure';
                $statusText = $result['success'] ? 'PASSED' : 'FAILED';

                $html .= '
        <tr>
            <td>' . htmlspecialchars($result['name']) . '</td>
            <td class="' . $statusClass . '">' . $statusText . '</td>
            <td>' . htmlspecialchars($result['timestamp']) . '</td>
        </tr>';
            }

            $html .= '
    </table>';
        }

        if (!empty($this->errors)) {
            $html .= '
    <div class="error-section">
        <h2>Errors</h2>';

            foreach ($this->errors as $error) {
                $html .= '
        <div class="error">
            <p><strong>Error:</strong> ' . htmlspecialchars($error['message']) . '</p>
            <p><strong>Time:</strong> ' . htmlspecialchars($error['timestamp']) . '</p>';

                if (isset($error['exception'])) {
                    $html .= '
            <p><strong>Exception:</strong> ' . htmlspecialchars($error['exception']['class']) . '</p>
            <p><strong>Message:</strong> ' . htmlspecialchars($error['exception']['message']) . '</p>
            <p><strong>Location:</strong> ' . htmlspecialchars($error['exception']['file']) . ':' . $error['exception']['line'] . '</p>';
                }

                $html .= '
        </div>';
            }

            $html .= '
    </div>';
        }

        $html .= '
</body>
</html>';

        return $html;
    }

    /**
     * Generate a JSON report
     *
     * @return string JSON report content
     */
    private function generateJsonReport(): string
    {
        $report = [
            'name' => $this->reportName,
            'metadata' => $this->reportMetadata,
            'results' => $this->results,
            'errors' => $this->errors,
            'summary' => [
                'total' => count($this->results),
                'success' => count(array_filter($this->results, function ($result) {
                    return $result['success'];
                })),
                'failure' => count(array_filter($this->results, function ($result) {
                    return !$result['success'];
                })),
                'error_count' => count($this->errors)
            ]
        ];

        return json_encode($report, JSON_PRETTY_PRINT);
    }

    /**
     * Generate an XML report
     *
     * @return string XML report content
     */
    private function generateXmlReport(): string
    {
        $successCount = count(array_filter($this->results, function ($result) {
            return $result['success'];
        }));

        $failureCount = count($this->results) - $successCount;

        $xml = '<?xml version="1.0" encoding="UTF-8"?>
<testReport>
    <name>' . $this->xmlEscape($this->reportName) . '</name>
    <metadata>
        <date>' . $this->xmlEscape($this->reportMetadata['date']) . '</date>
        <environment>' . $this->xmlEscape($this->reportMetadata['environment']) . '</environment>
        <brand>' . $this->xmlEscape($this->reportMetadata['brand']) . '</brand>
    </metadata>
    <summary>
        <total>' . count($this->results) . '</total>
        <success>' . $successCount . '</success>
        <failure>' . $failureCount . '</failure>
        <errorCount>' . count($this->errors) . '</errorCount>
    </summary>
    <results>';

        foreach ($this->results as $result) {
            $xml .= '
        <result>
            <name>' . $this->xmlEscape($result['name']) . '</name>
            <success>' . ($result['success'] ? 'true' : 'false') . '</success>
            <timestamp>' . $this->xmlEscape($result['timestamp']) . '</timestamp>
        </result>';
        }

        $xml .= '
    </results>
    <errors>';

        foreach ($this->errors as $error) {
            $xml .= '
        <error>
            <message>' . $this->xmlEscape($error['message']) . '</message>
            <timestamp>' . $this->xmlEscape($error['timestamp']) . '</timestamp>';

            if (isset($error['exception'])) {
                $xml .= '
            <exception>
                <class>' . $this->xmlEscape($error['exception']['class']) . '</class>
                <message>' . $this->xmlEscape($error['exception']['message']) . '</message>
                <file>' . $this->xmlEscape($error['exception']['file']) . '</file>
                <line>' . $error['exception']['line'] . '</line>
            </exception>';
            }

            $xml .= '
        </error>';
        }

        $xml .= '
    </errors>
</testReport>';

        return $xml;
    }

    /**
     * Escape a string for XML
     *
     * @param string $string String to escape
     * @return string Escaped string
     */
    private function xmlEscape(string $string): string
    {
        return htmlspecialchars($string, ENT_XML1 | ENT_QUOTES, 'UTF-8');
    }
}
