<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for upsell-related functionality
 */
class UpsellContext extends BaseContext
{
    private PageFactoryInterface $pageFactory;
    private BrowserServiceInterface $browserService;
    private SharedStateServiceInterface $stateService;

    /**
     * Reference to email context for verification steps
     */
    private ?EmailContext $emailContext = null;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param PageFactoryInterface|null $pageFactory Page factory service
     * @param BrowserServiceInterface|null $browserService Browser service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface          $container = null,
        ?PageFactoryInterface        $pageFactory = null,
        ?BrowserServiceInterface     $browserService = null,
        ?SharedStateServiceInterface $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
            $this->browserService = $browserService ?? $container->get(BrowserServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
            $this->browserService = $browserService ?? $this->createMockBrowserService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $this->logInfo("UpsellContext initialized");
    }

    /**
     * Create a mock page factory for testing
     *
     * @return PageFactoryInterface
     */
    private function createMockPageFactory(): PageFactoryInterface
    {
        return new class implements PageFactoryInterface {
            public function createPage(string $pageClass, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot create pages');
            }

            public function getPage(string $pageName, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot get pages');
            }

            public function hasPage(string $pageName): bool
            {
                return false;
            }
        };
    }

    /**
     * Create a mock browser service for testing
     *
     * @return BrowserServiceInterface
     */
    private function createMockBrowserService(): BrowserServiceInterface
    {
        return new class implements BrowserServiceInterface {
            public function elementExists(string $selector): bool
            {
                return true;
            }

            public function wait(int $seconds): void
            { /* do nothing */
            }

            public function isSessionActive(): bool
            {
                return true;
            }

            public function getDriverType(): string
            {
                return 'mock';
            }

            public function hasContent(string $text): bool
            {
                return true;
            }

            public function navigateBack(): void
            { /* do nothing */
            }

            public function getPageTitle(): string
            {
                return 'Mock Page Title';
            }

            public function waitForUrlContains(string $text, int $timeout = 30): bool
            {
                return true;
            }

            public function isBrowserStackSession(): bool
            {
                return false;
            }

            public function findElement(string $selector): ?\Behat\Mink\Element\NodeElement
            {
                return null;
            }

            public function getCurrentUrl(): string
            {
                return 'https://example.com';
            }

            public function visit(string $url): void
            { /* do nothing */
            }

            public function getSession(): \Behat\Mink\Session
            {
                throw new \RuntimeException('Not implemented');
            }

            public function waitForElement(string $selector, int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForPageToLoad(int $timeout = 30): void
            { /* do nothing */
            }

            public function takeScreenshot(string $name = null): string
            {
                return '/path/to/screenshot.png';
            }

            public function fillField(string $field, string $value): void
            { /* do nothing */
            }

            public function selectOption(string $select, string $option): void
            { /* do nothing */
            }

            public function executeScript(string $script)
            {
                return null;
            }

            public function findElements(string $selector): array
            {
                return [];
            }

            public function waitForElementVisible(string $selector, int $timeout = 30): bool
            {
                return true;
            }

            public function scrollToElement(string $selector): void
            { /* do nothing */
            }

            public function clickElement(string $selector): void
            { /* do nothing */
            }

            public function getElementText(string $selector): string
            {
                return 'Mock Text';
            }

            public function isElementVisible(string $selector): bool
            {
                return true;
            }

            public function waitForDocumentReady(int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForAjaxToComplete(int $timeout = 30): void
            { /* do nothing */
            }

            public function clearCookies(): void
            { /* do nothing */
            }

            public function waitForPageLoad(): void
            { /* do nothing */
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        $environment = $scope->getEnvironment();

        // Get related contexts if available
        if ($environment->hasContextClass(EmailContext::class)) {
            $this->emailContext = $environment->getContext(EmailContext::class);
        }

        $this->logInfo("Gathered contexts in BeforeScenario hook");
    }

    /**
     * @Then /^I should see the upsell message$/
     */
    public function iShouldSeeTheUpsellMessage(): void
    {
        try {
            $upsellPage = $this->pageFactory->getPage('UpsellPage');
            $upsellMessage = $upsellPage->getUpsellMessage();

            if (empty($upsellMessage)) {
                throw new \RuntimeException('Upsell message not found on page');
            }

            $this->stateService->set('upsell.message', $upsellMessage);
            $this->logInfo("Verified upsell message is displayed");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify upsell message", $e);
            throw new \RuntimeException(
                sprintf('Failed to verify upsell message: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I accept the first upsell offer$/
     */
    public function iAcceptTheFirstUpsellOffer(): void
    {
        try {
            $this->iAcceptTheUpsellOffer();
            $this->stateService->set('upsell.first_accepted', true);
            $this->logInfo("Accepted first upsell offer");
        } catch (\Throwable $e) {
            $this->logError("Failed to accept first upsell offer", $e);
            throw $e;
        }
    }

    /**
     * @When /^I accept the upsell offer$/
     */
    public function iAcceptTheUpsellOffer(): void
    {
        try {
            $upsellPage = $this->pageFactory->getPage('UpsellPage');
            $upsellPage->acceptOffer();

            $this->stateService->set('upsell.accepted', true);
            $this->logInfo("Accepted upsell offer");
        } catch (\Throwable $e) {
            $this->logError("Failed to accept upsell offer", $e);
            throw new \RuntimeException(
                sprintf('Failed to accept upsell offer: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I accept the second upsell offer$/
     */
    public function iAcceptTheSecondUpsellOffer(): void
    {
        try {
            $this->iAcceptTheUpsellOffer();
            $this->stateService->set('upsell.second_accepted', true);
            $this->logInfo("Accepted second upsell offer");
        } catch (\Throwable $e) {
            $this->logError("Failed to accept second upsell offer", $e);
            throw $e;
        }
    }

    /**
     * @When /^I decline the first upsell offer$/
     */
    public function iDeclineTheFirstUpsellOffer(): void
    {
        try {
            $this->iDeclineTheUpsellOffer();
            $this->stateService->set('upsell.first_declined', true);
            $this->logInfo("Declined first upsell offer");
        } catch (\Throwable $e) {
            $this->logError("Failed to decline first upsell offer", $e);
            throw $e;
        }
    }

    /**
     * @When /^I decline the upsell offer$/
     */
    public function iDeclineTheUpsellOffer(): void
    {
        try {
            $upsellPage = $this->pageFactory->getPage('UpsellPage');
            $upsellPage->declineOffer();

            $this->stateService->set('upsell.declined', true);
            $this->logInfo("Declined upsell offer");
        } catch (\Throwable $e) {
            $this->logError("Failed to decline upsell offer", $e);
            throw new \RuntimeException(
                sprintf('Failed to decline upsell offer: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I decline the second upsell offer$/
     */
    public function iDeclineTheSecondUpsellOffer(): void
    {
        try {
            $this->iDeclineTheUpsellOffer();
            $this->stateService->set('upsell.second_declined', true);
            $this->logInfo("Declined second upsell offer");
        } catch (\Throwable $e) {
            $this->logError("Failed to decline second upsell offer", $e);
            throw $e;
        }
    }

    /**
     * @When /^I click the accept button multiple times$/
     */
    public function iClickTheAcceptButtonMultipleTimes(): void
    {
        try {
            $upsellPage = $this->pageFactory->getPage('UpsellPage');

            // Since the page object doesn't have a specific method for multiple clicks,
            // we'll implement it here by clicking multiple times with a short delay
            for ($i = 0; $i < 3; $i++) {
                $this->browserService->clickElement('.upsell-accept');
                usleep(200000); // 0.2 second delay between clicks
            }

            $this->stateService->set('upsell.multiple_clicks', true);
            $this->logInfo("Clicked accept button multiple times");
        } catch (\Throwable $e) {
            $this->logError("Failed to click accept button multiple times", $e);
            throw new \RuntimeException(
                sprintf('Failed to click accept button multiple times: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify dietary restriction warnings are displayed$/
     */
    public function iVerifyDietaryRestrictionWarningsAreDisplayed(): void
    {
        try {
            $upsellPage = $this->pageFactory->getPage('UpsellPage');
            $funnel = $this->stateService->get('currentFunnel');

            if (!isset($funnel['restrictions']) || !isset($funnel['restrictions']['dietary'])) {
                throw new \RuntimeException('No dietary restrictions defined for this funnel');
            }

            $warnings = $funnel['restrictions']['dietary'];
            $displayedRestrictions = $upsellPage->getProductRestrictions();

            foreach ($warnings as $warning) {
                $found = false;
                foreach ($displayedRestrictions as $restriction) {
                    if (strpos($restriction, $warning) !== false) {
                        $found = true;
                        break;
                    }
                }

                if (!$found) {
                    throw new \RuntimeException(
                        sprintf('Expected dietary restriction warning "%s" not found', $warning)
                    );
                }
            }

            $this->logInfo("Verified dietary restriction warnings are displayed");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify dietary restriction warnings", $e);
            throw new \RuntimeException(
                sprintf('Failed to verify dietary restriction warnings: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should be redirected to the upsell page
     * @param string $position Optional position (e.g., "first", "second") - CURRENTLY UNUSED, matches legacy method signature
     */
    public function iShouldBeRedirectedToTheUpsellPage(string $position = ''): void
    {
        try {
            // Get the current URL
            $currentUrl = $this->browserService->getCurrentUrl();

            // Determine expected path based on state (simplification from plan)
            // The plan suggested getting funnel data, but a simple check for 'upsell' might suffice for now.
            // TODO: Enhance this with more specific funnel data checks if needed.
            $expectedPath = 'upsell';

            if (strpos($currentUrl, $expectedPath) === false) {
                throw new \RuntimeException(
                    sprintf('Expected to be redirected to upsell page (containing "%s"), but current URL is: %s', $expectedPath, $currentUrl)
                );
            }

            $this->stateService->set('page.current', 'upsell');
            $this->logInfo(sprintf("Verified redirect to upsell page: %s", $currentUrl));
        } catch (\Throwable $e) {
            $this->logError("Failed to verify redirect to upsell page", $e);
            throw $e;
        }
    }
}