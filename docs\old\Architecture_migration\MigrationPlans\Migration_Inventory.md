# Migration Inventory

This document tracks the migration status of components from the old architecture to the new service-oriented architecture.

## Page Objects

| Page Object      | Current Location                             | Target Location               | Dependencies                                 | Status    |
|------------------|----------------------------------------------|-------------------------------|----------------------------------------------|-----------|
| BasePage         | features/bootstrap/Page/BasePage.php         | src/Page/Base/BasePage.php    | BrowserService                               | Completed |
| HomePage         | features/bootstrap/Page/HomePage.php         | src/Page/HomePage.php         | BasePage, BrowserService                     | Completed |
| ProductPage      | features/bootstrap/Page/ProductPage.php      | src/Page/ProductPage.php      | BasePage, BrowserService, TestDataService    | Completed |
| CartPage         | features/bootstrap/Page/CartPage.php         | src/Page/CartPage.php         | BasePage, BrowserService                     | Completed |
| CheckoutPage     | features/bootstrap/Page/CheckoutPage.php     | src/Page/CheckoutPage.php     | BasePage, BrowserService, ValidationService  | Completed |
| ConfirmationPage | features/bootstrap/Page/ConfirmationPage.php | src/Page/ConfirmationPage.php | BasePage, BrowserService                     | Completed |
| PayPalPage       | features/bootstrap/Page/PayPalPage.php       | src/Page/PayPalPage.php       | BasePage, BrowserService                     | Completed |
| Stripe3DSPage    | features/bootstrap/Page/Stripe3DSPage.php    | src/Page/Stripe3DSPage.php    | BasePage, BrowserService                     | Completed |
| UpsellPage       | features/bootstrap/Page/UpsellPage.php       | src/Page/UpsellPage.php       | BasePage, BrowserService, SharedStateService | Completed |

## Contexts

| Context              | Current Location                                    | Target Location                      | Dependencies                                                             | Status    |
|----------------------|-----------------------------------------------------|--------------------------------------|--------------------------------------------------------------------------|-----------|
| BaseContext          | features/bootstrap/Context/BaseContext.php          | src/Context/Base/BaseContext.php     | ServiceAwareContext                                                      | Completed |
| FeatureContext       | features/bootstrap/FeatureContext.php               | src/Context/FeatureContext.php       | BaseContext, PageFactory, SharedStateService                             | Completed |
| BrandContext         | features/bootstrap/Context/BrandContext.php         | src/Context/BrandContext.php         | BaseContext, ConfigurationService, SharedStateService                    | Completed |
| ProductContext       | features/bootstrap/Context/ProductContext.php       | src/Context/ProductContext.php       | BaseContext, ProductPage, SharedStateService                             | Completed |
| CartContext          | features/bootstrap/Context/CartContext.php          | src/Context/CartContext.php          | BaseContext, CartPage, SharedStateService                                | Completed |
| CheckoutContext      | features/bootstrap/Context/CheckoutContext.php      | src/Context/CheckoutContext.php      | BaseContext, CheckoutPage, ValidationService, SharedStateService         | Completed |
| PaymentContext       | features/bootstrap/Context/PaymentContext.php       | src/Context/PaymentContext.php       | BaseContext, CheckoutPage, PayPalPage, Stripe3DSPage, SharedStateService | Completed |
| EmailContext         | features/bootstrap/Context/EmailContext.php         | src/Context/EmailContext.php         | BaseContext, ConfigurationService, SharedStateService                    | Completed |
| UpsellContext        | features/bootstrap/Context/UpsellContext.php        | src/Context/UpsellContext.php        | BaseContext, UpsellPage, SharedStateService                              | Completed |
| SalesFunnelContext   | features/bootstrap/Context/SalesFunnelContext.php   | src/Context/SalesFunnelContext.php   | BaseContext, multiple page objects, SharedStateService                   | Completed |
| ValidationContext    | features/bootstrap/Context/ValidationContext.php    | src/Context/ValidationContext.php    | BaseContext, ValidationService, SharedStateService                       | Completed |
| AdminCommandContext  | features/bootstrap/Context/AdminCommandContext.php  | src/Context/AdminCommandContext.php  | BaseContext, ConfigurationService, SharedStateService                    | Completed |
| AbandonedCartContext | features/bootstrap/Context/AbandonedCartContext.php | src/Context/AbandonedCartContext.php | BaseContext, CartPage, CheckoutPage, SharedStateService                  | Completed |
| DatabaseContext      | features/bootstrap/Context/DatabaseContext.php      | src/Context/DatabaseContext.php      | BaseContext, ConfigurationService                                        | Completed |
| SSHContext           | features/bootstrap/Context/SSHContext.php           | src/Context/SSHContext.php           | BaseContext, ConfigurationService                                        | Completed |

## Common Utility Methods

| Method                | Current Location                | Target Location                       | Description                        | Status    |
|-----------------------|---------------------------------|---------------------------------------|------------------------------------|-----------|
| waitForElementVisible | BasePage::waitForElementVisible | BrowserService::waitForElementVisible | Waits for an element to be visible | Completed |
| scrollToElement       | BasePage::scrollToElement       | BrowserService::scrollToElement       | Scrolls to an element              | Completed |
| clickElement          | BasePage::clickElement          | BrowserService::clickElement          | Clicks on an element               | Completed |
| fillField             | BasePage::fillField             | BrowserService::fillField             | Fills a form field                 | Completed |
| selectOption          | BasePage::selectOption          | BrowserService::selectOption          | Selects an option from a dropdown  | Completed |
| getElementText        | BasePage::getElementText        | BrowserService::getElementText        | Gets text from an element          | Completed |
| isElementVisible      | BasePage::isElementVisible      | BrowserService::isElementVisible      | Checks if an element is visible    | Completed |
| waitForPageToLoad     | BasePage::waitForPageToLoad     | BrowserService::waitForPageToLoad     | Waits for page to load             | Completed |
| takeScreenshot        | FeatureContext::takeScreenshot  | BrowserService::takeScreenshot        | Takes a screenshot                 | Completed |
| logStep               | FeatureContext::logStep         | LoggingService::logStep               | Logs a step                        | Completed |

## Singleton Pattern Usage

| Singleton            | Current Usage                         | Replacement                                   | Status    |
|----------------------|---------------------------------------|-----------------------------------------------|-----------|
| SharedDataContext    | Used across contexts for data sharing | SharedStateService injected via constructor   | Completed |
| ConfigurationManager | Used for configuration management     | ConfigurationService injected via constructor | Completed |
| TestDataRegistry     | Used for test data management         | TestDataService injected via constructor      | Completed |

## Migration Progress

- **Page Objects**: 9/9 (100%)
- **Contexts**: 15/15 (100%)
- **Utility Methods**: 10/10 (100%)
- **Singleton Replacements**: 3/3 (100%)

## Next Steps

1. Start with migrating the base classes:
   - ServiceAwareContext
   - BaseContext
   - BasePage

2. Migrate core services:
   - SharedStateService (to replace SharedDataContext)
   - ConfigurationService (to replace ConfigurationManager)
   - TestDataService (to replace TestDataRegistry)
   - BrowserService (to encapsulate browser interactions)

3. Migrate page objects in this order:
   - HomePage (simplest)
   - ProductPage
   - CartPage
   - CheckoutPage
   - ConfirmationPage
   - PayPalPage
   - Stripe3DSPage
   - UpsellPage

4. Migrate contexts in this order:
   - FeatureContext
   - BrandContext
   - ProductContext
   - CartContext
   - CheckoutContext
   - Other contexts

5. Update step definitions to use the new services and page objects

6. Verify functionality with comprehensive tests
