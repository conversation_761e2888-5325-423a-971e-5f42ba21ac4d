<?php

require_once __DIR__ . '/vendor/autoload.php';

// Check if the FriendsOfBehat\MinkExtension class exists
if (class_exists('FriendsOfBehat\MinkExtension\ServiceContainer\MinkExtension')) {
    echo "FriendsOfBehat\\MinkExtension\\ServiceContainer\\MinkExtension class exists\n";
} else {
    echo "FriendsOfBehat\\MinkExtension\\ServiceContainer\\MinkExtension class does not exist\n";
}

// Check if the friends-of-behat/mink-extension directory exists
if (is_dir(__DIR__ . '/vendor/friends-of-behat/mink-extension')) {
    echo "friends-of-behat/mink-extension directory exists\n";

    // List the files in the directory
    echo "Files in the directory:\n";
    $files = scandir(__DIR__ . '/vendor/friends-of-behat/mink-extension');
    print_r($files);

    // Check if the ServiceContainer directory exists
    if (is_dir(__DIR__ . '/vendor/friends-of-behat/mink-extension/src/ServiceContainer')) {
        echo "ServiceContainer directory exists\n";
    } else {
        echo "ServiceContainer directory does not exist\n";
    }

    // Check if the ServiceContainer directory exists in the Behat namespace
    if (is_dir(__DIR__ . '/vendor/friends-of-behat/mink-extension/src/Behat/MinkExtension/ServiceContainer')) {
        echo "Behat/MinkExtension/ServiceContainer directory exists\n";

        // List the files in the directory
        echo "Files in the Behat/MinkExtension/ServiceContainer directory:\n";
        $files = scandir(__DIR__ . '/vendor/friends-of-behat/mink-extension/src/Behat/MinkExtension/ServiceContainer');
        print_r($files);
    } else {
        echo "Behat/MinkExtension/ServiceContainer directory does not exist\n";
    }
} else {
    echo "friends-of-behat/mink-extension directory does not exist\n";
}

// Check the autoload files
echo "Checking autoload files:\n";
$autoloadFiles = require __DIR__ . '/vendor/composer/autoload_files.php';
echo "Number of autoload files: " . count($autoloadFiles) . "\n";

// Check the autoload classmap
echo "Checking autoload classmap:\n";
$autoloadClassmap = require __DIR__ . '/vendor/composer/autoload_classmap.php';
$minkExtensionClasses = array_filter(array_keys($autoloadClassmap), function ($className) {
    return strpos($className, 'MinkExtension') !== false;
});
echo "MinkExtension classes in classmap:\n";
print_r($minkExtensionClasses);

// Check the autoload namespaces
echo "Checking autoload namespaces:\n";
$autoloadNamespaces = require __DIR__ . '/vendor/composer/autoload_namespaces.php';
echo "Namespaces:\n";
print_r($autoloadNamespaces);

// Check the autoload PSR-4
echo "Checking autoload PSR-4:\n";
$autoloadPsr4 = require __DIR__ . '/vendor/composer/autoload_psr4.php';
$minkExtensionNamespaces = array_filter(array_keys($autoloadPsr4), function ($namespace) {
    return strpos($namespace, 'MinkExtension') !== false || strpos($namespace, 'Behat\\') !== false || strpos($namespace, 'FriendsOfBehat\\') !== false;
});
echo "MinkExtension namespaces in PSR-4:\n";
print_r($minkExtensionNamespaces);
