<?php

namespace Features\Bootstrap\Traits;

use Behat\Mink\Session;
use Behat\MinkExtension\Context\RawMinkContext;

trait MinkSessionTrait
{
    protected ?Session $session = null;
    
    public function getSession($name = null): Session 
    {
        if (!$this->session) {
            if (!$this instanceof RawMinkContext) {
                throw new \RuntimeException('MinkSessionTrait can only be used in classes that extend RawMinkContext');
            }
            $this->session = parent::getSession($name);
        }
        return $this->session;
    }

    public function visit($page): void
    {
        $this->getSession()->visit($page);
    }

    public function assertUrlContains($text): void
    {
        $currentUrl = $this->getSession()->getCurrentUrl();
        if (strpos($currentUrl, $text) === false) {
            throw new \RuntimeException(sprintf(
                'Current URL "%s" does not contain "%s"',
                $currentUrl,
                $text
            ));
        }
    }
} 