<?php

require_once __DIR__ . '/../../../vendor/autoload.php';

use Features\Bootstrap\Helper\DatabaseHelper;

// Load environment variables
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found at: $envFile\n");
}

$env = parse_ini_file($envFile, true);
if ($env === false) {
    die("Error: Failed to parse .env file\n");
}

// Configure database connection
$config = [
    'ssh' => [
        'host' => $env['SSH_HOST'] ?? null,
        'user' => $env['SSH_USER'] ?? null,
        'private_key' => $env['SSH_KEY'] ?? null,
    ],
    'db' => [
        'host' => $env['DB_HOST'] ?? null,
        'port' => (int)($env['DB_PORT'] ?? 3306),
        'name' => $env['DB_NAME'] ?? null,
        'user' => $env['DB_USER'] ?? null,
        'password' => $env['DB_PASSWORD'] ?? null,
    ]
];

// Debug output to verify config
echo "Configuration loaded:\n";
echo "SSH Host: " . ($config['ssh']['host'] ?? 'not set') . "\n";
echo "SSH User: " . ($config['ssh']['user'] ?? 'not set') . "\n";
echo "SSH Key: " . (isset($config['ssh']['private_key']) ? 'present' : 'not set') . "\n";
echo "DB Host: " . ($config['db']['host'] ?? 'not set') . "\n";
echo "DB Name: " . ($config['db']['name'] ?? 'not set') . "\n\n";

try {
    echo "Starting database verification...\n\n";

    // Create database helper
    $db = new DatabaseHelper($config);

    // Connect to database
    echo "Connecting to database...\n";
    $db->connect();

    // Test query
    echo "\nTesting database query...\n";
    $result = $db->query('SELECT VERSION() as version');
    echo "MySQL Version: " . $result[0]['version'] . "\n";

    // Test database access
    echo "\nTesting database access...\n";
    $result = $db->query('SHOW DATABASES');
    echo "Available databases:\n";
    foreach ($result as $row) {
        echo "- " . $row['Database'] . "\n";
    }

    // Test table access
    echo "\nTesting table access...\n";
    $result = $db->query('SHOW TABLES');
    echo "Tables in current database:\n";
    foreach ($result as $row) {
        $tableName = reset($row);
        echo "- " . $tableName . "\n";
    }

    echo "\nDatabase verification completed successfully.\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
} 