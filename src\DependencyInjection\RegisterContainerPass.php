<?php

namespace App\DependencyInjection;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

/**
 * Compiler pass to register the container as a service
 */
class RegisterContainerPass implements CompilerPassInterface
{
    /**
     * Set the container after compilation
     *
     * @param ContainerBuilder $container
     */
    public static function setContainerAfterCompilation(ContainerBuilder $container)
    {
        // Set the container in the global scope
        $GLOBALS['service_container'] = $container;

        // Call the setServiceContainer function if it exists
        if (function_exists('setServiceContainer')) {
            setServiceContainer($container);
        }

        // Set the container interface service
        if ($container->has('Symfony\Component\DependencyInjection\ContainerInterface')) {
            try {
                $container->get('Symfony\Component\DependencyInjection\ContainerInterface');
            } catch (\Exception $e) {
                // Ignore if we can't get it
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function process(ContainerBuilder $container)
    {
        // Register the container interface as a synthetic service
        if (!$container->has('Symfony\Component\DependencyInjection\ContainerInterface')) {
            $container->register('Symfony\Component\DependencyInjection\ContainerInterface', 'Symfony\Component\DependencyInjection\ContainerInterface')
                ->setSynthetic(true)
                ->setPublic(true);
        }

        // Set the container in the global scope
        $GLOBALS['service_container'] = $container;

        // Call the setServiceContainer function if it exists
        if (function_exists('setServiceContainer')) {
            setServiceContainer($container);
        }
    }
}
