<?php
$url = 'http://selenium_chrome:4444/wd/hub/status';
echo "Attempting to curl: " . $url . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10 second connect timeout
curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30 second total timeout
curl_setopt($ch, CURLOPT_VERBOSE, true); // Enable verbose output

$response = curl_exec($ch);
$errorNo = curl_errno($ch);
$errorMsg = curl_error($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($errorNo !== 0) {
    echo "\n--- PHP cURL Error ---\n";
    echo "Error Number: " . $errorNo . "\n";
    echo "Error Message: " . $errorMsg . "\n";
    exit(1);
} else {
    echo "\n--- PHP cURL Success ---\n";
    echo "HTTP Status Code: " . $httpCode . "\n";
    echo "Response:\n" . $response . "\n";
    exit(0);
} 