# New Architecture Step Registry

This document provides a comprehensive registry of all Behat step definitions used in the test suite, mapping each step to its corresponding context method implementation with input/output parameters.

## Step Definition Registry by Context

### Product Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I am viewing the product :productIdentifier` | `ProductContext::iAmViewingTheProduct()` | `$productIdentifier` (string, optional) | void | High |
| `I am on the :productIdentifier product page` | `ProductContext::iAmViewingTheProduct()` | `$productIdentifier` (string, optional) | void | High |
| `I am on the product page` | `ProductContext::iAmViewingTheProduct()` | none | void | Very High |
| `I select flavor :flavor` | `ProductContext::iSelectFlavor()` | `$flavor` (string) | void | High |
| `I select the flavor :flavor` | `ProductContext::iSelectFlavor()` | `$flavor` (string) | void | High |
| `I select quantity :quantity` | `ProductContext::iSelectQuantity()` | `$quantity` (string/int) | void | Medium |
| `I select the :quantity quantity option` | `ProductContext::iSelectQuantity()` | `$quantity` (string/int) | void | High |
| `I select :quantity quantity` | `ProductContext::iSelectQuantity()` | `$quantity` (string/int) | void | Medium |
| `I select purchase type :purchaseType` | `ProductContext::iSelectPurchaseType()` | `$purchaseType` (string) | void | Medium |
| `I select :purchaseType purchase` | `ProductContext::iSelectPurchaseType()` | `$purchaseType` (string) | void | Medium |
| `I select the :purchaseType purchase type` | `ProductContext::iSelectPurchaseType()` | `$purchaseType` (string) | void | Medium |
| `I set the supply to :frequency` | `ProductContext::iSetTheSupplyTo()` | `$frequency` (string) | void | Medium |
| `I set the quantity to :quantity` | `ProductContext::iSetTheQuantityTo()` | `$quantity` (string) | void | High |
| `I subscribe to product` | `ProductContext::iSubscribeToProduct()` | none | void | Low |
| `I add the product to cart` | `ProductContext::iAddTheProductToCart()` | none | void | Very High |
| `I click add to cart` | `ProductContext::iAddTheProductToCart()` | none | void | Very High |
| `I add the product to the cart` | `ProductContext::iAddTheProductToCart()` | none | void | Very High |
| `I update the quantity to :quantity` | `ProductContext::iUpdateTheQuantityTo()` | `$quantity` (string) | void | Medium |
| `Expected sum of products should be calculated correctly` | `ProductContext::expectedSumOfProductsShouldBeCalculatedCorrectly()` | none | void | Low |
| `I verify product content matches configuration` | `ProductContext::iVerifyProductContentMatchesConfiguration()` | none | void | Medium |
| `I should see the product details for :productSlug` | `ProductContext::iShouldSeeTheProductDetailsFor()` | `$productSlug` (string) | void | Medium |
| `The selected flavor should be :expectedFlavor` | `ProductContext::theSelectedFlavorShouldBe()` | `$expectedFlavor` (string) | void | High |
| `the purchase option :expectedOption should be selected` | `ProductContext::verifySelectedPurchaseOption()` | `$expectedOption` (string) | void | Medium |
| `The product should have flavor options` | `ProductContext::theProductShouldHaveFlavorOptions()` | none | void | Low |
| `I select :purchaseType with frequency :frequency` | `ProductContext::iSelectPurchaseTypeWithFrequency()` | `$purchaseType` (string), `$frequency` (string) | void | Low |
| `I decrease the quantity to :quantity` | `ProductContext::iDecreaseTheQuantityTo()` | `$quantity` (string) | void | Low |

### Cart Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I am on the cart page` | `CartContext::iAmOnTheCartPage()` | none | void | Medium |
| `I view my cart` | `CartContext::iAmOnTheCartPage()` | none | void | Medium |
| `I proceed to checkout` | `CartContext::iProceedToCheckout()` | none | void | Very High |
| `I update the quantity of item :index to :quantity` | `CartContext::iUpdateTheQuantityOfItemTo()` | `$index` (int), `$quantity` (int) | void | Medium |
| `I remove item :index from the cart` | `CartContext::iRemoveItemFromTheCart()` | `$index` (int, default=1) | void | Low |
| `I remove item from cart` | `CartContext::iRemoveItemFromTheCart()` | none | void | Low |
| `I should see :count item(s) in the cart` | `CartContext::iShouldSeeItemsInTheCart()` | `$count` (int) | void | Medium |
| `the cart should contain :count item(s)` | `CartContext::iShouldSeeItemsInTheCart()` | `$count` (int) | void | Medium |
| `the cart should contain :count subscription items` | `CartContext::theCartShouldContainSubscriptionItems()` | `$count` (int) | void | Low |
| `the cart should contain :count one-time purchase items` | `CartContext::theCartShouldContainOneTimePurchaseItems()` | `$count` (int) | void | Low |
| `the subscription items should have correct frequencies` | `CartContext::theSubscriptionItemsShouldHaveCorrectFrequencies()` | none | void | Low |
| `the cart should be empty` | `CartContext::theCartShouldBeEmpty()` | none | void | Low |
| `the cart should not be empty` | `CartContext::theCartShouldBeEmpty()` | `$not` (string) | void | Low |
| `my cart should be restored` | `CartContext::myCartShouldBeRestored()` | none | void | Low |

### Checkout Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I fill in the shipping information with :userType user data` | `CheckoutContext::iFillInTheShippingInformationWithUserData()` | `$userType` (string) | void | Very High |
| `I use the same address for billing` | `CheckoutContext::iUseTheSameAddressForBilling()` | none | void | High |
| `I enter :paymentType payment details` | `CheckoutContext::iEnterPaymentDetails()` | `$paymentType` (string) | void | Very High |
| `I complete the purchase` | `CheckoutContext::iCompleteThePurchase()` | none | void | Very High |
| `I wait for the order confirmation page to load` | `CheckoutContext::iWaitForTheOrderConfirmationPageToLoad()` | none | void | Very High |
| `I verify the order details are correct` | `CheckoutContext::iVerifyTheOrderDetailsAreCorrect()` | none | void | Very High |
| `I verify the mixed cart order details are correct` | `CheckoutContext::iVerifyTheMixedCartOrderDetailsAreCorrect()` | none | void | Low |
| `The shipping method :method should be selected` | `CheckoutContext::theShippingMethodShouldBeSelected()` | `$method` (string) | void | High |
| `The shipping method :method should be selected with :cost shipping` | `CheckoutContext::theShippingMethodShouldBeSelectedWithShipping()` | `$method` (string), `$cost` (string) | void | Low |
| `I verify the shipping cost is :cost` | `CheckoutContext::iVerifyTheShippingCostIs()` | `$cost` (string) | void | High |
| `I verify the shipping cost` | `CheckoutContext::iVerifyTheShippingCost()` | none | void | High |
| `I verify the international shipping cost` | `CheckoutContext::iVerifyTheInternationalShippingCost()` | none | void | Low |
| `I verify the international shipping cost is correct` | `CheckoutContext::iVerifyTheInternationalShippingCostIsCorrect()` | none | void | Low |
| `I verify shipping is :cost` | `CheckoutContext::iVerifyShippingIs()` | `$cost` (string) | void | Low |
| `I should see the 3DS page` | `CheckoutContext::iShouldSeeThe3DSPage()` | none | void | Medium |
| `I press the :buttonText button` | `CheckoutContext::iPressTheButton()` | `$buttonText` (string) | void | Medium |
| `I fill in the checkout form with email :email` | `CheckoutContext::iFillInTheCheckoutFormWithEmail()` | `$email` (string) | void | Medium |
| `I select the :method shipping method` | `CheckoutContext::iSelectTheShippingMethod()` | `$method` (string) | void | Medium |
| `the international shipping methods should be available` | `CheckoutContext::theInternationalShippingMethodsShouldBeAvailable()` | none | void | Low |
| `I select the subscription frequency :frequency` | `CheckoutContext::iSelectTheSubscriptionFrequency()` | `$frequency` (string) | void | Low |
| `my shipping information should be pre-filled` | `CheckoutContext::myShippingInformationShouldBePreFilled()` | none | void | Low |
| `I refresh the browser` | `CheckoutContext::iRefreshTheBrowser()` | none | void | Low |
| `my shipping information should still be present` | `CheckoutContext::myShippingInformationShouldStillBePresent()` | none | void | Low |
| `I enter the discount code` | `CheckoutContext::iEnterTheDiscountCode()` | none | void | Low |
| `the discount should be applied to the order` | `CheckoutContext::theDiscountShouldBeAppliedToTheOrder()` | none | void | Low |
| `I should see the order confirmation` | `CheckoutContext::iShouldSeeTheOrderConfirmation()` | none | void | Low |

### Email Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I verify the order confirmation email` | `EmailContext::iVerifyTheOrderConfirmationEmail()` | none | void | High |
| `I verify the order confirmation email was sent` | `EmailContext::iVerifyTheOrderConfirmationEmailWasSent()` | none | void | Medium |
| `I verify the order confirmation email for initial product only` | `EmailContext::iVerifyTheOrderConfirmationEmailForInitialProductOnly()` | none | void | Low |
| `I verify the order confirmation email for the renewal order` | `EmailContext::iVerifyTheOrderConfirmationEmailForTheRenewalOrder()` | none | void | Low |
| `I verify the order confirmation email contains all purchased items` | `EmailContext::iVerifyTheOrderConfirmationEmailContainsAllPurchasedItems()` | none | void | Low |
| `I verify an abandoned cart email was sent` | `EmailContext::iVerifyAnAbandonedCartEmailWasSent()` | none | void | Low |
| `I verify the welcome email contains account credentials` | `EmailContext::iVerifyTheWelcomeEmailContainsAccountCredentials()` | none | void | Medium |
| `I verify the subscription confirmation email` | `EmailContext::iVerifyTheSubscriptionConfirmationEmail()` | none | void | Low |
| `I verify the subscription confirmation email was sent` | `EmailContext::iVerifyTheSubscriptionConfirmationEmailWasSent()` | none | void | Low |
| `I should receive an abandoned cart email within :hours hour(s)` | `EmailContext::iShouldReceiveAnAbandonedCartEmailWithinHours()` | `$hours` (string) | void | Medium |
| `I should receive an abandoned cart email` | `EmailContext::iShouldReceiveAnAbandonedCartEmail()` | none | void | Medium |
| `I should receive a priority abandoned cart email within :minutes minutes` | `EmailContext::iShouldReceiveAPriorityAbandonedCartEmailWithinMinutes()` | `$minutes` (string) | void | Low |
| `I should receive an abandoned cart email with subject :subject` | `EmailContext::iShouldReceiveAnAbandonedCartEmailWithSubject()` | `$subject` (string) | void | Low |
| `I should receive :count abandoned cart emails` | `EmailContext::iShouldReceiveAbandonedCartEmails()` | `$count` (string) | void | Low |
| `I should NOT see an abandoned cart email` | `EmailContext::iShouldNotSeeAnAbandonedCartEmail()` | none | void | Low |
| `I have received an abandoned cart recovery email` | `EmailContext::iHaveReceivedAnAbandonedCartRecoveryEmail()` | none | void | Low |
| `I click the recovery link in the email` | `EmailContext::iClickTheRecoveryLinkInTheEmail()` | none | void | Low |
| `I follow the recovery link in the email` | `EmailContext::iClickTheRecoveryLinkInTheEmail()` | none | void | Low |
| `I follow the recovery link in the email again` | `EmailContext::iFollowTheRecoveryLinkInTheEmailAgain()` | none | void | Low |
| `the email should contain both subscription and one-time items` | `EmailContext::theEmailShouldContainBothSubscriptionAndOneTimeItems()` | none | void | Low |
| `the email should mention the applied discount` | `EmailContext::theEmailShouldMentionTheAppliedDiscount()` | none | void | Low |
| `the email should contain a special discount offer` | `EmailContext::theEmailShouldContainASpecialDiscountOffer()` | none | void | Low |
| `the email should contain account creation information` | `EmailContext::theEmailShouldContainAccountCreationInformation()` | none | void | Low |
| `the abandoned cart email contains the correct product information` | `EmailContext::theAbandonedCartEmailContainsTheCorrectProductInformation()` | none | void | Low |
| `the abandoned cart email contains a recovery link` | `EmailContext::theAbandonedCartEmailContainsARecoveryLink()` | none | void | Low |
| `the order confirmation email shows the discount` | `EmailContext::theOrderConfirmationEmailShowsTheDiscount()` | none | void | Low |
| `the order confirmation email contains international shipping details` | `EmailContext::theOrderConfirmationEmailContainsInternationalShippingDetails()` | none | void | Low |

### Sales Funnel Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I am on the sales funnel page :funnelId` | `SalesFunnelContext::iAmOnTheSalesFunnelPage()` | `$funnelId` (string) | void | High |
| `I am on the subscription funnel page :funnelId` | `SalesFunnelContext::iAmOnTheSubscriptionFunnelPage()` | `$funnelId` (string) | void | Low |
| `I am on the multi-step funnel page :funnelId` | `SalesFunnelContext::iAmOnTheMultiStepFunnelPage()` | `$funnelId` (string) | void | Low |
| `I am on the high-value funnel page :funnelId` | `SalesFunnelContext::iAmOnTheHighValueFunnelPage()` | `$funnelId` (string) | void | Low |
| `I verify the funnel product details` | `SalesFunnelContext::iVerifyTheFunnelProductDetails()` | none | void | High |
| `I should be redirected to the upsell page` | `SalesFunnelContext::iShouldBeRedirectedToTheUpsellPage()` | none | void | High |
| `I should be redirected to the first upsell page` | `SalesFunnelContext::iShouldBeRedirectedToTheFirstUpsellPage()` | none | void | Low |
| `I should be redirected to the second upsell page` | `SalesFunnelContext::iShouldBeRedirectedToTheSecondUpsellPage()` | none | void | Low |
| `I should be redirected to the first cross-sell page` | `SalesFunnelContext::iShouldBeRedirectedToTheFirstCrossSellPage()` | none | void | Low |
| `I should be redirected to the second cross-sell page` | `SalesFunnelContext::iShouldBeRedirectedToTheSecondCrossSellPage()` | none | void | Low |
| `I accept the upsell offer` | `SalesFunnelContext::iAcceptTheUpsellOffer()` | none | void | High |
| `I accept the first upsell offer` | `SalesFunnelContext::iAcceptTheFirstUpsellOffer()` | none | void | Low |
| `I accept the second upsell offer` | `SalesFunnelContext::iAcceptTheSecondUpsellOffer()` | none | void | Low |
| `I accept the first cross-sell offer` | `SalesFunnelContext::iAcceptTheFirstCrossSellOffer()` | none | void | Low |
| `I decline the upsell offer` | `SalesFunnelContext::iDeclineTheUpsellOffer()` | none | void | Low |
| `I decline the second cross-sell offer` | `SalesFunnelContext::iDeclineTheSecondCrossSellOffer()` | none | void | Low |
| `I verify both products are in the order` | `SalesFunnelContext::iVerifyBothProductsAreInTheOrder()` | none | void | Medium |
| `I verify the order contains all three products` | `SalesFunnelContext::iVerifyTheOrderContainsAllThreeProducts()` | none | void | Low |
| `I verify the order contains the initial product and first cross-sell` | `SalesFunnelContext::iVerifyTheOrderContainsTheInitialProductAndFirstCrossSell()` | none | void | Low |
| `I verify the order contains only the initial product` | `SalesFunnelContext::iVerifyTheOrderContainsOnlyTheInitialProduct()` | none | void | Low |
| `I verify the order contains a subscription product` | `SalesFunnelContext::iVerifyTheOrderContainsASubscriptionProduct()` | none | void | Low |
| `I verify the subscription frequency is :frequency` | `SalesFunnelContext::iVerifyTheSubscriptionFrequencyIs()` | `$frequency` (string) | void | Low |
| `I verify the order confirmation page shows both initial and upsell products` | `SalesFunnelContext::iVerifyTheOrderConfirmationPageShowsBothInitialAndUpsellProducts()` | none | void | Low |
| `I verify the discount was applied to the initial product only` | `SalesFunnelContext::iVerifyTheDiscountWasAppliedToTheInitialProductOnly()` | none | void | Low |
| `the order total should exceed the high-value threshold` | `SalesFunnelContext::theOrderTotalShouldExceedTheHighValueThreshold()` | none | void | Low |
| `I verify dietary restriction warnings are displayed` | `SalesFunnelContext::iVerifyDietaryRestrictionWarningsAreDisplayed()` | none | void | Low |
| `I verify the product instructions contain all warnings` | `SalesFunnelContext::iVerifyTheProductInstructionsContainAllWarnings()` | none | void | Low |
| `I click the accept button multiple times` | `SalesFunnelContext::iClickTheAcceptButtonMultipleTimes()` | none | void | Low |
| `I verify only one upsell product is in the order` | `SalesFunnelContext::iVerifyOnlyOneUpsellProductIsInTheOrder()` | none | void | Low |
| `I have a valid discount code :code` | `SalesFunnelContext::iHaveAValidDiscountCode()` | `$code` (string) | void | Low |
| `I manually navigate to the order completion page due to upsell page failure` | `SalesFunnelContext::iManuallyNavigateToTheOrderCompletionPageDueToUpsellPageFailure()` | none | void | Low |

### Upsell Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I navigate back in browser` | `UpsellContext::iNavigateBackInBrowser()` | none | void | Low |
| `I should see an error message indicating the card has expired` | `UpsellContext::iShouldSeeAnErrorMessageIndicatingTheCardHasExpired()` | none | void | Medium |
| `I should see an error message indicating the payment was declined` | `UpsellContext::iShouldSeeAnErrorMessageIndicatingThePaymentWasDeclined()` | none | void | Low |
| `I should remain on the checkout page` | `UpsellContext::iShouldRemainOnTheCheckoutPage()` | none | void | Medium |
| `the upsell page fails to load` | `UpsellContext::theUpsellPageFailsToLoad()` | none | void | Low |
| `I wait for the automatic order completion timeout` | `UpsellContext::iWaitForTheAutomaticOrderCompletionTimeout()` | none | void | Low |

### Payment Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I select the :method payment method` | `PaymentContext::iSelectThePaymentMethod()` | `$method` (string) | void | Medium |
| `I should be redirected to the PayPal login page` | `PaymentContext::iShouldBeRedirectedToThePayPalLoginPage()` | none | void | Medium |
| `I log in to PayPal with :credentialType credentials` | `PaymentContext::iLogInToPayPalWithCredentials()` | `$credentialType` (string) | void | Medium |
| `I confirm the PayPal payment` | `PaymentContext::iConfirmThePayPalPayment()` | none | void | Medium |
| `I choose to pay with PayPal` | `PaymentContext::iChooseToPayWithPayPal()` | none | void | Medium |
| `I am redirected to the PayPal sandbox page` | `PaymentContext::iAmRedirectedToThePayPalSandboxPage()` | none | void | Medium |
| `I log in to PayPal sandbox with :credentialType credentials` | `PaymentContext::iLogInToPayPalSandboxWithCredentials()` | `$credentialType` (string) | void | Medium |
| `I should see the correct payment amount in PayPal` | `PaymentContext::iShouldSeeTheCorrectPaymentAmountInPayPal()` | none | void | Low |
| `I should be redirected back to the merchant site` | `PaymentContext::iShouldBeRedirectedBackToTheMerchantSite()` | none | void | Low |
| `the PayPal payment should be successful` | `PaymentContext::thePayPalPaymentShouldBeSuccessful()` | none | void | Low |
| `I should see a PayPal login error message` | `PaymentContext::iShouldSeeAPayPalLoginErrorMessage()` | none | void | Low |
| `I close the browser without completing payment` | `PaymentContext::iCloseTheBrowserWithoutCompletingPayment()` | none | void | Low |
| `I abandon the PayPal checkout` | `PaymentContext::iAbandonThePayPalCheckout()` | none | void | Low |

### Abandoned Cart Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I abandon the checkout` | `AbandonedCartContext::iAbandonTheCheckout()` | none | void | Medium |
| `I should see that the order status is :status in the database` | `AbandonedCartContext::iShouldSeeThatTheOrderStatusIsInTheDatabase()` | `$status` (string) | void | Medium |
| `I repeat the process :count more times` | `AbandonedCartContext::iRepeatTheProcessMoreTimes()` | `$count` (string) | void | Low |
| `I should see that cart was cancelled :count times with different order ids` | `AbandonedCartContext::iShouldSeeThatCartWasCancelledTimesWithDifferentOrderIds()` | `$count` (string) | void | Low |
| `I have an abandoned cart` | `AbandonedCartContext::iHaveAnAbandonedCart()` | none | void | Low |
| `I have an abandoned funnel purchase for :funnelId` | `AbandonedCartContext::iHaveAnAbandonedFunnelPurchaseFor()` | `$funnelId` (string) | void | Low |
| `I should be redirected to my cart` | `AbandonedCartContext::iShouldBeRedirectedToMyCart()` | none | void | Low |
| `I should be redirected to the funnel checkout page` | `AbandonedCartContext::iShouldBeRedirectedToTheFunnelCheckoutPage()` | none | void | Low |
| `my cart should contain the previously selected items` | `AbandonedCartContext::myCartShouldContainThePreviouslySelectedItems()` | none | void | Low |
| `the order status should be :status in the database` | `AbandonedCartContext::theOrderStatusShouldBeInTheDatabase()` | `$status` (string) | void | Low |
| `the order status should be :status` | `AbandonedCartContext::theOrderStatusShouldBe()` | `$status` (string) | void | Low |
| `I have an abandoned cart from :hours hours ago` | `AbandonedCartContext::iHaveAnAbandonedCartFromHoursAgo()` | `$hours` (string) | void | Low |
| `the abandoned cart cleanup job runs` | `AbandonedCartContext::theAbandonedCartCleanupJobRuns()` | none | void | Low |
| `I should not receive any more recovery emails` | `AbandonedCartContext::iShouldNotReceiveAnyMoreRecoveryEmails()` | none | void | Low |
| `I have abandoned :count carts in the past :hours hours` | `AbandonedCartContext::iHaveAbandonedCartsInThePastHours()` | `$count` (string), `$hours` (string) | void | Low |
| `I try to abandon another cart` | `AbandonedCartContext::iTryToAbandonAnotherCart()` | none | void | Low |
| `I should see a message :message` | `AbandonedCartContext::iShouldSeeAMessage()` | `$message` (string) | void | Low |
| `I should be required to complete the purchase` | `AbandonedCartContext::iShouldBeRequiredToCompleteThePurchase()` | none | void | Low |
| `an abandoned cart email has been sent` | `AbandonedCartContext::anAbandonedCartEmailHasBeenSent()` | none | void | Low |
| `a shop user should be created with the provided email` | `AbandonedCartContext::aShopUserShouldBeCreatedWithTheProvidedEmail()` | none | void | Low |
| `I run the abandoned cart command` | `AbandonedCartContext::iRunTheAbandonedCartCommand()` | none | void | Low |
| `I run the abandoned cart recovery command` | `AbandonedCartContext::iRunTheAbandonedCartRecoveryCommand()` | none | void | Low |

### Validation Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I verify Google Tag Manager is present` | `ValidationContext::iVerifyGoogleTagManagerIsPresent()` | none | void | High |
| `I apply a valid coupon code :code` | `ValidationContext::iApplyAValidCouponCode()` | `$code` (string) | void | Medium |
| `The order total should be calculated correctly` | `ValidationContext::theOrderTotalShouldBeCalculatedCorrectly()` | none | void | Medium |
| `Total price should be updated correctly` | `ValidationContext::totalPriceShouldBeUpdatedCorrectly()` | none | void | Medium |
| `I verify the cart contains the correct product details` | `ValidationContext::iVerifyTheCartContainsTheCorrectProductDetails()` | none | void | Medium |
| `The flavor on confirmation page should be :expectedFlavor` | `ValidationContext::theFlavorOnConfirmationPageShouldBe()` | `$expectedFlavor` (string) | void | Medium |

### Brand Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I load brand configuration` | `BrandContext::iLoadBrandConfiguration()` | none | void | Very High |
| `I load product data` | `BrandContext::iLoadProductData()` | none | void | Very High |

### Admin Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I am logged into the admin panel` | `AdminContext::iAmLoggedIntoTheAdminPanel()` | none | void | Medium |
| `I navigate to the sales funnel configuration page` | `AdminContext::iNavigateToTheSalesFunnelConfigurationPage()` | none | void | Medium |
| `I obtain the :funnelId sales funnel URL` | `AdminContext::iObtainTheSalesFunnelURL()` | `$funnelId` (string) | void | Medium |
| `I navigate to the sales funnel URL` | `AdminContext::iNavigateToTheSalesFunnelURL()` | none | void | Medium |
| `I run the sales funnel complete-payments command` | `AdminContext::iRunTheSalesFunnelCompletePaymentsCommand()` | none | void | Low |
| `I run the sales funnel completion command` | `AdminContext::iRunTheSalesFunnelCompletionCommand()` | none | void | Low |
| `I verify the order exists in the admin panel` | `AdminContext::iVerifyTheOrderExistsInTheAdminPanel()` | none | void | Low |
| `I verify the order status is :status in the admin panel` | `AdminContext::iVerifyTheOrderStatusIsInTheAdminPanel()` | `$status` (string) | void | Low |
| `I verify the order status is :status and payment status is :paymentStatus` | `AdminContext::iVerifyTheOrderStatusAndPaymentStatus()` | `$status` (string), `$paymentStatus` (string) | void | Low |
| `I have an active subscription order for :user` | `AdminContext::iHaveAnActiveSubscriptionOrderFor()` | `$user` (string) | void | Low |
| `the initial subscription order has been fulfilled` | `AdminContext::theInitialSubscriptionOrderHasBeenFulfilled()` | none | void | Low |
| `I run the subscription renewal command` | `AdminContext::iRunTheSubscriptionRenewalCommand()` | none | void | Low |
| `a sales funnel item with code :code exists as an initial product` | `AdminContext::aSalesFunnelItemWithCodeExistsAsAnInitialProduct()` | `$code` (string) | void | Low |
| `I open the sales funnel in a new browser window` | `AdminContext::iOpenTheSalesFunnelInANewBrowserWindow()` | none | void | Low |

### Database Context Steps

| Step Definition | Method | Input Parameters | Return Value | Usage Count |
|----------------|--------|-----------------|-------------|------------|
| `I have a subscription order with following items:` | `DatabaseContext::iHaveASubscriptionOrderWithFollowingItems()` | Table (product, quantity, purchase_type) | void | Medium |
| `I execute app:reorder command` | `DatabaseContext::iExecuteAppReorderCommand()` | none | void | Medium |
| `the command should be successful` | `DatabaseContext::theCommandShouldBeSuccessful()` | none | void | Medium |
| `a new order should be created with same subscription items` | `DatabaseContext::aNewOrderShouldBeCreatedWithSameSubscriptionItems()` | none | void | Low |
| `a new order should be created with status :status` | `DatabaseContext::aNewOrderShouldBeCreatedWithStatus()` | `$status` (string) | void | Low |
| `the new order should have same delivery address` | `DatabaseContext::theNewOrderShouldHaveSameDeliveryAddress()` | none | void | Low |
| `the new order should have same payment method` | `DatabaseContext::theNewOrderShouldHaveSamePaymentMethod()` | none | void | Low |
| `a new order should be created with subscription items only` | `DatabaseContext::aNewOrderShouldBeCreatedWithSubscriptionItemsOnly()` | none | void | Low |
| `the new order should not contain one-time items` | `DatabaseContext::theNewOrderShouldNotContainOneTimeItems()` | none | void | Low |
| `the order should contain only the initial product` | `DatabaseContext::theOrderShouldContainOnlyTheInitialProduct()` | none | void | Low |
| `the order should contain both initial and upsell products` | `DatabaseContext::theOrderShouldContainBothInitialAndUpsellProducts()` | none | void | Low |

## Steps with Multiple Definitions

| Step Definition | Methods | Usage Count |
|----------------|---------|------------|
| `I add the product to the cart` / `I add the product to cart` / `I click add to cart` | `ProductContext::iAddTheProductToCart()` | Very High |
| `I follow the recovery link in the email` / `I click the recovery link in the email` | `EmailContext::iClickTheRecoveryLinkInTheEmail()` | Low |
| `the order status should be :status` / `the order status should be :status in the database` | `AbandonedCartContext::theOrderStatusShouldBeInTheDatabase()` | Low |
| `I verify the order contains only the initial product` / `the order should contain only the initial product` | Multiple contexts: `SalesFunnelContext` and `DatabaseContext` | Low |

## Most Frequently Used Steps

| Step Definition | Context | Usage Count |
|----------------|---------|------------|
| `I load brand configuration` | BrandContext | Very High |
| `I load product data` | BrandContext | Very High |
| `I am on the product page` | ProductContext | Very High |
| `I add the product to cart` / `I click add to cart` / `I add the product to the cart` | ProductContext | Very High |
| `I proceed to checkout` | CartContext | Very High |
| `I fill in the shipping information with :userType user data` | CheckoutContext | Very High |
| `I enter :paymentType payment details` | CheckoutContext | Very High |
| `I complete the purchase` | CheckoutContext | Very High |
| `I wait for the order confirmation page to load` | CheckoutContext | Very High |
| `I verify the order details are correct` | CheckoutContext | Very High |
| `I set the quantity to :quantity` | ProductContext | High |
| `I verify Google Tag Manager is present` | ValidationContext | High |
| `I am on the :productIdentifier product page` | ProductContext | High |
| `I select flavor :flavor` | ProductContext | High |
| `I select the flavor :flavor` | ProductContext | High |
| `I select the :quantity quantity option` | ProductContext | High |
| `The selected flavor should be :expectedFlavor` | ProductContext | High |
| `I use the same address for billing` | CheckoutContext | High |
| `The shipping method :method should be selected` | CheckoutContext | High |
| `I verify the shipping cost is :cost` | CheckoutContext | High |
| `I verify the shipping cost` | CheckoutContext | High |
| `I verify the order confirmation email` | EmailContext | High |
| `I am on the sales funnel page :funnelId` | SalesFunnelContext | High |
| `I verify the funnel product details` | SalesFunnelContext | High |
| `I should be redirected to the upsell page` | SalesFunnelContext | High |
| `I accept the upsell offer` | SalesFunnelContext | High |

## Newly Added Step Definitions

The following step definitions have been implemented to address gaps identified in the feature files:

| Step Definition | Method | Context | Feature File | 
|----------------|--------|---------|-------------|
| `the cart should contain :count subscription items` | `CartContext::theCartShouldContainSubscriptionItems()` | CartContext | mixedCart.feature |
| `the cart should contain :count one-time purchase items` | `CartContext::theCartShouldContainOneTimePurchaseItems()` | CartContext | mixedCart.feature |
| `the subscription items should have correct frequencies` | `CartContext::theSubscriptionItemsShouldHaveCorrectFrequencies()` | CartContext | mixedCart.feature |
| `my cart should be restored` | `CartContext::myCartShouldBeRestored()` | CartContext | abandoned_cart_extended.feature |
| `I verify the order contains all three products` | `SalesFunnelContext::iVerifyOrderContainsProducts()` | SalesFunnelContext | salesFunnel_variations.feature |
| `I verify the order contains only the initial product` | `SalesFunnelContext::iVerifyOrderContainsProducts()` | SalesFunnelContext | salesFunnel_variations.feature |
| `I verify the order contains the initial product and first cross-sell` | `SalesFunnelContext::iVerifyOrderContainsProducts()` | SalesFunnelContext | salesFunnel_variations.feature |
| `I verify the order contains a subscription product` | `SalesFunnelContext::iVerifyTheOrderContainsASubscriptionProduct()` | SalesFunnelContext | salesFunnel_variations.feature |
| `I verify the subscription frequency is :frequency` | `SalesFunnelContext::iVerifyTheSubscriptionFrequencyIs()` | SalesFunnelContext | salesFunnel_variations.feature |
| `the order confirmation email contains all products` | `EmailContext::iVerifyTheOrderConfirmationEmailContainsAllProducts()` | EmailContext | salesFunnel_variations.feature |
| `the email should contain a special discount offer` | `EmailContext::theEmailShouldContainASpecialDiscountOffer()` | EmailContext | abandoned_cart_extended.feature |
| `the email should contain account creation information` | `EmailContext::theEmailShouldContainAccountCreationInformation()` | EmailContext | abandoned_cart_extended.feature |
| `the email should contain both subscription and one-time items` | `EmailContext::theEmailShouldContainBothSubscriptionAndOneTimeItems()` | EmailContext | abandoned_cart_extended.feature |
| `the email should mention the applied discount` | `EmailContext::theEmailShouldMentionTheAppliedDiscount()` | EmailContext | abandoned_cart_extended.feature |
| `I run the sales funnel completion command` | `AdminContext::iRunTheSalesFunnelCompletionCommand()` | AdminContext | salesFunnel_error_recovery.feature |
| `I wait for the automatic order completion timeout` | `UpsellContext::iWaitForTheAutomaticOrderCompletionTimeout()` | UpsellContext | salesFunnel_error_recovery.feature |
| `I verify the international shipping cost is correct` | `CheckoutContext::iVerifyTheInternationalShippingCostIsCorrect()` | CheckoutContext | salesFunnel_variations.feature |
| `the international shipping methods should be available` | `CheckoutContext::theInternationalShippingMethodsShouldBeAvailable()` | CheckoutContext | salesFunnel_variations.feature |
| `my shipping information should be pre-filled` | `CheckoutContext::myShippingInformationShouldBePreFilled()` | CheckoutContext | salesFunnel_error_recovery.feature |
| `my shipping information should still be present` | `CheckoutContext::myShippingInformationShouldStillBePresent()` | CheckoutContext | salesFunnel_error_recovery.feature | 