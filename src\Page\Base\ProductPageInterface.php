<?php

namespace App\Page\Base;

/**
 * Interface for pages that handle product operations.
 */
interface ProductPageInterface
{
    /**
     * Select supply duration/frequency
     *
     * @param string $frequency The frequency to select
     * @return int Days between deliveries
     */
    public function selectSupplyDuration(string $frequency);

    /**
     * Select product quantity
     *
     * @param mixed $quantity The quantity to select
     * @return void
     */
    public function selectQuantity($quantity);
}
