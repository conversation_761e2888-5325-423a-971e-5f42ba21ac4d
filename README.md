# Malaberg Test Automation Framework

This repository contains a Behat test framework for e-commerce testing with a focus on product content, purchase flows,
and data-driven testing. The framework uses BrowserStack for cloud-based browser automation.

## Configuration Structure

The test framework uses a hierarchical configuration system:

- **ConfigurationManager**: Handles brand and environment configuration from YAML files
- **TestDataRegistry**: Manages test data loading and validation from YAML fixtures
- **DataValidator**: Ensures test data follows required structure and contains all mandatory fields
- **SharedDataContext**: Provides cross-context data sharing during test execution

## Directory Structure

```
/config/
  /brands/
    /aeons/
      stage.yml            # Brand and environment configuration
/features/
  /bootstrap/
    /Context/              # Behat context classes
      BrandContext.php     # Brand-specific context
      CartContext.php      # Cart-related steps
      ...
    /Core/                 # Core framework classes
      ConfigurationManager.php
      TestDataRegistry.php
      DataValidator.php
      ...
    /Page/                 # Page object classes
      BasePage.php         # Base page with common methods
      CartPage.php         # Cart page object
      ...
    FeatureContext.php     # Main feature context
    SharedDataContext.php  # Shared data between contexts
  /fixtures/
    /brands/
      /aeons/
        products.yml       # Product test data
        users.yml          # User test data
        ...
  salesFunnel.feature      # Feature file with test scenarios
/vendor/
  ...
behat.yml                  # Behat configuration
```

## Running Tests

### Using BrowserStack

The framework uses BrowserStack for browser automation. To run tests with BrowserStack:

```powershell
# Run all tests
.\run-browserstack-tests.ps1

# Run a specific feature
.\run-browserstack-tests.ps1 -Feature "features/salesFunnel.feature"

# Run tests with specific tags
.\run-browserstack-tests.ps1 -Tags "@high-priority"

# Run tests for a specific brand and environment
.\run-browserstack-tests.ps1 -Brand "aeons" -Environment "stage"

# Run tests for a specific product
.\run-browserstack-tests.ps1 -Product "golden_harvest"

# Run in dry-run mode
.\run-browserstack-tests.ps1 -DryRun
```

### Using Behat Directly

You can also run tests directly with Behat using the BrowserStack profile:

```powershell
# Run all tests with BrowserStack profile
.\vendor\bin\behat --profile=browserstack

# Run a specific feature with BrowserStack profile
.\vendor\bin\behat --profile=browserstack features/salesFunnel.feature

# Run tests with specific tags with BrowserStack profile
.\vendor\bin\behat --profile=browserstack --tags="@funnel,@high-priority"
```

## Test Data Setup

### Brand Configuration

Brand configuration is stored in YAML files under `/config/brands/{brand}/{env}.yml`. Example:

```yaml
brand:
  name: "Aeons"
  code: "aeons"
  url: "https://aeons.com"
  currency: "GBP"
  features:
    subscription: true
    upsell: true
```

### Product Data

Product test data is stored in YAML files under `/features/fixtures/brands/{brand}/products.yml`. Each product must have the following fields:

- `name`: Product name
- `slug`: URL-friendly identifier
- `prices`: Pricing options (one_time and subscription)
- `options`: Purchase types and quantities
- `content`: Product description and features

## Page Object Implementation

The framework uses the Page Object pattern to encapsulate interactions with web pages:

### BasePage

The `BasePage` class provides common functionalities for all page objects:

- Flexible constructor that handles both explicit URLs and Mink Session objects
- Methods for finding elements, interacting with the page, and waiting for elements
- Error handling and validation

Example:

```php
// BasePage constructor can handle either a string URL or a Session object
public function __construct($baseUrl)
{
    // If baseUrl is a Session object, extract the base URL from environment variables
    if ($baseUrl instanceof Session) {
        $this->baseUrl = getenv('TEST_BASE_URL') ?: 'https://aeonstest.info';

        // Set the session property (inherited from Page)
        $this->session = $baseUrl;
    } else {
        $this->baseUrl = (string)$baseUrl;
    }
}
```

### Specialized Page Objects

Each page type has its own page object class with specific methods:

- `CartPage`: For interacting with the shopping cart
- `ProductPage`: For product details and interactions
- `CheckoutPage`: For checkout process interactions

## Implementing New Tests

1. **Define Scenarios**: Create or update feature files with Given-When-Then scenarios
2. **Implement Step Definitions**: Add necessary step methods in appropriate context classes
3. **Add Test Data**: Create or update YAML fixtures for test data
4. **Run Dry Run**: Use the `--dry-run` flag to verify step definitions
5. **Execute Tests**: Run tests with specific tags

## Troubleshooting

- **Missing Step Definitions**: Run with `-DryRun` to identify missing steps
- **Data Validation Errors**: Check error messages for details about missing fields
- **Configuration Issues**: Verify YAML structure and required fields
- **Page Object Issues**: Check that `BasePage` is properly initialized with either a URL or Session object
- **BrowserStack Issues**: Check the BrowserStack dashboard for test results, screenshots, and logs
- **Authentication Errors**: Verify your BrowserStack username and access key in the `.env` file
- **Session Creation Failures**: Check that you have enough parallel sessions available in your BrowserStack plan

## BrowserStack Integration

The framework uses BrowserStack for browser automation. This provides several benefits:

- **Cloud-Based Testing**: No need to install or maintain local browsers or WebDrivers
- **Cross-Browser Testing**: Test on multiple browsers and operating systems
- **Parallel Testing**: Run tests in parallel to reduce execution time
- **Visual Debugging**: Access screenshots, videos, and logs for failed tests
- **CI/CD Integration**: Easily integrate with CI/CD pipelines

For more information, see the [BrowserStack Testing Setup Guide](docs/browserstack_testing_setup.md).

## Demo Script

You can run the `test-behat-config.php` script to test your configuration setup:

```bash
php test-behat-config.php
```

This will demonstrate:
- Configuration loading
- Test data retrieval and validation
- Typical usage in a Behat context
