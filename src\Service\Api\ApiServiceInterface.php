<?php

namespace App\Service\Api;

/**
 * Interface for API service operations
 *
 * This interface defines methods for interacting with external APIs
 */
interface ApiServiceInterface
{
    /**
     * Send a request to an API endpoint
     *
     * @param string $endpoint API endpoint
     * @param array $parameters Request parameters
     * @param string $method HTTP method (GET, POST, etc.)
     * @return mixed Response data
     */
    public function sendRequest(string $endpoint, array $parameters = [], string $method = 'GET');

    /**
     * Get API base URL
     *
     * @return string Base URL
     */
    public function getBaseUrl(): string;

    /**
     * Set API base URL
     *
     * @param string $baseUrl Base URL
     * @return void
     */
    public function setBaseUrl(string $baseUrl): void;
}
