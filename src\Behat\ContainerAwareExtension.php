<?php

namespace App\Behat;

use Behat\Testwork\ServiceContainer\Extension;
use Behat\Testwork\ServiceContainer\ExtensionManager;
use Symfony\Component\Config\Definition\Builder\ArrayNodeDefinition;
use Symfony\Component\DependencyInjection\ContainerBuilder;

/**
 * Extension to ensure the container is properly set up for contexts
 */
class ContainerAwareExtension implements Extension
{
    /**
     * {@inheritdoc}
     */
    public function getConfigKey()
    {
        return 'container_aware_extension';
    }

    /**
     * {@inheritdoc}
     */
    public function initialize(ExtensionManager $extensionManager)
    {
        // No initialization needed
    }

    /**
     * {@inheritdoc}
     */
    public function configure(ArrayNodeDefinition $builder)
    {
        // No configuration needed
    }

    /**
     * {@inheritdoc}
     */
    public function load(ContainerBuilder $container, array $config)
    {
        // Register the container as a service
        if (!$container->has('service_container')) {
            $container->register('service_container', 'Symfony\Component\DependencyInjection\ContainerInterface')
                ->setSynthetic(true)
                ->setPublic(true);
        }

        // Register the container interface
        if (!$container->has('Symfony\Component\DependencyInjection\ContainerInterface')) {
            $container->setAlias('Symfony\Component\DependencyInjection\ContainerInterface', 'service_container')
                ->setPublic(true);
        }

        // Set the container in the global scope
        $GLOBALS['service_container'] = $container;
    }

    /**
     * {@inheritdoc}
     */
    public function process(ContainerBuilder $container)
    {
        // Set the container in the global scope
        $GLOBALS['service_container'] = $container;

        // Call the setServiceContainer function if it exists
        if (function_exists('setServiceContainer')) {
            setServiceContainer($container);
        }
    }
}
