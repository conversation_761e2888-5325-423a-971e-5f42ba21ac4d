<?php

namespace Features\Bootstrap\Core;

use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Registry for managing test data across contexts
 */
class TestDataRegistry
{
    private array $testData = [];
    private array $cache = [];
    private DataValidator $validator;
    private string $fixturesPath;
    private ?ConfigurationManager $configManager;

    public function __construct(DataValidator $validator = null, string $fixturesPath = null, ConfigurationManager $configManager = null)
    {
        $this->configManager = $configManager;
        $this->validator = $validator ?? new DataValidator();
        
        // Default fixtures path if not provided
        if (!$fixturesPath) {
            $projectRoot = dirname(__DIR__, 3); // Go up 3 levels: Core -> bootstrap -> features
            $this->fixturesPath = $projectRoot . '/features/bootstrap/fixtures';
        } else {
            $this->fixturesPath = $fixturesPath;
        }
    }

    /**
     * Loads and registers test data for a brand
     *
     * @param string $brand Brand identifier
     * @param string $type Data type (products, users, etc)
     * @param string|null $key Optional specific data key
     * @return array The loaded test data
     * @throws RuntimeException When data cannot be loaded
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        $cacheKey = sprintf('%s_%s_%s', $brand, $type, $key ?? 'all');
        error_log(sprintf("[TestDataRegistry] Loading test data - Brand: %s, Type: %s, Key: %s", $brand, $type, $key ?? 'all'));
        
        if (isset($this->cache[$cacheKey])) {
            error_log("[TestDataRegistry] Returning cached data for key: " . $cacheKey);
            return $this->cache[$cacheKey];
        }

        // Try multiple possible paths
        $paths = [
            sprintf('%s/brands/%s/%s.yml', $this->fixturesPath, $brand, $type),
            sprintf('%s/brands/%s/data/%s.yml', $this->fixturesPath, $brand, $type),
            sprintf('%s/brands/%s/fixtures/%s.yml', $this->fixturesPath, $brand, $type),
        ];
        
        $loadedPath = null;
        $data = null;
        
        foreach ($paths as $path) {
            error_log("[TestDataRegistry] Trying path: " . $path);
            if (file_exists($path)) {
                error_log("[TestDataRegistry] Loading file: " . $path);
                $loadedPath = $path;
                break;
            }
        }
        
        if (!$loadedPath) {
            error_log("[TestDataRegistry] ERROR: No valid fixture file found. Tried: " . implode(', ', $paths));
            throw new RuntimeException(
                sprintf('Test data file not found. Tried: %s', implode(', ', $paths))
            );
        }

        try {
            $data = Yaml::parseFile($loadedPath);
            error_log(sprintf("[TestDataRegistry] Loaded YAML data. Keys available: %s", 
                is_array($data) ? implode(', ', array_keys($data)) : 'NOT_ARRAY'));
        } catch (\Exception $e) {
            error_log("[TestDataRegistry] ERROR: Failed to parse YAML: " . $e->getMessage());
            throw new RuntimeException(
                sprintf('Failed to parse YAML in %s: %s', $loadedPath, $e->getMessage())
            );
        }

        if (!is_array($data)) {
            error_log("[TestDataRegistry] ERROR: Invalid YAML content type: " . gettype($data));
            throw new RuntimeException(
                sprintf('Invalid YAML content in %s. Expected array, got %s', $loadedPath, gettype($data))
            );
        }

        // For products, validate the structure before extracting specific product
        if ($type === 'products') {
            error_log("[TestDataRegistry] Validating product data structure");
            foreach ($data as $productKey => $product) {
                error_log(sprintf("[TestDataRegistry] Checking product: %s", $productKey));
                if (!is_array($product)) {
                    error_log(sprintf("[TestDataRegistry] ERROR: Invalid product data type for %s: %s", $productKey, gettype($product)));
                    throw new RuntimeException(
                        sprintf('Invalid product data for key "%s". Expected array, got %s', $productKey, gettype($product))
                    );
                }
            }
        }
        
        if ($key !== null) {
            error_log(sprintf("[TestDataRegistry] Extracting specific key: %s", $key));
            if (!isset($data[$key])) {
                $availableKeys = array_keys($data);
                error_log(sprintf("[TestDataRegistry] ERROR: Key not found. Available keys: %s", implode(', ', $availableKeys)));
                throw new RuntimeException(
                    sprintf('Test data key "%s" not found in %s. Available keys: %s',
                        $key,
                        $loadedPath,
                        implode(', ', $availableKeys)
                    )
                );
            }
            $data = $data[$key];
            error_log("[TestDataRegistry] Successfully extracted data for key: " . $key);
        }

        error_log("[TestDataRegistry] Validating extracted data");
        $this->validateData($type, $data);
        error_log("[TestDataRegistry] Data validation successful");
        
        $this->cache[$cacheKey] = $data;
        error_log("[TestDataRegistry] Data cached with key: " . $cacheKey);
        
        return $data;
    }

    /**
     * Registers test data for later use
     *
     * @param string $key Data key
     * @param mixed $data Data to register
     */
    public function registerData(string $key, $data): void
    {
        $this->testData[$key] = $data;
    }

    /**
     * Retrieves registered test data
     *
     * @param string $key Data key
     * @return mixed The registered data
     * @throws RuntimeException When data is not found
     */
    public function getData(string $key)
    {
        if (!isset($this->testData[$key])) {
            throw new RuntimeException(
                sprintf('Test data not found for key: %s', $key)
            );
        }
        
        return $this->testData[$key];
    }

    /**
     * Checks if test data exists
     *
     * @param string $key Data key
     * @return bool Whether the data exists
     */
    public function hasData(string $key): bool
    {
        return isset($this->testData[$key]);
    }

    /**
     * Clears all registered test data
     */
    public function clearData(): void
    {
        $this->testData = [];
        $this->cache = [];
    }

    /**
     * Validates test data based on type
     *
     * @param string $type Data type
     * @param array $data Data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateData(string $type, array $data): void
    {
        error_log(sprintf("[TestDataRegistry] Starting data validation for type: %s", $type));
        
        switch ($type) {
            case 'products':
                if (isset($data['name']) || isset($data['slug'])) {
                    error_log("[TestDataRegistry] Validating single product");
                    $this->validator->validateProductData($data);
                } else {
                    error_log("[TestDataRegistry] Validating product collection");
                    foreach ($data as $productKey => $product) {
                        error_log(sprintf("[TestDataRegistry] Validating product: %s", $productKey));
                        if (!is_array($product)) {
                            error_log(sprintf("[TestDataRegistry] ERROR: Invalid product data type for %s: %s", $productKey, gettype($product)));
                            throw new RuntimeException(
                                sprintf('Invalid product data for key "%s". Expected array, got %s', $productKey, gettype($product))
                            );
                        }
                        $this->validator->validateProductData($product);
                    }
                }
                break;
            
            case 'users':
                if (isset($data['email'])) {
                    // Single user validation
                    $this->validator->validateTestUser($data);
                } else {
                    // User collection validation
                    foreach ($data as $user) {
                        $this->validator->validateTestUser($user);
                    }
                }
                break;
            
            case 'shipping':
                $this->validator->validateShippingData($data);
                break;
            
            case 'payment_methods':
                if (isset($data['type'])) {
                    // Single payment method validation
                    $this->validator->validatePaymentMethod($data);
                } else {
                    // Payment method collection validation
                    foreach ($data as $method) {
                        $this->validator->validatePaymentMethod($method);
                    }
                }
                break;
        }
        error_log("[TestDataRegistry] Data validation completed successfully");
    }
} 