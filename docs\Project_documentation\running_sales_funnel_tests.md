# Running Sales Funnel Tests

This document provides detailed instructions for running the sales funnel tests in the Malaberg test automation framework, with a focus on the `test:sales-funnel:headless` command.

## Overview

The sales funnel tests verify the critical e-commerce flows from product selection through checkout, upsell, and order confirmation. These tests are essential for ensuring that customers can successfully complete purchases through the sales funnel.

## Test:Sales-Funnel:Headless Command

The `test:sales-funnel:headless` command is defined in `composer.json` as:

```json
"test:sales-funnel:headless": "@php bin/run-tests.php --brand=aeons --env=stage --feature=features/salesFunnel.feature --tags=@@high-priority"
```

This command runs the high-priority scenarios in the `salesFunnel.feature` file using a headless Chrome browser.

### Command Parameters

- `--brand=aeons`: Sets the brand to "aeons"
- `--env=stage`: Sets the environment to "stage"
- `--feature=features/salesFunnel.feature`: Specifies the feature file to run
- `--tags=@@high-priority`: Filters to only run scenarios with the `@high-priority` tag

## Prerequisites

Before running the sales funnel tests, ensure you have the following prerequisites:

1. **PHP 8.1 or higher**: The tests require PHP 8.1 or higher.

2. **Composer**: Composer must be installed to manage dependencies.

3. **Chrome Browser**: Google Chrome must be installed on your system.

4. **Chrome Driver**: The tests use Chrome in debug mode with remote debugging enabled.

5. **Environment Setup**: The test requires specific environment variables:
   - `TEST_BRAND=aeons`
   - `TEST_ENV=stage`
   - `TEST_BASE_URL=https://aeonstest.info`

## Setup Instructions

### 1. Install Dependencies

```bash
composer install
```

### 2. Start Chrome in Debug Mode

The tests require Chrome to be running in debug mode with remote debugging enabled on port 9222. You can start Chrome in debug mode using the provided `start-chrome-debug.bat` script:

```bash
start-chrome-debug.bat
```

Or manually:

```bash
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --disable-gpu --headless
```

### 3. Set Environment Variables

You can set environment variables in a `.env` file in the project root:

```
TEST_BRAND=aeons
TEST_ENV=stage
TEST_BASE_URL=https://aeonstest.info
```

Or set them directly in your terminal:

```bash
# Windows
set TEST_BRAND=aeons
set TEST_ENV=stage
set TEST_BASE_URL=https://aeonstest.info

# Linux/Mac
export TEST_BRAND=aeons
export TEST_ENV=stage
export TEST_BASE_URL=https://aeonstest.info
```

## Running the Tests

### Run the Sales Funnel Headless Test

```bash
composer test:sales-funnel:headless
```

This will execute the high-priority scenarios in the `salesFunnel.feature` file using a headless Chrome browser.

### Run with Specific Parameters

You can also run the tests with specific parameters using the `bin/run-tests.php` script directly:

```bash
php bin/run-tests.php --brand=aeons --env=stage --feature=features/salesFunnel.feature --tags=@high-priority
```

## Test Flow

The high-priority scenario in `salesFunnel.feature` tests the following flow:

1. Navigate to a sales funnel page
2. Verify product details
3. Proceed to checkout
4. Fill in shipping information
5. Verify shipping method and cost
6. Enter payment details
7. Complete the purchase
8. Verify redirection to the upsell page
9. Accept the upsell offer
10. Verify order confirmation
11. Verify order confirmation email

This is a critical flow that tests the entire sales funnel process from start to finish.

## Troubleshooting

### Chrome Not Running

If Chrome is not running in debug mode, you'll get an error like "Could not connect to Chrome on port 9222".

**Solution**: Run the `start-chrome-debug.bat` script or start Chrome manually with the `--remote-debugging-port=9222` flag.

### Wrong Chrome Path

If Chrome is installed in a different location, update the path in the `start-chrome-debug.bat` script.

**Solution**: Edit the `start-chrome-debug.bat` script to use the correct path to Chrome.

### Port Already in Use

If port 9222 is already in use, you'll need to kill the existing Chrome process or use a different port.

**Solution**: 
1. Kill the existing Chrome process:
   ```bash
   # Windows
   taskkill /F /IM chrome.exe
   
   # Linux/Mac
   pkill -f chrome
   ```
2. Or use a different port and update the configuration in `config/services/core.yml`:
   ```yaml
   mink.session_factory:
     class: App\Service\Browser\SessionFactory
     arguments:
       $driverName: 'chrome'
       $driverOptions:
         api_url: 'http://localhost:9223'  # Change port here
   ```

### Missing Dependencies

If you're missing dependencies, you'll get errors about missing classes.

**Solution**: Run `composer install` to install all dependencies.

### Environment Variables Not Set

If environment variables are not set correctly, the test may fail.

**Solution**: Check the `.env` file or set environment variables manually as described above.

## Advanced Configuration

### Running in Non-Headless Mode

To run the tests in non-headless mode (visible browser), modify the Chrome startup command:

```bash
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222
```

### Running with Different Tags

To run different scenarios, you can modify the tags parameter:

```bash
php bin/run-tests.php --brand=aeons --env=stage --feature=features/salesFunnel.feature --tags=@regression
```

### Running Multiple Features

To run multiple features, you can omit the `--feature` parameter:

```bash
php bin/run-tests.php --brand=aeons --env=stage --tags=@funnel
```

## Reporting

Test results are stored in the `reports` directory. Each test run generates a report with details about the test execution, including:

- Test name
- Test status (pass/fail)
- Error messages (if any)
- Screenshots (if any)

## Conclusion

The sales funnel tests are critical for ensuring the e-commerce functionality works correctly. By following the instructions in this document, you can successfully run the `test:sales-funnel:headless` command and verify the sales funnel flow.
