<?php

// Set environment variables
putenv("TEST_BASE_URL=https://aeonstest.info");
putenv("TEST_BRAND=aeons");
putenv("TEST_ENV=stage");
putenv("TEST_PRODUCT=total_harmony");
putenv("BROWSER_NAME=chrome");
putenv("WEBDRIVER_HOST=http://localhost:4444/wd/hub");

// Print environment variables for debugging
echo "Environment variables:\n";
echo "TEST_BASE_URL: " . getenv("TEST_BASE_URL") . "\n";
echo "TEST_BRAND: " . getenv("TEST_BRAND") . "\n";
echo "TEST_ENV: " . getenv("TEST_ENV") . "\n";
echo "TEST_PRODUCT: " . getenv("TEST_PRODUCT") . "\n";

// Build the command with additional options for dry-run
// --dry-run: Don't execute scenarios, just show definitions
// --no-interaction: Do not ask any interactive question
// --no-snippets: Do not print snippets for undefined steps
$cmd = "vendor\\bin\\behat features/ecommerce_checkout_flows.feature features/salesFunnel_admin.feature features/salesFunnel_error_recovery.feature features/salesFunnel_variations.feature features/salesFunnel.feature --dry-run --no-interaction --no-snippets";

// Execute the command
echo "Running: $cmd\n";
system($cmd, $returnCode);
echo "Return code: $returnCode\n"; 