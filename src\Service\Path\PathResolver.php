<?php

namespace App\Service\Path;

/**
 * Service for resolving paths with parameter placeholders
 */
class PathResolver implements PathResolverInterface
{
    private string $projectRoot;
    private string $configDir;
    private string $fixturesDir;

    /**
     * Constructor
     *
     * @param string $projectRoot Project root directory
     */
    public function __construct(string $projectRoot)
    {
        $this->projectRoot = $projectRoot;
        $this->configDir = $projectRoot . '/config';
        $this->fixturesDir = $projectRoot . '/features/fixtures';
    }

    /**
     * {@inheritdoc}
     */
    public function resolvePath(string $path): string
    {
        $path = str_replace('%paths.base%', $this->projectRoot, $path);
        $path = str_replace('%app.project_root%', $this->projectRoot, $path);
        $path = str_replace('%app.config_dir%', $this->configDir, $path);
        $path = str_replace('%app.fixtures_dir%', $this->fixturesDir, $path);

        return $path;
    }

    /**
     * {@inheritdoc}
     */
    public function getProjectRoot(): string
    {
        return $this->projectRoot;
    }

    /**
     * {@inheritdoc}
     */
    public function getConfigDir(): string
    {
        return $this->configDir;
    }

    /**
     * {@inheritdoc}
     */
    public function getFixturesDir(): string
    {
        return $this->fixturesDir;
    }
}