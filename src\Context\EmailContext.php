<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for email-related functionality
 */
class EmailContext extends BaseContext
{
    private const MAX_RETRIES = 3;
    private const RETRY_DELAY = 2;
    private const DEFAULT_TIMEFRAME = 3600;
    private const MAILTRAP_API_URL = 'https://mailtrap.io/api/v1';
    private const INBOX_MESSAGES_ENDPOINT = '/inboxes/%s/messages';
    private const MESSAGE_ENDPOINT = '/inboxes/%s/messages/%s';
    private BrowserServiceInterface $browserService;
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;
    private Client $httpClient;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param BrowserServiceInterface|null $browserService Browser service
     * @param ConfigurationServiceInterface|null $configService Configuration service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface            $container = null,
        ?BrowserServiceInterface       $browserService = null,
        ?ConfigurationServiceInterface $configService = null,
        ?SharedStateServiceInterface   $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->browserService = $browserService ?? $container->get(BrowserServiceInterface::class);
            $this->configService = $configService ?? $container->get(ConfigurationServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->browserService = $browserService ?? $this->createMockBrowserService();
            $this->configService = $configService ?? $this->createMockConfigService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $this->httpClient = new Client();

        $this->logInfo("EmailContext initialized");
    }

    /**
     * Create a mock browser service for testing
     *
     * @return BrowserServiceInterface
     */
    private function createMockBrowserService(): BrowserServiceInterface
    {
        return new class implements BrowserServiceInterface {
            public function elementExists(string $selector): bool
            {
                return true;
            }

            public function wait(int $seconds): void
            { /* do nothing */
            }

            public function isSessionActive(): bool
            {
                return true;
            }

            public function getDriverType(): string
            {
                return 'mock';
            }

            public function hasContent(string $text): bool
            {
                return true;
            }

            public function navigateBack(): void
            { /* do nothing */
            }

            public function getPageTitle(): string
            {
                return 'Mock Page Title';
            }

            public function waitForUrlContains(string $text, int $timeout = 30): bool
            {
                return true;
            }

            public function isBrowserStackSession(): bool
            {
                return false;
            }

            public function findElement(string $selector): ?\Behat\Mink\Element\NodeElement
            {
                return null;
            }

            public function getCurrentUrl(): string
            {
                return 'https://example.com';
            }

            public function visit(string $url): void
            { /* do nothing */
            }

            public function getSession(): \Behat\Mink\Session
            {
                throw new \RuntimeException('Not implemented');
            }

            public function waitForElement(string $selector, int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForPageToLoad(int $timeout = 30): void
            { /* do nothing */
            }

            public function takeScreenshot(string $name = null): string
            {
                return '/path/to/screenshot.png';
            }

            public function fillField(string $field, string $value): void
            { /* do nothing */
            }

            public function selectOption(string $select, string $option): void
            { /* do nothing */
            }

            public function executeScript(string $script)
            {
                return null;
            }

            public function findElements(string $selector): array
            {
                return [];
            }

            public function waitForElementVisible(string $selector, int $timeout = 30): bool
            {
                return true;
            }

            public function scrollToElement(string $selector): void
            { /* do nothing */
            }

            public function clickElement(string $selector): void
            { /* do nothing */
            }

            public function getElementText(string $selector): string
            {
                return 'Mock Text';
            }

            public function isElementVisible(string $selector): bool
            {
                return true;
            }

            public function waitForDocumentReady(int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForAjaxToComplete(int $timeout = 30): void
            { /* do nothing */
            }
        };
    }

    /**
     * Create a mock configuration service for testing
     *
     * @return ConfigurationServiceInterface
     */
    private function createMockConfigService(): ConfigurationServiceInterface
    {
        return new class implements ConfigurationServiceInterface {
            private string $currentBrand = 'aeons';
            private string $currentEnvironment = 'stage';
            private array $config = [
                'base_url' => 'https://aeonstest.info'
            ];

            public function getBrandConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getEnvironmentConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getCurrentBrand(): string
            {
                return $this->currentBrand;
            }

            public function getCurrentEnvironment(): string
            {
                return $this->currentEnvironment;
            }

            public function setBrand(string $brand): void
            {
                $this->currentBrand = $brand;
            }

            public function setEnvironment(string $environment): void
            {
                $this->currentEnvironment = $environment;
            }

            public function getConfigValue(string $key)
            {
                return $this->config[$key] ?? null;
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @Then I should receive an abandoned cart email within :hours hour
     */
    public function iShouldReceiveAbandonedCartEmailWithinHour(string $hours): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                $userEmail,
                'Complete your purchase',
                intval($hours) * 3600
            );

            if (!$email) {
                throw new \RuntimeException(
                    sprintf('Abandoned cart email not received within %s hour(s)', $hours)
                );
            }

            $this->stateService->set('email.recovery', $email);
            $this->stateService->set('email.last_check_time', time());
            $this->logInfo('Received abandoned cart email');
        } catch (\Throwable $e) {
            $this->logError('Failed to receive abandoned cart email', $e);
            throw $e;
        }
    }

    /**
     * @Then I should not receive any more recovery emails
     */
    public function iShouldNotReceiveAnyMoreRecoveryEmails(): void
    {
        try {
            $lastEmailTime = $this->stateService->get('email.last_check_time');
            if (!$lastEmailTime) {
                throw new \RuntimeException('Last email check time not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $emails = $this->findEmails(
                $userEmail,
                'Complete your purchase',
                time() - $lastEmailTime
            );

            if (!empty($emails)) {
                throw new \RuntimeException(
                    sprintf('Found %d unexpected recovery emails after cart expiry', count($emails))
                );
            }

            $this->logInfo(sprintf('Verified no additional recovery emails for user %s after last check', $userEmail));
        } catch (\Throwable $e) {
            $this->logError('Failed to check for new recovery emails', $e);
            throw $e;
        }
    }

    /**
     * Get user email from shared state
     *
     * @return string User email
     * @throws \RuntimeException When user email is not found
     */
    private function getUserEmail(): string
    {
        // First check userData which is the new structure
        $userData = $this->stateService->get('user.data');
        if ($userData && isset($userData['email'])) {
            return $userData['email'];
        }

        // Then check the legacy key for backward compatibility
        $userEmail = $this->stateService->get('user.email');
        if ($userEmail) {
            return $userEmail;
        }

        throw new \RuntimeException('User email not found in shared state');
    }

    /**
     * Wait for an email to arrive
     *
     * @param string $toEmail Recipient email
     * @param string $subject Email subject
     * @param int $timeframeSeconds Timeframe in seconds
     * @return array|null Email data or null if not found
     */
    public function waitForEmail(string $toEmail, string $subject, int $timeframeSeconds): ?array
    {
        $this->logInfo(sprintf(
            'Waiting for email to %s with subject "%s" within %d seconds',
            $toEmail,
            $subject,
            $timeframeSeconds
        ));

        $startTime = time();
        $endTime = $startTime + $timeframeSeconds;

        $retryCount = 0;

        while (time() < $endTime && $retryCount < self::MAX_RETRIES) {
            try {
                $emails = $this->fetchEmails($toEmail);

                foreach ($emails as $email) {
                    if (stripos($email['subject'], $subject) !== false) {
                        $this->logInfo(sprintf('Found email with subject "%s"', $subject));
                        return $email;
                    }
                }

                $this->logInfo('Email not found, waiting before retry');
                sleep(self::RETRY_DELAY);
                $retryCount++;
            } catch (\Throwable $e) {
                $this->logError('Error fetching emails', $e);
                sleep(self::RETRY_DELAY);
                $retryCount++;
            }
        }

        $this->logInfo(sprintf('Email with subject "%s" not found within timeframe', $subject));
        return null;
    }

    /**
     * Find emails matching criteria
     *
     * @param string $toEmail Recipient email
     * @param string $subject Email subject (optional)
     * @param int $timeframeSeconds Timeframe in seconds
     * @return array Found emails
     */
    private function findEmails(string $toEmail, string $subject = '', int $timeframeSeconds = null): array
    {
        try {
            $emails = $this->fetchEmails($toEmail);
            $result = [];

            foreach ($emails as $email) {
                // Filter by subject if provided
                if ($subject && stripos($email['subject'], $subject) === false) {
                    continue;
                }

                // Filter by timeframe if provided
                if ($timeframeSeconds !== null) {
                    $emailDate = strtotime($email['created_at']);
                    if (time() - $emailDate > $timeframeSeconds) {
                        continue;
                    }
                }

                $result[] = $email;
            }

            return $result;
        } catch (\Throwable $e) {
            $this->logError('Error finding emails', $e);
            throw $e;
        }
    }

    /**
     * Fetch emails for a recipient
     *
     * @param string $toEmail Recipient email
     * @return array Email data
     * @throws \RuntimeException When emails cannot be fetched
     */
    private function fetchEmails(string $toEmail): array
    {
        try {
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $token = $this->getMailtrapToken();

            $url = self::MAILTRAP_API_URL . sprintf(self::INBOX_MESSAGES_ENDPOINT, $inboxId);

            $response = $this->httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                ],
                'query' => [
                    'search' => $toEmail,
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!is_array($data)) {
                throw new \RuntimeException('Invalid response from Mailtrap API');
            }

            return $data;
        } catch (GuzzleException $e) {
            throw new \RuntimeException(
                sprintf('Failed to fetch emails: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Get Mailtrap inbox ID for a brand
     *
     * @param string $brand Brand identifier
     * @return string Inbox ID
     * @throws \RuntimeException When inbox ID is not found
     */
    private function getInboxId(string $brand): string
    {
        $inboxIdEnvVar = sprintf('MAILTRAP_%s_INBOX_ID', strtoupper($brand));
        $inboxId = getenv($inboxIdEnvVar);

        if (!$inboxId) {
            throw new \RuntimeException(
                sprintf('Mailtrap inbox ID not found for brand "%s"', $brand)
            );
        }

        return $inboxId;
    }

    /**
     * Get Mailtrap API token
     *
     * @return string API token
     * @throws \RuntimeException When API token is not found
     */
    private function getMailtrapToken(): string
    {
        $token = getenv('MAILTRAP_TOKEN');

        if (!$token) {
            throw new \RuntimeException('Mailtrap API token not found');
        }

        return $token;
    }

    /**
     * @Then I should receive an order confirmation email
     */
    public function iShouldReceiveAnOrderConfirmationEmail(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $orderNumber = $this->stateService->get('order.number');
            $subject = $orderNumber ?
                sprintf('Order Confirmation #%s', $orderNumber) :
                'Order Confirmation';

            $email = $this->waitForEmail(
                $userEmail,
                $subject,
                self::DEFAULT_TIMEFRAME
            );

            if (!$email) {
                throw new \RuntimeException('Order confirmation email not received');
            }

            $this->stateService->set('email.confirmation', $email);
            $this->logInfo('Received order confirmation email');
        } catch (\Throwable $e) {
            $this->logError('Failed to receive order confirmation email', $e);
            throw $e;
        }
    }

    /**
     * @When I click the recovery link in the email
     */
    public function iClickTheRecoveryLinkInTheEmail(): void
    {
        try {
            $email = $this->stateService->get('email.recovery');

            if (!$email) {
                throw new \RuntimeException('No recovery email found');
            }

            $recoveryLink = $this->extractLinkFromEmail($email, 'recovery');

            if (!$recoveryLink) {
                throw new \RuntimeException('Recovery link not found in email');
            }

            $this->browserService->visit($recoveryLink);
            $this->stateService->set('email.recovery_link_clicked', true);
            $this->logInfo('Clicked recovery link in abandoned cart email');
        } catch (\Throwable $e) {
            $this->logError('Failed to click recovery link in abandoned cart email', $e);
            throw $e;
        }
    }

    /**
     * @When I follow the :linkText link in the email
     */
    public function iFollowTheLinkInTheEmail(string $linkText): void
    {
        try {
            $email = $this->stateService->get('email.recovery') ??
                $this->stateService->get('email.confirmation') ??
                $this->stateService->get('email.abandoned_cart');

            if (!$email) {
                throw new \RuntimeException('No email found in shared context');
            }

            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);

            // Get full email content
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Find link matching the requested text
            $links = $this->extractLinksFromHtml($emailContent, $linkText);

            if (empty($links)) {
                throw new \RuntimeException(sprintf('Link with text "%s" not found in email', $linkText));
            }

            // Visit the first matching link
            $this->browserService->visit($links[0]);
            $this->logInfo(sprintf('Followed "%s" link in email', $linkText));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to follow link with text "%s" in email', $linkText), $e);
            throw $e;
        }
    }

    /**
     * @Then I verify the welcome email contains account credentials
     */
    public function iVerifyTheWelcomeEmailContainsAccountCredentials(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                $userEmail,
                'Welcome',
                self::DEFAULT_TIMEFRAME
            );

            if (!$email) {
                throw new \RuntimeException('Welcome email not received');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Verify it contains login credentials
            if (stripos($emailContent, 'login') === false) {
                throw new \RuntimeException('Welcome email does not contain login information');
            }

            if (stripos($emailContent, 'password') === false) {
                throw new \RuntimeException('Welcome email does not contain password information');
            }

            $this->stateService->set('email.welcome', $email);
            $this->logInfo('Verified welcome email contains account credentials');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify welcome email credentials', $e);
            throw $e;
        }
    }

    /**
     * @Then I verify the subscription confirmation email
     */
    public function iVerifyTheSubscriptionConfirmationEmail(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                $userEmail,
                'Your subscription has been created',
                self::DEFAULT_TIMEFRAME
            );

            if (!$email) {
                throw new \RuntimeException('Subscription confirmation email not received');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Get order data from shared state
            $orderData = $this->stateService->get('order.data');
            if (!$orderData) {
                throw new \RuntimeException('Order data not found in shared context');
            }

            // Verify email contains product name
            if (isset($orderData['products'][0]['name'])) {
                $productName = $orderData['products'][0]['name'];
                if (stripos($emailContent, $productName) === false) {
                    throw new \RuntimeException(sprintf('Product name "%s" not found in subscription email', $productName));
                }
            }

            // Verify email contains subscription frequency
            if (stripos($emailContent, 'Ships Every') === false &&
                stripos($emailContent, 'Delivery frequency') === false) {
                throw new \RuntimeException('Subscription frequency not found in email');
            }

            // Verify email contains order total if available
            if (isset($orderData['total']) && stripos($emailContent, $orderData['total']) === false) {
                throw new \RuntimeException('Order total not found in subscription email');
            }

            $this->stateService->set('email.subscription', $email);
            $this->logInfo('Verified subscription confirmation email');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify subscription confirmation email', $e);
            throw $e;
        }
    }

    /**
     * Get email content by ID
     *
     * @param string $inboxId Mailtrap inbox ID
     * @param string $emailId Email ID
     * @return string Email HTML content
     * @throws \RuntimeException When email content cannot be retrieved
     */
    private function getEmailContent(string $inboxId, string $emailId): string
    {
        try {
            $token = $this->getMailtrapToken();
            $url = self::MAILTRAP_API_URL . sprintf(self::MESSAGE_ENDPOINT, $inboxId, $emailId);

            $response = $this->httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!is_array($data) || !isset($data['html_body'])) {
                throw new \RuntimeException('Invalid response from Mailtrap API');
            }

            return $data['html_body'];
        } catch (GuzzleException $e) {
            throw new \RuntimeException(
                sprintf('Failed to get email content: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Extract links from HTML content matching link text
     *
     * @param string $htmlContent HTML content
     * @param string $linkText Text to match
     * @return array Found links
     */
    private function extractLinksFromHtml(string $htmlContent, string $linkText): array
    {
        $links = [];
        $pattern = '/<a[^>]*href=["\'](https?:\/\/[^"\']*)["\'][^>]*>' . preg_quote($linkText, '/') . '<\/a>/i';

        if (preg_match_all($pattern, $htmlContent, $matches)) {
            $links = $matches[1];
        }

        return $links;
    }

    /**
     * Extract a link from an email
     *
     * @param array $email Email data
     * @param string $linkType Link type
     * @return string|null Link URL or null if not found
     */
    private function extractLinkFromEmail(array $email, string $linkType): ?string
    {
        try {
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $token = $this->getMailtrapToken();

            $url = self::MAILTRAP_API_URL . sprintf(self::MESSAGE_ENDPOINT, $inboxId, $email['id']);

            $response = $this->httpClient->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Accept' => 'application/json',
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!is_array($data) || !isset($data['html_body'])) {
                throw new \RuntimeException('Invalid response from Mailtrap API');
            }

            $htmlBody = $data['html_body'];

            // Extract link based on link type
            switch ($linkType) {
                case 'recovery':
                    if (preg_match('/<a[^>]*href=["\'](https?:\/\/[^"\']*\/cart\/recover\/[^"\']*)["\'][^>]*>/i', $htmlBody, $matches)) {
                        return $matches[1];
                    }
                    break;

                case 'order':
                    if (preg_match('/<a[^>]*href=["\'](https?:\/\/[^"\']*\/order\/[^"\']*)["\'][^>]*>/i', $htmlBody, $matches)) {
                        return $matches[1];
                    }
                    break;

                default:
                    if (preg_match('/<a[^>]*href=["\'](https?:\/\/[^"\']*)["\'][^>]*>/i', $htmlBody, $matches)) {
                        return $matches[1];
                    }
                    break;
            }

            return null;
        } catch (GuzzleException $e) {
            throw new \RuntimeException(
                sprintf('Failed to extract link from email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should be redirected back to the merchant site from email
     */
    public function iShouldBeRedirectedBackToTheMerchantSiteFromEmail(): void
    {
        try {
            $baseUrl = $this->getConfigService()->getBaseUrl();
            $browserService = $this->getBrowserService();

            if (!$browserService->waitForUrlContains($baseUrl, 10)) {
                throw new \RuntimeException("Not redirected back to merchant site within timeout");
            }

            $this->logInfo("Verified redirection back to merchant site from email");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify redirection to merchant site from email", $e);
            throw new \RuntimeException("Failed to verify redirection to merchant site from email: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Then /^I should receive an abandoned cart email with subject "([^"]*)"$/
     */
    public function iShouldReceiveAnAbandonedCartEmailWithSubject(string $subject): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail($userEmail, $subject, self::DEFAULT_TIMEFRAME);

            if (!$email) {
                throw new \RuntimeException(sprintf('Abandoned cart email with subject "%s" not received', $subject));
            }

            $this->stateService->set('email.abandoned_cart', $email);
            $this->logInfo(sprintf('Received abandoned cart email with subject "%s"', $subject));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to receive abandoned cart email with subject "%s"', $subject), $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should receive an abandoned cart email with a coupon code$/
     */
    public function iShouldReceiveAnAbandonedCartEmailWithACouponCode(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail($userEmail, 'Special offer', self::DEFAULT_TIMEFRAME);

            if (!$email) {
                throw new \RuntimeException('Abandoned cart email with coupon not received');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Extract coupon code using regex
            if (!preg_match('/[A-Z0-9]{6,}/', $emailContent, $matches)) {
                throw new \RuntimeException('Coupon code not found in email content');
            }

            $this->stateService->set('email.coupon_code', $matches[0]);
            $this->logInfo(sprintf('Found coupon code %s in abandoned cart email', $matches[0]));
        } catch (\Throwable $e) {
            $this->logError('Failed to receive abandoned cart email with coupon', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should not see an abandoned cart email$/
     */
    public function iShouldNotSeeAnAbandonedCartEmail(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $emails = $this->findEmails($userEmail, 'Complete your purchase', self::DEFAULT_TIMEFRAME);

            if (!empty($emails)) {
                throw new \RuntimeException(sprintf('Found %d unexpected abandoned cart emails', count($emails)));
            }

            $this->logInfo('Verified no abandoned cart emails were sent');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify absence of abandoned cart emails', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see that (\d+) emails was sent for each time$/
     */
    public function iShouldSeeThatEmailsWasSentForEachTime(int $expectedCount): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $emails = $this->findEmails($userEmail, '', self::DEFAULT_TIMEFRAME);

            if (count($emails) != $expectedCount) {
                throw new \RuntimeException(
                    sprintf('Expected %d emails, but found %d', $expectedCount, count($emails))
                );
            }

            $this->logInfo(sprintf('Verified %d emails were sent', $expectedCount));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to verify email count (expected: %d)', $expectedCount), $e);
            throw $e;
        }
    }

    /**
     * @Then /^I verify the order confirmation email for initial product only$/
     */
    public function iVerifyTheOrderConfirmationEmailForInitialProductOnly(): void
    {
        try {
            $orderNumber = $this->stateService->get('order.number');
            if (!$orderNumber) {
                throw new \RuntimeException('Order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                $userEmail,
                sprintf('Order Confirmation #%s', $orderNumber),
                self::DEFAULT_TIMEFRAME
            );

            if (!$email) {
                throw new \RuntimeException('Order confirmation email not received');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Verify initial product is in the email
            $funnelData = $this->stateService->get('funnel.current_funnel');
            if (!$funnelData) {
                throw new \RuntimeException('Funnel data not found in shared context');
            }

            $initialProduct = $funnelData['entry']['product'] ?? 'Dark Spot Vanish';
            if (stripos($emailContent, $initialProduct) === false) {
                throw new \RuntimeException(sprintf('Initial product "%s" not found in email', $initialProduct));
            }

            // Verify upsell product is NOT in the email
            $upsellProduct = $funnelData['upsell']['product'] ?? 'Relax + Restore';
            if (stripos($emailContent, $upsellProduct) !== false) {
                throw new \RuntimeException('Upsell product found in email when it should not be there');
            }

            $this->logInfo('Verified order confirmation email contains only initial product');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify order confirmation email for initial product only', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I verify the order confirmation email for the renewal order$/
     */
    public function iVerifyTheOrderConfirmationEmailForTheRenewalOrder(): void
    {
        try {
            $renewalOrderNumber = $this->stateService->get('order.renewal_number');
            if (!$renewalOrderNumber) {
                throw new \RuntimeException('Renewal order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                $userEmail,
                sprintf('Order Confirmation #%s', $renewalOrderNumber),
                self::DEFAULT_TIMEFRAME
            );

            if (!$email) {
                throw new \RuntimeException('Renewal order confirmation email not received');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Verify subscription product is in the email
            $subscriptionProduct = $this->stateService->get('subscription.product') ?? 'Younger You Skin Cream';
            if (stripos($emailContent, $subscriptionProduct) === false) {
                throw new \RuntimeException(sprintf('Subscription product "%s" not found in email', $subscriptionProduct));
            }

            $this->logInfo('Verified order confirmation email for renewal order');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify renewal order confirmation email', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I verify the order confirmation email contains all purchased items$/
     */
    public function iVerifyTheOrderConfirmationEmailContainsAllPurchasedItems(): void
    {
        try {
            $orderNumber = $this->stateService->get('order.number');
            if (!$orderNumber) {
                throw new \RuntimeException('Order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                $userEmail,
                sprintf('Order Confirmation #%s', $orderNumber),
                self::DEFAULT_TIMEFRAME
            );

            if (!$email) {
                throw new \RuntimeException('Order confirmation email not received');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Verify both products are in the email
            $funnelData = $this->stateService->get('funnel.current_funnel');
            if (!$funnelData) {
                throw new \RuntimeException('Funnel data not found in shared context');
            }

            $initialProduct = $funnelData['entry']['product'] ?? 'Dark Spot Vanish';
            if (stripos($emailContent, $initialProduct) === false) {
                throw new \RuntimeException(sprintf('Initial product "%s" not found in email', $initialProduct));
            }

            $upsellProduct = $funnelData['upsell']['product'] ?? 'Relax + Restore';
            if (stripos($emailContent, $upsellProduct) === false) {
                throw new \RuntimeException(sprintf('Upsell product "%s" not found in email', $upsellProduct));
            }

            $this->logInfo('Verified order confirmation email contains all purchased items');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify order confirmation email with all items', $e);
            throw $e;
        }
    }

    /**
     * @Then /^the abandoned cart email contains the correct product information$/
     */
    public function theAbandonedCartEmailContainsTheCorrectProductInformation(): void
    {
        try {
            $email = $this->stateService->get('email.recovery');
            if (!$email) {
                throw new \RuntimeException('No recovery email found in shared context');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Verify product information is in the email
            $funnelData = $this->stateService->get('funnel.current_funnel');
            if (!$funnelData) {
                throw new \RuntimeException('Funnel data not found in shared context');
            }

            $product = $funnelData['entry']['product'] ?? 'Dark Spot Vanish';
            if (stripos($emailContent, $product) === false) {
                throw new \RuntimeException(sprintf('Product "%s" not found in abandoned cart email', $product));
            }

            // Check for recovery link
            $recoveryLink = $this->extractLinkFromEmail($email, 'recovery');
            if (!$recoveryLink) {
                throw new \RuntimeException('Recovery link not found in abandoned cart email');
            }

            $this->logInfo('Verified abandoned cart email contains correct product information');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify product information in abandoned cart email', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I verify the order confirmation email$/
     */
    public function iVerifyTheOrderConfirmationEmail(): void
    {
        try {
            $orderNumber = $this->stateService->get('order.number');
            if (!$orderNumber) {
                throw new \RuntimeException('Order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                $userEmail,
                sprintf('Order Confirmation #%s', $orderNumber),
                self::DEFAULT_TIMEFRAME
            );

            if (!$email) {
                throw new \RuntimeException('Order confirmation email not received');
            }

            // Get full email content
            $emailId = $email['id'];
            $brand = $this->configService->getCurrentBrand();
            $inboxId = $this->getInboxId($brand);
            $emailContent = $this->getEmailContent($inboxId, $emailId);

            // Verify order data in email
            $orderData = $this->stateService->get('order.data');
            if (!$orderData) {
                throw new \RuntimeException('Order data not found in shared context');
            }

            if (isset($orderData['orderNumber']) && stripos($emailContent, $orderData['orderNumber']) === false) {
                throw new \RuntimeException('Order number not found in email');
            }

            if (isset($orderData['total']) && stripos($emailContent, $orderData['total']) === false) {
                throw new \RuntimeException('Order total not found in email');
            }

            if (isset($orderData['products']) && is_array($orderData['products'])) {
                foreach ($orderData['products'] as $product) {
                    if (isset($product['name']) && stripos($emailContent, $product['name']) === false) {
                        throw new \RuntimeException(sprintf('Product "%s" not found in email', $product['name']));
                    }
                }
            }

            $this->stateService->set('email.order_confirmation', $email);
            $this->logInfo('Verified order confirmation email');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify order confirmation email', $e);
            throw $e;
        }
    }

    /**
     * Wait for multiple emails
     *
     * @param string $toEmail Target email address
     * @param int $expectedCount Expected number of emails
     * @param int $timeframeSeconds Timeframe in seconds
     * @return array Found emails
     */
    private function waitForEmails(
        string $toEmail,
        int    $expectedCount,
        int    $timeframeSeconds = self::DEFAULT_TIMEFRAME
    ): array
    {
        try {
            $attempts = self::MAX_RETRIES;
            while ($attempts > 0) {
                $emails = $this->findEmails($toEmail, '', $timeframeSeconds);
                if (count($emails) >= $expectedCount) {
                    return $emails;
                }
                sleep(self::RETRY_DELAY);
                $attempts--;
            }

            $this->logInfo(sprintf('Timeout waiting for %d emails', $expectedCount));
            return [];
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to wait for %d emails', $expectedCount), $e);
            throw new \RuntimeException(
                sprintf('Failed to wait for emails: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }
}
