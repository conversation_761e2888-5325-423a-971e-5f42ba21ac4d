# Phase 3: Context Migration

## Service-Oriented Test Architecture Migration

**Version:** 1.0  
**Last Updated:** 2025-05-01  
**Author:** AI Assistant

## Table of Contents

1. [Overview](#1-overview)
2. [Current Context Analysis](#2-current-context-analysis)
3. [Base Context](#3-base-context)
4. [Feature Context](#4-feature-context)
5. [Brand Context](#5-brand-context)
6. [Product Context](#6-product-context)
7. [Cart Context](#7-cart-context)
8. [Checkout Context](#8-checkout-context)
9. [Other Contexts](#9-other-contexts)
10. [Implementation Plan](#10-implementation-plan)
11. [Testing Strategy](#11-testing-strategy)
12. [Rollback Plan](#12-rollback-plan)

---

## 1. Overview

Phase 3 focuses on migrating the existing Behat contexts to use the new service-oriented architecture. This phase builds
upon the foundation established in Phase 1 and the core services implemented in Phase 2. The goal is to replace direct
dependencies and singleton usage with proper service injection.

### 1.1 Objectives

- Create a new base context that uses services
- Migrate all existing contexts to use the new base context
- Replace direct dependencies with service calls
- Ensure backward compatibility with existing step definitions
- Provide comprehensive tests for each context

### 1.2 Timeline

- Estimated duration: 2 weeks
- Dependencies: Phase 1 (Foundation Setup), Phase 2 (Core Services Implementation)

---

## 2. Current Context Analysis

Before migrating the contexts, we need to understand the current implementation to ensure proper migration and backward
compatibility.

### 2.1 Context Hierarchy

The current context hierarchy is as follows:

```
BaseContext
├── FeatureContext
├── BrandContext
├── ProductContext
├── CartContext
├── CheckoutContext
├── PaymentContext
├── EmailContext
├── AdminCommandContext
└── Other specialized contexts
```

### 2.2 BaseContext Analysis

The current `BaseContext` provides:

- Access to the `SharedDataContext` singleton
- Basic logging functionality
- Access to the context manager

Key issues:

- Direct dependency on `SharedDataContext` singleton
- No service container access
- Limited error handling
- Inconsistent logging

### 2.3 Context Dependencies

The current contexts have the following dependencies:

| Context             | Dependencies                                           |
|---------------------|--------------------------------------------------------|
| FeatureContext      | SharedDataContext, various page objects                |
| BrandContext        | SharedDataContext, ConfigurationManager                |
| ProductContext      | SharedDataContext, ProductPage                         |
| CartContext         | SharedDataContext, CartPage, ProductPage, CheckoutPage |
| CheckoutContext     | SharedDataContext, CheckoutPage, DataValidator         |
| PaymentContext      | SharedDataContext, PaymentPage                         |
| EmailContext        | SharedDataContext, EmailService                        |
| AdminCommandContext | SharedDataContext, AdminService                        |

### 2.4 Step Definition Analysis

The current step definitions often:

- Access the `SharedDataContext` singleton directly
- Create instances of services directly
- Have duplicated error handling logic
- Lack consistent logging

---

## 3. Base Context

### 3.1 Implementation Details

The new `BaseContext` will extend the `ServiceAwareContext` created in Phase 1 and provide access to all core services.

#### 3.1.1 Class Structure

```php
namespace App\Context\Base;

use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Behat\Behat\Context\Context;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

abstract class BaseContext extends ServiceAwareContext implements Context
{
    /**
     * Get the configuration service
     *
     * @return ConfigurationServiceInterface
     */
    protected function getConfigService(): ConfigurationServiceInterface
    {
        return $this->getService(ConfigurationServiceInterface::class);
    }
    
    /**
     * Get the test data service
     *
     * @return TestDataServiceInterface
     */
    protected function getTestDataService(): TestDataServiceInterface
    {
        return $this->getService(TestDataServiceInterface::class);
    }
    
    /**
     * Get the shared state service
     *
     * @return SharedStateServiceInterface
     */
    protected function getSharedStateService(): SharedStateServiceInterface
    {
        return $this->getService(SharedStateServiceInterface::class);
    }
    
    /**
     * Get the browser service
     *
     * @return BrowserServiceInterface
     */
    protected function getBrowserService(): BrowserServiceInterface
    {
        return $this->getService(BrowserServiceInterface::class);
    }
    
    /**
     * Get the validation service
     *
     * @return ValidationServiceInterface
     */
    protected function getValidationService(): ValidationServiceInterface
    {
        return $this->getService(ValidationServiceInterface::class);
    }
    
    /**
     * Log an informational message
     *
     * @param string $message Message to log
     * @param array $context Context data
     * @return void
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if ($this->container->has(LoggerInterface::class)) {
            $this->container->get(LoggerInterface::class)->info($message, $context);
        } else {
            error_log("[INFO] $message");
        }
    }
    
    /**
     * Log an error message
     *
     * @param string $message Message to log
     * @param \Throwable|null $exception Exception that occurred
     * @param array $context Context data
     * @return void
     */
    protected function logError(string $message, ?\Throwable $exception = null, array $context = []): void
    {
        if ($exception) {
            $context['exception'] = $exception;
            $context['trace'] = $exception->getTraceAsString();
        }
        
        if ($this->container->has(LoggerInterface::class)) {
            $this->container->get(LoggerInterface::class)->error($message, $context);
        } else {
            $logMessage = "[ERROR] $message";
            if ($exception) {
                $logMessage .= " - " . $exception->getMessage();
            }
            error_log($logMessage);
        }
    }
}
```

#### 3.1.2 Key Features

- **Service Access**: Convenient access to all core services
- **Consistent Logging**: Standardized logging with context support
- **Error Handling**: Improved error handling with exception support
- **Container Access**: Access to the service container for specialized needs
- **Type Safety**: Type hints for all service methods

#### 3.1.3 Backward Compatibility

To ensure backward compatibility with existing code:

1. Maintain the same method signatures for common methods
2. Provide helper methods that mimic the behavior of the old base context
3. Document migration patterns for common use cases

### 3.2 Unit Tests

Create comprehensive unit tests for the `BaseContext`:

1. Test service access methods
2. Test logging functionality
3. Test error handling
4. Test container access

---

## 4. Feature Context

### 4.1 Implementation Details

The `FeatureContext` is the main context class that orchestrates the test execution. It will be updated to use the new
service-oriented architecture.

#### 4.1.1 Class Structure

```php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Symfony\Component\DependencyInjection\ContainerInterface;

class FeatureContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private SharedStateServiceInterface $stateService;
    
    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->browserService = $browserService;
        $this->stateService = $stateService;
    }
    
    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        // Gather other contexts if needed
    }
    
    // Step definitions using services...
}
```

#### 4.1.2 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
3. Replace direct page object usage with `BrowserServiceInterface`
4. Update step definitions to use services
5. Update hooks to use services

#### 4.1.3 Backward Compatibility

To ensure backward compatibility:

1. Maintain the same step definition signatures
2. Ensure the same behavior for all step definitions
3. Provide helper methods for common patterns

### 4.2 Unit Tests

Create comprehensive unit tests for the `FeatureContext`:

1. Test constructor and initialization
2. Test hook methods
3. Test step definitions
4. Test service usage

---

## 5. Brand Context

### 5.1 Implementation Details

The `BrandContext` handles brand-specific functionality. It will be updated to use the `ConfigurationService`.

#### 5.1.1 Class Structure

```php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class BrandContext extends BaseContext
{
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;
    
    public function __construct(
        ContainerInterface $container,
        ConfigurationServiceInterface $configService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->configService = $configService;
        $this->stateService = $stateService;
    }
    
    /**
     * @Given I am using the :brand brand
     */
    public function iAmUsingTheBrand(string $brand): void
    {
        $this->configService->setBrand($brand);
        $this->stateService->set('brand.current', $brand);
        $this->logInfo(sprintf('Set current brand to: %s', $brand));
    }
    
    // Other step definitions...
}
```

#### 5.1.2 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `ConfigurationManager` usage with `ConfigurationServiceInterface`
3. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
4. Update step definitions to use services

#### 5.1.3 Backward Compatibility

To ensure backward compatibility:

1. Maintain the same step definition signatures
2. Ensure the same behavior for all step definitions
3. Store brand information in the same format in the shared state

### 5.2 Unit Tests

Create comprehensive unit tests for the `BrandContext`:

1. Test constructor and initialization
2. Test step definitions
3. Test service usage

---

## 6. Product Context

### 6.1 Implementation Details

The `ProductContext` handles product-related functionality. It will be updated to use the `TestDataService` and
`BrowserService`.

#### 6.1.1 Class Structure

```php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class ProductContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;
    
    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        TestDataServiceInterface $dataService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->browserService = $browserService;
        $this->dataService = $dataService;
        $this->stateService = $stateService;
    }
    
    /**
     * @When I select flavor :flavor
     */
    public function iSelectFlavor(string $flavor): void
    {
        try {
            // Use browser service to interact with the page
            $this->browserService->clickElement(".flavor[data-value='$flavor']");
            $this->logInfo(sprintf('Selected flavor: %s', $flavor));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to select flavor "%s"', $flavor), $e);
            throw $e;
        }
    }
    
    // Other step definitions...
}
```

#### 6.1.2 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `ProductPage` usage with `BrowserServiceInterface`
3. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
4. Replace direct test data access with `TestDataServiceInterface`
5. Update step definitions to use services

#### 6.1.3 Backward Compatibility

To ensure backward compatibility:

1. Maintain the same step definition signatures
2. Ensure the same behavior for all step definitions
3. Store product information in the same format in the shared state

### 6.2 Unit Tests

Create comprehensive unit tests for the `ProductContext`:

1. Test constructor and initialization
2. Test step definitions
3. Test service usage

---

## 7. Cart Context

### 7.1 Implementation Details

The `CartContext` handles cart-related functionality. It will be updated to use the `BrowserService` and
`SharedStateService`.

#### 7.1.1 Class Structure

```php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class CartContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private SharedStateServiceInterface $stateService;
    
    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->browserService = $browserService;
        $this->stateService = $stateService;
    }
    
    /**
     * @When I proceed to checkout
     */
    public function iProceedToCheckout(): void
    {
        try {
            // Check if cart is empty
            if ($this->isCartEmpty()) {
                throw new \RuntimeException('Cannot proceed to checkout with empty cart');
            }

            // Click checkout button
            $this->browserService->clickElement('button.checkout-btn');
            $this->stateService->set('cart.checkout_started', true);
            $this->logInfo('Proceeded to checkout');
        } catch (\Throwable $e) {
            $this->logError('Failed to proceed to checkout', $e);
            throw new \RuntimeException('Failed to proceed to checkout: ' . $e->getMessage(), 0, $e);
        }
    }
    
    /**
     * Check if the cart is empty
     */
    private function isCartEmpty(): bool
    {
        try {
            $element = $this->browserService->findElement('.cart-title .alert');
            return trim($element->getText()) === 'Your cart is empty';
        } catch (\Throwable $e) {
            return false;
        }
    }
    
    // Other step definitions...
}
```

#### 7.1.2 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `CartPage`, `ProductPage`, and `CheckoutPage` usage with `BrowserServiceInterface`
3. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
4. Update step definitions to use services
5. Extract page-specific logic into private methods

#### 7.1.3 Backward Compatibility

To ensure backward compatibility:

1. Maintain the same step definition signatures
2. Ensure the same behavior for all step definitions
3. Store cart information in the same format in the shared state

### 7.2 Unit Tests

Create comprehensive unit tests for the `CartContext`:

1. Test constructor and initialization
2. Test step definitions
3. Test service usage
4. Test helper methods

---

## 8. Checkout Context

### 8.1 Implementation Details

The `CheckoutContext` handles checkout-related functionality. It will be updated to use the `BrowserService`,
`ValidationService`, and `SharedStateService`.

#### 8.1.1 Class Structure

```php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class CheckoutContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;
    private ValidationServiceInterface $validationService;
    
    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        TestDataServiceInterface $dataService,
        SharedStateServiceInterface $stateService,
        ValidationServiceInterface $validationService
    ) {
        parent::__construct($container);
        $this->browserService = $browserService;
        $this->dataService = $dataService;
        $this->stateService = $stateService;
        $this->validationService = $validationService;
    }
    
    /**
     * @When I fill in the shipping information with :userType user data
     */
    public function iFillInTheShippingInformationWith(string $userType = 'default'): void
    {
        try {
            $userData = $this->getUserData($userType);
            $this->fillShippingInformation($userData);
            $this->validateCheckoutState();
            $this->stateService->set('checkout.shipping_address', $userData);
            $this->logInfo(sprintf('Filled shipping information with %s user data', $userType));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to fill shipping information with %s user data', $userType), $e);
            throw new \RuntimeException(
                sprintf('Failed to fill shipping information: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }
    
    /**
     * Get user data for the specified type
     */
    private function getUserData(string $userType): array
    {
        $userData = $this->dataService->loadTestData(
            $this->getConfigService()->getCurrentBrand(),
            'users',
            $userType
        );
        
        $this->validationService->validateUserData($userData);
        return $userData;
    }
    
    /**
     * Fill shipping information form
     */
    private function fillShippingInformation(array $userData): void
    {
        // Fill form fields using browser service
        $this->browserService->fillField('#first_name', $userData['firstName']);
        $this->browserService->fillField('#last_name', $userData['lastName']);
        $this->browserService->fillField('#email', $userData['email']);
        $this->browserService->fillField('#address', $userData['address']);
        $this->browserService->fillField('#city', $userData['city']);
        $this->browserService->selectOption('#state', $userData['state']);
        $this->browserService->fillField('#zip', $userData['zip']);
        $this->browserService->fillField('#phone', $userData['phone']);
        $this->browserService->clickElement('#shipping-continue');
    }
    
    /**
     * Validate checkout state
     */
    private function validateCheckoutState(): void
    {
        // Wait for checkout state to update
        $this->browserService->waitForElement('.checkout-step.active');
    }
    
    // Other step definitions...
}
```

#### 8.1.2 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `CheckoutPage` usage with `BrowserServiceInterface`
3. Replace direct `DataValidator` usage with `ValidationServiceInterface`
4. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
5. Update step definitions to use services
6. Extract page-specific logic into private methods

#### 8.1.3 Backward Compatibility

To ensure backward compatibility:

1. Maintain the same step definition signatures
2. Ensure the same behavior for all step definitions
3. Store checkout information in the same format in the shared state

### 8.2 Unit Tests

Create comprehensive unit tests for the `CheckoutContext`:

1. Test constructor and initialization
2. Test step definitions
3. Test service usage
4. Test helper methods

---

## 9. Other Contexts

### 9.1 PaymentContext

#### 9.1.1 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `PaymentPage` usage with `BrowserServiceInterface`
3. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
4. Update step definitions to use services

### 9.2 EmailContext

#### 9.2.1 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `EmailService` usage with a new `EmailServiceInterface`
3. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
4. Update step definitions to use services

### 9.3 AdminCommandContext

#### 9.3.1 Migration Tasks

1. Update the constructor to accept the service container and required services
2. Replace direct `AdminService` usage with a new `AdminServiceInterface`
3. Replace direct `SharedDataContext` usage with `SharedStateServiceInterface`
4. Update step definitions to use services

### 9.4 Other Specialized Contexts

For each specialized context:

1. Identify the dependencies
2. Create appropriate service interfaces if needed
3. Update the constructor to accept the service container and required services
4. Replace direct dependencies with service interfaces
5. Update step definitions to use services

---

## 10. Implementation Plan

### 10.1 Implementation Order

Migrate the contexts in the following order to respect dependencies:

1. `BaseContext`: Foundation for all other contexts
2. `FeatureContext`: Main context that orchestrates the test execution
3. `BrandContext`: Handles brand-specific functionality
4. `ProductContext`: Handles product-related functionality
5. `CartContext`: Handles cart-related functionality
6. `CheckoutContext`: Handles checkout-related functionality
7. `PaymentContext`: Handles payment-related functionality
8. Other specialized contexts

### 10.2 Step-by-Step Implementation

#### 10.2.1 Base Context

1. Create `src/Context/Base/BaseContext.php`
2. Implement service access methods
3. Implement logging and error handling
4. Create unit tests
5. Update service container configuration

#### 10.2.2 Feature Context

1. Create `src/Context/FeatureContext.php`
2. Implement constructor with service dependencies
3. Migrate step definitions to use services
4. Create unit tests
5. Update service container configuration

#### 10.2.3 Brand Context

1. Create `src/Context/BrandContext.php`
2. Implement constructor with service dependencies
3. Migrate step definitions to use services
4. Create unit tests
5. Update service container configuration

#### 10.2.4 Product Context

1. Create `src/Context/ProductContext.php`
2. Implement constructor with service dependencies
3. Migrate step definitions to use services
4. Create unit tests
5. Update service container configuration

#### 10.2.5 Cart Context

1. Create `src/Context/CartContext.php`
2. Implement constructor with service dependencies
3. Migrate step definitions to use services
4. Create unit tests
5. Update service container configuration

#### 10.2.6 Checkout Context

1. Create `src/Context/CheckoutContext.php`
2. Implement constructor with service dependencies
3. Migrate step definitions to use services
4. Create unit tests
5. Update service container configuration

#### 10.2.7 Other Contexts

For each remaining context:

1. Create the context class in `src/Context/`
2. Implement constructor with service dependencies
3. Migrate step definitions to use services
4. Create unit tests
5. Update service container configuration

### 10.3 Detailed Task Breakdown

#### Week 1

| Day | Tasks                                                         |
|-----|---------------------------------------------------------------|
| 1   | Analyze current contexts, create detailed implementation plan |
| 2   | Implement `BaseContext` and unit tests                        |
| 3   | Implement `FeatureContext` and unit tests                     |
| 4   | Implement `BrandContext` and unit tests                       |
| 5   | Implement `ProductContext` and unit tests                     |

#### Week 2

| Day | Tasks                                                     |
|-----|-----------------------------------------------------------|
| 1   | Implement `CartContext` and unit tests                    |
| 2   | Implement `CheckoutContext` and unit tests                |
| 3   | Implement `PaymentContext` and other specialized contexts |
| 4   | Update service container configuration, integration tests |
| 5   | Final testing and bug fixes                               |

---

## 11. Testing Strategy

### 11.1 Unit Testing

Create unit tests for each context to verify:

1. Constructor initializes services correctly
2. Step definitions work as expected
3. Service methods are called correctly
4. Error handling works properly

### 11.2 Integration Testing

Create integration tests to verify:

1. Contexts work together correctly
2. Service container configuration is correct
3. Step definitions interact with services correctly
4. Hooks work as expected

### 11.3 Functional Testing

Run existing Behat tests with the new contexts to verify:

1. Existing functionality continues to work
2. Performance is maintained or improved
3. No regressions are introduced

### 11.4 Test Coverage

Aim for high test coverage:

1. 90%+ line coverage for step definitions
2. 100% coverage for constructor and initialization
3. 80%+ coverage for helper methods
4. 70%+ coverage for error handling

---

## 12. Rollback Plan

### 12.1 Rollback Triggers

Consider rolling back if:

1. Critical step definitions are broken
2. Performance is significantly degraded
3. Backward compatibility is broken
4. Integration with existing code fails

### 12.2 Rollback Process

1. Revert service container configuration to use original contexts
2. Remove new context implementations
3. Verify original functionality works
4. Document issues for future resolution

### 12.3 Partial Rollback

Consider partial rollback if only specific contexts are problematic:

1. Identify problematic contexts
2. Revert only those contexts to original implementation
3. Keep working contexts in place
4. Document issues for future resolution
