<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Data\TestDataServiceInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for payment-related functionality
 */
class PaymentContext extends BaseContext
{
    private PageFactoryInterface $pageFactory;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param PageFactoryInterface|null $pageFactory Page factory service
     * @param TestDataServiceInterface|null $dataService Test data service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface          $container = null,
        ?PageFactoryInterface        $pageFactory = null,
        ?TestDataServiceInterface    $dataService = null,
        ?SharedStateServiceInterface $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
            $this->dataService = $dataService ?? $container->get(TestDataServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
            $this->dataService = $dataService ?? $this->createMockDataService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $this->logInfo("PaymentContext initialized");
    }

    /**
     * Create a mock page factory for testing
     *
     * @return PageFactoryInterface
     */
    private function createMockPageFactory(): PageFactoryInterface
    {
        return new class implements PageFactoryInterface {
            public function createPage(string $pageClass, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot create pages');
            }

            public function getPage(string $pageName, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot get pages');
            }

            public function hasPage(string $pageName): bool
            {
                return false;
            }
        };
    }

    /**
     * Create a mock test data service for testing
     *
     * @return TestDataServiceInterface
     */
    private function createMockDataService(): TestDataServiceInterface
    {
        return new class implements TestDataServiceInterface {
            private array $testData = [];

            public function loadTestData(string $brand, string $type, ?string $key = null): array
            {
                return [];
            }

            public function getTestData(string $type, ?string $key = null): array
            {
                return [];
            }

            public function getRandomTestData(string $type): array
            {
                return [];
            }

            public function validateTestData(string $type, array $data): bool
            {
                return true;
            }

            public function registerData(string $key, array $data): void
            {
                $this->testData[$key] = $data;
            }

            public function getData(string $key)
            {
                return $this->testData[$key] ?? null;
            }

            public function hasData(string $key): bool
            {
                return isset($this->testData[$key]);
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @When I select payment method :method
     */
    public function iSelectPaymentMethod(string $method): void
    {
        try {
            $paymentPage = $this->pageFactory->getPage('PaymentPage');
            $paymentPage->selectPaymentMethod($method);

            $this->stateService->set('payment.method', $method);
            $this->logInfo(sprintf("Selected payment method: %s", $method));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to select payment method: %s", $method), $e);
            throw $e;
        }
    }

    /**
     * @When I fill in credit card information with :cardType card
     */
    public function iFillInCreditCardInformationWith(string $cardType): void
    {
        try {
            // Load card data
            $cardData = $this->getCardData($cardType);

            $paymentPage = $this->pageFactory->getPage('PaymentPage');
            $paymentPage->fillCreditCardInformation($cardData);

            $this->stateService->set('payment.card_type', $cardType);
            $this->logInfo(sprintf("Filled in credit card information with %s card", $cardType));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to fill in credit card information with %s card", $cardType), $e);
            throw $e;
        }
    }

    /**
     * Get card data for the specified type
     */
    private function getCardData(string $cardType): array
    {
        $brand = $this->getConfigService()->getCurrentBrand();
        $paymentData = $this->dataService->loadTestData($brand, 'payment_methods');

        if (!isset($paymentData['credit_cards'][$cardType])) {
            throw new \RuntimeException(sprintf('Credit card data for type "%s" not found', $cardType));
        }

        return $paymentData['credit_cards'][$cardType];
    }


    /**
     * @When I complete the PayPal checkout
     */
    public function iCompleteThePayPalCheckout(): void
    {
        try {
            $paymentPage = $this->pageFactory->getPage('PaymentPage');
            $paymentPage->completePayPalCheckout();

            $this->stateService->set('payment.paypal_completed', true);
            $this->logInfo("Completed PayPal checkout");
        } catch (\Throwable $e) {
            $this->logError("Failed to complete PayPal checkout", $e);
            throw $e;
        }
    }



    /**
     * @When I handle 3D Secure authentication
     */
    public function iHandle3DSecureAuthentication(): void
    {
        try {
            $paymentPage = $this->pageFactory->getPage('PaymentPage');
            $paymentPage->handle3DSecureAuthentication();

            $this->stateService->set('payment.3ds_completed', true);
            $this->logInfo("Completed 3D Secure authentication");
        } catch (\Throwable $e) {
            $this->logError("Failed to complete 3D Secure authentication", $e);
            throw $e;
        }
    }

    /**
     * @When /^I select the "([^"]*)" payment method$/
     */
    public function iSelectThePaymentMethod(string $method): void
    {
        try {
            // Reuse the existing method for consistency
            $this->iSelectPaymentMethod($method);
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to select payment method: %s", $method), $e);
            throw $e;
        }
    }

    /**
     * @When /^I log in to PayPal with "([^"]*)" credentials$/
     */
    public function iLogInToPayPalWithCredentials(string $credentialType): void
    {
        try {
            // Load PayPal credentials
            $paypalCredentials = $this->getPayPalCredentials($credentialType);

            // Get PaymentPage and log in to PayPal
            $paymentPage = $this->pageFactory->getPage('PaymentPage');
            $paymentPage->loginToPayPal($paypalCredentials);

            // Set state
            $this->stateService->set('payment.paypal_login_type', $credentialType);
            $this->stateService->set('payment.paypal_logged_in', true);

            $this->logInfo(sprintf("Logged in to PayPal with %s credentials", $credentialType));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to log in to PayPal with %s credentials", $credentialType), $e);
            throw $e;
        }
    }

    /**
     * @When /^I choose to pay with PayPal$/
     */
    public function iChooseToPayWithPayPal(): void
    {
        try {
            $checkoutPage = $this->pageFactory->getPage('CheckoutPage');
            $paypalPage = $this->pageFactory->getPage('PayPalPage');

            // Select PayPal as payment method
            $checkoutPage->choosePayPalPayment();

            // Wait for redirect to PayPal
            $paypalPage->waitForRedirectToPayPal();

            // Set state
            $this->stateService->set('payment.method', 'paypal');

            $this->logInfo("Selected PayPal as payment method");
        } catch (\Throwable $e) {
            $this->logError("Failed to select PayPal as payment method", $e);
            throw $e;
        }
    }

    /**
     * @When /^I am redirected to the PayPal sandbox page$/
     */
    public function iAmRedirectedToThePayPalSandboxPage(): void
    {
        try {
            $paypalPage = $this->pageFactory->getPage('PayPalPage');

            // Wait for redirect to PayPal
            $paypalPage->waitForRedirectToPayPal();

            // Set state
            $this->stateService->set('payment.on_paypal_page', true);

            $this->logInfo("Redirected to PayPal sandbox page");
        } catch (\Throwable $e) {
            $this->logError("Failed to detect redirect to PayPal sandbox page", $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see the correct payment amount in PayPal$/
     */
    public function iShouldSeeTheCorrectPaymentAmountInPayPal(): void
    {
        try {
            // Get expected amount from state
            $expectedAmount = $this->stateService->get('orderTotal');
            if (empty($expectedAmount)) {
                throw new \RuntimeException('Order total not found in state');
            }

            // Get PayPal page and verify amount
            $paypalPage = $this->pageFactory->getPage('PayPalPage');
            $actualAmount = $paypalPage->getDisplayedAmount();

            // Verify amounts match
            if ($expectedAmount !== $actualAmount) {
                throw new \RuntimeException(sprintf(
                    'Expected PayPal amount %s, but found %s',
                    $expectedAmount,
                    $actualAmount
                ));
            }

            $this->logInfo(sprintf("Verified correct PayPal payment amount: %s", $actualAmount));
        } catch (\Throwable $e) {
            $this->logError("Failed to verify PayPal payment amount", $e);
            throw $e;
        }
    }

    /**
     * @When /^I confirm the PayPal payment$/
     */
    public function iConfirmThePayPalPayment(): void
    {
        try {
            $paypalPage = $this->pageFactory->getPage('PayPalPage');

            // Confirm payment in PayPal
            $paypalPage->confirmPayment();

            // Set state
            $this->stateService->set('payment.paypal_confirmed', true);

            $this->logInfo("Confirmed PayPal payment");
        } catch (\Throwable $e) {
            $this->logError("Failed to confirm PayPal payment", $e);
            throw $e;
        }
    }

    /**
     * @Then /^the PayPal payment should be successful$/
     */
    public function thePayPalPaymentShouldBeSuccessful(): void
    {
        try {
            // Get the base URL from the configuration service
            $baseUrl = $this->getConfigService()->getBrandConfig('base_url');

            // Wait for redirect back to the merchant site
            $this->getBrowserService()->waitForUrlContains($baseUrl);

            // Get checkout page and verify payment success
            $checkoutPage = $this->pageFactory->getPage('CheckoutPage');
            $success = $checkoutPage->verifyPayPalPaymentSuccess();

            if (!$success) {
                throw new \RuntimeException('PayPal payment was not successful');
            }

            // Set state
            $this->stateService->set('payment.successful', true);

            $this->logInfo("Verified successful PayPal payment");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify successful PayPal payment", $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see a PayPal login error message$/
     */
    public function iShouldSeeAPayPalLoginErrorMessage(): void
    {
        try {
            $paypalPage = $this->pageFactory->getPage('PayPalPage');

            // Verify PayPal login error
            $hasError = $paypalPage->hasLoginError();

            if (!$hasError) {
                throw new \RuntimeException('PayPal login error message not found');
            }

            // Set state
            $this->stateService->set('payment.paypal_login_error', true);

            $this->logInfo("Verified PayPal login error message displayed");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify PayPal login error message", $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see the PayPal login error$/
     */
    public function iShouldSeeThePayPalLoginError(): void
    {
        // Reuse the existing method for consistency
        $this->iShouldSeeAPayPalLoginErrorMessage();
    }

    /**
     * @When /^I log in to PayPal sandbox with "([^"]*)" credentials$/
     */
    public function iLogInToPayPalSandboxWithCredentials(string $credentialType): void
    {
        try {
            // Map credential type to expected format
            $credentialKey = $credentialType === 'valid' ? 'paypal_sandbox' : 'paypal_invalid';

            // Load PayPal credentials
            $credentials = $this->getPayPalCredentials($credentialKey);

            // Get PayPal page and login
            $paypalPage = $this->pageFactory->getPage('PayPalPage');
            $paypalPage->login($credentials['username'], $credentials['password']);

            // Set state
            $this->stateService->set('payment.paypal_login_type', $credentialType);
            $this->stateService->set('payment.paypal_logged_in', true);

            $this->logInfo(sprintf("Logged in to PayPal sandbox with %s credentials", $credentialType));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to log in to PayPal sandbox with %s credentials", $credentialType), $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see the 3DS page$/
     */
    public function iShouldSeeThe3DSPage(): void
    {
        try {
            $stripe3DSPage = $this->pageFactory->getPage('Stripe3DSPage');

            // Verify 3DS page is displayed
            $is3DSDisplayed = $stripe3DSPage->is3DSPageDisplayed();

            if (!$is3DSDisplayed) {
                throw new \RuntimeException('3DS page is not displayed');
            }

            // Set state
            $this->stateService->set('payment.on_3ds_page', true);

            $this->logInfo("Verified 3DS page is displayed");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify 3DS page is displayed", $e);
            throw $e;
        }
    }

    /**
     * @When /^I press the "([^"]*)" button$/
     */
    public function iPressTheButton(string $button): void
    {
        try {
            $stripe3DSPage = $this->pageFactory->getPage('Stripe3DSPage');

            // Press the appropriate button on the 3DS page
            switch (strtolower($button)) {
                case 'complete':
                    $stripe3DSPage->clickCompleteButton();
                    $this->stateService->set('payment.3ds_action', 'complete');
                    break;

                case 'fail':
                    $stripe3DSPage->clickFailButton();
                    $this->stateService->set('payment.3ds_action', 'fail');
                    break;

                default:
                    throw new \RuntimeException(sprintf('Unknown button "%s"', $button));
            }

            $this->logInfo(sprintf("Pressed %s button on 3DS page", $button));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to press %s button on 3DS page", $button), $e);
            throw $e;
        }
    }

    /**
     * @When /^I enter "([^"]*)" payment details$/
     */
    public function iEnterPaymentDetails(string $paymentType): void
    {
        try {
            // Get payment data based on payment type
            $paymentData = $this->getPaymentData($paymentType);

            // Get payment page and fill payment form
            $paymentPage = $this->pageFactory->getPage('PaymentPage');
            $paymentPage->fillPaymentForm($paymentData);

            // Store payment details in state
            $this->stateService->set('payment.details', $paymentData);
            $this->stateService->set('payment.type', $paymentType);

            $this->logInfo(sprintf("Entered %s payment details", $paymentType));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to enter %s payment details", $paymentType), $e);
            throw $e;
        }
    }

    /**
     * Get payment data for the specified type
     */
    private function getPaymentData(string $paymentType): array
    {
        $brand = $this->getConfigService()->getCurrentBrand();
        $paymentData = $this->dataService->loadTestData($brand, 'payment_methods');

        if (!isset($paymentData['types'][$paymentType])) {
            throw new \RuntimeException(sprintf('Payment data for type "%s" not found', $paymentType));
        }

        return $paymentData['types'][$paymentType];
    }

    /**
     * Get PayPal credentials for the specified type
     */
    private function getPayPalCredentials(string $credentialType): array
    {
        $brand = $this->getConfigService()->getCurrentBrand();
        $paymentData = $this->dataService->loadTestData($brand, 'payment_methods');

        if (!isset($paymentData['paypal'][$credentialType])) {
            throw new \RuntimeException(sprintf('PayPal credentials for type "%s" not found', $credentialType));
        }

        return $paymentData['paypal'][$credentialType];
    }
}
