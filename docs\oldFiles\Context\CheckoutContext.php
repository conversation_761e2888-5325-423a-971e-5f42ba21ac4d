<?php

namespace Features\Bootstrap\Context;

use Behat\Mink\Session;
use ElementNotFoundException;
use Features\Bootstrap\Core\DataValidator;
use Features\Bootstrap\Page\CheckoutPage;
use RuntimeException;
use Throwable;

/**
 * Manages checkout-related step definitions and functionality
 *
 * This context handles all checkout-related operations including:
 * - Shipping information management
 * - Billing address handling
 * - Payment processing
 * - Order completion
 */
class CheckoutContext extends BaseContext
{
    private CheckoutPage $checkoutPage;
    private DataValidator $dataValidator;
    private Session $session;

    /**
     * @param CheckoutPage $checkoutPage Checkout page object
     * @param DataValidator $dataValidator Validator for payment data
     * @param Session $session Mink session
     */
    public function __construct(
        CheckoutPage  $checkoutPage,
        DataValidator $dataValidator,
        Session       $session
    )
    {
        parent::__construct();
        $this->checkoutPage = $checkoutPage;
        $this->dataValidator = $dataValidator;
        $this->session = $session;
        $this->logInfo('CheckoutContext initialized');
    }

    /**
     * @When /^I fill in the shipping information with "([^"]*)" user data$/
     * @throws RuntimeException
     * @sets checkout.shipping_address
     */
    public function iFillInTheShippingInformationWith(string $userType = 'default'): void
    {
        try {
            $userData = $this->getUserData($userType);
            $this->checkoutPage->fillShippingInformation($userData);
            $this->validateCheckoutState();
            $this->stateService->set('checkout.shipping_address', $userData);
            $this->logInfo(sprintf('Filled shipping information with %s user data', $userType));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to fill shipping information with %s user data', $userType), $e);
            throw new RuntimeException(
                sprintf('Failed to fill shipping information: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError(sprintf('Error filling shipping information with %s user data', $userType), $e);
            throw $e;
        }
    }

    /**
     * @When /^I (use|choose|select) the same address for billing$/
     * @throws RuntimeException
     * @sets checkout.billing_address
     */
    public function iChooseToUseTheSameAddressForBilling(): void
    {
        try {
            $this->checkoutPage->useSameAddressForBilling();
            $this->validateCheckoutState();

            $shippingAddress = $this->stateService->get('checkout.shipping_address');
            if ($shippingAddress) {
                $this->stateService->set('checkout.billing_address', $shippingAddress);
            }

            $this->logInfo('Set billing address same as shipping address');
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to set same address for billing', $e);
            throw new RuntimeException(
                sprintf('Failed to set same address for billing: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Error setting same address for billing', $e);
            throw $e;
        }
    }

    /**
     * @When /^I select "([^"]*)" as the shipping method$/
     * @throws RuntimeException
     * @sets checkout.shipping_method
     */
    public function iSelectAsTheShippingMethod(string $method): void
    {
        try {
            $this->checkoutPage->selectShippingMethod($method);
            $this->validateCheckoutState();
            $this->stateService->set('checkout.shipping_method', $method);
            $this->logInfo(sprintf('Selected shipping method: %s', $method));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to select shipping method: %s', $method), $e);
            throw new RuntimeException(
                sprintf('Failed to select shipping method: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError(sprintf('Error selecting shipping method: %s', $method), $e);
            throw $e;
        }
    }

    /**
     * @Then /^The shipping cost is displayed$/
     * @throws RuntimeException
     */
    public function theShippingCostIsDisplayed(): void
    {
        try {
            $this->checkoutPage->verifyShippingCostDisplayed();
            $this->logInfo('Verified shipping cost is displayed');
        } catch (ElementNotFoundException $e) {
            $this->logError('Shipping cost not displayed', $e);
            throw new RuntimeException(
                sprintf('Shipping cost not displayed: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Error verifying shipping cost display', $e);
            throw $e;
        }
    }

    /**
     * @When /^I enter "([^"]*)" payment details$/
     * @throws RuntimeException
     * @sets checkout.payment_method, checkout.payment_data
     */
    public function iEnterThePaymentDetails(string $paymentType): void
    {
        try {
            $paymentData = $this->getPaymentData($paymentType);
            $this->validatePaymentData($paymentData);
            $this->checkoutPage->enterPaymentDetails($paymentData);
            $this->validateCheckoutState();

            $this->stateService->set('checkout.payment_data', $paymentData);
            $this->stateService->set('checkout.payment_method', $paymentType);

            $this->logInfo(sprintf('Entered %s payment details', $paymentType));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to enter %s payment details', $paymentType), $e);
            throw new RuntimeException(
                sprintf('Failed to enter payment details: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError(sprintf('Error entering %s payment details', $paymentType), $e);
            throw $e;
        }
    }

    /**
     * @When /^I complete the purchase$/
     * @throws RuntimeException
     */
    public function iCompleteThePurchase(): void
    {
        try {
            $this->checkoutPage->completePurchase();
            $this->validateCheckoutState();
            $this->logInfo('Completed purchase');
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to complete purchase', $e);
            throw new RuntimeException(
                sprintf('Failed to complete purchase: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Error completing purchase', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I verify the shipping cost$/
     * @throws RuntimeException
     */
    public function iVerifyTheShippingCostIs(): void
    {
        try {
            $this->checkoutPage->verifyShippingCost();
            $this->logInfo('Verified shipping cost');
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to verify shipping cost', $e);
            throw new RuntimeException(
                sprintf('Failed to verify shipping cost: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Error verifying shipping cost', $e);
            throw $e;
        }
    }

    /**
     * @When /^I choose to pay with PayPal$/
     * @throws RuntimeException
     * @sets checkout.payment_method
     */
    public function iChooseToPayWithPayPal(): void
    {
        try {
            $this->checkoutPage->selectPaymentMethod('PayPal');
            $this->validateCheckoutState();
            $this->stateService->set('checkout.payment_method', 'PayPal');
            $this->logInfo('Selected PayPal as payment method');
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to select PayPal payment method', $e);
            throw new RuntimeException(
                sprintf('Failed to select PayPal payment method: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Error selecting PayPal payment method', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I am redirected to the PayPal sandbox page$/
     * @throws RuntimeException
     */
    public function iAmRedirectedToThePayPalSandboxPage(): void
    {
        try {
            // Wait for PayPal redirect
            $this->session->wait(10000, "return window.location.href.includes('paypal.com');");
            $currentUrl = $this->session->getCurrentUrl();

            if (!str_contains($currentUrl, 'paypal.com')) {
                throw new RuntimeException('Not redirected to PayPal sandbox page');
            }

            // Wait for PayPal page to load
            $this->session->wait(5000, "return document.readyState === 'complete';");

            $this->logInfo('Redirected to PayPal sandbox page');
        } catch (Throwable $e) {
            $this->logError('Failed to verify PayPal sandbox redirection', $e);
            throw new RuntimeException(
                sprintf('Failed to verify PayPal sandbox redirection: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I log in to PayPal sandbox with "([^"]*)" credentials$/
     * @throws RuntimeException
     */
    public function iLogInToPayPalSandboxWithCredentials(string $credentialType): void
    {
        try {
            // Wait for PayPal login form to load
            $this->session->wait(5000, "return document.readyState === 'complete';");

            // Get PayPal credentials from shared data or configuration
            $paypalCredentials = $this->getPayPalCredentials($credentialType);

            // Fill in email
            $emailField = $this->session->getPage()->find('css', 'input#email');
            if (!$emailField) {
                throw new ElementNotFoundException('PayPal email field not found');
            }
            $emailField->setValue($paypalCredentials['email']);

            // Submit email
            $nextButton = $this->session->getPage()->find('css', 'button#btnNext');
            if (!$nextButton) {
                throw new ElementNotFoundException('PayPal next button not found');
            }
            $nextButton->click();

            // Wait for password field to be visible
            $this->session->wait(5000, "return !!document.querySelector('input#password');");

            // Fill in password
            $passwordField = $this->session->getPage()->find('css', 'input#password');
            if (!$passwordField) {
                throw new ElementNotFoundException('PayPal password field not found');
            }
            $passwordField->setValue($paypalCredentials['password']);

            // Submit login form
            $loginButton = $this->session->getPage()->find('css', 'button#btnLogin');
            if (!$loginButton) {
                throw new ElementNotFoundException('PayPal login button not found');
            }
            $loginButton->click();

            // Wait for successful login
            $this->session->wait(5000, "return document.readyState === 'complete';");

            $this->logInfo(sprintf('Logged in to PayPal sandbox with %s credentials', $credentialType));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to log in to PayPal sandbox with %s credentials', $credentialType), $e);
            throw new RuntimeException(
                sprintf('Failed to log in to PayPal sandbox: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError(sprintf('Error logging in to PayPal sandbox with %s credentials', $credentialType), $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see the correct payment amount in PayPal$/
     * @throws RuntimeException
     */
    public function iShouldSeeTheCorrectPaymentAmountInPayPal(): void
    {
        try {
            // Wait for PayPal summary page to load
            $this->session->wait(5000, "return document.readyState === 'complete';");

            $orderTotal = $this->stateService->get('order.total');
            if (!$orderTotal) {
                throw new RuntimeException('Order total not found in shared context');
            }

            // Check for amount on PayPal page
            $paymentAmount = $this->session->getPage()->find('css', '.transactionAmount');
            if (!$paymentAmount) {
                throw new RuntimeException('Payment amount element not found on PayPal page');
            }

            $displayedAmount = $paymentAmount->getText();
            if (!str_contains($displayedAmount, $orderTotal)) {
                throw new RuntimeException(
                    sprintf('Expected amount %s, but found %s', $orderTotal, $displayedAmount)
                );
            }

            $this->logInfo(sprintf('Verified correct payment amount in PayPal: %s', $orderTotal));
        } catch (Throwable $e) {
            $this->logError('Failed to verify PayPal payment amount', $e);
            throw new RuntimeException(
                sprintf('Failed to verify PayPal payment amount: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I confirm the PayPal payment$/
     * @throws RuntimeException
     */
    public function iConfirmThePayPalPayment(): void
    {
        try {
            // Wait for PayPal page to load
            $this->session->wait(5000, "return document.readyState === 'complete';");

            // Look for continue/confirm button
            $continueButton = $this->session->getPage()->find('css', 'button#payment-submit-btn');
            if (!$continueButton) {
                // Try alternative selectors if the main one fails
                $continueButton = $this->session->getPage()->find('css', 'button.continueButton');
            }

            if (!$continueButton) {
                throw new ElementNotFoundException('PayPal continue/confirm button not found');
            }

            $continueButton->click();

            // Wait for processing
            $this->session->wait(10000, "return document.readyState === 'complete';");

            $this->logInfo('Confirmed PayPal payment');
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to confirm PayPal payment', $e);
            throw new RuntimeException(
                sprintf('Failed to confirm PayPal payment: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Error confirming PayPal payment', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should be redirected back to the merchant site$/
     * @throws RuntimeException
     */
    public function iShouldBeRedirectedBackToTheMerchantSite(): void
    {
        try {
            // Wait for redirect back to the store
            $baseUrl = $this->getBaseUrl();
            $this->session->wait(10000, "return window.location.href.includes('" . $baseUrl . "');");

            $currentUrl = $this->session->getCurrentUrl();
            if (!str_contains($currentUrl, $baseUrl)) {
                throw new RuntimeException('Not redirected back to the merchant site');
            }

            // Wait for page to load
            $this->session->wait(5000, "return document.readyState === 'complete';");

            $this->logInfo('Redirected back to merchant site');
        } catch (Throwable $e) {
            $this->logError('Failed to verify redirection back to merchant site', $e);
            throw new RuntimeException(
                sprintf('Failed to verify redirection back to merchant site: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^the PayPal payment should be successful$/
     * @throws RuntimeException
     */
    public function thePayPalPaymentShouldBeSuccessful(): void
    {
        try {
            // Check for success indicators on the page
            if (!$this->session->getPage()->hasContent('Congratulations') &&
                !$this->session->getPage()->hasContent('Order Is Complete')) {
                throw new RuntimeException('PayPal payment was not successful');
            }

            $this->logInfo('Verified PayPal payment was successful');
        } catch (Throwable $e) {
            $this->logError('Failed to verify successful PayPal payment', $e);
            throw new RuntimeException(
                sprintf('Failed to verify successful PayPal payment: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^The shipping method "([^"]*)" should be selected$/
     * @throws RuntimeException
     */
    public function theShippingMethodShouldBeSelected(string $expectedMethod): void
    {
        try {
            $this->checkoutPage->verifySelectedShippingMethod($expectedMethod);
            $this->logInfo(sprintf('Verified shipping method "%s" is selected', $expectedMethod));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to verify shipping method "%s"', $expectedMethod), $e);
            throw new RuntimeException(
                sprintf('Failed to verify shipping method: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError(sprintf('Error verifying shipping method "%s"', $expectedMethod), $e);
            throw $e;
        }
    }

    /**
     * @Then /^The shipping method "([^"]*)" should be selected with "([^"]*)" shipping$/
     * @throws RuntimeException
     */
    public function theShippingMethodShouldBeSelectedWithShipping(string $expectedMethod, string $costLabel): void
    {
        try {
            $this->checkoutPage->verifySelectedShippingMethod($expectedMethod);
            $this->checkoutPage->verifyShippingCostLabel($costLabel);
            $this->logInfo(sprintf('Verified shipping method "%s" with cost label "%s"', $expectedMethod, $costLabel));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to verify shipping method "%s" or cost label "%s"', $expectedMethod, $costLabel), $e);
            throw new RuntimeException(
                sprintf('Failed to verify shipping method or cost: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError(sprintf('Error verifying shipping method "%s" with cost label "%s"', $expectedMethod, $costLabel), $e);
            throw $e;
        }
    }

    /**
     * Get base URL for the current brand
     *
     * @return string Base URL for the current brand/environment
     * @throws RuntimeException When base URL not available
     */
    private function getBaseUrl(): string
    {
        try {
            if ($this->contextManager !== null) {
                $brandContext = $this->contextManager->getContext(BrandContext::class);
                return $brandContext->getBaseUrl();
            }

            $baseUrl = getenv('TEST_BASE_URL');
            if (!$baseUrl) {
                throw new RuntimeException('TEST_BASE_URL not set');
            }

            return $baseUrl;
        } catch (Throwable $e) {
            $this->logError('Failed to get base URL', $e);
            throw new RuntimeException(
                sprintf('Failed to get base URL: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Validates the current checkout state
     *
     * @throws RuntimeException When checkout state is invalid
     */
    private function validateCheckoutState(): void
    {
        try {
            $this->checkoutPage->validateState();
        } catch (ElementNotFoundException $e) {
            $this->logError('Invalid checkout state', $e);
            throw new RuntimeException(
                sprintf('Invalid checkout state: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Error validating checkout state', $e);
            throw $e;
        }
    }

    /**
     * Gets user data from shared data
     *
     * @param string $userType Type of user (default, guest, etc.)
     * @return array User data
     * @throws RuntimeException When user data not found
     */
    private function getUserData(string $userType): array
    {
        try {
            $userData = $this->stateService->get("user.data.$userType");
            if (!$userData) {
                throw new RuntimeException("User data not found for type: $userType");
            }
            return $userData;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get user data for type "%s"', $userType), $e);
            throw new RuntimeException(
                sprintf('Failed to get user data for type "%s": %s', $userType, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets payment data from shared data
     *
     * @param string $paymentType Type of payment (credit_card, paypal, etc.)
     * @return array Payment data
     * @throws RuntimeException When payment data not found
     */
    private function getPaymentData(string $paymentType): array
    {
        try {
            $paymentData = $this->stateService->get("payment.data.$paymentType");
            if (!$paymentData) {
                throw new RuntimeException("Payment data not found for type: $paymentType");
            }
            return $paymentData;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get payment data for type "%s"', $paymentType), $e);
            throw new RuntimeException(
                sprintf('Failed to get payment data for type "%s": %s', $paymentType, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Validates payment data structure
     *
     * @param array $paymentData Payment data to validate
     * @throws RuntimeException When payment data is invalid
     */
    private function validatePaymentData(array $paymentData): void
    {
        try {
            if (!$this->dataValidator->validatePaymentData($paymentData)) {
                throw new RuntimeException('Invalid payment data provided');
            }
        } catch (Throwable $e) {
            $this->logError('Failed to validate payment data', $e);
            throw new RuntimeException(
                sprintf('Failed to validate payment data: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Get PayPal credentials for testing
     *
     * @param string $type Credential type (valid, invalid)
     * @return array PayPal credentials (email, password)
     * @throws RuntimeException When credential type not recognized
     */
    private function getPayPalCredentials(string $type): array
    {
        try {
            if ($type === 'valid') {
                return [
                    'email' => getenv('PAYPAL_TEST_EMAIL') ?: '<EMAIL>',
                    'password' => getenv('PAYPAL_TEST_PASSWORD') ?: 'test_password'
                ];
            } elseif ($type === 'invalid') {
                return [
                    'email' => '<EMAIL>',
                    'password' => 'invalid_password'
                ];
            } else {
                throw new RuntimeException(sprintf('Unknown PayPal credential type: %s', $type));
            }
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get PayPal credentials for type "%s"', $type), $e);
            throw new RuntimeException(
                sprintf('Failed to get PayPal credentials for type "%s": %s', $type, $e->getMessage()),
                0,
                $e
            );
        }
    }

    // Steps not currently used in feature files
    /**
     * @Then /^I verify the product details and pricing are correct$/
     */
    public function iVerifyTheProductDetailsAndPricingAreCorrect(): void
    {
        // Implementation preserved for future use
    }

    /**
     * @Then /^I should see an error message indicating the card has expired$/
     */
    public function iShouldSeeAnErrorMessageIndicatingTheCardHasExpired(): void
    {
        // Implementation preserved for future use
    }

    /**
     * @Then /^I verify the order total is correct$/
     */
    public function iVerifyTheOrderTotalIsCorrect(): void
    {
        // Implementation preserved for future use
    }

    /**
     * @Then /^I should remain on checkout page$/
     */
    public function iShouldRemainOnCheckoutPage(): void
    {
        // Implementation preserved for future use
    }

    /**
     * @Then /^I leave the checkout page$/
     */
    public function iLeaveTheCheckoutPage(): void
    {
        // Implementation preserved for future use
    }

    /**
     * @Then /^I should be on the checkout page$/
     */
    public function iShouldBeOnTheCheckoutPage(): void
    {
        // Implementation preserved for future use
    }

    /**
     * @Then /^I should see that the coupon is applied$/
     */
    public function iShouldSeeThatTheCouponIsApplied(): void
    {
        // Implementation preserved for future use
    }
}
