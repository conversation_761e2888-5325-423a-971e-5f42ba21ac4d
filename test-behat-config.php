<?php
/**
 * Test script for Behat test configuration
 * 
 * This script demonstrates how to use ConfigurationManager and TestDataRegistry
 * in a Behat test context.
 */
require __DIR__ . '/vendor/autoload.php';
use Features\Bootstrap\Core\ConfigurationManager;
use Features\Bootstrap\Core\TestDataRegistry;
use Features\Bootstrap\Core\DataValidator;

function printSection($title) {
    echo str_repeat("=", 80) . PHP_EOL;
    echo "  " . strtoupper($title) . PHP_EOL;
    echo str_repeat("=", 80) . PHP_EOL;
}

// Test 1: Test ConfigurationManager
try {
    printSection("Testing Configuration Manager");
    
    // Initialize ConfigurationManager with brand and environment
    $confManager = new ConfigurationManager(__DIR__ . '/config', 'aeons', 'stage');
    
    // Print configuration info
    echo "Brand: " . $confManager->getCurrentBrand() . PHP_EOL;
    echo "Environment: " . $confManager->getCurrentEnvironment() . PHP_EOL;
    echo "Base URL: " . $confManager->getEnvironmentConfig('base_url') . PHP_EOL;
    
    // Get other config values
    echo PHP_EOL . "Brand configuration values:" . PHP_EOL;
    try {
        $brandName = $confManager->getBrandConfig('name');
        echo "- Brand Name: " . $brandName . PHP_EOL;
    } catch (Exception $e) {
        echo "- Brand Name: Not available (" . $e->getMessage() . ")" . PHP_EOL;
    }
    
    try {
        $currency = $confManager->getBrandConfig('currency');
        echo "- Currency: " . $currency . PHP_EOL;
    } catch (Exception $e) {
        echo "- Currency: Not available (" . $e->getMessage() . ")" . PHP_EOL;
    }
    
    echo PHP_EOL . "Environment configuration values:" . PHP_EOL;
    try {
        $apiKey = $confManager->getEnvironmentConfig('api_key');
        echo "- API Key: " . $apiKey . PHP_EOL;
    } catch (Exception $e) {
        echo "- API Key: Not available (" . $e->getMessage() . ")" . PHP_EOL;
    }
    
    echo "Configuration Manager test completed successfully" . PHP_EOL;
} catch (Exception $e) {
    echo "Configuration Manager test failed: " . $e->getMessage() . PHP_EOL;
}

// Test 2: Test TestDataRegistry
try {
    printSection("Testing TestDataRegistry");
    
    // Initialize TestDataRegistry with validator and fixtures path
    $validator = new DataValidator();
    $dataRegistry = new TestDataRegistry($validator, __DIR__ . '/features/fixtures', $confManager);
    
    // Load product data
    echo "Loading product data for 'total_harmony'..." . PHP_EOL;
    $productData = $dataRegistry->loadTestData('aeons', 'products', 'total_harmony');
    
    // Print product info
    echo PHP_EOL . "Product information:" . PHP_EOL;
    echo "- Name: " . $productData['name'] . PHP_EOL;
    echo "- Slug: " . $productData['slug'] . PHP_EOL;
    echo "- Description: " . $productData['description'] . PHP_EOL;
    
    // Print pricing info
    echo PHP_EOL . "Pricing information:" . PHP_EOL;
    echo "- One-time minimum price: $" . $productData['prices']['one_time']['minimum'] . PHP_EOL;
    echo "- Subscription minimum price: $" . $productData['prices']['subscription']['minimum'] . PHP_EOL;
    
    // Print options info
    echo PHP_EOL . "Purchase options:" . PHP_EOL;
    echo "- One-time purchase: " . ($productData['options']['purchase_types']['one_time']['enabled'] ? "Enabled" : "Disabled") . PHP_EOL;
    echo "- Subscription: " . ($productData['options']['purchase_types']['subscription']['enabled'] ? "Enabled" : "Disabled") . PHP_EOL;
    if ($productData['options']['purchase_types']['subscription']['enabled']) {
        echo "  - Discount: " . $productData['options']['purchase_types']['subscription']['discount_percentage'] . "%" . PHP_EOL;
    }
    
    // Print quantity options
    echo PHP_EOL . "Quantity options:" . PHP_EOL;
    foreach ($productData['options']['quantities'] as $key => $quantity) {
        echo "- " . $key . ": " . $quantity['fullName'] . " (" . $quantity['numberOfItems'] . " items)" . PHP_EOL;
    }
    
    echo PHP_EOL . "Test Data Registry test completed successfully" . PHP_EOL;
} catch (Exception $e) {
    echo "Test Data Registry test failed: " . $e->getMessage() . PHP_EOL;
}

// Test 3: Demonstrate typical usage in a Behat context
printSection("Typical Usage in Behat Context");

echo <<<EOT
// Example implementation in a Behat context class:

class ProductContext extends BaseContext
{
    private ConfigurationManager \$configManager;
    private TestDataRegistry \$testDataRegistry;
    private array \$currentProduct;
    
    public function __construct(ConfigurationManager \$configManager, TestDataRegistry \$testDataRegistry)
    {
        \$this->configManager = \$configManager;
        \$this->testDataRegistry = \$testDataRegistry;
    }
    
    /**
     * @Given I load product :productKey
     */
    public function iLoadProduct(string \$productKey): void
    {
        \$brand = \$this->configManager->getCurrentBrand();
        \$this->currentProduct = \$this->testDataRegistry->loadTestData(\$brand, 'products', \$productKey);
    }
    
    /**
     * @When I view the product page
     */
    public function iViewTheProductPage(): void
    {
        \$baseUrl = \$this->configManager->getEnvironmentConfig('base_url');
        \$productUrl = \$baseUrl . '/products/' . \$this->currentProduct['slug'];
        
        // Navigate to the product page
        \$this->getSession()->visit(\$productUrl);
    }
    
    /**
     * @Then I should see the product name :name
     */
    public function iShouldSeeTheProductName(string \$name): void
    {
        \$page = \$this->getSession()->getPage();
        \$element = \$page->find('css', '.product-name');
        
        if (!\$element) {
            throw new Exception('Product name element not found');
        }
        
        \$actualName = \$element->getText();
        if (\$actualName !== \$name) {
            throw new Exception(
                sprintf('Expected product name "%s", got "%s"', \$name, \$actualName)
            );
        }
    }
}
EOT;

echo PHP_EOL . PHP_EOL;
echo "Script execution completed successfully" . PHP_EOL; 