# Phase 4: Page Object Migration

## Service-Oriented Test Architecture Migration

**Version:** 1.0
**Last Updated:** 2025-05-15
**Author:** AI Assistant

## Table of Contents

1. [Overview](#1-overview)
2. [Current Page Object Analysis](#2-current-page-object-analysis)
3. [Base Page Interface and Implementation](#3-base-page-interface-and-implementation)
4. [Page Factory Service](#4-page-factory-service)
5. [Home Page Migration](#5-home-page-migration)
6. [Product Page Migration](#6-product-page-migration)
7. [Cart Page Migration](#7-cart-page-migration)
8. [Checkout Page Migration](#8-checkout-page-migration)
9. [Other Page Objects Migration](#9-other-page-objects-migration)
10. [Implementation Plan](#10-implementation-plan)
11. [Testing Strategy](#11-testing-strategy)
12. [Rollback Plan](#12-rollback-plan)

---

## 1. Overview

Phase 4 focuses on migrating the existing Page Objects to use the new service-oriented architecture. This phase builds
upon the foundation established in Phase 1, the core services implemented in Phase 2, and the context migration
completed in Phase 3. The goal is to replace direct Mink dependencies with the BrowserService and implement a consistent
page object pattern.

### 1.1 Objectives

- Create a new base page interface and implementation
- Implement a page factory service for page object management
- Migrate all existing page objects to use the BrowserService
- Ensure backward compatibility with existing contexts
- Provide comprehensive tests for each page object

### 1.2 Timeline

- Estimated duration: 1 week
- Dependencies: Phase 1 (Foundation Setup), Phase 2 (Core Services Implementation), Phase 3 (Context Migration)

## 2. Current Page Object Analysis

Before migrating the page objects, we need to understand the current implementation to ensure proper migration and
backward compatibility.

### 2.1 Page Object Hierarchy

The current page object hierarchy is as follows:

```
Page (from SensioLabs\Behat\PageObjectExtension)
└── BasePage
    ├── HomePage
    ├── ProductPage
    ├── CartPage
    ├── CheckoutPage
    ├── PaymentPage
    ├── ConfirmationPage
    ├── UpsellPage
    └── Other specialized pages
```

### 2.2 BasePage Analysis

The current BasePage provides:

- Common functionality for all page objects
- Direct access to Mink Session
- Methods for element interaction (click, fill, etc.)
- Methods for waiting and navigation
- URL handling

Key issues:

- Direct dependency on Mink Session
- No service container access
- Limited error handling
- Inconsistent waiting strategies
- Duplicated browser interaction code

### 2.3 Page Object Dependencies

The current page objects have the following dependencies:

| Page Object      | Dependencies                                  |
|------------------|-----------------------------------------------|
| BasePage         | Mink Session, Page (from PageObjectExtension) |
| HomePage         | BasePage                                      |
| ProductPage      | BasePage, product data                        |
| CartPage         | BasePage, product data                        |
| CheckoutPage     | BasePage, user data, shipping data            |
| PaymentPage      | BasePage, payment data                        |
| ConfirmationPage | BasePage, order data                          |
| UpsellPage       | BasePage, product data                        |

### 2.4 Page Object Usage

The current page objects are used in contexts through:

- Direct instantiation in some cases
- PageObjectExtension's getPage() method in others
- Inconsistent initialization patterns

## 3. Base Page Interface and Implementation

### 3.1 Base Page Interface

Create a new BasePageInterface that defines the contract for all page objects.

#### 3.1.1 Interface Definition

```php
namespace App\Page\Base;

interface BasePageInterface
{
    /**
     * Open the page
     *
     * @param array $urlParameters Parameters to include in the URL
     * @return void
     */
    public function open(array $urlParameters = []): void;

    /**
     * Get the URL of the page
     *
     * @param array $urlParameters Parameters to include in the URL
     * @return string
     */
    public function getUrl(array $urlParameters = []): string;

    /**
     * Check if the page is open
     *
     * @return bool
     */
    public function isOpen(): bool;

    /**
     * Wait for the page to load
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForPageToLoad(int $timeout = 30): void;

    /**
     * Get the page title
     *
     * @return string
     */
    public function getTitle(): string;

    /**
     * Take a screenshot of the page
     *
     * @param string|null $name Name for the screenshot
     * @return string Path to the screenshot
     */
    public function takeScreenshot(?string $name = null): string;
}
```

### 3.2 Base Page Implementation

Create a new BasePage class that implements the BasePageInterface and uses the BrowserService.

#### 3.2.1 Class Structure

```php
namespace App\Page\Base;

use App\Service\Browser\BrowserServiceInterface;
use RuntimeException;

abstract class BasePage implements BasePageInterface
{
    protected BrowserServiceInterface $browserService;
    protected string $path = '/';
    protected string $baseUrl;

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(BrowserServiceInterface $browserService, ?string $baseUrl = null)
    {
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    /**
     * {@inheritdoc}
     */
    public function open(array $urlParameters = []): void
    {
        $url = $this->getUrl($urlParameters);
        $this->browserService->visit($url);
        $this->waitForPageToLoad();
    }

    /**
     * {@inheritdoc}
     */
    public function getUrl(array $urlParameters = []): string
    {
        $path = $this->path;

        // Replace path parameters
        foreach ($urlParameters as $key => $value) {
            $path = str_replace(sprintf('{%s}', $key), $value, $path);
        }

        return $this->baseUrl . $path;
    }

    /**
     * {@inheritdoc}
     */
    public function isOpen(): bool
    {
        try {
            $this->verifyPage();
            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function waitForPageToLoad(int $timeout = 30): void
    {
        $this->browserService->waitForDocumentReady($timeout);
    }

    /**
     * {@inheritdoc}
     */
    public function getTitle(): string
    {
        return $this->browserService->getSession()->getPage()->find('css', 'title')->getText();
    }

    /**
     * {@inheritdoc}
     */
    public function takeScreenshot(?string $name = null): string
    {
        return $this->browserService->takeScreenshot($name);
    }

    /**
     * Verify that we're on the expected page
     *
     * @throws RuntimeException When verification fails
     */
    protected function verifyPage(): void
    {
        // To be implemented by child classes
    }

    /**
     * Click on an element
     *
     * @param string $selector Element selector
     * @return void
     */
    protected function clickElement(string $selector): void
    {
        $this->browserService->clickElement($selector);
    }

    /**
     * Fill a field with a value
     *
     * @param string $selector Field selector
     * @param string $value Value to fill
     * @return void
     */
    protected function fillField(string $selector, string $value): void
    {
        $this->browserService->fillField($selector, $value);
    }

    /**
     * Select an option from a select field
     *
     * @param string $selector Select field selector
     * @param string $value Option value to select
     * @return void
     */
    protected function selectOption(string $selector, string $value): void
    {
        $this->browserService->selectOption($selector, $value);
    }

    /**
     * Check if an element exists
     *
     * @param string $selector Element selector
     * @return bool
     */
    protected function elementExists(string $selector): bool
    {
        return $this->browserService->elementExists($selector);
    }

    /**
     * Get text from an element
     *
     * @param string $selector Element selector
     * @return string
     */
    protected function getElementText(string $selector): string
    {
        return $this->browserService->getElementText($selector);
    }

    /**
     * Wait for an element to be visible
     *
     * @param string $selector Element selector
     * @param int $timeout Timeout in seconds
     * @return void
     */
    protected function waitForElementVisible(string $selector, int $timeout = 30): void
    {
        $this->browserService->waitForElementVisible($selector, $timeout);
    }

    /**
     * Wait for AJAX requests to complete
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    protected function waitForAjaxToComplete(int $timeout = 30): void
    {
        $this->browserService->waitForAjaxToComplete($timeout);
    }
}
```

### 3.3 Backward Compatibility

To ensure backward compatibility with existing code:

- Create a LegacyPageAdapter that wraps the new page objects
- Implement the same interface as the old page objects
- Forward all method calls to the new page objects

```php
namespace App\Compatibility;

use App\Page\Base\BasePageInterface;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Session;
use RuntimeException;

/**
 * Adapter to provide backward compatibility with old page objects
 */
class LegacyPageAdapter
{
    private BasePageInterface $page;
    private Session $session;

    /**
     * Constructor
     *
     * @param BasePageInterface $page New page object
     * @param Session $session Mink session
     */
    public function __construct(BasePageInterface $page, Session $session)
    {
        $this->page = $page;
        $this->session = $session;
    }

    /**
     * Get the Mink session
     *
     * @return Session
     */
    public function getSession(): Session
    {
        return $this->session;
    }

    /**
     * Open the page
     *
     * @param array $urlParameters
     * @return void
     */
    public function open(array $urlParameters = []): void
    {
        $this->page->open($urlParameters);
    }

    /**
     * Get the URL of the page
     *
     * @param array $urlParameters
     * @return string
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->page->getUrl($urlParameters);
    }

    /**
     * Find an element on the page
     *
     * @param string $selector
     * @return NodeElement|null
     */
    public function findElement(string $selector): ?NodeElement
    {
        return $this->session->getPage()->find('css', $selector);
    }

    // Other methods from the old page objects...
}
```

## 4. Page Factory Service

### 4.1 Page Factory Interface

Create a new PageFactoryInterface that defines the contract for the page factory service.

#### 4.1.1 Interface Definition

```php
namespace App\Service\Page;

use App\Page\Base\BasePageInterface;

interface PageFactoryInterface
{
    /**
     * Create a page object
     *
     * @param string $pageClass Page class name
     * @param array $parameters Constructor parameters
     * @return BasePageInterface
     */
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface;

    /**
     * Get a page object by name
     *
     * @param string $pageName Page name (e.g., 'HomePage')
     * @param array $parameters Constructor parameters
     * @return BasePageInterface
     */
    public function getPage(string $pageName, array $parameters = []): BasePageInterface;

    /**
     * Check if a page exists
     *
     * @param string $pageName Page name
     * @return bool
     */
    public function hasPage(string $pageName): bool;
}
```

### 4.2 Page Factory Implementation

Create a new PageFactory class that implements the PageFactoryInterface.

#### 4.2.1 Class Structure

```php
namespace App\Service\Page;

use App\Page\Base\BasePageInterface;
use App\Service\AbstractService;
use App\Service\Browser\BrowserServiceInterface;
use RuntimeException;
use Symfony\Component\DependencyInjection\ContainerInterface;

class PageFactory extends AbstractService implements PageFactoryInterface
{
    private ContainerInterface $container;
    private BrowserServiceInterface $browserService;
    private string $baseUrl;
    private array $pageNamespaces = [
        'App\\Page\\',
        'Features\\Bootstrap\\Page\\' // For backward compatibility
    ];

    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     * @param BrowserServiceInterface $browserService Browser service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        ?string $baseUrl = null
    ) {
        $this->container = $container;
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    /**
     * {@inheritdoc}
     */
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface
    {
        if (!class_exists($pageClass)) {
            throw new RuntimeException(sprintf('Page class "%s" does not exist', $pageClass));
        }

        // Add default parameters
        $parameters = array_merge([
            'browserService' => $this->browserService,
            'baseUrl' => $this->baseUrl
        ], $parameters);

        // Create reflection class
        $reflectionClass = new \ReflectionClass($pageClass);

        // Check if the class implements BasePageInterface
        if (!$reflectionClass->implementsInterface(BasePageInterface::class)) {
            throw new RuntimeException(sprintf('Page class "%s" must implement BasePageInterface', $pageClass));
        }

        // Get constructor parameters
        $constructor = $reflectionClass->getConstructor();
        if (!$constructor) {
            throw new RuntimeException(sprintf('Page class "%s" has no constructor', $pageClass));
        }

        // Prepare constructor arguments
        $arguments = [];
        foreach ($constructor->getParameters() as $parameter) {
            $paramName = $parameter->getName();
            if (isset($parameters[$paramName])) {
                $arguments[] = $parameters[$paramName];
            } elseif ($parameter->isDefaultValueAvailable()) {
                $arguments[] = $parameter->getDefaultValue();
            } elseif ($parameter->getType() && !$parameter->getType()->isBuiltin() && $this->container->has($parameter->getType()->getName())) {
                $arguments[] = $this->container->get($parameter->getType()->getName());
            } else {
                throw new RuntimeException(sprintf('Cannot resolve parameter "%s" for page class "%s"', $paramName, $pageClass));
            }
        }

        // Create page object
        return $reflectionClass->newInstanceArgs($arguments);
    }

    /**
     * {@inheritdoc}
     */
    public function getPage(string $pageName, array $parameters = []): BasePageInterface
    {
        // Check if the page is registered as a service
        $serviceId = sprintf('app.page.%s', strtolower($pageName));
        if ($this->container->has($serviceId)) {
            return $this->container->get($serviceId);
        }

        // Try to find the page class
        $pageClass = $this->findPageClass($pageName);
        if (!$pageClass) {
            throw new RuntimeException(sprintf('Page "%s" not found', $pageName));
        }

        // Create the page object
        return $this->createPage($pageClass, $parameters);
    }

    /**
     * {@inheritdoc}
     */
    public function hasPage(string $pageName): bool
    {
        // Check if the page is registered as a service
        $serviceId = sprintf('app.page.%s', strtolower($pageName));
        if ($this->container->has($serviceId)) {
            return true;
        }

        // Try to find the page class
        return $this->findPageClass($pageName) !== null;
    }

    /**
     * Find the page class for a page name
     *
     * @param string $pageName Page name
     * @return string|null Page class or null if not found
     */
    private function findPageClass(string $pageName): ?string
    {
        // Add 'Page' suffix if not present
        if (!str_ends_with($pageName, 'Page')) {
            $pageName .= 'Page';
        }

        // Try each namespace
        foreach ($this->pageNamespaces as $namespace) {
            $pageClass = $namespace . $pageName;
            if (class_exists($pageClass)) {
                return $pageClass;
            }
        }

        return null;
    }
}
```

### 4.3 Service Configuration

Update the service container configuration to include the page factory service:

```yaml
# config/services/pages.yml
services:
  # Page Factory
  App\Service\Page\PageFactoryInterface:
    alias: App\Service\Page\PageFactory

  App\Service\Page\PageFactory:
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true

  # Legacy Page Factory (for backward compatibility)
  SensioLabs\Behat\PageObjectExtension\PageFactory\DefaultFactory:
    factory: [ '@App\Compatibility\LegacyPageFactoryAdapter', 'getInstance' ]
    public: true
```

### 4.4 Legacy Page Factory Adapter

Create a LegacyPageFactoryAdapter to provide backward compatibility with the PageObjectExtension:

```php
namespace App\Compatibility;

use App\Service\Page\PageFactoryInterface;
use Behat\Mink\Session;
use SensioLabs\Behat\PageObjectExtension\PageFactory\DefaultFactory;

/**
 * Adapter to provide backward compatibility with PageObjectExtension
 */
class LegacyPageFactoryAdapter
{
    private static ?DefaultFactory $instance = null;
    private PageFactoryInterface $pageFactory;
    private Session $session;

    /**
     * Constructor
     *
     * @param PageFactoryInterface $pageFactory Page factory service
     * @param Session $session Mink session
     */
    public function __construct(PageFactoryInterface $pageFactory, Session $session)
    {
        $this->pageFactory = $pageFactory;
        $this->session = $session;
    }

    /**
     * Get the singleton instance
     *
     * @return DefaultFactory
     */
    public function getInstance(): DefaultFactory
    {
        if (self::$instance === null) {
            // Create a new instance of the DefaultFactory
            self::$instance = new DefaultFactory();

            // Override the createPage method
            $pageFactory = $this->pageFactory;
            $session = $this->session;

            self::$instance->createPage = function ($pageClass) use ($pageFactory, $session) {
                try {
                    // Try to create the page using the new page factory
                    $page = $pageFactory->createPage($pageClass);

                    // Wrap it in a legacy adapter
                    return new LegacyPageAdapter($page, $session);
                } catch (\Throwable $e) {
                    // Fallback to the original implementation
                    return new $pageClass($session);
                }
            };
        }

        return self::$instance;
    }
}
```

## 5. Home Page Migration

### 5.1 Implementation Details

Migrate the HomePage to use the new architecture.

#### 5.1.1 Class Structure

```php
namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;
use RuntimeException;

class HomePage extends BasePage
{
    protected string $path = '/';

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible('a.btn[href="/range"]');
    }

    /**
     * Navigate to the product range page
     *
     * @return void
     */
    public function goToProductRange(): void
    {
        $this->clickElement('a.btn[href="/range"]');
        $this->waitForPageToLoad();
    }

    /**
     * Check if the user is logged in
     *
     * @return bool
     */
    public function isUserLoggedIn(): bool
    {
        return $this->elementExists('.user-account-menu');
    }

    /**
     * Get the featured products
     *
     * @return array List of featured products
     */
    public function getFeaturedProducts(): array
    {
        $products = [];
        $productElements = $this->browserService->findElements('.featured-product');

        foreach ($productElements as $element) {
            $products[] = [
                'name' => $element->find('css', '.product-name')->getText(),
                'price' => $element->find('css', '.product-price')->getText(),
                'url' => $element->find('css', 'a')->getAttribute('href')
            ];
        }

        return $products;
    }

    // Other methods specific to the home page...
}
```

### 5.2 Migration Tasks

1. Create `src/Page/HomePage.php`
2. Implement the `verifyPage` method
3. Migrate methods from the old HomePage
4. Update method signatures to use BrowserService instead of direct Mink access
5. Add new methods for common home page interactions

### 5.3 Service Registration

Register the HomePage as a service:

```yaml
# config/services/pages.yml
services:
  # Home Page
  App\Page\HomePage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'app.page' ]

  app.page.homepage:
    alias: App\Page\HomePage
    public: true
```

## 6. Product Page Migration

### 6.1 Implementation Details

Migrate the ProductPage to use the new architecture.

#### 6.1.1 Class Structure

```php
namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use RuntimeException;

class ProductPage extends BasePage
{
    protected string $path = '/product/{slug}';
    private TestDataServiceInterface $dataService;

    /**
     * Product name to URL slug mapping
     * @var array<string, string>
     */
    private array $productSlugs = [
        'Total Harmony' => 'aeons-total-harmony',
        // Add other products as needed
    ];

    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'QUANTITY' => '[data-value="1"]',
        'PURCHASE_TYPE' => '.ratio-title.product-variant-label-info',
        'PRODUCT_NAME' => '.title',
        'PRICE' => '#product-price',
        'RESTRICTIONS' => '.product-restrictions .warning',
        'FLAVOR' => '.flavor'
    ];

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     * @param TestDataServiceInterface $dataService Test data service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(
        BrowserServiceInterface $browserService,
        TestDataServiceInterface $dataService,
        ?string $baseUrl = null
    ) {
        parent::__construct($browserService, $baseUrl);
        $this->dataService = $dataService;
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible('#sylius-product-adding-to-cart');
    }

    /**
     * Loads the product page for a specific product.
     *
     * @param string $productName The actual product name (e.g., "Total Harmony")
     * @param ?array $productData Optional product data
     * @throws RuntimeException If product name is not recognized
     */
    public function loadWithName(string $productName, ?array $productData = null): void
    {
        if ($productData) {
            // Verify product name matches
            if ($productData['name'] !== $productName) {
                throw new RuntimeException(
                    sprintf('Product name mismatch. Expected: %s, Got: %s',
                        $productName,
                        $productData['name']
                    )
                );
            }
            $fullUrl = $this->baseUrl . '/' . trim($productData['url_path'], '/');
        } else {
            // Fallback to using slugs if no data provided
            if (!isset($this->productSlugs[$productName])) {
                throw new RuntimeException("Unknown product: $productName");
            }
            $fullUrl = $this->baseUrl . '/product/' . $this->productSlugs[$productName];
        }

        $this->browserService->visit($fullUrl);
        $this->waitForPageToLoad();
    }

    /**
     * Select a flavor
     *
     * @param string $flavor Flavor name
     * @return void
     */
    public function selectFlavor(string $flavor): void
    {
        $this->clickElement(self::SELECTORS['FLAVOR'] . "[data-value='$flavor']");
        $this->waitForAjaxToComplete();
    }

    /**
     * Add the product to cart
     *
     * @param int $quantity Quantity to add
     * @return void
     */
    public function addToCart(int $quantity = 1): void
    {
        // Set quantity if needed
        if ($quantity > 1) {
            $this->setQuantity($quantity);
        }

        // Click add to cart button
        $this->clickElement('#sylius-product-adding-to-cart button[type="submit"]');
        $this->waitForAjaxToComplete();
        $this->waitForElementVisible('.cart-summary');
    }

    /**
     * Set the product quantity
     *
     * @param int $quantity Quantity to set
     * @return void
     */
    public function setQuantity(int $quantity): void
    {
        $this->fillField('#sylius_add_to_cart_cartItem_quantity', (string)$quantity);
    }

    /**
     * Get the product price
     *
     * @return string
     */
    public function getPrice(): string
    {
        return $this->getElementText(self::SELECTORS['PRICE']);
    }

    /**
     * Get the product name
     *
     * @return string
     */
    public function getProductName(): string
    {
        return $this->getElementText(self::SELECTORS['PRODUCT_NAME']);
    }

    // Other methods specific to the product page...
}
```

### 6.2 Migration Tasks

1. Create `src/Page/ProductPage.php`
2. Implement the `verifyPage` method
3. Migrate methods from the old ProductPage
4. Update method signatures to use BrowserService instead of direct Mink access
5. Add dependency on TestDataService for product data
6. Add new methods for common product page interactions

### 6.3 Service Registration

Register the ProductPage as a service:

```yaml
# config/services/pages.yml
services:
  # Product Page
  App\Page\ProductPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'app.page' ]

  app.page.productpage:
    alias: App\Page\ProductPage
    public: true
```

## 7. Cart Page Migration

### 7.1 Implementation Details

Migrate the CartPage to use the new architecture.

#### 7.1.1 Class Structure

```php
namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;
use RuntimeException;

class CartPage extends BasePage
{
    protected string $path = '/cart';

    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'CART_ITEMS' => '.cart-items .cart-item',
        'CART_TOTAL' => '.cart-summary .cart-total',
        'CHECKOUT_BUTTON' => '.checkout-button',
        'EMPTY_CART_MESSAGE' => '.cart-empty-message',
        'REMOVE_ITEM_BUTTON' => '.remove-item-button',
        'QUANTITY_INPUT' => '.item-quantity input',
        'UPDATE_CART_BUTTON' => '.update-cart-button'
    ];

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible('.cart-summary');
    }

    /**
     * Check if the cart is empty
     *
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->elementExists(self::SELECTORS['EMPTY_CART_MESSAGE']);
    }

    /**
     * Get the cart items
     *
     * @return array List of cart items
     */
    public function getCartItems(): array
    {
        $items = [];
        $itemElements = $this->browserService->findElements(self::SELECTORS['CART_ITEMS']);

        foreach ($itemElements as $element) {
            $items[] = [
                'name' => $element->find('css', '.item-name')->getText(),
                'price' => $element->find('css', '.item-price')->getText(),
                'quantity' => (int)$element->find('css', '.item-quantity input')->getAttribute('value')
            ];
        }

        return $items;
    }

    /**
     * Get the cart total
     *
     * @return string
     */
    public function getCartTotal(): string
    {
        return $this->getElementText(self::SELECTORS['CART_TOTAL']);
    }

    /**
     * Proceed to checkout
     *
     * @return void
     */
    public function proceedToCheckout(): void
    {
        if ($this->isEmpty()) {
            throw new RuntimeException('Cannot proceed to checkout with empty cart');
        }

        $this->clickElement(self::SELECTORS['CHECKOUT_BUTTON']);
        $this->waitForPageToLoad();
        $this->waitForElementVisible('.checkout-steps');
    }

    /**
     * Update the quantity of a cart item
     *
     * @param string $productName Product name
     * @param int $quantity New quantity
     * @return void
     * @throws RuntimeException If the product is not in the cart
     */
    public function updateItemQuantity(string $productName, int $quantity): void
    {
        $itemElement = $this->findCartItemByName($productName);
        if (!$itemElement) {
            throw new RuntimeException(sprintf('Product "%s" not found in cart', $productName));
        }

        $quantityInput = $itemElement->find('css', self::SELECTORS['QUANTITY_INPUT']);
        $quantityInput->setValue((string)$quantity);

        $this->clickElement(self::SELECTORS['UPDATE_CART_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Remove an item from the cart
     *
     * @param string $productName Product name
     * @return void
     * @throws RuntimeException If the product is not in the cart
     */
    public function removeItem(string $productName): void
    {
        $itemElement = $this->findCartItemByName($productName);
        if (!$itemElement) {
            throw new RuntimeException(sprintf('Product "%s" not found in cart', $productName));
        }

        $removeButton = $itemElement->find('css', self::SELECTORS['REMOVE_ITEM_BUTTON']);
        $removeButton->click();

        $this->waitForAjaxToComplete();
    }

    /**
     * Find a cart item by product name
     *
     * @param string $productName Product name
     * @return NodeElement|null Cart item element or null if not found
     */
    private function findCartItemByName(string $productName): ?NodeElement
    {
        $itemElements = $this->browserService->findElements(self::SELECTORS['CART_ITEMS']);

        foreach ($itemElements as $element) {
            $name = $element->find('css', '.item-name')->getText();
            if ($name === $productName) {
                return $element;
            }
        }

        return null;
    }
}
```

### 7.2 Migration Tasks

1. Create `src/Page/CartPage.php`
2. Implement the `verifyPage` method
3. Migrate methods from the old CartPage
4. Update method signatures to use BrowserService instead of direct Mink access
5. Add new methods for common cart page interactions

### 7.3 Service Registration

Register the CartPage as a service:

```yaml
# config/services/pages.yml
services:
  # Cart Page
  App\Page\CartPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'app.page' ]

  app.page.cartpage:
    alias: App\Page\CartPage
    public: true
```

## 8. Checkout Page Migration

### 8.1 Implementation Details

Migrate the CheckoutPage to use the new architecture.

#### 8.1.1 Class Structure

```php
namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use RuntimeException;

class CheckoutPage extends BasePage
{
    protected string $path = '/checkout';
    private TestDataServiceInterface $dataService;

    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'SHIPPING_FORM' => '#shipping-address-form',
        'BILLING_FORM' => '#billing-address-form',
        'PAYMENT_FORM' => '#payment-form',
        'CONTINUE_BUTTON' => '.continue-button',
        'BACK_BUTTON' => '.back-button',
        'CHECKOUT_STEPS' => '.checkout-steps',
        'ACTIVE_STEP' => '.checkout-step.active',
        'ORDER_SUMMARY' => '.order-summary',
        'ERROR_MESSAGE' => '.error-message'
    ];

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     * @param TestDataServiceInterface $dataService Test data service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(
        BrowserServiceInterface $browserService,
        TestDataServiceInterface $dataService,
        ?string $baseUrl = null
    ) {
        parent::__construct($browserService, $baseUrl);
        $this->dataService = $dataService;
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible(self::SELECTORS['CHECKOUT_STEPS']);
    }

    /**
     * Fill shipping information form
     *
     * @param array $userData User data with shipping information
     * @return void
     */
    public function fillShippingInformation(array $userData): void
    {
        $this->waitForElementVisible(self::SELECTORS['SHIPPING_FORM']);

        // Fill form fields
        $this->fillField('#first_name', $userData['firstName']);
        $this->fillField('#last_name', $userData['lastName']);
        $this->fillField('#email', $userData['email']);
        $this->fillField('#address', $userData['address']);
        $this->fillField('#city', $userData['city']);
        $this->selectOption('#state', $userData['state']);
        $this->fillField('#zip', $userData['zip']);
        $this->fillField('#phone', $userData['phone']);

        // Continue to next step
        $this->clickElement(self::SELECTORS['CONTINUE_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Fill billing information form
     *
     * @param array $userData User data with billing information
     * @param bool $sameAsShipping Whether billing is same as shipping
     * @return void
     */
    public function fillBillingInformation(array $userData, bool $sameAsShipping = true): void
    {
        $this->waitForElementVisible(self::SELECTORS['BILLING_FORM']);

        if ($sameAsShipping) {
            // Check the "Same as shipping" checkbox
            $this->clickElement('#same_as_shipping');
        } else {
            // Fill form fields
            $this->fillField('#billing_first_name', $userData['firstName']);
            $this->fillField('#billing_last_name', $userData['lastName']);
            $this->fillField('#billing_address', $userData['address']);
            $this->fillField('#billing_city', $userData['city']);
            $this->selectOption('#billing_state', $userData['state']);
            $this->fillField('#billing_zip', $userData['zip']);
        }

        // Continue to next step
        $this->clickElement(self::SELECTORS['CONTINUE_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Fill payment information form
     *
     * @param array $paymentData Payment data
     * @return void
     */
    public function fillPaymentInformation(array $paymentData): void
    {
        $this->waitForElementVisible(self::SELECTORS['PAYMENT_FORM']);

        // Fill credit card information
        $this->fillField('#card_number', $paymentData['cardNumber']);
        $this->fillField('#card_expiry', $paymentData['expiryDate']);
        $this->fillField('#card_cvv', $paymentData['cvv']);

        // Continue to next step
        $this->clickElement(self::SELECTORS['CONTINUE_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Place the order
     *
     * @return void
     */
    public function placeOrder(): void
    {
        $this->waitForElementVisible('#place-order-button');
        $this->clickElement('#place-order-button');
        $this->waitForPageToLoad();
    }

    /**
     * Get the current checkout step
     *
     * @return string Step name
     */
    public function getCurrentStep(): string
    {
        $activeStep = $this->browserService->findElement(self::SELECTORS['ACTIVE_STEP']);
        return $activeStep->getText();
    }

    /**
     * Check if there are validation errors
     *
     * @return bool
     */
    public function hasValidationErrors(): bool
    {
        return $this->elementExists(self::SELECTORS['ERROR_MESSAGE']);
    }

    /**
     * Get validation error messages
     *
     * @return array List of error messages
     */
    public function getValidationErrors(): array
    {
        $errors = [];
        $errorElements = $this->browserService->findElements(self::SELECTORS['ERROR_MESSAGE']);

        foreach ($errorElements as $element) {
            $errors[] = $element->getText();
        }

        return $errors;
    }
}
```

### 8.2 Migration Tasks

1. Create `src/Page/CheckoutPage.php`
2. Implement the `verifyPage` method
3. Migrate methods from the old CheckoutPage
4. Update method signatures to use BrowserService instead of direct Mink access
5. Add dependency on TestDataService for user and payment data
6. Add new methods for common checkout page interactions

### 8.3 Service Registration

Register the CheckoutPage as a service:

```yaml
# config/services/pages.yml
services:
  # Checkout Page
  App\Page\CheckoutPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'app.page' ]

  app.page.checkoutpage:
    alias: App\Page\CheckoutPage
    public: true
```

## 9. Other Page Objects Migration

### 9.1 Payment Page

#### 9.1.1 Implementation Details

Migrate the PaymentPage to use the new architecture following the same pattern as the other pages.

#### 9.1.2 Migration Tasks

1. Create `src/Page/PaymentPage.php`
2. Implement the `verifyPage` method
3. Migrate methods from the old PaymentPage
4. Update method signatures to use BrowserService instead of direct Mink access

### 9.2 Confirmation Page

#### 9.2.1 Implementation Details

Migrate the ConfirmationPage to use the new architecture following the same pattern as the other pages.

#### 9.2.2 Migration Tasks

1. Create `src/Page/ConfirmationPage.php`
2. Implement the `verifyPage` method
3. Migrate methods from the old ConfirmationPage
4. Update method signatures to use BrowserService instead of direct Mink access

### 9.3 Other Specialized Pages

For each remaining page object:

1. Create a new class in the `src/Page/` directory
2. Extend the `BasePage` class
3. Implement the `verifyPage` method
4. Migrate methods from the old page object
5. Update method signatures to use BrowserService instead of direct Mink access
6. Register the page as a service in `config/services/pages.yml`

## 10. Implementation Plan

### 10.1 Implementation Order

Migrate the page objects in the following order to respect dependencies:

1. `BasePage` and `BasePageInterface`: Foundation for all other page objects
2. `PageFactory`: Service for creating page objects
3. `HomePage`: Simple page with minimal dependencies
4. `ProductPage`: Page with product data dependencies
5. `CartPage`: Page that depends on product data
6. `CheckoutPage`: Page with complex form handling
7. `PaymentPage`: Page with payment processing
8. `ConfirmationPage`: Page that shows order confirmation
9. Other specialized pages

### 10.2 Step-by-Step Implementation

#### 10.2.1 Base Page and Interface

1. Create `src/Page/Base/BasePageInterface.php`
2. Create `src/Page/Base/BasePage.php`
3. Create unit tests for the base page
4. Update service container configuration

#### 10.2.2 Page Factory

1. Create `src/Service/Page/PageFactoryInterface.php`
2. Create `src/Service/Page/PageFactory.php`
3. Create unit tests for the page factory
4. Update service container configuration

#### 10.2.3 Home Page

1. Create `src/Page/HomePage.php`
2. Create unit tests for the home page
3. Update service container configuration

#### 10.2.4 Product Page

1. Create `src/Page/ProductPage.php`
2. Create unit tests for the product page
3. Update service container configuration

#### 10.2.5 Cart Page

1. Create `src/Page/CartPage.php`
2. Create unit tests for the cart page
3. Update service container configuration

#### 10.2.6 Checkout Page

1. Create `src/Page/CheckoutPage.php`
2. Create unit tests for the checkout page
3. Update service container configuration

#### 10.2.7 Other Pages

For each remaining page:

1. Create the page class in `src/Page/`
2. Create unit tests for the page
3. Update service container configuration

### 10.3 Detailed Task Breakdown

#### Week 1

| Day | Tasks                                                             |
|-----|-------------------------------------------------------------------|
| 1   | Analyze current page objects, create detailed implementation plan |
| 2   | Implement `BasePageInterface`, `BasePage`, and unit tests         |
| 3   | Implement `PageFactory` and unit tests                            |
| 4   | Implement `HomePage`, `ProductPage`, and unit tests               |
| 5   | Implement `CartPage`, `CheckoutPage`, and unit tests              |

## 11. Testing Strategy

### 11.1 Unit Testing

Create unit tests for each page object to verify:

1. Constructor initializes properties correctly
2. Methods work as expected
3. BrowserService methods are called correctly
4. Error handling works properly

### 11.2 Integration Testing

Create integration tests to verify:

1. Page objects work with the BrowserService
2. Page factory creates page objects correctly
3. Page objects interact with other services correctly

### 11.3 Functional Testing

Run existing Behat tests with the new page objects to verify:

1. Existing functionality continues to work
2. Performance is maintained or improved
3. No regressions are introduced

### 11.4 Test Coverage

Aim for high test coverage:

1. 90%+ line coverage for page object methods
2. 100% coverage for constructor and initialization
3. 80%+ coverage for helper methods
4. 70%+ coverage for error handling

## 12. Rollback Plan

### 12.1 Rollback Triggers

Consider rolling back if:

1. Critical page functionality is broken
2. Performance is significantly degraded
3. Backward compatibility is broken
4. Integration with existing contexts fails

### 12.2 Rollback Process

1. Revert service container configuration to use original page objects
2. Remove new page object implementations
3. Verify original functionality works
4. Document issues for future resolution

### 12.3 Partial Rollback

Consider partial rollback if only specific page objects are problematic:

1. Identify problematic page objects
2. Revert only those page objects to original implementation
3. Keep working page objects in place
4. Document issues for future resolution