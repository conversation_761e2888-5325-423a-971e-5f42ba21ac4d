<?php

namespace Features\Bootstrap;

use App\Context\Base\ServiceAwareContext;
use Behat\Behat\Context\Context;
use Behat\Behat\Context\Initializer\ContextInitializer;
use Behat\Behat\EventDispatcher\Event\ScenarioTested;
use <PERSON><PERSON>fony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Initializes contexts with the container
 */
class ContainerAwareInitializer implements ContextInitializer, EventSubscriberInterface
{
    /**
     * @var ContainerInterface
     */
    private $container;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Get container from globals
        if (isset($GLOBALS['service_container'])) {
            $this->container = $GLOBALS['service_container'];
        } else {
            throw new \RuntimeException('Container not available in globals. Make sure bootstrap.php is loaded.');
        }
    }

    /**
     * {@inheritdoc}
     */
    public function initializeContext(Context $context)
    {
        // Check if this is a context with a service ID from the container
        $contextClass = get_class($context);
        error_log("Initializing context: " . $contextClass);

        // If this is a service-aware context but wasn't loaded from container
        if ($context instanceof ServiceAwareContext && !isset($context->container)) {
            if (property_exists($context, 'container')) {
                $reflectionProperty = new \ReflectionProperty($contextClass, 'container');
                if ($reflectionProperty->isProtected() || $reflectionProperty->isPrivate()) {
                    $reflectionProperty->setAccessible(true);
                    $reflectionProperty->setValue($context, $this->container);
                    $reflectionProperty->setAccessible(false);
                } else {
                    $context->container = $this->container;
                }
            }
            error_log("Injected container into context: " . $contextClass);
        }
    }

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents()
    {
        return [
            ScenarioTested::BEFORE => ['prepareScenario', 100],
        ];
    }

    /**
     * Prepare each scenario
     *
     * @param ScenarioTested $event
     */
    public function prepareScenario(ScenarioTested $event)
    {
        // No-op for now, could be used for scenario-level container preparation
        error_log("Preparing scenario...");
    }
}
