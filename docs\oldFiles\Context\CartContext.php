<?php

namespace Features\Bootstrap\Context;

use ElementNotFoundException;
use Features\Bootstrap\Page\CartPage;
use Features\Bootstrap\Page\CheckoutPage;
use Features\Bootstrap\Page\ProductPage;
use RuntimeException;
use Throwable;

/**
 * Handles cart-related actions and assertions
 */
class CartContext extends BaseContext
{
    private CartPage $cartPage;
    private ProductPage $productPage;
    private CheckoutPage $checkoutPage;

    /**
     * @param CartPage $cartPage Cart page object
     * @param ProductPage $productPage Product page object
     * @param CheckoutPage $checkoutPage Checkout page object
     */
    public function __construct(
        CartPage     $cartPage,
        ProductPage  $productPage,
        CheckoutPage $checkoutPage
    )
    {
        parent::__construct();
        $this->cartPage = $cartPage;
        $this->productPage = $productPage;
        $this->checkoutPage = $checkoutPage;
    }

    /**
     * Gets the current product data or throws an exception
     *
     * @return array Product data
     * @throws RuntimeException When no product data is available
     */
    private function getCurrentProductData(): array
    {
        $productData = $this->stateService->get('product.data');
        if (!$productData) {
            $this->logError('No product data loaded');
            throw new RuntimeException('No product data loaded');
        }
        return $productData;
    }

    /**
     * @When /^I proceed to checkout$/
     * @throws RuntimeException
     * @sets cart.checkout_started
     */
    public function iProceedToCheckout(): void
    {
        try {
            if ($this->cartPage->isEmpty()) {
                throw new RuntimeException('Cannot proceed to checkout with empty cart');
            }

            $this->cartPage->proceedToCheckout();
            $this->stateService->set('cart.checkout_started', true);
            $this->logInfo('Proceeded to checkout');
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to proceed to checkout', $e);
            throw new RuntimeException('Failed to proceed to checkout: ' . $e->getMessage(), 0, $e);
        } catch (Throwable $e) {
            $this->logError('Failed to proceed to checkout', $e);
            throw $e;
        }
    }

    /**
     * Adds a product to cart
     *
     * @throws RuntimeException When product cannot be added
     * @sets cart.item_count
     */
    public function addToCart(): void
    {
        try {
            $this->productPage->addToCart();
            $this->logInfo('Added product to cart');

            // Get cart item count
            $itemCount = $this->cartPage->getItemCount();
            $this->stateService->set('cart.item_count', $itemCount);
            $this->logInfo(sprintf('Cart now has %d items', $itemCount));
        } catch (Throwable $e) {
            $this->logError('Failed to add product to cart', $e);
            throw new RuntimeException('Failed to add product to cart: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @When /^I view my cart$/
     * @throws RuntimeException
     * @sets cart.is_empty, cart.item_count, cart.total
     */
    public function iViewMyCart(): void
    {
        try {
            $this->cartPage->open();
            $this->logInfo('Opened cart page');

            // Store cart state in shared data
            $isEmpty = $this->cartPage->isEmpty();
            $this->stateService->set('cart.is_empty', $isEmpty);

            if (!$isEmpty) {
                $itemCount = $this->cartPage->getItemCount();
                $total = $this->cartPage->getCartTotal();

                $this->stateService->set('cart.item_count', $itemCount);
                $this->stateService->set('cart.total', $total);

                $this->logInfo(sprintf('Cart has %d items with total %s', $itemCount, $total));
            } else {
                $this->logInfo('Cart is empty');
            }
        } catch (Throwable $e) {
            $this->logError('Failed to view cart', $e);
            throw new RuntimeException('Failed to view cart: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @When /^I remove item from cart$/
     * @throws RuntimeException
     * @sets cart.item_count
     */
    public function iRemoveItemFromCart(): void
    {
        try {
            $itemCountBefore = $this->cartPage->getItemCount();
            $this->cartPage->removeFirstItem();
            $itemCountAfter = $this->cartPage->getItemCount();

            $this->stateService->set('cart.item_count', $itemCountAfter);
            $this->logInfo(sprintf('Removed item from cart. Items before: %d, after: %d', $itemCountBefore, $itemCountAfter));
        } catch (Throwable $e) {
            $this->logError('Failed to remove item from cart', $e);
            throw new RuntimeException('Failed to remove item from cart: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Then /^the cart should (not )?be empty$/
     * @throws RuntimeException
     */
    public function theCartShouldBeEmpty(string $not = null): void
    {
        try {
            $shouldBeEmpty = $not === null;
            $isEmpty = $this->cartPage->isEmpty();

            if ($shouldBeEmpty && !$isEmpty) {
                throw new RuntimeException('Expected cart to be empty but it contains items');
            } elseif (!$shouldBeEmpty && $isEmpty) {
                throw new RuntimeException('Expected cart to contain items but it is empty');
            }

            $this->logInfo(sprintf('Verified cart %s empty', $shouldBeEmpty ? 'is' : 'is not'));
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to check if cart is empty', $e);
            throw new RuntimeException('Failed to check if cart is empty: ' . $e->getMessage(), 0, $e);
        } catch (Throwable $e) {
            if (!($e instanceof RuntimeException && strpos($e->getMessage(), 'Expected cart') === 0)) {
                $this->logError('Failed to check if cart is empty', $e);
            }
            throw $e;
        }
    }

    /**
     * @Then /^the cart should contain (\d+) items?$/
     * @throws RuntimeException
     */
    public function theCartShouldContainItems(int $expectedCount): void
    {
        try {
            $actualCount = $this->cartPage->getItemCount();

            if ($actualCount !== $expectedCount) {
                throw new RuntimeException(
                    sprintf('Expected %d items in cart, but found %d', $expectedCount, $actualCount)
                );
            }

            $this->logInfo(sprintf('Verified cart contains %d items', $expectedCount));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to verify cart item count (%d)', $expectedCount), $e);
            throw new RuntimeException(
                sprintf('Failed to verify cart item count (%d): %s', $expectedCount, $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            if (!($e instanceof RuntimeException && strpos($e->getMessage(), 'Expected') === 0)) {
                $this->logError(sprintf('Failed to verify cart item count (%d)', $expectedCount), $e);
            }
            throw $e;
        }
    }
} 