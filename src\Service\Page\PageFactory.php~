<?php

namespace App\Service\Page;

use App\Page\Base\BasePageInterface;
use App\Service\AbstractService;
use App\Service\Browser\BrowserServiceInterface;
use RuntimeException;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Factory service for creating page objects.
 */
class PageFactory extends AbstractService implements PageFactoryInterface
{
    /**
     * Service container
     *
     * @var ContainerInterface
     */
    private ContainerInterface $container;

    /**
     * Browser service
     *
     * @var BrowserServiceInterface
     */
    private BrowserServiceInterface $browserService;

    /**
     * Base URL for the application
     *
     * @var string
     */
    private string $baseUrl;

    /**
     * Namespaces to search for page classes
     *
     * @var array
     */
    private array $pageNamespaces = [
        'App\\Page\\',
        'Features\\Bootstrap\\Page\\' // For backward compatibility
    ];

    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     * @param BrowserServiceInterface $browserService Browser service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(
        ContainerInterface      $container,
        BrowserServiceInterface $browserService,
        ?string                 $baseUrl = null
    )
    {
        $this->container = $container;
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    /**
     * {@inheritdoc}
     */
    public function getPage(string $pageName, array $parameters = []): BasePageInterface
    {
        // Check if the page is registered as a service
        $serviceId = sprintf('app.page.%s', strtolower($pageName));
        if ($this->container->has($serviceId)) {
            return $this->container->get($serviceId);
        }

        // Try to find the page class
        $pageClass = $this->findPageClass($pageName);
        if (!$pageClass) {
            throw new RuntimeException(sprintf('Page "%s" not found', $pageName));
        }

        // Create the page object
        return $this->createPage($pageClass, $parameters);
    }

    /**
     * Find the page class for a page name
     *
     * @param string $pageName Page name
     * @return string|null Page class or null if not found
     */
    private function findPageClass(string $pageName): ?string
    {
        // Add 'Page' suffix if not present
        if (!str_ends_with($pageName, 'Page')) {
            $pageName .= 'Page';
        }

        // Try each namespace
        foreach ($this->pageNamespaces as $namespace) {
            $pageClass = $namespace . $pageName;
            if (class_exists($pageClass)) {
                return $pageClass;
            }
        }

        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface
    {
        if (!class_exists($pageClass)) {
            throw new RuntimeException(sprintf('Page class "%s" does not exist', $pageClass));
        }

        // Add default parameters
        $parameters = array_merge([
            'browserService' => $this->browserService,
            'baseUrl' => $this->baseUrl
        ], $parameters);

        // Create reflection class
        $reflectionClass = new \ReflectionClass($pageClass);

        // Check if the class implements BasePageInterface
        if (!$reflectionClass->implementsInterface(BasePageInterface::class)) {
            throw new RuntimeException(sprintf('Page class "%s" must implement BasePageInterface', $pageClass));
        }

        // Get constructor parameters
        $constructor = $reflectionClass->getConstructor();
        if (!$constructor) {
            throw new RuntimeException(sprintf('Page class "%s" has no constructor', $pageClass));
        }

        // Prepare constructor arguments
        $arguments = [];
        foreach ($constructor->getParameters() as $parameter) {
            $paramName = $parameter->getName();
            if (isset($parameters[$paramName])) {
                $arguments[] = $parameters[$paramName];
            } elseif ($parameter->isDefaultValueAvailable()) {
                $arguments[] = $parameter->getDefaultValue();
            } elseif ($parameter->getType() && !$parameter->getType()->isBuiltin() && $this->container->has($parameter->getType()->getName())) {
                $arguments[] = $this->container->get($parameter->getType()->getName());
            } else {
                throw new RuntimeException(sprintf('Cannot resolve parameter "%s" for page class "%s"', $paramName, $pageClass));
            }
        }

        // Create page object
        return $reflectionClass->newInstanceArgs($arguments);
    }

    /**
     * {@inheritdoc}
     */
    public function hasPage(string $pageName): bool
    {
        // Check if the page is registered as a service
        $serviceId = sprintf('app.page.%s', strtolower($pageName));
        if ($this->container->has($serviceId)) {
            return true;
        }

        // Try to find the page class
        return $this->findPageClass($pageName) !== null;
    }
}
