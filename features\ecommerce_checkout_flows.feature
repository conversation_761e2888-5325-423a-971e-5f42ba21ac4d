Feature: E-commerce Checkout and Post-Purchase Flows
  As an e-commerce customer
  I want to purchase products through various payment methods
  So that I can complete transactions in different scenarios

  Background:
    Given I load brand configuration
    And I load product data

  @paypal @one_time_purchase
  Scenario: Successful One-Time Purchase using PayPal
    Given I am on the product page "younger-you-skin-cream"
    When I select "One-Time Purchase"
    And I add the product to the cart
    And I proceed to checkout
    And I fill in the shipping information with "paypal_test_user" user data
    And I use the same address for billing
    Then The shipping method "Domestic tracked" should be selected
    And I verify the shipping cost
    When I choose to pay with PayPal
    And I complete the purchase
    And I am redirected to the PayPal sandbox page
    And I log in to PayPal sandbox with "valid" credentials
    Then I should see the correct payment amount in PayPal
    And I confirm the PayPal payment
    And I should be redirected back to the merchant site
    And I wait for the order confirmation page to load
    Then I verify the order details are correct
    And I verify the order confirmation email
    And I verify the welcome email contains account credentials

  @subscription @credit_card
  Scenario: Successful Subscription Purchase using Credit Card
    Given I am on the product page "younger-you-skin-cream"
    When I select "Subscribe & Save" with frequency "30 Days"
    And I add the product to the cart
    And I proceed to checkout
    And I fill in the shipping information with "subscription_test_user" user data
    And I use the same address for billing
    Then The shipping method "Domestic tracked" should be selected with "FREE" shipping
    When I enter "stripe_valid" payment details
    And I complete the purchase
    And I wait for the order confirmation page to load
    Then I verify the order details are correct
    And I verify the order confirmation email
    And I verify the subscription confirmation email
    And I verify the welcome email contains account credentials

  @funnel @upsell_failure @credit_card
  Scenario: Purchase with upsell via sales funnel (with frontend upsell page failure)
    Given I am logged into the admin panel
    And I navigate to the sales funnel configuration page
    And I obtain the "demo-dsv-1" sales funnel URL
    When I navigate to the sales funnel URL
    And I fill in the shipping information with "funnel_test_user" user data
    And I use the same address for billing
    And I select the "Domestic tracked" shipping method
    And I enter "stripe_valid" payment details
    And I complete the purchase
    And I manually navigate to the order completion page due to upsell page failure
    Then I verify the order confirmation page shows both initial and upsell products
    And I verify the order confirmation email contains all purchased items
    And I verify the welcome email contains account credentials

  @funnel @backend_command
  Scenario: Force complete a sales funnel order using backend command
    Given I am logged into the admin panel
    And I navigate to the sales funnel configuration page
    And I obtain the "demo-dsv-1" sales funnel URL
    When I navigate to the sales funnel URL
    And I fill in the shipping information with "backend_test_user" user data
    And I use the same address for billing
    And I select the "Domestic tracked" shipping method
    And I enter "stripe_valid" payment details
    And I complete the purchase
    And I run the sales funnel complete-payments command
    Then the command should be successful
    And I verify the order exists in the admin panel
    And I verify the order status is "New" and payment status is "Paid"
    And I verify the order confirmation email for initial product only
    And I verify the welcome email contains account credentials

  @abandoned_cart @paypal @funnel
  Scenario: Abandon cart during PayPal checkout in a sales funnel
    Given I am logged into the admin panel
    And I navigate to the sales funnel configuration page
    And I obtain the "demo-dsv-1" sales funnel URL
    When I navigate to the sales funnel URL
    And I fill in the shipping information with "abandoned_cart_test_user" user data
    And I use the same address for billing
    And I select the "Domestic tracked" shipping method
    And I choose to pay with PayPal
    And I complete the purchase
    And I am redirected to the PayPal sandbox page
    And I abandon the PayPal checkout
    And I run the abandoned cart recovery command
    Then the command should be successful
    And I verify the order status is "Cancelled" in the admin panel
    And I should receive an abandoned cart email
    And the abandoned cart email contains the correct product information

  @subscription @renewal @backend_command
  Scenario: Manually trigger a subscription renewal using backend command
    Given I have an active subscription order for "subscription_renewal_test_user"
    And the initial subscription order has been fulfilled
    When I run the subscription renewal command
    Then the command should be successful
    And a new order should be created with same subscription items
    And I verify the order confirmation email for the renewal order 