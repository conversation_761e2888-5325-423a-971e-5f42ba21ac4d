<?php

namespace App\Service\Validation;

interface ValidationServiceInterface
{
    /**
     * Validate product data structure
     *
     * @param array $data Product data to validate
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateProductData(array $data): void;

    /**
     * Validate user data structure
     *
     * @param array $data User data to validate
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateUserData(array $data): void;

    /**
     * Validate shipping data structure
     *
     * @param array $data Shipping data to validate
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateShippingData(array $data): void;

    /**
     * Validate data against a schema
     *
     * @param array $data Data to validate
     * @param string $schema Schema name
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateSchema(array $data, string $schema): void;
}
