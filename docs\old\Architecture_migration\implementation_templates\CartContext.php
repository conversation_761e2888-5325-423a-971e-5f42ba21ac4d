<?php

namespace App\Context;

// Add new step definitions for cart context

/**
 * @Then the cart should contain :count subscription items
 */
public function theCartShouldContainSubscriptionItems(int $count): void
{
    try {
        $cartPage = $this->pageFactory->getPage('CartPage');
        $subscriptionItems = $cartPage->getSubscriptionItemCount();

        if ($subscriptionItems !== $count) {
            throw new \RuntimeException(
                sprintf('Expected %d subscription items in cart, but found %d', $count, $subscriptionItems)
            );
        }

        $this->logInfo(sprintf("Verified cart contains %d subscription items", $count));
    } catch (\Throwable $e) {
        $this->logError(sprintf("Failed to verify subscription item count: %d", $count), $e);
        throw $e;
    }
}

/**
 * @Then the cart should contain :count one-time purchase items
 */
public function theCartShouldContainOneTimePurchaseItems(int $count): void
{
    try {
        $cartPage = $this->pageFactory->getPage('CartPage');
        $oneTimeItems = $cartPage->getOneTimeItemCount();

        if ($oneTimeItems !== $count) {
            throw new \RuntimeException(
                sprintf('Expected %d one-time purchase items in cart, but found %d', $count, $oneTimeItems)
            );
        }

        $this->logInfo(sprintf("Verified cart contains %d one-time purchase items", $count));
    } catch (\Throwable $e) {
        $this->logError(sprintf("Failed to verify one-time purchase item count: %d", $count), $e);
        throw $e;
    }
}

/**
 * @Then the subscription items should have correct frequencies
 */
public function theSubscriptionItemsShouldHaveCorrectFrequencies(): void
{
    try {
        $cartPage = $this->pageFactory->getPage('CartPage');
        $cartItems = $this->stateService->get('cart.items', 'scenario') ?? [];
        
        $subscriptionItems = array_filter($cartItems, function($item) {
            return isset($item['purchase_type']) && $item['purchase_type'] === 'subscription';
        });
        
        foreach ($subscriptionItems as $item) {
            $expectedFrequency = $item['subscription_frequency_display'] ?? null;
            if (!$expectedFrequency) {
                continue;
            }
            
            $itemName = $item['product'];
            $actualFrequency = $cartPage->getSubscriptionFrequencyForProduct($itemName);
            
            if ($actualFrequency !== $expectedFrequency) {
                throw new \RuntimeException(
                    sprintf('Expected frequency "%s" for product "%s", but got "%s"', 
                        $expectedFrequency, $itemName, $actualFrequency)
                );
            }
        }
        
        $this->logInfo("Verified all subscription items have correct frequencies");
    } catch (\Throwable $e) {
        $this->logError("Failed to verify subscription frequencies", $e);
        throw $e;
    }
}

/**
 * @Then my cart should be restored
 */
public function myCartShouldBeRestored(): void
{
    try {
        $cartPage = $this->pageFactory->getPage('CartPage');
        $abandonedItems = $this->stateService->get('abandoned_cart.items');
        
        if (!$abandonedItems || !is_array($abandonedItems)) {
            throw new \RuntimeException('No abandoned cart items found in state');
        }
        
        $currentItems = $cartPage->getCartItems();
        
        // Check item count first
        if (count($currentItems) !== count($abandonedItems)) {
            throw new \RuntimeException(
                sprintf('Expected %d items in restored cart, but found %d', 
                    count($abandonedItems), 
                    count($currentItems)
                )
            );
        }
        
        // Check each item matches the abandoned cart
        foreach ($abandonedItems as $i => $abandonedItem) {
            if (!isset($currentItems[$i]) || 
                $currentItems[$i]['product'] !== $abandonedItem['product'] ||
                $currentItems[$i]['quantity'] !== $abandonedItem['quantity']) {
                throw new \RuntimeException(
                    sprintf('Mismatch in restored cart item %d', $i + 1)
                );
            }
        }
        
        $this->logInfo("Verified cart has been correctly restored with all expected items");
    } catch (\Throwable $e) {
        $this->logError("Failed to verify cart restoration", $e);
        throw $e;
    }
} 