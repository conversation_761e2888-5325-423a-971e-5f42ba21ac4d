# Context Service Pattern

This document describes the service-oriented pattern for Behat contexts in the Malaberg Test Framework.

## Overview

In the new architecture, Behat contexts are defined as services in the Symfony container, allowing for proper dependency
injection and better testability. This approach eliminates the need for global state and mock services, making the
codebase more maintainable and reliable.

## Context Service Definition

Contexts are defined as services in `config/services/contexts.yml` with the following pattern:

```yaml
behat.context.{name}:
  class: App\Context\{ClassName}
  public: true
  arguments:
    $container: '@service_container'
    $specificService1: '@App\Service\Specific\ServiceInterface'
    $specificService2: '@App\Service\Another\ServiceInterface'
  tags: [ 'context.service' ]
```

For example, the SalesFunnelContext is defined as:

```yaml
behat.context.sales_funnel:
  class: App\Context\SalesFunnelContext
  public: true
  arguments:
    $container: '@service_container'
    $browserService: '@App\Service\Browser\BrowserServiceInterface'
    $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
    $stateService: '@App\Service\State\SharedStateServiceInterface'
  tags: [ 'context.service' ]
```

## Behat Configuration

In `behat.yml`, contexts are referenced by their service IDs rather than class names:

```yaml
default:
  suites:
    default:
      contexts:
        - 'behat.context.feature'
        - 'behat.context.brand'
        - 'behat.context.product'
        # ... other contexts
```

## Context Implementation

Contexts should follow these principles:

1. Extend `App\Context\Base\BaseContext`
2. Accept dependencies through constructor injection
3. Do not create mock services or fallbacks
4. Use type hints for all dependencies

Example:

```php
<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class SalesFunnelContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;

    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        ConfigurationServiceInterface $configService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);

        $this->browserService = $browserService;
        $this->configService = $configService;
        $this->stateService = $stateService;
    }

    // Step definitions and other methods...
}
```

## Benefits

This pattern provides several benefits:

1. **Explicit Dependencies**: All dependencies are explicitly declared and injected
2. **Testability**: Contexts can be easily unit tested with mock services
3. **Maintainability**: No global state or service locator pattern
4. **Consistency**: All contexts follow the same pattern
5. **Flexibility**: Services can be easily swapped or decorated

## Verification

To verify that contexts are properly defined and can be instantiated, run:

```bash
php bin/verify-container.php
```

This script checks that all context services are registered and can be instantiated with their dependencies.

## Migration Checklist

When migrating a context to this pattern:

1. Define the context as a service in `config/services/contexts.yml`
2. Update the constructor to accept specific dependencies
3. Remove any mock service creation methods
4. Update any references to the context in `behat.yml` to use the service ID
5. Run `bin/verify-container.php` to verify the context can be instantiated

## Troubleshooting

If a context cannot be instantiated, check:

1. That all dependencies are properly defined in the service container
2. That the constructor parameter names match the service definition
3. That the context is properly tagged with `context.service`
4. That there are no circular dependencies
