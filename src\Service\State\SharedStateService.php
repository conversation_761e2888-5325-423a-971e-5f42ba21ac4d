<?php

namespace App\Service\State;

use App\Service\AbstractService;
use Behat\Behat\EventDispatcher\Event\FeatureTested;
use Behat\Behat\EventDispatcher\Event\ScenarioTested;
use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Service for managing shared state between contexts
 */
class SharedStateService extends AbstractService implements SharedStateServiceInterface, EventSubscriberInterface
{
    private array $scenarioState = [];
    private array $featureState = [];
    private array $globalState = [];

    /**
     * Constructor
     *
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?LoggerInterface $logger = null)
    {
        parent::__construct($logger);
        $this->logInfo("Initializing SharedStateService");
    }

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents()
    {
        return [
            ScenarioTested::AFTER => ['resetScenarioState'],
            FeatureTested::AFTER => ['resetFeatureState'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function set(string $key, $value, string $scope = 'scenario'): void
    {
        $this->logInfo(sprintf("Setting '%s' in '%s' scope", $key, $scope));

        switch ($scope) {
            case 'scenario':
                $this->scenarioState[$key] = $value;
                break;
            case 'feature':
                $this->featureState[$key] = $value;
                break;
            case 'global':
                $this->globalState[$key] = $value;
                break;
            default:
                throw new \InvalidArgumentException(
                    sprintf("Invalid scope: %s. Expected 'scenario', 'feature', or 'global'", $scope)
                );
        }
    }

    /**
     * {@inheritdoc}
     */
    public function get(string $key, string $scope = 'scenario')
    {
        $this->logInfo(sprintf("Getting '%s' from '%s' scope", $key, $scope));

        // First check in the specified scope
        switch ($scope) {
            case 'scenario':
                if (isset($this->scenarioState[$key])) {
                    return $this->scenarioState[$key];
                }
            // Fall through to feature scope if not found

            case 'feature':
                if (isset($this->featureState[$key])) {
                    return $this->featureState[$key];
                }
            // Fall through to global scope if not found

            case 'global':
                if (isset($this->globalState[$key])) {
                    return $this->globalState[$key];
                }
                break;

            default:
                throw new \InvalidArgumentException(
                    sprintf("Invalid scope: %s. Expected 'scenario', 'feature', or 'global'", $scope)
                );
        }

        // If we get here, the key wasn't found in any applicable scope
        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function has(string $key, string $scope = 'scenario'): bool
    {
        switch ($scope) {
            case 'scenario':
                return isset($this->scenarioState[$key]);

            case 'feature':
                return isset($this->featureState[$key]);

            case 'global':
                return isset($this->globalState[$key]);

            default:
                throw new \InvalidArgumentException(
                    sprintf("Invalid scope: %s. Expected 'scenario', 'feature', or 'global'", $scope)
                );
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getAll(string $scope = 'scenario'): array
    {
        switch ($scope) {
            case 'scenario':
                return $this->scenarioState;

            case 'feature':
                return $this->featureState;

            case 'global':
                return $this->globalState;

            default:
                throw new \InvalidArgumentException(
                    sprintf("Invalid scope: %s. Expected 'scenario', 'feature', or 'global'", $scope)
                );
        }
    }

    /**
     * {@inheritdoc}
     */
    public function reset(string $scope = 'scenario'): void
    {
        $this->logInfo(sprintf("Resetting '%s' scope", $scope));

        switch ($scope) {
            case 'scenario':
                $this->resetScenarioState();
                break;

            case 'feature':
                $this->resetFeatureState();
                break;

            case 'global':
                $this->resetGlobalState();
                break;

            case 'all':
                $this->resetScenarioState();
                $this->resetFeatureState();
                $this->resetGlobalState();
                break;

            default:
                throw new \InvalidArgumentException(
                    sprintf("Invalid scope: %s. Expected 'scenario', 'feature', 'global', or 'all'", $scope)
                );
        }
    }

    /**
     * Reset scenario state
     *
     * This is called automatically after each scenario
     */
    public function resetScenarioState(): void
    {
        $this->logInfo("Resetting scenario state");
        $this->scenarioState = [];
    }

    /**
     * Reset feature state
     *
     * This is called automatically after each feature
     */
    public function resetFeatureState(): void
    {
        $this->logInfo("Resetting feature state");
        $this->featureState = [];
    }

    /**
     * Reset global state
     *
     * This must be called manually when needed
     */
    public function resetGlobalState(): void
    {
        $this->logInfo("Resetting global state");
        $this->globalState = [];
    }
}
