# Page Objects Documentation

## Overview

Page Objects are a design pattern used in test automation to encapsulate the interaction with web pages. They provide a higher-level API for tests to use, abstracting away the details of the HTML structure and browser interactions. This documentation covers the page objects used in the Malaberg test automation framework.

## Base Page Structure

### BasePageInterface

The `BasePageInterface` defines the contract that all page objects must implement. It provides methods for common page operations.

```php
interface BasePageInterface
{
    /**
     * Open the page
     *
     * @param array $urlParameters Parameters to include in the URL
     * @return void
     */
    public function open(array $urlParameters = []): void;

    /**
     * Get the URL of the page
     *
     * @param array $urlParameters Parameters to include in the URL
     * @return string
     */
    public function getUrl(array $urlParameters = []): string;

    /**
     * Check if the page is open
     *
     * @return bool
     */
    public function isOpen(): bool;

    /**
     * Wait for the page to load
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForPageToLoad(int $timeout = 30): void;

    /**
     * Get the page title
     *
     * @return string
     */
    public function getTitle(): string;

    /**
     * Take a screenshot of the page
     *
     * @param string|null $name Name for the screenshot
     * @return string Path to the screenshot
     */
    public function takeScreenshot(?string $name = null): string;

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int;

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int;

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array;

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array;
}
```

### BasePage

The `BasePage` class is the base implementation of the `BasePageInterface`. All page objects extend this class to inherit common functionality.

```php
abstract class BasePage implements BasePageInterface
{
    /**
     * Browser service for interacting with the browser
     *
     * @var BrowserServiceInterface
     */
    protected BrowserServiceInterface $browserService;

    /**
     * Path to the page, relative to the base URL
     *
     * @var string
     */
    protected string $path = '/';

    /**
     * Base URL for the application
     *
     * @var string
     */
    protected string $baseUrl;

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(BrowserServiceInterface $browserService, ?string $baseUrl = null)
    {
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    // Implementation of interface methods...
}
```

### Specialized Interfaces

In addition to the `BasePageInterface`, there are specialized interfaces for specific types of pages:

#### ProductPageInterface

```php
interface ProductPageInterface
{
    /**
     * Select supply duration/frequency
     *
     * @param string $frequency The frequency to select
     * @return int Days between deliveries
     */
    public function selectSupplyDuration(string $frequency);

    /**
     * Select product quantity
     *
     * @param mixed $quantity The quantity to select
     * @return void
     */
    public function selectQuantity($quantity);
}
```

#### PaymentPageInterface

```php
interface PaymentPageInterface
{
    /**
     * Complete the PayPal checkout process
     *
     * @return void
     */
    public function completePayPalCheckout();

    /**
     * Handle 3D Secure authentication
     *
     * @return void
     */
    public function handle3DSecureAuthentication();
}
```

## Page Object Classes

### HomePage

The `HomePage` class represents the main landing page of the application.

#### Properties

- `path`: The path of the home page, set to `/`

#### Methods

- `goToProductRange()`: Navigates to the product range page
- `isUserLoggedIn()`: Checks if the user is logged in
- `getFeaturedProducts()`: Gets the list of featured products
- `searchForProduct(string $searchTerm)`: Searches for a product
- `getPageTitle()`: Gets the page title
- `verifyPage()`: Verifies that we're on the expected page

### ProductPage

The `ProductPage` class represents the product detail page.

#### Properties

- `path`: The path of the product page, set to `/product/{slug}`
- `productSlugs`: A mapping of product names to URL slugs

#### Methods

- `loadWithName(string $productName, ?array $productData = null)`: Loads the product page for a specific product
- `selectFlavor(string $flavor)`: Selects a product flavor
- `selectQuantity($quantity)`: Selects a product quantity
- `selectPurchaseType(string $purchaseType)`: Selects a purchase type (one-time or subscription)
- `selectSupplyDuration(string $frequency)`: Selects a supply duration/frequency
- `addToCart()`: Adds the product to the cart
- `getPrice()`: Gets the product price
- `getProductName()`: Gets the product name
- `hasProductName(string $name)`: Checks if the product has a specific name
- `verifyPage()`: Verifies that we're on the expected page

### CartPage

The `CartPage` class represents the shopping cart page.

#### Properties

- `path`: The path of the cart page, set to `/cart`

#### Methods

- `getItemCount()`: Gets the number of items in the cart
- `getCartTotal()`: Gets the cart total
- `proceedToCheckout()`: Proceeds to checkout
- `isEmpty()`: Checks if the cart is empty
- `removeItemByIndex(int $index)`: Removes an item from the cart by index
- `removeFirstItem()`: Removes the first item from the cart
- `verifyPage()`: Verifies that we're on the expected page

### CheckoutPage

The `CheckoutPage` class represents the checkout page.

#### Properties

- `path`: The path of the checkout page, set to `/checkout`

#### Methods

- `fillShippingInformation(array $userData)`: Fills in the shipping information
- `fillBillingInformation(array $userData, bool $sameAsShipping = false)`: Fills in the billing information
- `selectShippingMethod(string $method)`: Selects a shipping method
- `selectPaymentMethod(string $method)`: Selects a payment method
- `completeOrder()`: Completes the order
- `verifyPage()`: Verifies that we're on the expected page

## Common Page Object Methods

### Navigation Methods

- `open(array $urlParameters = [])`: Opens the page
- `getUrl(array $urlParameters = [])`: Gets the URL of the page
- `waitForPageToLoad(int $timeout = 30)`: Waits for the page to load
- `isOpen()`: Checks if the page is open

### Element Interaction Methods

- `clickElement(string $selector)`: Clicks on an element
- `fillField(string $selector, string $value)`: Fills a field with a value
- `selectOption(string $selector, string $value)`: Selects an option from a select field
- `elementExists(string $selector)`: Checks if an element exists
- `getElementText(string $selector)`: Gets text from an element
- `waitForElementVisible(string $selector, int $timeout = 30)`: Waits for an element to be visible
- `waitForAjaxToComplete(int $timeout = 30)`: Waits for AJAX requests to complete

### Utility Methods

- `getTitle()`: Gets the page title
- `takeScreenshot(?string $name = null)`: Takes a screenshot of the page
- `verifyPage()`: Verifies that we're on the expected page

## Best Practices for Page Objects

1. **Single Responsibility**: Each page object should represent a single page or component.
2. **Encapsulation**: Page objects should encapsulate the details of the page structure and provide a high-level API.
3. **Verification**: Each page object should have a `verifyPage()` method to verify that we're on the expected page.
4. **Error Handling**: Page objects should handle errors gracefully and provide meaningful error messages.
5. **Reusability**: Common functionality should be extracted to the base page class.
6. **Maintainability**: Page objects should be easy to maintain and update when the page structure changes.
7. **Documentation**: Page objects should be well-documented with clear method descriptions.

## Creating New Page Objects

To create a new page object:

1. Create a new class in the `src/Page` directory
2. Extend the `BasePage` class
3. Implement the `verifyPage()` method
4. Add methods for interacting with the page

Example:

```php
<?php

namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;

class MyNewPage extends BasePage
{
    protected string $path = '/my-new-page';

    protected function verifyPage(): void
    {
        $this->waitForElementVisible('.my-page-element');
    }

    public function doSomething(): void
    {
        $this->clickElement('.my-button');
        $this->waitForPageToLoad();
    }
}
```
