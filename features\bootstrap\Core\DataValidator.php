<?php

namespace Features\Bootstrap\Core;

use RuntimeException;

/**
 * Validates test data structure and content
 */
class DataValidator
{
    /**
     * Validates product data structure
     *
     * @param array $data Product data to validate
     * @throws RuntimeException When validation fails
     */
    public function validateProductData(array $data): void
    {
        $this->validateRequiredFields($data, [
            'name',
            'slug',
            'prices',
            'options',
            'content'
        ], 'product');

        $this->validatePricing($data['prices']);
        $this->validateOptions($data['options']);
        $this->validateContent($data['content']);
    }

    /**
     * Validates test user data structure
     *
     * @param array $data User data to validate
     * @throws RuntimeException When validation fails
     */
    public function validateTestUser(array $data): void
    {
        $this->validateRequiredFields($data, [
            'email',
            'password',
            'address'
        ], 'test user');

        $this->validateAddress($data['address']);
    }

    /**
     * Validates shipping data structure
     *
     * @param array $data Shipping data to validate
     * @throws RuntimeException When validation fails
     */
    public function validateShippingData(array $data): void
    {
        $this->validateRequiredFields($data, [
            'method',
            'cost',
            'zones'
        ], 'shipping');

        foreach ($data['zones'] as $zone) {
            $this->validateRequiredFields($zone, [
                'code',
                'name',
                'countries'
            ], 'shipping zone');
        }
    }

    /**
     * Validates payment method data structure
     *
     * @param array $data Payment method data to validate
     * @throws RuntimeException When validation fails
     */
    public function validatePaymentMethod(array $data): void
    {
        $this->validateRequiredFields($data, [
            'type',
            'name',
            'enabled'
        ], 'payment method');

        if ($data['type'] === 'card') {
            $this->validateRequiredFields($data, [
                'supported_cards',
                'test_cards'
            ], 'card payment method');
        }
    }

    /**
     * Validates required fields in data structure
     *
     * @param array $data Data to validate
     * @param array $fields Required fields
     * @param string $context Validation context
     * @throws RuntimeException When required fields are missing
     */
    private function validateRequiredFields(array $data, array $fields, string $context): void
    {
        foreach ($fields as $field) {
            if (!isset($data[$field])) {
                throw new RuntimeException(
                    sprintf('Missing required %s field: %s', $context, $field)
                );
            }
        }
    }

    /**
     * Validates product pricing structure
     *
     * @param array $pricing Pricing data to validate
     * @throws RuntimeException When validation fails
     */
    private function validatePricing(array $pricing): void
    {
        error_log("[DataValidator] Starting price validation");
        if (!isset($pricing['one_time']) && !isset($pricing['subscription'])) {
            throw new RuntimeException(
                'Product must have at least one price type (one_time or subscription)'
            );
        }

        if (isset($pricing['one_time'])) {
            error_log("[DataValidator] Validating one_time pricing");
            $this->validatePriceData($pricing['one_time'], 'one_time');
        }

        if (isset($pricing['subscription'])) {
            error_log("[DataValidator] Validating subscription pricing");
            $this->validatePriceData($pricing['subscription'], 'subscription');
        }
        error_log("[DataValidator] Price validation completed successfully");
    }

    /**
     * Validates price data structure
     *
     * @param array $priceData Price data to validate
     * @param string $type Price type
     * @throws RuntimeException When validation fails
     */
    private function validatePriceData(array $priceData, string $type): void
    {
        error_log(sprintf("[DataValidator] Validating %s price data", $type));
        $this->validateRequiredFields($priceData, [
            'minimum',
            'medium',
            'maximum'
        ], "$type price");

        // Validate that prices are numeric
        foreach (['minimum', 'medium', 'maximum'] as $priceKey) {
            if (!is_numeric($priceData[$priceKey])) {
                throw new RuntimeException(
                    sprintf('Invalid %s price value for %s: %s. Expected numeric value.',
                        $type,
                        $priceKey,
                        $priceData[$priceKey]
                    )
                );
            }
        }

        // Validate price order (minimum < medium < maximum)
        if ($priceData['minimum'] >= $priceData['medium']) {
            throw new RuntimeException(
                sprintf('%s minimum price (%s) must be less than medium price (%s)',
                    ucfirst($type),
                    $priceData['minimum'],
                    $priceData['medium']
                )
            );
        }
        if ($priceData['medium'] >= $priceData['maximum']) {
            throw new RuntimeException(
                sprintf('%s medium price (%s) must be less than maximum price (%s)',
                    ucfirst($type),
                    $priceData['medium'],
                    $priceData['maximum']
                )
            );
        }
    }

    /**
     * Validates product options structure
     *
     * @param array $options Options data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateOptions(array $options): void
    {
        error_log("[DataValidator] Starting options validation");

        // Validate required option types
        $this->validateRequiredFields($options, [
            'purchase_types',
            'quantities'
        ], 'product options');

        error_log("[DataValidator] Validating quantities");
        if (empty($options['quantities'])) {
            throw new RuntimeException('Product must have at least one quantity option');
        }

        // Validate required quantity options
        $requiredQuantities = ['minimum', 'medium', 'maximum'];
        foreach ($requiredQuantities as $key) {
            if (!isset($options['quantities'][$key])) {
                throw new RuntimeException(
                    sprintf('Missing required quantity option: %s', $key)
                );
            }
        }

        // Validate each quantity option
        foreach ($options['quantities'] as $key => $quantity) {
            error_log(sprintf("[DataValidator] Validating quantity option: %s", $key));
            $this->validateRequiredFields($quantity, [
                'fullName',
                'numberOfItems'
            ], "quantity option '$key'");

            if (!is_numeric($quantity['numberOfItems'])) {
                throw new RuntimeException(
                    sprintf('Invalid numberOfItems for quantity option "%s": must be numeric', $key)
                );
            }
        }

        // Validate purchase types
        error_log("[DataValidator] Validating purchase types");
        if (empty($options['purchase_types'])) {
            throw new RuntimeException('Product must have at least one purchase type');
        }

        $requiredPurchaseTypes = ['one_time', 'subscription'];
        foreach ($requiredPurchaseTypes as $type) {
            if (!isset($options['purchase_types'][$type])) {
                throw new RuntimeException(
                    sprintf('Missing required purchase type: %s', $type)
                );
            }
        }

        error_log("[DataValidator] Options validation completed successfully");
    }

    /**
     * Validates product content structure
     *
     * @param array $content Content data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateContent(array $content): void
    {
        error_log("[DataValidator] Starting content validation");

        // Required base fields
        $this->validateRequiredFields($content, [
            'description'
        ], 'product content');

        // Validate badges if present
        if (isset($content['badges'])) {
            error_log("[DataValidator] Validating badges");
            foreach ($content['badges'] as $badge) {
                $this->validateRequiredFields($badge, [
                    'name',
                    'icon'
                ], 'product badge');
            }
        }

        // Validate trust badges if present
        if (isset($content['trust_badges'])) {
            error_log("[DataValidator] Validating trust badges");
            foreach ($content['trust_badges'] as $badge) {
                $this->validateRequiredFields($badge, [
                    'text',
                    'icon'
                ], 'trust badge');
            }
        }

        // Validate testimonial if present
        if (isset($content['testimonial'])) {
            error_log("[DataValidator] Validating testimonial");
            $this->validateRequiredFields($content['testimonial'], [
                'quote',
                'author'
            ], 'testimonial');
        }

        // Validate details if present
        if (isset($content['details'])) {
            error_log("[DataValidator] Validating details");
            foreach ($content['details'] as $detail) {
                $this->validateRequiredFields($detail, [
                    'title',
                    'content'
                ], 'product detail');
            }
        }

        // Validate features if present
        if (isset($content['features'])) {
            error_log("[DataValidator] Validating features");
            $this->validateRequiredFields($content['features'], [
                'title',
                'subtitle'
            ], 'features section');
        }

        // Validate FAQ if present
        if (isset($content['faq'])) {
            error_log("[DataValidator] Validating FAQ");
            foreach ($content['faq'] as $faq) {
                $this->validateRequiredFields($faq, [
                    'question',
                    'answer'
                ], 'FAQ item');
            }
        }

        // Validate related products if present
        if (isset($content['related_products'])) {
            error_log("[DataValidator] Validating related products");
            foreach ($content['related_products'] as $product) {
                $this->validateRequiredFields($product, [
                    'name',
                    'subtitle',
                    'description',
                    'url'
                ], 'related product');
            }
        }

        error_log("[DataValidator] Content validation completed successfully");
    }

    /**
     * Validates address data structure
     *
     * @param array $address Address data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateAddress(array $address): void
    {
        $this->validateRequiredFields($address, [
            'first_name',
            'last_name',
            'street',
            'city',
            'country',
            'postcode'
        ], 'address');
    }
} 