<?php

require_once __DIR__ . '/vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables from .env file
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Set required environment variables if not already set
$requiredVars = [
    'TEST_BRAND',
    'TEST_ENV',
    'TEST_BASE_URL'
];

foreach ($requiredVars as $var) {
    if (!isset($_ENV[$var])) {
        throw new RuntimeException("Required environment variable $var is not set");
    }
}

// Initialize shared data context
Features\Bootstrap\SharedDataContext::getInstance();
