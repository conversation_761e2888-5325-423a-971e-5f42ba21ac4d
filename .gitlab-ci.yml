variables:
  PHP_VERSION: "8.1"
  COMPOSER_ALLOW_SUPERUSER: 1
  TEST_BRAND: "aeons"
  TEST_ENV: "stage"
  TEST_BASE_URL: ${TEST_BASE_URL}
  BUILD_NUMBER: ${CI_PIPELINE_ID}
  DEFAULT_SESSION: "browserstack"
  BROWSERSTACK_USERNAME: ${BROWSERSTACK_USERNAME}
  BROWSERSTACK_ACCESS_KEY: ${BROWSERSTACK_ACCESS_KEY}
  MAILTRAP_AEONS_INBOX_ID: ${MAILTRAP_AEONS_INBOX_ID}
  MAILTRAP_ACCOUNT_ID: ${MAILTRAP_ACCOUNT_ID}
  MAILTRAP_TOKEN: ${MAILTRAP_TOKEN}
  # SSH Configuration
  SSH_HOST: ${SSH_HOST}
  SSH_USER: ${SSH_USER}
  SSH_KEY: ${SSH_KEY}

stages:
  - build
  - setup
  - test
  - report

build_docker_image:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $CI_REGISTRY_IMAGE/project-behat-test:latest -t $CI_REGISTRY_IMAGE/project-behat-test:$CI_COMMIT_SHORT_SHA .
    - docker push $CI_REGISTRY_IMAGE/project-behat-test:latest
    - docker push $CI_REGISTRY_IMAGE/project-behat-test:$CI_COMMIT_SHORT_SHA
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - Dockerfile
        - composer.json
        - composer.lock

.base_test:
  image: ${CI_REGISTRY_IMAGE}/project-behat-test:latest
  before_script:
    - mkdir -p logs screenshots
    # Setup SSH for CI environment
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\tUserKnownHostsFile=/dev/null\n" > ~/.ssh/config
    - chmod 600 ~/.ssh/config
    - php -v

install_dependencies:
  extends: .base_test
  stage: setup
  script:
    - composer install --no-progress --no-interaction --prefer-dist --optimize-autoloader
  cache:
    key: ${CI_COMMIT_REF_SLUG}-composer
    paths:
      - vendor/
      - .composer
  artifacts:
    paths:
      - vendor/
    expire_in: 1 day

test:aeons:smoke:stage:golden:
  extends: .base_test
  stage: test
  script:
    - composer test:aeons:smoke:stage:golden
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  dependencies:
    - install_dependencies
  artifacts:
    when: always
    paths:
      - screenshots/
      - logs/
    reports:
      junit: report.xml
    expire_in: 1 week

# BrowserStack-based test job for critical funnel tests
test:critical-funnel:browserstack:
  extends: .base_test
  stage: test
  script:
    # Run the critical funnel tests with BrowserStack
    - vendor/bin/behat --profile=browserstack features/salesFunnel.feature --tags="@high-priority" --format=pretty,junit --out=,report.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  dependencies:
    - install_dependencies
  artifacts:
    when: always
    paths:
      - screenshots/
      - logs/
      - report.xml
    reports:
      junit: report.xml
    expire_in: 1 week

generate_report:
  stage: report
  dependencies:
    - test:aeons:smoke:stage:golden
  script:
    - echo "Generating test report..."
  artifacts:
    reports:
      junit: report.xml
    paths:
      - screenshots/
      - logs/
    expire_in: 1 week