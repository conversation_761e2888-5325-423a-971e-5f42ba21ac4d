<?php

use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;
use Symfony\Component\DependencyInjection\Dumper\PhpDumper;

require_once __DIR__ . '/vendor/autoload.php';

// Set default environment variables
putenv("TEST_BRAND=aeons");
putenv("TEST_ENV=stage");
putenv("TEST_BASE_URL=https://aeonstest.info");
putenv("BROWSER_NAME=chrome");
putenv("BROWSER_VERSION=latest");
putenv("WEBDRIVER_HOST=http://localhost:9222");

// Set critical environment variables to force real services
putenv("USE_MOCKS=false");
putenv("TEST_MODE=real");
putenv("FORCE_REAL_SERVICES=true");
putenv("DISABLE_MOCKS=true");

// Print environment variables for debugging
echo "Environment variables set:\n";
echo "USE_MOCKS: " . getenv("USE_MOCKS") . "\n";
echo "TEST_MODE: " . getenv("TEST_MODE") . "\n";
echo "FORCE_REAL_SERVICES: " . getenv("FORCE_REAL_SERVICES") . "\n";
echo "DISABLE_MOCKS: " . getenv("DISABLE_MOCKS") . "\n";
echo "TEST_BRAND: " . getenv("TEST_BRAND") . "\n";
echo "TEST_ENV: " . getenv("TEST_ENV") . "\n";
echo "TEST_BASE_URL: " . getenv("TEST_BASE_URL") . "\n";

// Initialize container
$container = new ContainerBuilder();

// Set base parameters
$projectRoot = __DIR__;
$container->setParameter('kernel.project_dir', $projectRoot);
$container->setParameter('paths.base', $projectRoot);
$container->setParameter('app.project_root', $projectRoot);
$container->setParameter('app.config_dir', $projectRoot . '/config');
$container->setParameter('app.fixtures_dir', $projectRoot . '/features/fixtures');
$container->setParameter('app.cache_dir', $projectRoot . '/var/cache');
$container->setParameter('app.logs_dir', $projectRoot . '/var/logs');
$container->setParameter('app.downloads_dir', $projectRoot . '/downloads');
$container->setParameter('kernel.debug', true);

// Load service configuration
try {
    echo "Loading service configuration...\n";
    $loader = new YamlFileLoader($container, new FileLocator($projectRoot . '/config'));
    $loader->load('services.yml');

    echo "Compiling container...\n";
    $container->compile();
    echo "Container compiled successfully.\n";

    // Create a cache directory if it doesn't exist
    if (!is_dir($projectRoot . '/var/cache/container')) {
        mkdir($projectRoot . '/var/cache/container', 0777, true);
        echo "Created container cache directory.\n";
    }

    // Debug: Dump compiled container for inspection
    $dumper = new PhpDumper($container);
    file_put_contents(
        $projectRoot . '/var/cache/container/CompiledContainer.php',
        $dumper->dump(['class' => 'CompiledContainer'])
    );
    echo "Dumped compiled container for debugging.\n";

} catch (\Exception $e) {
    echo "Container compilation error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " (Line: " . $e->getLine() . ")\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

// Store container in global variable for access in contexts
$GLOBALS['service_container'] = $container;

// Register a shutdown function to ensure the container is available throughout the test run
register_shutdown_function(function () use ($container) {
    // This ensures the container is not garbage collected
    global $service_container;
    $service_container = $container;
});

// Return the container for use in the test runner
return $container;
