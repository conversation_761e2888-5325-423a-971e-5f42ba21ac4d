<?php

require __DIR__ . '/vendor/autoload.php';

use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// Function to load and compile services incrementally
function testServiceCompilation($serviceFile)
{
    try {
        // Initialize container
        $container = new ContainerBuilder();

        // Set required parameters
        $container->setParameter('paths.base', __DIR__);
        $container->setParameter('kernel.project_dir', __DIR__);

        // Load services configuration
        $loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/config'));

        // Load main services file
        $loader->load('services.yml');

        // Load the specific service file to test
        if ($serviceFile !== 'services.yml') {
            $loader->load($serviceFile);
        }

        // Try to compile the container
        $container->compile();

        return [true, "Successfully compiled with $serviceFile"];
    } catch (Exception $e) {
        return [false, "Error compiling with $serviceFile: " . $e->getMessage()];
    }
}

// Find all service files
$serviceFiles = glob(__DIR__ . '/config/services/*.yml');
$serviceFiles = array_map(function ($path) {
    return 'services/' . basename($path);
}, $serviceFiles);

// Add the main services.yml file
array_unshift($serviceFiles, 'services.yml');

// Test each service file individually
echo "Testing service files individually:\n";
echo "==================================\n";
foreach ($serviceFiles as $serviceFile) {
    list($success, $message) = testServiceCompilation($serviceFile);
    echo ($success ? "✓ " : "✗ ") . $message . "\n";
}

// Test service files incrementally
echo "\nTesting service files incrementally:\n";
echo "==================================\n";
$loadedFiles = [];
foreach ($serviceFiles as $serviceFile) {
    $loadedFiles[] = $serviceFile;

    try {
        // Initialize container
        $container = new ContainerBuilder();

        // Set required parameters
        $container->setParameter('paths.base', __DIR__);
        $container->setParameter('kernel.project_dir', __DIR__);

        // Load services configuration
        $loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/config'));

        // Load all files up to this point
        foreach ($loadedFiles as $file) {
            $loader->load($file);
        }

        // Try to compile the container
        $container->compile();

        echo "✓ Successfully compiled with " . implode(', ', $loadedFiles) . "\n";
    } catch (Exception $e) {
        echo "✗ Error compiling with " . implode(', ', $loadedFiles) . ":\n";
        echo "  " . $e->getMessage() . "\n";
        echo "  in " . $e->getFile() . " (line " . $e->getLine() . ")\n";

        // Try to get more specific information about the error
        if ($e instanceof RuntimeException) {
            if (preg_match('/class "([^"]+)" not found/', $e->getMessage(), $matches)) {
                $missingClass = $matches[1];
                echo "  Missing class: $missingClass\n";

                // Check if the class file exists
                $classPath = str_replace('\\', '/', $missingClass) . '.php';
                $possiblePaths = [
                    __DIR__ . '/src/' . $classPath,
                    __DIR__ . '/vendor/' . $classPath
                ];

                echo "  Checking for class file:\n";
                foreach ($possiblePaths as $path) {
                    echo "    " . $path . ": " . (file_exists($path) ? "exists" : "not found") . "\n";
                }
            }
        }

        break;
    }
}
