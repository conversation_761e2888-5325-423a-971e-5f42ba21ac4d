<?php

namespace App\Service\Browser;

use Behat\Mink\Element\NodeElement;
use Behat\Mink\Session;

interface BrowserServiceInterface
{
    /**
     * Get the Mink session
     *
     * @return Session
     */
    public function getSession(): Session;

    /**
     * Visit a URL
     *
     * @param string $url URL to visit
     * @return void
     */
    public function visit(string $url): void;

    /**
     * Take a screenshot
     *
     * @param string|null $name Screenshot name
     * @return string Path to the screenshot
     */
    public function takeScreenshot(string $name = null): string;

    /**
     * Wait for an element to be present
     *
     * @param string $selector Element selector
     * @param int $timeout Timeout in seconds
     * @return void
     * @throws \RuntimeException When element is not found within timeout
     */
    public function waitForElement(string $selector, int $timeout = 30): void;

    /**
     * Execute JavaScript
     *
     * @param string $script JavaScript to execute
     * @return mixed Result of the script
     */
    public function executeScript(string $script);

    /**
     * Find all elements matching a selector
     *
     * @param string $selector CSS selector
     * @return NodeElement[] Array of matching elements
     */
    public function findElements(string $selector): array;

    /**
     * Wait for an element to be visible
     *
     * @param string $selector Element selector
     * @param int $timeout Timeout in seconds
     * @return bool True if element became visible within timeout
     */
    public function waitForElementVisible(string $selector, int $timeout = 30): bool;

    /**
     * Scroll to an element
     *
     * @param string $selector Element selector
     * @return void
     */
    public function scrollToElement(string $selector): void;

    /**
     * Click on an element
     *
     * @param string $selector Element selector
     * @return void
     */
    public function clickElement(string $selector): void;

    /**
     * Fill a field with a value
     *
     * @param string $selector Field selector
     * @param string $value Value to fill
     * @return void
     */
    public function fillField(string $selector, string $value): void;

    /**
     * Select an option from a select field
     *
     * @param string $selector Select field selector
     * @param string $value Option value to select
     * @return void
     */
    public function selectOption(string $selector, string $value): void;

    /**
     * Get text from an element
     *
     * @param string $selector Element selector
     * @return string Element text
     */
    public function getElementText(string $selector): string;

    /**
     * Check if an element is visible
     *
     * @param string $selector Element selector
     * @return bool True if element is visible
     */
    public function isElementVisible(string $selector): bool;

    /**
     * Wait for page to load completely
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForPageToLoad(int $timeout = 30): void;

    /**
     * Wait for document to be ready
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForDocumentReady(int $timeout = 30): void;

    /**
     * Wait for AJAX requests to complete
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForAjaxToComplete(int $timeout = 30): void;

    /**
     * Check if an element exists
     *
     * @param string $selector Element selector
     * @return bool True if element exists
     */
    public function elementExists(string $selector): bool;

    /**
     * Get the driver type (e.g., Selenium2Driver, ChromeDriver)
     *
     * @return string The driver type
     */
    public function getDriverType(): string;

    /**
     * Navigate back in the browser history
     *
     * @return void
     */
    public function navigateBack(): void;

    /**
     * Check if the browser session is active
     *
     * @return bool True if the session is active
     */
    public function isSessionActive(): bool;

    /**
     * Check if the current session is a BrowserStack session
     *
     * @return bool True if running in BrowserStack
     */
    public function isBrowserStackSession(): bool;

    /**
     * Get the current page title
     *
     * @return string The page title
     */
    public function getPageTitle(): string;

    /**
     * Wait for a specified number of seconds
     *
     * @param int $seconds Number of seconds to wait
     * @return void
     */
    public function wait(int $seconds): void;

    /**
     * Get the current URL
     *
     * @return string The current URL
     */
    public function getCurrentUrl(): string;

    /**
     * Find a single element matching a selector
     *
     * @param string $selector CSS selector
     * @return NodeElement|null The matching element or null if not found
     */
    public function findElement(string $selector): ?NodeElement;

    /**
     * Wait for URL to contain a specific string
     *
     * @param string $text Text that should be in the URL
     * @param int $timeout Timeout in seconds
     * @return bool True if URL contains the text within timeout
     */
    public function waitForUrlContains(string $text, int $timeout = 30): bool;

    /**
     * Check if page content contains specific text
     *
     * @param string $text Text to search for
     * @return bool True if content contains the text
     */
    public function hasContent(string $text): bool;

    /**
     * Wait for a redirect chain to complete, tracking URL changes
     *
     * @param int $timeout Total timeout in seconds for all redirects
     * @param int $maxRedirects Maximum number of redirects to follow
     * @param int $stabilityDuration Time in seconds URL should remain stable to consider redirects complete
     * @return array Array containing redirect chain details with original URL, final URL, and all URLs visited
     */
    public function waitForRedirect(int $timeout = 60, int $maxRedirects = 5, int $stabilityDuration = 2): array;
}
