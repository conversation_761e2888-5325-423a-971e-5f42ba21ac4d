<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Exception\ElementNotFoundException;

/**
 * UpsellPage handles actions on the upsell page.
 */
class UpsellPage extends BasePage
{
    /**
     * The path of the upsell page.
     *
     * @var string
     */
    protected $path = '/upsell';

    // Define selectors based on expected DOM structure
    private const SELECTORS = [
        'ACCEPT_BUTTON' => '.upsell-accept',
        'DECLINE_BUTTON' => '.upsell-decline',
        'UPSELL_MESSAGE' => '.upsell-message',
        'PRODUCT_TITLE' => '.product-title',
        'PRODUCT_PRICE' => '.product-price .amount',
        'PRODUCT_RESTRICTIONS' => '.product-restrictions .warning'
    ];

    // Extended selectors for funnel-specific pages
    private const FUNNEL_SELECTORS = [
        'ACCEPT_BUTTON' => '.funnel-upsell-accept, .upsell-accept',
        'DECLINE_BUTTON' => '.funnel-upsell-decline, .upsell-decline',
        'PRODUCT_NAME' => '.funnel-product-title, .product-title',
        'PRODUCT_PRICE' => '.funnel-product-price, .product-price .amount',
        'PRODUCT_DESCRIPTION' => '.funnel-product-description, .product-description',
        'PRODUCT_WARNINGS' => '.funnel-product-warnings li, .product-restrictions .warning',
        'STEP_INDICATOR' => '.funnel-step-indicator',
        'DISCOUNT_BADGE' => '.funnel-discount-badge'
    ];

    /**
     * Verifies that we're on the expected page.
     *
     * @throws ElementNotFoundException If required elements are not found
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        try {
            $this->waitForElementVisible(self::SELECTORS['UPSELL_MESSAGE']);
        } catch (ElementNotFoundException $e) {
            error_log("[UpsellPage] Failed to verify page: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Gets the URL for the upsell page.
     *
     * @return string The complete URL
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Wait for the upsell page to load
     *
     * @param int $timeout Maximum time to wait in milliseconds
     * @throws ElementNotFoundException If required elements are not found
     */
    public function waitForPageToLoad(int $timeout = 10000): void
    {
        try {
            error_log("[UpsellPage] Waiting for page to load");
            parent::waitForPageToLoad($timeout);
            $this->waitForElementVisible(self::SELECTORS['ACCEPT_BUTTON']);
            $this->waitForElementVisible(self::SELECTORS['DECLINE_BUTTON']);
            error_log("[UpsellPage] Page loaded successfully");
        } catch (ElementNotFoundException $e) {
            error_log("[UpsellPage] Failed to load page: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Waits for upsell page to fully load including all dynamic content
     *
     * @param int $timeout Timeout in milliseconds
     * @throws ElementNotFoundException
     */
    public function waitForUpsellPageToLoad(int $timeout = 10000): void
    {
        parent::waitForPageToLoad($timeout);

        try {
            // Try first with funnel-specific selectors, then fallback to standard selectors
            $acceptButtonSelector = $this->isElementPresent(self::FUNNEL_SELECTORS['ACCEPT_BUTTON'])
                ? self::FUNNEL_SELECTORS['ACCEPT_BUTTON']
                : self::SELECTORS['ACCEPT_BUTTON'];

            $productNameSelector = $this->isElementPresent(self::FUNNEL_SELECTORS['PRODUCT_NAME'])
                ? self::FUNNEL_SELECTORS['PRODUCT_NAME']
                : self::SELECTORS['PRODUCT_TITLE'];

            // Wait for critical page elements
            $this->waitForElementVisible($productNameSelector, $timeout);
            $this->waitForElementVisible($acceptButtonSelector, $timeout);

            // Wait for AJAX loading to complete
            $this->waitForAjaxToComplete($timeout);

            error_log("[UpsellPage] Upsell page fully loaded");
        } catch (ElementNotFoundException $e) {
            error_log("[UpsellPage] Error waiting for upsell page: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Accept the upsell offer
     *
     * @throws \RuntimeException If accepting the upsell fails
     */
    public function acceptUpsell(): void
    {
        try {
            error_log("[UpsellPage] Attempting to accept upsell");
            $this->clickElement(self::SELECTORS['ACCEPT_BUTTON']);
            $this->waitForAjaxToComplete();

            // Store the acceptance in shared data
            this->stateService->set('upsellAccepted', true);
            
            error_log("[UpsellPage] Successfully accepted upsell");
        } catch (\Exception $e) {
            // Try the funnel-specific selector as a fallback
            try {
                $this->clickElement(self::FUNNEL_SELECTORS['ACCEPT_BUTTON']);
                $this->waitForAjaxToComplete();

                this->stateService->set('upsellAccepted', true);
                error_log("[UpsellPage] Successfully accepted upsell (with funnel selector)");
                return;
            } catch (\Exception $fallbackE) {
                $error = sprintf("Failed to accept upsell: %s", $e->getMessage());
                error_log("[UpsellPage] " . $error);
                throw new \RuntimeException($error);
            }
        }
    }

    /**
     * Accept the upsell offer, with retry logic
     *
     * @param int $maxRetries Maximum number of attempts to accept
     * @throws \RuntimeException If accepting fails after all retries
     */
    public function acceptUpsellWithRetry(int $maxRetries = 3): void
    {
        $attempt = 0;
        $lastError = null;

        while ($attempt < $maxRetries) {
            try {
                // Try with the funnel-specific selector first, then fallback
                try {
                    $this->clickElement(self::FUNNEL_SELECTORS['ACCEPT_BUTTON']);
                } catch (\Exception $e) {
                    $this->clickElement(self::SELECTORS['ACCEPT_BUTTON']);
                }

                $this->waitForAjaxToComplete();

                // Store acceptance in shared data
                $currentStep = $this->getCurrentStep();
                if ($currentStep) {
                    $key = "upsellAccepted_step$currentStep";
                } else {
                    $key = "upsellAccepted";
                }

                this->stateService->set($key, true);

                error_log("[UpsellPage] Successfully accepted upsell with retry");
                return;
            } catch (\Exception $e) {
                $lastError = $e;
                $attempt++;
                error_log("[UpsellPage] Attempt $attempt failed: " . $e->getMessage());
                usleep(500000); // 0.5 second delay before retry
            }
        }

        throw new \RuntimeException(
            sprintf("Failed to accept upsell after %d attempts: %s", $maxRetries, $lastError->getMessage())
        );
    }

    /**
     * Decline the upsell offer
     *
     * @throws \RuntimeException If declining the upsell fails
     */
    public function declineUpsell(): void
    {
        try {
            error_log("[UpsellPage] Attempting to decline upsell");
            $this->clickElement(self::SELECTORS['DECLINE_BUTTON']);
            $this->waitForAjaxToComplete();

            // Store the decline in shared data
            this->stateService->set('upsellDeclined', true);
            
            error_log("[UpsellPage] Successfully declined upsell");
        } catch (\Exception $e) {
            // Try the funnel-specific selector as a fallback
            try {
                $this->clickElement(self::FUNNEL_SELECTORS['DECLINE_BUTTON']);
                $this->waitForAjaxToComplete();

                this->stateService->set('upsellDeclined', true);
                error_log("[UpsellPage] Successfully declined upsell (with funnel selector)");
                return;
            } catch (\Exception $fallbackE) {
                $error = sprintf("Failed to decline upsell: %s", $e->getMessage());
                error_log("[UpsellPage] " . $error);
                throw new \RuntimeException($error);
            }
        }
    }

    /**
     * Decline the upsell offer, with retry logic
     *
     * @param int $maxRetries Maximum number of attempts to decline
     * @throws \RuntimeException If declining fails after all retries
     */
    public function declineUpsellWithRetry(int $maxRetries = 3): void
    {
        $attempt = 0;
        $lastError = null;

        while ($attempt < $maxRetries) {
            try {
                // Try with the funnel-specific selector first, then fallback
                try {
                    $this->clickElement(self::FUNNEL_SELECTORS['DECLINE_BUTTON']);
                } catch (\Exception $e) {
                    $this->clickElement(self::SELECTORS['DECLINE_BUTTON']);
                }

                $this->waitForAjaxToComplete();

                // Store decline in shared data
                $currentStep = $this->getCurrentStep();
                if ($currentStep) {
                    $key = "upsellDeclined_step$currentStep";
                } else {
                    $key = "upsellDeclined";
                }

                this->stateService->set($key, true);

                error_log("[UpsellPage] Successfully declined upsell with retry");
                return;
            } catch (\Exception $e) {
                $lastError = $e;
                $attempt++;
                error_log("[UpsellPage] Attempt $attempt failed: " . $e->getMessage());
                usleep(500000); // 0.5 second delay before retry
            }
        }

        throw new \RuntimeException(
            sprintf("Failed to decline upsell after %d attempts: %s", $maxRetries, $lastError->getMessage())
        );
    }

    /**
     * Get the upsell message text
     *
     * @return string The upsell message
     * @throws ElementNotFoundException If message element is not found
     */
    public function getUpsellMessage(): string
    {
        try {
            return $this->getElementText(self::SELECTORS['UPSELL_MESSAGE']);
        } catch (ElementNotFoundException $e) {
            error_log("[UpsellPage] Failed to get upsell message: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get the upsell product name
     *
     * @return string The product name
     * @throws ElementNotFoundException If product title element is not found
     */
    public function getUpsellProductName(): string
    {
        try {
            return $this->getElementText(self::SELECTORS['PRODUCT_TITLE']);
        } catch (ElementNotFoundException $e) {
            // Try the funnel-specific selector as a fallback
            try {
                return $this->getElementText(self::FUNNEL_SELECTORS['PRODUCT_NAME']);
            } catch (ElementNotFoundException $fallbackE) {
                error_log("[UpsellPage] Failed to get product name: " . $e->getMessage());
                throw $e;
            }
        }
    }

    /**
     * Get the upsell price
     *
     * @return float The price as a float
     * @throws ElementNotFoundException If price element is not found
     */
    public function getUpsellPrice(): float
    {
        try {
            $priceText = $this->getElementText(self::SELECTORS['PRODUCT_PRICE']);
            return $this->parsePrice($priceText);
        } catch (ElementNotFoundException $e) {
            // Try the funnel-specific selector as a fallback
            try {
                $priceText = $this->getElementText(self::FUNNEL_SELECTORS['PRODUCT_PRICE']);
                return $this->parsePrice($priceText);
            } catch (ElementNotFoundException $fallbackE) {
                error_log("[UpsellPage] Failed to get upsell price: " . $e->getMessage());
                throw $e;
            }
        }
    }

    /**
     * Check if we're on the upsell page
     *
     * @return bool True if on upsell page
     */
    public function isUpsellPage(): bool
    {
        try {
            // Check standard selectors first
            $isUpsellPage = $this->isElementPresent(self::SELECTORS['ACCEPT_BUTTON']) && 
                           $this->isElementPresent(self::SELECTORS['DECLINE_BUTTON']);

            // If not found, check funnel-specific selectors
            if (!$isUpsellPage) {
                $isUpsellPage = $this->isElementPresent(self::FUNNEL_SELECTORS['ACCEPT_BUTTON']) &&
                    $this->isElementPresent(self::FUNNEL_SELECTORS['DECLINE_BUTTON']);
            }
            
            error_log("[UpsellPage] Is upsell page: " . ($isUpsellPage ? 'true' : 'false'));
            return $isUpsellPage;
        } catch (\Exception $e) {
            error_log("[UpsellPage] Error checking if upsell page: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Wait for redirect to a specific URL
     *
     * @param string $expectedUrl The expected URL to redirect to
     * @param int $timeout Maximum time to wait in milliseconds
     * @throws \RuntimeException If redirect timeout occurs
     */
    public function waitForRedirect(string $expectedUrl, int $timeout = 10000): void
    {
        try {
            error_log("[UpsellPage] Waiting for redirect to: " . $expectedUrl);
            $this->getSession()->wait($timeout, 
                "window.location.href.includes('$expectedUrl')"
            );
            error_log("[UpsellPage] Successfully redirected");
        } catch (\Exception $e) {
            $error = sprintf("Failed to redirect to %s: %s", $expectedUrl, $e->getMessage());
            error_log("[UpsellPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Gets the current upsell step number (for multi-step funnels)
     *
     * @return int|null The current step number or null if not a multi-step funnel
     */
    public function getCurrentStep(): ?int
    {
        try {
            $stepIndicator = $this->findElement(self::FUNNEL_SELECTORS['STEP_INDICATOR']);
            if (!$stepIndicator) {
                return null;
            }

            $stepText = $stepIndicator->getText();
            if (preg_match('/step\s+(\d+)/i', $stepText, $matches)) {
                return (int)$matches[1];
            }

            return null;
        } catch (ElementNotFoundException $e) {
            return null;
        }
    }

    /**
     * Verifies the upsell product matches expected data
     *
     * @param array $expectedProduct Expected product data
     * @return bool True if product matches expectations
     */
    public function verifyUpsellProduct(array $expectedProduct): bool
    {
        try {
            $productName = $this->getUpsellProductName();
            $productPrice = $this->getElementText(self::FUNNEL_SELECTORS['PRODUCT_PRICE']);

            $nameMatches = stripos($productName, $expectedProduct['name']) !== false;
            $priceMatches = stripos($productPrice, (string)$expectedProduct['price']) !== false ||
                stripos($productPrice, $this->formatPrice($expectedProduct['price'])) !== false;

            return $nameMatches && $priceMatches;
        } catch (ElementNotFoundException $e) {
            error_log("[UpsellPage] Error verifying upsell product: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Formats a price value to match common display format
     *
     * @param float $price The price to format
     * @return string Formatted price
     */
    private function formatPrice(float $price): string
    {
        return sprintf('£%.2f', $price);
    }

    /**
     * Parse price string to float
     *
     * @param string $priceText The price text to parse
     * @return float The parsed price
     */
    private function parsePrice(string $priceText): float
    {
        return (float)preg_replace('/[^0-9.]/', '', $priceText);
    }

    /**
     * Check if a specific warning is displayed
     *
     * @param string $warning The warning text to look for
     * @return bool True if warning is found
     */
    public function hasRestrictionWarning(string $warning): bool
    {
        try {
            // Try both selector types for maximum compatibility
            $warningSelectors = [
                self::SELECTORS['PRODUCT_RESTRICTIONS'],
                self::FUNNEL_SELECTORS['PRODUCT_WARNINGS']
            ];

            foreach ($warningSelectors as $selector) {
                try {
                    $warningElements = $this->findElements($selector);
                    foreach ($warningElements as $element) {
                        if (str_contains($element->getText(), $warning)) {
                            error_log("[UpsellPage] Found restriction warning: " . $warning);
                            return true;
                        }
                    }
                } catch (\Exception $e) {
                    // Continue to next selector if this one fails
                    continue;
                }
            }
            
            error_log("[UpsellPage] Restriction warning not found: " . $warning);
            return false;
        } catch (\Exception $e) {
            error_log("[UpsellPage] Error checking restriction warning: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Checks if discount indicator is displayed for the upsell product
     *
     * @return bool True if discount is shown
     */
    public function hasDiscountIndicator(): bool
    {
        return $this->isElementPresent(self::FUNNEL_SELECTORS['DISCOUNT_BADGE']);
    }

    /**
     * Gets the discount amount/percentage displayed
     *
     * @return string|null Discount text or null if not found
     */
    public function getDiscountText(): ?string
    {
        try {
            $discountElement = $this->findElement(self::FUNNEL_SELECTORS['DISCOUNT_BADGE']);
            return $discountElement ? $discountElement->getText() : null;
        } catch (ElementNotFoundException $e) {
            return null;
        }
    }

    /**
     * Simulates multiple rapid clicks on the accept button
     *
     * @param int $clickCount Number of clicks to simulate
     */
    public function clickAcceptMultipleTimes(int $clickCount = 3): void
    {
        for ($i = 0; $i < $clickCount; $i++) {
            try {
                // Try both selectors to ensure compatibility
                try {
                    $this->clickElement(self::FUNNEL_SELECTORS['ACCEPT_BUTTON']);
                } catch (\Exception $e) {
                    $this->clickElement(self::SELECTORS['ACCEPT_BUTTON']);
                }

                usleep(300000); // 0.3 second delay between clicks
            } catch (\Exception $e) {
                error_log("[UpsellPage] Error during multiple clicks: " . $e->getMessage());
                // Continue with next click attempt
            }
        }

        // Wait for any pending AJAX to complete
        $this->waitForAjaxToComplete();

        // Store in shared data
        this->stateService->set('multipleUpsellClicks', true);

        error_log("[UpsellPage] Clicked accept button $clickCount times");
    }
}