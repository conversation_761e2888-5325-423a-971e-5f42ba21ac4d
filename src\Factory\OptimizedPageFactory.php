<?php

namespace App\Factory;

use App\Page\Base\BasePageInterface;
use App\Service\AbstractService;
use App\Service\Cache\CacheServiceInterface;
use App\Service\Page\PageFactoryInterface;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;

/**
 * Optimized page factory with caching
 */
class OptimizedPageFactory extends AbstractService implements PageFactoryInterface
{
    private ContainerInterface $container;
    private CacheServiceInterface $cacheService;
    private array $pageInstances = [];

    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     * @param CacheServiceInterface $cacheService Cache service
     * @param LoggerInterface|null $logger Logger
     */
    public function __construct(
        ContainerInterface    $container,
        CacheServiceInterface $cacheService,
        ?LoggerInterface      $logger = null
    )
    {
        parent::__construct($logger);
        $this->container = $container;
        $this->cacheService = $cacheService;
    }

    /**
     * {@inheritdoc}
     */
    public function getPage(string $pageName, array $parameters = []): BasePageInterface
    {
        // Convert page name to class name (e.g., 'HomePage' to 'App\Page\HomePage')
        $pageClass = $this->resolvePageClass($pageName);

        return $this->createPage($pageClass, $parameters);
    }

    /**
     * Resolve page name to full class name
     *
     * @param string $pageName Page name (e.g., 'HomePage')
     * @return string Full class name
     */
    private function resolvePageClass(string $pageName): string
    {
        // If it's already a fully qualified class name, return it
        if (class_exists($pageName)) {
            return $pageName;
        }

        // Try to resolve from the App\Page namespace
        $className = 'App\\Page\\' . $pageName;
        if (class_exists($className)) {
            return $className;
        }

        // If we can't resolve it, throw an exception
        throw new \InvalidArgumentException(sprintf('Page "%s" not found', $pageName));
    }

    /**
     * {@inheritdoc}
     */
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface
    {
        // Generate a cache key based on the class name and parameters
        $cacheKey = $this->generateCacheKey($pageClass, $parameters);

        // Check if we already have an instance of this page
        if (isset($this->pageInstances[$cacheKey])) {
            $this->logInfo(sprintf("Returning cached page instance for %s", $pageClass));
            return $this->pageInstances[$cacheKey];
        }

        // Create a new instance
        $this->logInfo(sprintf("Creating new page instance for %s", $pageClass));
        $page = $this->container->get($pageClass);

        // Cache the instance
        $this->pageInstances[$cacheKey] = $page;

        return $page;
    }

    /**
     * Generate a cache key for a page instance
     *
     * @param string $pageClass Page class name
     * @param array $parameters Constructor parameters
     * @return string Cache key
     */
    private function generateCacheKey(string $pageClass, array $parameters = []): string
    {
        return $pageClass . ':' . md5(serialize($parameters));
    }

    /**
     * {@inheritdoc}
     */
    public function hasPage(string $pageName): bool
    {
        try {
            $pageClass = $this->resolvePageClass($pageName);
            return $this->container->has($pageClass);
        } catch (\Exception $e) {
            $this->logWarning(sprintf("Page %s not found: %s", $pageName, $e->getMessage()));
            return false;
        }
    }
}