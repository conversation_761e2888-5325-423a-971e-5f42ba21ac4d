<?php

namespace App\Service\Cache;

/**
 * Filesystem-based cache service
 */
class FilesystemCacheService implements CacheServiceInterface
{
    private string $cacheDir;
    private int $defaultLifetime;

    /**
     * Constructor
     *
     * @param string $cacheDir Cache directory
     * @param int $defaultLifetime Default cache lifetime in seconds
     */
    public function __construct(string $cacheDir, int $defaultLifetime = 3600)
    {
        $this->cacheDir = $cacheDir;
        $this->defaultLifetime = $defaultLifetime;

        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0777, true);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function get(string $key): mixed
    {
        $file = $this->getFilePath($key);

        if (!file_exists($file)) {
            return null;
        }

        $data = unserialize(file_get_contents($file));

        if (!is_array($data) || !isset($data['expires']) || !isset($data['value'])) {
            $this->delete($key);
            return null;
        }

        if ($data['expires'] !== null && $data['expires'] < time()) {
            $this->delete($key);
            return null;
        }

        return $data['value'];
    }

    /**
     * Get the file path for a cache key
     *
     * @param string $key The cache key
     * @return string The file path
     */
    private function getFilePath(string $key): string
    {
        return $this->cacheDir . '/' . md5($key) . '.cache';
    }

    /**
     * {@inheritdoc}
     */
    public function delete(string $key): void
    {
        $file = $this->getFilePath($key);

        if (file_exists($file)) {
            // unlink() returns false on failure and raises E_WARNING
            // We don't return the value to comply with the void interface.
            unlink($file);
        }

        // No return needed if file doesn't exist, as the state is achieved.
    }

    /**
     * {@inheritdoc}
     */
    public function set(string $key, mixed $value, ?int $lifetime = null): void
    {
        $file = $this->getFilePath($key);
        $lifetime = $lifetime ?? $this->defaultLifetime;

        $data = [
            'key' => $key,
            'value' => $value,
            'expires' => $lifetime > 0 ? time() + $lifetime : null,
        ];

        // file_put_contents returns false on failure
        if (file_put_contents($file, serialize($data)) === false) {
            // Optionally: Log an error here if needed
            // Example: $this->logger->error('Failed to write cache file', ['file' => $file]);
            // However, the interface requires void return, so we don't return anything.
        }
    }

    /**
     * {@inheritdoc}
     */
    public function has(string $key): bool
    {
        $file = $this->getFilePath($key);

        if (!file_exists($file)) {
            return false;
        }

        $data = unserialize(file_get_contents($file));

        // Added check for unserialize failure
        if ($data === false || !is_array($data) || !isset($data['expires'])) {
            $this->delete($key); // Delete potentially corrupt file
            return false;
        }

        if ($data['expires'] !== null && $data['expires'] < time()) {
            $this->delete($key);
            return false;
        }

        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function deletePattern(string $pattern): void
    {
        // Basic pattern conversion: replace * with glob wildcard
        // Note: This is a simple implementation. More complex patterns might need more robust handling.
        $globPattern = str_replace('*', '*', $pattern);
        $files = glob($this->cacheDir . '/' . md5($globPattern) . '.cache');

        if ($files === false) {
            // Handle glob error, e.g., log it
            // $this->logger->error('Glob pattern failed', ['pattern' => $this->cacheDir . '/' . md5($globPattern) . '.cache']);
            return; // Comply with void return
        }

        foreach ($files as $file) {
            if (is_file($file)) {
                // unlink() raises E_WARNING on failure
                unlink($file);
            }
        }
    }
}