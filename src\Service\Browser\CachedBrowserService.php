<?php

namespace App\Service\Browser;

use App\Service\Cache\CacheServiceInterface;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Session;

/**
 * Cached implementation of BrowserService
 */
class CachedBrowserService implements BrowserServiceInterface
{
    private BrowserServiceInterface $innerService;
    private CacheServiceInterface $cacheService;

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $innerService Inner browser service
     * @param CacheServiceInterface $cacheService Cache service
     */
    public function __construct(BrowserServiceInterface $innerService, CacheServiceInterface $cacheService)
    {
        $this->innerService = $innerService;
        $this->cacheService = $cacheService;
    }

    /**
     * {@inheritdoc}
     */
    public function visit(string $url): void
    {
        $this->innerService->visit($url);
    }

    /**
     * {@inheritdoc}
     */
    public function waitForElementVisible(string $selector, int $timeout = 30): bool
    {
        return $this->innerService->waitForElementVisible($selector, $timeout);
    }

    /**
     * {@inheritdoc}
     */
    public function scrollToElement(string $selector): void
    {
        $this->innerService->scrollToElement($selector);
    }

    /**
     * {@inheritdoc}
     */
    public function clickElement(string $selector): void
    {
        $this->innerService->clickElement($selector);
    }

    /**
     * {@inheritdoc}
     */
    public function fillField(string $selector, string $value): void
    {
        $this->innerService->fillField($selector, $value);
    }

    /**
     * {@inheritdoc}
     */
    public function selectOption(string $selector, string $value): void
    {
        $this->innerService->selectOption($selector, $value);
    }

    /**
     * {@inheritdoc}
     */
    public function getElementText(string $selector): string
    {
        $cacheKey = 'element_text_' . md5($selector);

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $text = $this->innerService->getElementText($selector);

        // Cache the value for 5 seconds
        $this->cacheService->set($cacheKey, $text, 5);

        return $text;
    }

    /**
     * {@inheritdoc}
     */
    public function isElementVisible(string $selector): bool
    {
        $cacheKey = 'element_visible_' . md5($selector);

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $isVisible = $this->innerService->isElementVisible($selector);

        // Cache the value for 2 seconds
        $this->cacheService->set($cacheKey, $isVisible, 2);

        return $isVisible;
    }

    /**
     * {@inheritdoc}
     */
    public function waitForPageToLoad(int $timeout = 30): void
    {
        $this->innerService->waitForPageToLoad($timeout);
    }

    /**
     * {@inheritdoc}
     */
    public function takeScreenshot(?string $name = null): string
    {
        return $this->innerService->takeScreenshot($name);
    }

    /**
     * {@inheritdoc}
     */
    public function getSession(): Session
    {
        return $this->innerService->getSession();
    }

    /**
     * {@inheritdoc}
     */
    public function waitForElement(string $selector, int $timeout = 30): void
    {
        $this->innerService->waitForElement($selector, $timeout);
    }

    /**
     * {@inheritdoc}
     */
    public function executeScript(string $script)
    {
        return $this->innerService->executeScript($script);
    }

    /**
     * {@inheritdoc}
     */
    public function findElements(string $selector): array
    {
        // This is not a good candidate for caching as elements are tied to the current page state
        // and can become stale quickly
        return $this->innerService->findElements($selector);
    }

    /**
     * {@inheritdoc}
     */
    public function waitForDocumentReady(int $timeout = 30): void
    {
        $this->innerService->waitForDocumentReady($timeout);
    }

    /**
     * {@inheritdoc}
     */
    public function waitForAjaxToComplete(int $timeout = 30): void
    {
        $this->innerService->waitForAjaxToComplete($timeout);
    }

    /**
     * {@inheritdoc}
     */
    public function elementExists(string $selector): bool
    {
        $cacheKey = 'element_exists_' . md5($selector);

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $exists = $this->innerService->elementExists($selector);

        // Cache the value for 2 seconds
        $this->cacheService->set($cacheKey, $exists, 2);

        return $exists;
    }

    /**
     * {@inheritdoc}
     */
    public function wait(int $seconds): void
    {
        $this->innerService->wait($seconds);
    }

    /**
     * {@inheritdoc}
     */
    public function isSessionActive(): bool
    {
        return $this->innerService->isSessionActive();
    }

    /**
     * {@inheritdoc}
     */
    public function getDriverType(): string
    {
        return $this->innerService->getDriverType();
    }

    /**
     * {@inheritdoc}
     */
    public function hasContent(string $text): bool
    {
        $cacheKey = 'has_content_' . md5($text);

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $hasContent = $this->innerService->hasContent($text);

        // Cache the value for 2 seconds
        $this->cacheService->set($cacheKey, $hasContent, 2);

        return $hasContent;
    }

    /**
     * {@inheritdoc}
     */
    public function navigateBack(): void
    {
        $this->innerService->navigateBack();
    }

    /**
     * {@inheritdoc}
     */
    public function getPageTitle(): string
    {
        $cacheKey = 'page_title';

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $title = $this->innerService->getPageTitle();

        // Cache the value for 2 seconds
        $this->cacheService->set($cacheKey, $title, 2);

        return $title;
    }

    /**
     * {@inheritdoc}
     */
    public function waitForUrlContains(string $text, int $timeout = 30): bool
    {
        return $this->innerService->waitForUrlContains($text, $timeout);
    }

    /**
     * {@inheritdoc}
     */
    public function isBrowserStackSession(): bool
    {
        return $this->innerService->isBrowserStackSession();
    }

    /**
     * {@inheritdoc}
     */
    public function findElement(string $selector): ?NodeElement
    {
        // This is not a good candidate for caching as elements are tied to the current page state
        // and can become stale quickly
        return $this->innerService->findElement($selector);
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentUrl(): string
    {
        $cacheKey = 'current_url';

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $url = $this->innerService->getCurrentUrl();

        // Cache the value for 1 second
        $this->cacheService->set($cacheKey, $url, 1);

        return $url;
    }

    /**
     * {@inheritdoc}
     */
    public function waitForRedirect(int $timeout = 60, int $maxRedirects = 5, int $stabilityDuration = 2): array
    {
        // Not caching this method as it's a long-running operation that tracks state changes
        return $this->innerService->waitForRedirect($timeout, $maxRedirects, $stabilityDuration);
    }
}