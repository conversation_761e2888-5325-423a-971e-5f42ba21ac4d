# 01 Overview

The **Project BrowserStack + Behat Scaffold** provides a modular, flexible, and extensible testing framework to run
behavior‑driven tests against web applications. It integrates:

- **Behat** (BDD framework) for writing human‑readable feature scenarios.
- **Mink** (browser abstraction) to drive real or remote browsers.
- **BrowserStack** cloud grid for cross‑browser testing (Windows 10 + Chrome).
- **Symfony DependencyInjection** container to wire contexts, services, and page objects.
- **FriendsOfBehat PageObjectExtension** for a clean page‑object pattern.
- **YAML fixtures** per scenario for test data management.

Purpose:

- Reduce time to set up a robust test suite.
- Enforce best practices via configuration and conventions.
- Allow seamless local and CI execution.

Key goals:

1. Isolated DI‑driven contexts and page objects.
2. Reusable service adapters (e.g. MinkBrowserService).
3. Profiles for local and BrowserStack runs.
4. CI/CD and Docker support for reproducible environments.

Next: Dive into the architecture and file structure in [02 Architecture](02-architecture.md). 