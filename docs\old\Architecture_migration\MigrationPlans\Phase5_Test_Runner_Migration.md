# Phase 5: Test Runner Migration

## Service-Oriented Test Architecture Migration

**Version:** 1.0
**Last Updated:** 2025-05-20
**Author:** AI Assistant

## Table of Contents

1. [Overview](#1-overview)
2. [Current Test Runner Analysis](#2-current-test-runner-analysis)
3. [Test Runner Service Interface](#3-test-runner-service-interface)
4. [Test Runner Service Implementation](#4-test-runner-service-implementation)
5. [Command Line Interface](#5-command-line-interface)
6. [Reporting Service](#6-reporting-service)
7. [Environment Service](#7-environment-service)
8. [Implementation Plan](#8-implementation-plan)
9. [Testing Strategy](#9-testing-strategy)
10. [Rollback Plan](#10-rollback-plan)

---

## 1. Overview

Phase 5 focuses on migrating the existing test runner script to use the new service-oriented architecture. This phase
builds upon the foundation established in Phase 1, the core services implemented in Phase 2, the context migration
completed in Phase 3, and the page object migration completed in Phase 4. The goal is to replace the direct dependencies
in the test runner with proper service injection and implement a more modular and extensible test execution framework.

### 1.1 Objectives

- Create a TestRunnerService interface and implementation
- Implement a new command-line interface for the test runner
- Add support for service container initialization
- Implement a reporting service for test results
- Ensure backward compatibility with existing test execution workflows
- Provide comprehensive tests for the test runner

### 1.2 Timeline

- Estimated duration: 30 min
- Dependencies: Phase 1 (Foundation Setup), Phase 2 (Core Services Implementation), Phase 3 (Context Migration), Phase
  4 (Page Object Migration)

---

## 2. Current Test Runner Analysis

Before migrating the test runner, we need to understand the current implementation to ensure proper migration and
backward compatibility.

### 2.1 Current Implementation

The current test runner is implemented in `bin/run-tests.php` and has the following characteristics:

- Implemented as a standalone script with a `TestRunner` class
- Direct dependencies on `BrandContext`, `TestDataContext`, `ConfigurationManager`, and `TestDataRegistry`
- Command-line argument parsing using `getopt()`
- Environment variable management
- Support for running tests for all products or a specific product
- Support for dry-run mode
- Basic logging of test execution

### 2.2 Current Limitations

The current implementation has several limitations:

- Direct instantiation of services and contexts
- No dependency injection
- Limited extensibility
- No structured reporting
- Limited error handling and recovery
- No support for parallel test execution
- No integration with the service container

### 2.3 Migration Requirements

To successfully migrate the test runner, we need to:

1. Create a service interface for the test runner
2. Implement the service using dependency injection
3. Create a new command-line interface that initializes the service container
4. Ensure backward compatibility with existing command-line arguments
5. Add support for structured reporting
6. Implement proper error handling and recovery

---

## 3. Test Runner Service Interface

### 3.1 Interface Definition

The `TestRunnerServiceInterface` will define the contract for the test runner service:

```php
namespace App\Service\TestRunner;

interface TestRunnerServiceInterface
{
    /**
     * Run tests for all products
     *
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runAllProducts(): int;

    /**
     * Run tests for a specific product
     *
     * @param string $productSlug Product slug
     * @param bool $dryRun Whether to perform a dry run
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runSingleProduct(string $productSlug, bool $dryRun = false): int;

    /**
     * Run tests with specific tags
     *
     * @param string $tags Comma-separated list of tags
     * @param bool $dryRun Whether to perform a dry run
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runWithTags(string $tags, bool $dryRun = false): int;

    /**
     * Run tests for a specific feature file
     *
     * @param string $featureFile Path to feature file
     * @param bool $dryRun Whether to perform a dry run
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runFeature(string $featureFile, bool $dryRun = false): int;

    /**
     * Get the current test execution configuration
     *
     * @return array Test execution configuration
     */
    public function getConfiguration(): array;
}
```

### 3.2 Dependencies

The test runner service will depend on the following services:

- `ConfigurationServiceInterface` - For brand and environment configuration
- `TestDataServiceInterface` - For test data management
- `ReportingServiceInterface` - For test result reporting
- `EnvironmentServiceInterface` - For environment variable management

---

## 4. Test Runner Service Implementation

### 4.1 Service Implementation

The `TestRunnerService` will implement the `TestRunnerServiceInterface` and use the service container for dependency
injection:

```php
namespace App\Service\TestRunner;

use App\Service\AbstractService;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\Reporting\ReportingServiceInterface;
use App\Service\Environment\EnvironmentServiceInterface;
use Symfony\Component\Process\Process;

class TestRunnerService extends AbstractService implements TestRunnerServiceInterface
{
    private ConfigurationServiceInterface $configService;
    private TestDataServiceInterface $dataService;
    private ReportingServiceInterface $reportingService;
    private EnvironmentServiceInterface $envService;
    private string $projectRoot;
    private array $baseCmd;

    public function __construct(
        ConfigurationServiceInterface $configService,
        TestDataServiceInterface $dataService,
        ReportingServiceInterface $reportingService,
        EnvironmentServiceInterface $envService,
        string $projectRoot
    ) {
        $this->configService = $configService;
        $this->dataService = $dataService;
        $this->reportingService = $reportingService;
        $this->envService = $envService;
        $this->projectRoot = $projectRoot;

        // Initialize base command
        $behatBin = implode(DIRECTORY_SEPARATOR, ['vendor', 'bin', 'behat']);
        if (!file_exists($this->projectRoot . DIRECTORY_SEPARATOR . $behatBin)) {
            throw new \RuntimeException("Behat binary not found at: $behatBin");
        }

        $this->baseCmd = [$behatBin];
    }

    public function runAllProducts(): int
    {
        // Implementation details
    }

    public function runSingleProduct(string $productSlug, bool $dryRun = false): int
    {
        // Implementation details
    }

    public function runWithTags(string $tags, bool $dryRun = false): int
    {
        // Implementation details
    }

    public function runFeature(string $featureFile, bool $dryRun = false): int
    {
        // Implementation details
    }

    public function getConfiguration(): array
    {
        // Implementation details
    }

    private function executeCommand(array $cmdArray, string $description): int
    {
        // Implementation details
    }
}
```

### 4.2 Service Registration

The test runner service will be registered in the service container:

```yaml
# config/services.yml
services:
  # Test Runner Service
  App\Service\TestRunner\TestRunnerServiceInterface:
    alias: App\Service\TestRunner\TestRunnerService

  App\Service\TestRunner\TestRunnerService:
    arguments:
      $projectRoot: '%app.project_root%'
    public: true
```

---

## 5. Command Line Interface

### 5.1 New Command Line Interface

A new command-line interface will be created to initialize the service container and execute tests:

```php
#!/usr/bin/env php
<?php
// bin/run-tests-new.php

require __DIR__ . '/../vendor/autoload.php';

use App\Service\TestRunner\TestRunnerServiceInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

// Initialize container
$container = new ContainerBuilder();
$loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/../config'));
$loader->load('services.yml');
$container->compile();

// Parse command line options
$options = getopt('', ['brand::', 'env::', 'tags::', 'product::', 'feature::', 'all-products', 'dry-run', 'report::', 'format::']);

// Get test runner service
$testRunner = $container->get(TestRunnerServiceInterface::class);

// Run tests
try {
    $dryRun = isset($options['dry-run']);

    if (isset($options['all-products'])) {
        exit($testRunner->runAllProducts());
    } elseif (isset($options['tags'])) {
        exit($testRunner->runWithTags($options['tags'], $dryRun));
    } elseif (isset($options['feature'])) {
        exit($testRunner->runFeature($options['feature'], $dryRun));
    } else {
        $product = $options['product'] ?? 'total_harmony';
        exit($testRunner->runSingleProduct($product, $dryRun));
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
```

### 5.2 Backward Compatibility

To ensure backward compatibility, the original `bin/run-tests.php` script will be updated to use the new service-based
implementation:

```php
#!/usr/bin/env php
<?php
// bin/run-tests.php

require __DIR__ . '/../vendor/autoload.php';

use App\Service\TestRunner\TestRunnerServiceInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

// Initialize container
$container = new ContainerBuilder();
$loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/../config'));
$loader->load('services.yml');
$container->compile();

// Parse command line options (keep original format)
$options = getopt('', ['brand::', 'env::', 'tags::', 'product::', 'all-products', 'dry-run']);

// Get test runner service
$testRunner = $container->get(TestRunnerServiceInterface::class);

// Run tests using the same logic as before
try {
    $dryRun = isset($options['dry-run']);

    if (isset($options['all-products'])) {
        exit($testRunner->runAllProducts());
    } elseif (isset($options['tags'])) {
        exit($testRunner->runWithTags($options['tags'], $dryRun));
    } else {
        $product = $options['product'] ?? 'total_harmony';
        exit($testRunner->runSingleProduct($product, $dryRun));
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
```

---

## 6. Reporting Service

### 6.1 Reporting Service Interface

A new reporting service will be created to handle test result reporting:

```php
namespace App\Service\Reporting;

interface ReportingServiceInterface
{
    /**
     * Initialize a test report
     *
     * @param string $name Report name
     * @param array $metadata Report metadata
     * @return void
     */
    public function initReport(string $name, array $metadata = []): void;

    /**
     * Record a test result
     *
     * @param string $name Test name
     * @param bool $success Whether the test succeeded
     * @param array $metadata Test metadata
     * @return void
     */
    public function recordResult(string $name, bool $success, array $metadata = []): void;

    /**
     * Record an error
     *
     * @param string $message Error message
     * @param \Throwable|null $exception Exception if available
     * @return void
     */
    public function recordError(string $message, \Throwable $exception = null): void;

    /**
     * Generate a report
     *
     * @param string $format Report format (html, json, xml)
     * @param string|null $outputPath Output path
     * @return string Report content
     */
    public function generateReport(string $format = 'html', string $outputPath = null): string;
}
```

### 6.2 Reporting Service Implementation

The reporting service will be implemented to handle different report formats:

```php
namespace App\Service\Reporting;

use App\Service\AbstractService;

class ReportingService extends AbstractService implements ReportingServiceInterface
{
    private string $reportName;
    private array $reportMetadata;
    private array $results = [];
    private array $errors = [];
    private string $projectRoot;

    public function __construct(string $projectRoot)
    {
        $this->projectRoot = $projectRoot;
    }

    public function initReport(string $name, array $metadata = []): void
    {
        // Implementation details
    }

    public function recordResult(string $name, bool $success, array $metadata = []): void
    {
        // Implementation details
    }

    public function recordError(string $message, \Throwable $exception = null): void
    {
        // Implementation details
    }

    public function generateReport(string $format = 'html', string $outputPath = null): string
    {
        // Implementation details
    }

    private function generateHtmlReport(): string
    {
        // Implementation details
    }

    private function generateJsonReport(): string
    {
        // Implementation details
    }

    private function generateXmlReport(): string
    {
        // Implementation details
    }
}
```

### 6.3 Service Registration

The reporting service will be registered in the service container:

```yaml
# config/services.yml
services:
  # Reporting Service
  App\Service\Reporting\ReportingServiceInterface:
    alias: App\Service\Reporting\ReportingService

  App\Service\Reporting\ReportingService:
    arguments:
      $projectRoot: '%app.project_root%'
    public: true
```

---

## 7. Environment Service

### 7.1 Environment Service Interface

A new environment service will be created to handle environment variable management:

```php
namespace App\Service\Environment;

interface EnvironmentServiceInterface
{
    /**
     * Set an environment variable
     *
     * @param string $name Variable name
     * @param string $value Variable value
     * @return void
     */
    public function setVariable(string $name, string $value): void;

    /**
     * Get an environment variable
     *
     * @param string $name Variable name
     * @param string|null $default Default value
     * @return string|null Variable value
     */
    public function getVariable(string $name, string $default = null): ?string;

    /**
     * Check if an environment variable exists
     *
     * @param string $name Variable name
     * @return bool Whether the variable exists
     */
    public function hasVariable(string $name): bool;

    /**
     * Load environment variables from a file
     *
     * @param string $filePath Path to environment file
     * @return void
     */
    public function loadFromFile(string $filePath): void;

    /**
     * Get all environment variables
     *
     * @return array All environment variables
     */
    public function getAllVariables(): array;
}
```

### 7.2 Environment Service Implementation

The environment service will be implemented to handle environment variables:

```php
namespace App\Service\Environment;

use App\Service\AbstractService;

class EnvironmentService extends AbstractService implements EnvironmentServiceInterface
{
    private array $variables = [];

    public function __construct()
    {
        // Initialize with current environment variables
        $this->variables = getenv();
    }

    public function setVariable(string $name, string $value): void
    {
        // Implementation details
    }

    public function getVariable(string $name, string $default = null): ?string
    {
        // Implementation details
    }

    public function hasVariable(string $name): bool
    {
        // Implementation details
    }

    public function loadFromFile(string $filePath): void
    {
        // Implementation details
    }

    public function getAllVariables(): array
    {
        // Implementation details
    }
}
```

### 7.3 Service Registration

The environment service will be registered in the service container:

```yaml
# config/services.yml
services:
  # Environment Service
  App\Service\Environment\EnvironmentServiceInterface:
    alias: App\Service\Environment\EnvironmentService

  App\Service\Environment\EnvironmentService:
    public: true
```

---

## 8. Implementation Plan

### 8.1 Phase 1: Create Service Interfaces

1. Create `TestRunnerServiceInterface`
2. Create `ReportingServiceInterface`
3. Create `EnvironmentServiceInterface`
4. Update service container configuration

### 8.2 Phase 2: Implement Services

1. Implement `TestRunnerService`
2. Implement `ReportingService`
3. Implement `EnvironmentService`
4. Write unit tests for each service

### 8.3 Phase 3: Create Command Line Interface

1. Create `bin/run-tests-new.php`
2. Update `bin/run-tests.php` to use the new services
3. Add support for additional command-line arguments
4. Write integration tests for the command-line interface

### 8.4 Phase 4: Add Advanced Features

1. Implement parallel test execution
2. Add support for different report formats
3. Implement test result aggregation
4. Add support for test filtering

### 8.5 Phase 5: Documentation and Training

1. Update documentation with new test runner usage
2. Create examples for common test execution scenarios
3. Provide training for team members

---

## 9. Testing Strategy

### 9.1 Unit Testing

- Create unit tests for all service implementations
- Test each service in isolation with mocked dependencies
- Aim for high code coverage (>80%)

### 9.2 Integration Testing

- Test service interactions
- Verify container configuration
- Test command-line interface

### 9.3 Functional Testing

- Run existing tests with the new test runner
- Compare results with the previous implementation
- Verify all features still work as expected

### 9.4 Performance Testing

- Measure test execution time before and after migration
- Profile memory usage
- Identify and address bottlenecks

---

## 10. Rollback Plan

### 10.1 Rollback Triggers

- Critical functionality broken
- Significant performance degradation
- Incompatibility with existing tests

### 10.2 Rollback Process

1. Revert to the original `bin/run-tests.php` script
2. Remove the new service implementations
3. Update documentation to reflect the rollback
4. Document issues for future resolution

---

## 11. Appendix: Code Examples

### 11.1 Example: Running Tests with the New Test Runner

```bash
# Run all products for a brand
./bin/run-tests-new --brand=aeons --env=stage --all-products

# Run specific product tests
./bin/run-tests-new --brand=aeons --env=stage --product=golden_harvest

# Run tests with specific tags
./bin/run-tests-new --brand=aeons --env=stage --tags=@smoke_one_time

# Run a specific feature file
./bin/run-tests-new --brand=aeons --env=stage --feature=features/salesFunnel.feature

# Generate a report
./bin/run-tests-new --brand=aeons --env=stage --product=golden_harvest --report=report.html --format=html
```

### 11.2 Example: Using the Test Runner Service in Custom Scripts

```php
<?php
// custom-test-script.php

require __DIR__ . '/vendor/autoload.php';

use App\Service\TestRunner\TestRunnerServiceInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

// Initialize container
$container = new ContainerBuilder();
$loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/config'));
$loader->load('services.yml');
$container->compile();

// Get test runner service
$testRunner = $container->get(TestRunnerServiceInterface::class);

// Run custom test sequence
$testRunner->runWithTags('@smoke_one_time', false);
$testRunner->runWithTags('@regression', false);
$testRunner->runFeature('features/custom_flow.feature', false);
```

### 11.3 Example: Service Container Configuration

```yaml
# config/services.yml
parameters:
  app.project_root: '%paths.base%'
  app.config_dir: '%app.project_root%/config'
  app.fixtures_dir: '%app.project_root%/features/fixtures'

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false
    bind:
      $projectRoot: '%app.project_root%'
      $configDir: '%app.config_dir%'
      $fixturesDir: '%app.fixtures_dir%'

  # Test Runner Service
  App\Service\TestRunner\TestRunnerServiceInterface:
    alias: App\Service\TestRunner\TestRunnerService

  App\Service\TestRunner\TestRunnerService:
    arguments:
      $projectRoot: '%app.project_root%'
    public: true

  # Reporting Service
  App\Service\Reporting\ReportingServiceInterface:
    alias: App\Service\Reporting\ReportingService

  App\Service\Reporting\ReportingService:
    arguments:
      $projectRoot: '%app.project_root%'
    public: true

  # Environment Service
  App\Service\Environment\EnvironmentServiceInterface:
    alias: App\Service\Environment\EnvironmentService

  App\Service\Environment\EnvironmentService:
    public: true
```