# Page Objects

This directory contains page objects for the test framework. Page objects are used to encapsulate the interaction with
web pages, providing a higher-level API for tests to use.

## Available Page Objects

- `HomePage`: Handles actions on the main landing page
- `ProductPage`: Handles actions on the product detail page
- `CartPage`: Handles actions on the shopping cart page
- `CheckoutPage`: Handles actions on the checkout page
- `ConfirmationPage`: Handles actions on the order confirmation page
- `PayPalPage`: Handles actions on the PayPal payment page
- `Stripe3DSPage`: Handles actions on the Stripe 3D Secure authentication page
- `UpsellPage`: Handles actions on the upsell page

## Page Elements

- `SearchForm`: Handles search functionality

## Usage

Page objects can be used in two ways:

### 1. Using the PageFactory Service

```php
// In a context class
public function __construct(PageFactoryInterface $pageFactory)
{
    $this->pageFactory = $pageFactory;
}

public function someStep()
{
    $homePage = $this->pageFactory->getPage('HomePage');
    $homePage->open();
    $homePage->searchForProduct('Total Harmony');
}
```

### 2. Direct Instantiation

```php
// In a context class
public function __construct(BrowserServiceInterface $browserService)
{
    $this->browserService = $browserService;
}

public function someStep()
{
    $homePage = new HomePage($this->browserService);
    $homePage->open();
    $homePage->searchForProduct('Total Harmony');
}
```

## Creating New Page Objects

To create a new page object:

1. Create a new class in the `src/Page` directory
2. Extend the `BasePage` class
3. Implement the `verifyPage` method
4. Add methods for interacting with the page

Example:

```php
<?php

namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;

class MyNewPage extends BasePage
{
    protected string $path = '/my-new-page';

    protected function verifyPage(): void
    {
        $this->waitForElementVisible('.my-page-element');
    }

    public function doSomething(): void
    {
        $this->clickElement('.my-button');
        $this->waitForPageToLoad();
    }
}
```

## Testing Page Objects

Page objects can be tested using PHPUnit. See `tests/Page/HomePageTest.php` for an example.

## Backward Compatibility

For backward compatibility with existing code, the following adapters are provided:

- `LegacyPageAdapter`: Adapts new page objects to the old interface
- `LegacyPageFactoryAdapter`: Adapts the new page factory to the old interface

These adapters allow existing code to continue working while new code can use the new page objects.
