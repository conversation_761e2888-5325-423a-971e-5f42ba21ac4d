<?php

namespace App\Service\Configuration;

/**
 * Interface for parameter validator service
 */
interface ParameterValidatorInterface
{
    /**
     * Validate parameters
     *
     * @param array $parameters Parameters to validate
     * @return bool True if parameters are valid
     * @throws \RuntimeException If parameters are invalid
     */
    public function validateParameters(array $parameters): bool;
}