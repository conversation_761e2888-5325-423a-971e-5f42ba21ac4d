services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: true
  # Page Factory
  App\Service\Page\PageFactoryInterface:
    alias: App\Service\Page\PageFactory

  App\Service\Page\PageFactory:
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true

  # Page Factory is now the primary implementation
  # Legacy adapters have been removed

  # Page Objects
  App\Page\:
    resource: '../../src/Page/*'
    exclude: '../../src/Page/{Base,Element}/*'
    public: true
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    tags: [ 'page.service' ]

  # Page Elements
  App\Page\Element\:
    resource: '../../src/Page/Element/*'
    public: true
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
    tags: [ 'page.element' ]

  # Specific Page Objects with Additional Dependencies
  App\Page\UpsellPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]

  App\Page\ProductPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]

  App\Page\CheckoutPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]
