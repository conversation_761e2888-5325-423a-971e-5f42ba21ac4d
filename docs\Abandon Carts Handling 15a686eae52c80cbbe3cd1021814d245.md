# Abandon Carts Handling

For abandon carts we have the following (more or less complex) flow (from the application code point of view):

There are cron commands that handle abandon carts:

1. **app:abandon-carts:call-center** (runs every 15 minutes for all orders older than 15 minutes)
2. **app:abandon-carts:send-convertkit** (runs every 10 minutes for all orders older than 190 minutes)

The commands do almost the same, while the first command additionally informs the call-center agents about the abandon carts.

The second command historically was created first, and at the moment could be removed (as there are no orders for it because the first command processes all eligible orders earlier) - but it remains in our [Cron / App commands](https://www.notion.so/Cron-App-commands-104686eae52c803b8ae8e5f8b8043d71?pvs=21) for historical reasons yet.

# The app:abandon-carts:call-center command

Source code: [https://gitlab.com/samtos/crm-core/blob/master/src/Command/SendAbandonCartsToCallCenterCommand.php#L36](https://gitlab.com/samtos/crm-core/blob/master/src/Command/SendAbandonCartsToCallCenterCommand.php#L36)

## The Orders are qualified as abandon carts if (expand to see):

### 1. Order State and Payment State

An order must meet **one of the following combinations**:

- **Combination 1**:
    - Order state is `NEW`.
    - Payment state is `AWAITING_PAYMENT`.
- **Combination 2**:
    - Order state is `CART`.
    - At least one payment exists with a state of `FAILED`.
- **Combination 3**:
    - Order state is `CART`.
    - At least one payment exists with a state of `CART`.

### 2. Payment Method

At least one payment method must belong to the following:

- `credit-card`
- `paypal`

### 3. Additional Constraints

The following conditions must also be satisfied for an order to be considered:

- The order is **not synced with ConvertKit** (`********************** = false`).
- The order is **not created from another order** (`createdFromOrder = null`) (means IT’S NOT a Reorder for an existing subscription).
- The order was **last updated on or before the terminal date** provided (`updatedAt <= terminalDate`).
- The order has a customer associated with it (`customer is not null`).
- The order does **not originate from another abandoned cart** (`originalAbandonCart = null`).
- The order is **not created by an admin** (`isCreatedByAdmin = false`).

### 4. Channel-Specific Conditions

The order must belong to the (some) channel.

## Processing Flow

### 1. Retrieve Abandon Carts

- Orders are fetched using the `OrderRepository::findCallCenterAbandonCarts` method, based on the channel and terminal date provided (see the filtering logic in the section above).

### 2. Iterate Over Abandon Carts

For each cart:

1. **Skip Empty Carts**: Orders with no items are ignored.
2. **Sales Funnel Check**:
    - If the order is a sales funnel:
        - Check if any payment is in `AUTHORIZED` or `COMPLETED` state.
        - Remove payments in `CART` state.
        - Transition the order to `CREATED` state if it is still in `CART` state.
        - Transition through checkout stages and send a confirmation email if the payment state is `PAID`.
        - Log and skip further processing if a successful payment exists.

### 3. Create Shop User

- A shop user is created for the order if it doesn't already exist.
- Log a warning if the shop user is missing billing information.

### 4. Check for Recent Paid Orders

- Evaluate customer orders within the last month:
    - Skip tagging as abandoned if a recent paid order exists (completed within the last three days).

### 5. Tag as Abandoned

- Add an "abandon cart" tag to eligible orders.
- Mark the cart as abandoned and synced with ConvertKit (`********************** = true`).

### 6. Handle Payments

- Check the state of the last payment.
- If the payment state is not `FAILED`, dispatch a `PAYMENT_FAILED` event.

### 7. Send Notifications

- Dispatch `CART_ABANDONED` event for the cart - means send a request to ConvertKit to send an email to the customer.
- Dispatch a message to the call center with the cart ID.

## Expected Outputs

1. **Logs**:
    - Skipped orders (e.g., "Order id: X; Skipped due to 1 successful payment").
    - Successfully processed orders (e.g., "Order id: X; Synced with ConvertKit").
    - Errors (e.g., "Order id: X; [error message]").
2. **Email Notifications**:
    - Confirmation emails for orders with successful payments.
3. **Call Center Notifications**:
    - Abandoned carts are sent to the call center queue.

## Possible Testing Steps

### Step 1: Prepare Test Data

- Create orders in various states (`NEW`, `CART`) with different payment scenarios:
    - No payments.
    - Payments in `FAILED`, `CART`, `AUTHORIZED`, or `COMPLETED` states.
- Associate orders with customers:
    - Customers with recent paid orders.
    - Customers without recent paid orders.
- Use orders tagged as sales funnel and non-sales funnel.

### Step 2: Run the Command

- Execute the command with appropriate arguments:
    
    ```
    bin/console app:abandon-carts:call-center <channel-code> --last-updated-before="-2hours"
    ```
    

### Step 3: Validate Outputs

1. **Database Changes**:
    - Verify that abandoned carts are tagged and synced with ConvertKit.
    - Check for updates in order states and payment states.
2. **Event Dispatches**:
    - Confirm `CART_ABANDONED` and `PAYMENT_FAILED` events are dispatched.
    - Ensure `SendOrderToCallCenterMessage` is queued.
3. **Logs**:
    - Review logs for errors and confirmation of processed orders.
4. **Emails**:
    - Validate that confirmation emails are sent for fully paid orders.

## Possible Edge Cases to Test

1. Orders with no items.
2. Orders with partially paid or failed payments.
3. Customers with and without recent paid orders.
4. Orders associated with unsupported channels.
5. Orders created or updated after the terminal date.

## Debugging Tips

- Use the logs to identify skipped or errored orders.
- Check the database state before and after command execution for discrepancies.
- Validate that dispatched messages reach their destinations in the queue.