<?php

namespace Features\Bootstrap\Context;

use ElementNotFoundException;
use Exception;
use Features\Bootstrap\Page\CartPage;
use Features\Bootstrap\Page\ProductPage;
use Features\Bootstrap\Page\CheckoutPage;
use Features\Bootstrap\SharedDataContext;
use RuntimeException;
use SensioLabs\Behat\PageObjectExtension\Context\PageObjectContext;

/**
 * Handles cart-related actions and assertions
 */
class CartContext extends PageObjectContext
{
    private CartPage $cartPage;
    private ProductPage $productPage;
    private CheckoutPage $checkoutPage;
    private ?SharedDataContext $sharedData = null;

    public function __construct(
        CartPage $cartPage = null,
        ProductPage $productPage = null,
        CheckoutPage $checkoutPage = null,
        SharedDataContext $sharedData = null
    ) {
        $this->cartPage = $cartPage ?? new CartPage();
        $this->productPage = $productPage ?? new ProductPage();
        $this->checkoutPage = $checkoutPage ?? new CheckoutPage();
        $this->sharedData = $sharedData ?? SharedDataContext::getInstance();
    }

    // Rest of the file remains the same...
} 