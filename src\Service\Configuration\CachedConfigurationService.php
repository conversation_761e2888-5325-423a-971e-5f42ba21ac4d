<?php

namespace App\Service\Configuration;

use App\Service\Cache\CacheServiceInterface;

/**
 * Cached implementation of ConfigurationService
 */
class CachedConfigurationService implements ConfigurationServiceInterface
{
    private ConfigurationServiceInterface $innerService;
    private CacheServiceInterface $cacheService;

    /**
     * Constructor
     *
     * @param ConfigurationServiceInterface $innerService Inner configuration service
     * @param CacheServiceInterface $cacheService Cache service
     */
    public function __construct(ConfigurationServiceInterface $innerService, CacheServiceInterface $cacheService)
    {
        $this->innerService = $innerService;
        $this->cacheService = $cacheService;
    }

    /**
     * {@inheritdoc}
     */
    public function getEnvironmentConfig(string $key = null)
    {
        $cacheKey = 'env_config_' . ($key ?? 'all');

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $config = $this->innerService->getEnvironmentConfig($key);

        // Cache the value for 60 seconds
        $this->cacheService->set($cacheKey, $config, 60);

        return $config;
    }

    /**
     * {@inheritdoc}
     */
    public function getTestConfig(string $key = null)
    {
        $cacheKey = 'test_config_' . ($key ?? 'all');

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $config = $this->innerService->getTestConfig($key);

        // Cache the value for 60 seconds
        $this->cacheService->set($cacheKey, $config, 60);

        return $config;
    }

    /**
     * {@inheritdoc}
     */
    public function getFeatureConfig(string $featureName, string $key = null)
    {
        $cacheKey = 'feature_config_' . $featureName . '_' . ($key ?? 'all');

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $config = $this->innerService->getFeatureConfig($featureName, $key);

        // Cache the value for 60 seconds
        $this->cacheService->set($cacheKey, $config, 60);

        return $config;
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentBrand(): string
    {
        return $this->innerService->getCurrentBrand();
    }

    /**
     * {@inheritdoc}
     */
    public function setBrand(string $brand): void
    {
        $this->innerService->setBrand($brand);

        // Clear cache when brand changes
        $this->clearBrandRelatedCache();
    }

    /**
     * Clear cache entries related to brand configuration
     */
    private function clearBrandRelatedCache(): void
    {
        // Clear all brand-related cache entries
        // In a more sophisticated implementation, we might only clear specific keys
        // For now, we'll clear all cache entries that start with 'brand_config_'
        $this->cacheService->clear();
    }

    /**
     * {@inheritdoc}
     */
    public function getBrandConfig(string $key)
    {
        $cacheKey = 'brand_config_' . $key;

        // Check if we have a cached value
        if ($this->cacheService->has($cacheKey)) {
            return $this->cacheService->get($cacheKey);
        }

        // Get the value from the inner service
        $config = $this->innerService->getBrandConfig($key);

        // Cache the value for 60 seconds
        $this->cacheService->set($cacheKey, $config, 60);

        return $config;
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentEnvironment(): string
    {
        return $this->innerService->getCurrentEnvironment();
    }

    /**
     * {@inheritdoc}
     */
    public function setEnvironment(string $environment): void
    {
        $this->innerService->setEnvironment($environment);

        // Clear cache when environment changes
        $this->clearEnvironmentRelatedCache();
    }

    /**
     * Clear cache entries related to environment configuration
     */
    private function clearEnvironmentRelatedCache(): void
    {
        // Clear all environment-related cache entries
        // In a more sophisticated implementation, we might only clear specific keys
        // For now, we'll clear all cache entries that start with 'env_config_'
        $this->cacheService->clear();
    }
}