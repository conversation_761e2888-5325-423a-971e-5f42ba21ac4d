<?php

namespace App\Service\Data;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Service for loading critical test data
 */
class CriticalTestDataService extends AbstractService implements TestDataServiceInterface
{
    private array $testData = [];
    private array $cache = [];
    private string $criticalDataFile;

    /**
     * Constructor
     *
     * @param string $criticalDataFile Path to critical test data file
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        string $criticalDataFile,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->criticalDataFile = $criticalDataFile;
        $this->logInfo(sprintf("Initializing CriticalTestDataService with data file: %s", $criticalDataFile));

        // Load the critical data file
        $this->loadCriticalData();
    }

    /**
     * Load critical test data from file
     */
    private function loadCriticalData(): void
    {
        if (!file_exists($this->criticalDataFile)) {
            $this->logError(sprintf("Critical test data file not found: %s", $this->criticalDataFile));
            throw new RuntimeException(sprintf('Critical test data file not found: %s', $this->criticalDataFile));
        }

        try {
            $this->logInfo(sprintf("Loading critical test data from: %s", $this->criticalDataFile));
            $criticalData = Yaml::parseFile($this->criticalDataFile);

            // Register funnel data directly
            if (isset($criticalData['funnel_items'])) {
                foreach ($criticalData['funnel_items'] as $key => $item) {
                    if (isset($item['entry']['url'])) {
                        $funnelKey = $item['entry']['url'];
                        $this->registerData($funnelKey, $item);
                        $this->registerData('funnel.' . $funnelKey, $item);
                        $this->logInfo(sprintf("Registered funnel data with key: %s", $funnelKey));
                    }
                }
            }

            // Pre-register product data
            if (isset($criticalData['products'])) {
                foreach ($criticalData['products'] as $key => $product) {
                    $this->registerData('product.' . $key, $product);
                    $this->logInfo(sprintf("Registered product data with key: %s", $key));
                }
            }

            // Pre-register user data
            if (isset($criticalData['test_users'])) {
                foreach ($criticalData['test_users'] as $key => $user) {
                    $this->registerData('user.' . $key, $user);
                    $this->logInfo(sprintf("Registered user data with key: %s", $key));
                }
            }

            // Pre-register payment methods
            if (isset($criticalData['payment_methods'])) {
                foreach ($criticalData['payment_methods'] as $key => $method) {
                    $this->registerData('payment_method.' . $key, $method);
                    $this->logInfo(sprintf("Registered payment method with key: %s", $key));
                }
            }

            // Pre-register shipping methods
            if (isset($criticalData['shipping_methods'])) {
                foreach ($criticalData['shipping_methods'] as $key => $method) {
                    $this->registerData('shipping_method.' . $key, $method);
                    $this->logInfo(sprintf("Registered shipping method with key: %s", $key));
                }
            }

            // Store the entire data set for later use
            $this->testData['_critical_data'] = $criticalData;
            $this->logInfo("Critical test data loaded successfully");
        } catch (\Exception $e) {
            $this->logError(sprintf("Error loading critical test data: %s", $e->getMessage()), $e);
            throw new RuntimeException(sprintf('Error loading critical test data: %s', $e->getMessage()), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        $cacheKey = sprintf('%s_%s_%s', $brand, $type, $key ?? 'all');
        $this->logInfo(sprintf("Loading test data - Brand: %s, Type: %s, Key: %s", $brand, $type, $key ?? 'all'));

        if (isset($this->cache[$cacheKey])) {
            $this->logInfo(sprintf("Returning cached data for key: %s", $cacheKey));
            return $this->cache[$cacheKey];
        }

        // Special handling for funnel data
        if ($type === 'funnel' && $key !== null) {
            // Try direct key lookup first
            if ($this->hasData($key)) {
                $data = $this->getData($key);
                $this->logInfo(sprintf("Returning critical funnel data for %s", $key));
                $this->cache[$cacheKey] = $data;
                return $data;
            }

            // Try with funnel prefix
            if ($this->hasData('funnel.' . $key)) {
                $data = $this->getData('funnel.' . $key);
                $this->logInfo(sprintf("Returning critical funnel data for funnel.%s", $key));
                $this->cache[$cacheKey] = $data;
                return $data;
            }

            // Try to find it in the critical data
            if (isset($this->testData['_critical_data']['funnel_items'])) {
                foreach ($this->testData['_critical_data']['funnel_items'] as $itemKey => $item) {
                    if (isset($item['entry']['url']) && $item['entry']['url'] === $key) {
                        $this->logInfo(sprintf("Found %s in critical data funnel_items", $key));
                        $this->cache[$cacheKey] = $item;
                        return $item;
                    }
                }
            }
        }

        // For other types, try to find in the critical data
        if (isset($this->testData['_critical_data'][$type])) {
            if ($key && isset($this->testData['_critical_data'][$type][$key])) {
                $data = $this->testData['_critical_data'][$type][$key];
                $this->logInfo(sprintf("Found %s.%s in critical data", $type, $key));
                $this->cache[$cacheKey] = $data;
                return $data;
            } elseif (!$key) {
                $data = $this->testData['_critical_data'][$type];
                $this->logInfo(sprintf("Found %s in critical data", $type));
                $this->cache[$cacheKey] = $data;
                return $data;
            }
        }

        // Not found in critical data
        $this->logError(sprintf("Test data not found for Brand: %s, Type: %s, Key: %s", $brand, $type, $key ?? 'all'));
        throw new RuntimeException(
            sprintf('Test data not found for Brand: %s, Type: %s, Key: %s', $brand, $type, $key ?? 'all')
        );
    }

    /**
     * {@inheritdoc}
     */
    public function registerData(string $key, array $data): void
    {
        $this->logInfo(sprintf("Registering data with key: %s", $key));
        $this->testData[$key] = $data;
    }

    /**
     * {@inheritdoc}
     */
    public function getData(string $key)
    {
        if (!isset($this->testData[$key])) {
            $this->logError(sprintf("Test data not found for key: %s", $key));
            throw new RuntimeException(
                sprintf('Test data not found for key: %s', $key)
            );
        }

        return $this->testData[$key];
    }

    /**
     * {@inheritdoc}
     */
    public function hasData(string $key): bool
    {
        return isset($this->testData[$key]);
    }
}
