<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Page\Base\BasePageInterface;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for validating service dependencies
 */
class ValidateServicesContext extends BaseContext
{
    private SharedStateServiceInterface $stateService;
    private PageFactoryInterface $pageFactory;
    private BrowserServiceInterface $browserService;
    private ConfigurationServiceInterface $configService;
    private ?BasePageInterface $lastRequestedPage = null;

    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     * @param SharedStateServiceInterface $stateService Shared state service
     * @param PageFactoryInterface $pageFactory Page factory
     * @param BrowserServiceInterface $browserService Browser service
     * @param ConfigurationServiceInterface $configService Configuration service
     */
    public function __construct(
        ContainerInterface            $container,
        SharedStateServiceInterface   $stateService,
        PageFactoryInterface          $pageFactory,
        BrowserServiceInterface       $browserService,
        ConfigurationServiceInterface $configService
    )
    {
        parent::__construct($container);
        $this->stateService = $stateService;
        $this->pageFactory = $pageFactory;
        $this->browserService = $browserService;
        $this->configService = $configService;

        $this->logInfo("ValidateServicesContext initialized");
    }

    /**
     * @When I set :key as :value in the shared state
     */
    public function iSetKeyAsValueInTheSharedState(string $key, string $value): void
    {
        try {
            $this->stateService->set($key, $value);
            $this->logInfo(sprintf("Set '%s' = '%s' in shared state", $key, $value));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to set '%s' in shared state", $key), $e);
            throw new \RuntimeException(
                sprintf("Failed to set '%s' in shared state: %s", $key, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should be able to retrieve :key from the shared state
     */
    public function iShouldBeAbleToRetrieveFromTheSharedState(string $key): void
    {
        try {
            if (!$this->stateService->has($key)) {
                throw new \RuntimeException(sprintf("Key '%s' not found in shared state", $key));
            }
            $this->logInfo(sprintf("Key '%s' exists in shared state", $key));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to check for '%s' in shared state", $key), $e);
            throw new \RuntimeException(
                sprintf("Failed to check for '%s' in shared state: %s", $key, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then the value of :key should be :expectedValue
     */
    public function theValueOfShouldBe(string $key, string $expectedValue): void
    {
        try {
            $actualValue = $this->stateService->get($key);

            if ($actualValue !== $expectedValue) {
                throw new \RuntimeException(
                    sprintf("Value mismatch for key '%s': expected '%s', got '%s'", $key, $expectedValue, $actualValue)
                );
            }

            $this->logInfo(sprintf("Value for '%s' is correct: '%s'", $key, $expectedValue));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to validate value for '%s'", $key), $e);
            throw new \RuntimeException(
                sprintf("Failed to validate value for '%s': %s", $key, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When I request the :pageName from the page factory
     */
    public function iRequestThePageFromThePageFactory(string $pageName): void
    {
        try {
            $this->lastRequestedPage = $this->pageFactory->getPage($pageName);
            $this->logInfo(sprintf("Requested page '%s' from page factory", $pageName));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to request page '%s' from page factory", $pageName), $e);
            throw new \RuntimeException(
                sprintf("Failed to request page '%s' from page factory: %s", $pageName, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should receive a valid page object
     */
    public function iShouldReceiveAValidPageObject(): void
    {
        try {
            if (!$this->lastRequestedPage) {
                throw new \RuntimeException("No page has been requested yet");
            }

            if (!$this->lastRequestedPage instanceof BasePageInterface) {
                throw new \RuntimeException(
                    sprintf(
                        "Expected a BasePageInterface instance, got %s",
                        get_class($this->lastRequestedPage)
                    )
                );
            }

            $this->logInfo("Received a valid page object: " . get_class($this->lastRequestedPage));
        } catch (\Throwable $e) {
            $this->logError("Failed to validate page object", $e);
            throw new \RuntimeException(
                sprintf("Failed to validate page object: %s", $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should be able to navigate to the homepage
     */
    public function iShouldBeAbleToNavigateToTheHomepage(): void
    {
        try {
            if (!$this->lastRequestedPage) {
                throw new \RuntimeException("No page has been requested yet");
            }

            $this->lastRequestedPage->open();
            $this->logInfo("Navigated to the homepage using page object");
        } catch (\Throwable $e) {
            $this->logError("Failed to navigate to the homepage", $e);
            throw new \RuntimeException(
                sprintf("Failed to navigate to the homepage: %s", $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should be able to take a screenshot
     */
    public function iShouldBeAbleToTakeAScreenshot(): void
    {
        try {
            $screenshotPath = $this->browserService->takeScreenshot('service_validation');
            $this->logInfo(sprintf("Screenshot taken: %s", $screenshotPath));
        } catch (\Throwable $e) {
            $this->logError("Failed to take screenshot", $e);
            throw new \RuntimeException(
                sprintf("Failed to take screenshot: %s", $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should be able to check for elements
     */
    public function iShouldBeAbleToCheckForElements(): void
    {
        try {
            $bodyExists = $this->browserService->elementExists('body');
            if (!$bodyExists) {
                throw new \RuntimeException("Could not find 'body' element on the page");
            }

            $this->logInfo("Successfully checked for 'body' element");
        } catch (\Throwable $e) {
            $this->logError("Failed to check for elements", $e);
            throw new \RuntimeException(
                sprintf("Failed to check for elements: %s", $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should be able to execute JavaScript
     */
    public function iShouldBeAbleToExecuteJavaScript(): void
    {
        try {
            $title = $this->browserService->executeScript('return document.title;');
            $this->logInfo(sprintf("Successfully executed JavaScript and got: %s", $title));
        } catch (\Throwable $e) {
            $this->logError("Failed to execute JavaScript", $e);
            throw new \RuntimeException(
                sprintf("Failed to execute JavaScript: %s", $e->getMessage()),
                0,
                $e
            );
        }
    }
} 