# Traceability Matrix

This document provides traceability between feature files, step definitions, and page objects in the new architecture.

## Feature to Context Mapping

| Feature File | Context Class | Step Definitions |
|--------------|--------------|------------------|
| checkout.feature | CheckoutContext | I am on the checkout page<br>I enter my shipping information<br>I enter my billing information<br>I select payment method<br>I complete the checkout |
| cart.feature | CartContext | I add product to cart<br>I update product quantity<br>I remove product from cart<br>I view my cart |
| product.feature | ProductContext | I am on the product page<br>I select product options<br>I view product details<br>I add product to wishlist |
| payment.feature | PaymentContext | I enter my credit card details<br>I complete payment with PayPal<br>I verify payment confirmation<br>I see payment error message |
| upsell.feature | UpsellContext | I am shown an upsell offer<br>I accept the upsell offer<br>I decline the upsell offer<br>I see upsell confirmation |
| email.feature | EmailContext | I should receive a confirmation email<br>I should receive an order receipt<br>I verify the email contains order details<br>I click the link in the email |
| brand.feature | BrandContext | I am on the brand page<br>I select a brand<br>I filter products by brand<br>I view brand details |
| validation.feature | ValidationContext | I enter invalid email format<br>I enter invalid phone number<br>I enter invalid credit card<br>I see validation error message |
| admin.feature | AdminCommandContext | I log in as admin<br>I view order details<br>I update order status<br>I process refund |
| abandoned_cart.feature | AbandonedCartContext | I abandon my cart<br>I return to my abandoned cart<br>I complete checkout from abandoned cart<br>I receive abandoned cart email |

## Context to Page Object Mapping

| Context Class | Page Objects Used |
|--------------|-------------------|
| CheckoutContext | CheckoutPage, ConfirmationPage |
| CartContext | CartPage, ProductPage |
| ProductContext | ProductPage, HomePage |
| PaymentContext | CheckoutPage, PayPalPage, Stripe3DSPage, ConfirmationPage |
| UpsellContext | UpsellPage, ConfirmationPage |
| EmailContext | N/A (Uses services directly) |
| BrandContext | HomePage, ProductPage |
| ValidationContext | CheckoutPage, CartPage |
| AdminCommandContext | N/A (Uses services directly) |
| AbandonedCartContext | CartPage, CheckoutPage |

## Page Object to Service Mapping

| Page Object | Services Used |
|-------------|--------------|
| BasePage | BrowserService, LoggingService |
| HomePage | BrowserService, LoggingService |
| ProductPage | BrowserService, LoggingService, TestDataService |
| CartPage | BrowserService, LoggingService, ValidationService |
| CheckoutPage | BrowserService, LoggingService, ValidationService, ConfigurationService |
| ConfirmationPage | BrowserService, LoggingService |
| PayPalPage | BrowserService, LoggingService, ConfigurationService |
| Stripe3DSPage | BrowserService, LoggingService, ConfigurationService |
| UpsellPage | BrowserService, LoggingService, SharedStateService |

## Step Definition to Method Mapping

| Step Definition | Context Method | Page Method |
|-----------------|---------------|-------------|
| I am on the checkout page | CheckoutContext::iAmOnTheCheckoutPage | CheckoutPage::visit |
| I enter my shipping information | CheckoutContext::iEnterMyShippingInformation | CheckoutPage::fillShippingForm |
| I enter my billing information | CheckoutContext::iEnterMyBillingInformation | CheckoutPage::fillBillingForm |
| I select payment method | CheckoutContext::iSelectPaymentMethod | CheckoutPage::selectPaymentMethod |
| I complete the checkout | CheckoutContext::iCompleteTheCheckout | CheckoutPage::submitOrder |
| I add product to cart | CartContext::iAddProductToCart | ProductPage::addToCart |
| I update product quantity | CartContext::iUpdateProductQuantity | CartPage::updateQuantity |
| I remove product from cart | CartContext::iRemoveProductFromCart | CartPage::removeItem |
| I view my cart | CartContext::iViewMyCart | CartPage::visit |
| I am on the product page | ProductContext::iAmOnTheProductPage | ProductPage::visit |
| I select product options | ProductContext::iSelectProductOptions | ProductPage::selectOptions |
| I view product details | ProductContext::iViewProductDetails | ProductPage::viewDetails |
| I add product to wishlist | ProductContext::iAddProductToWishlist | ProductPage::addToWishlist |
| I enter my credit card details | PaymentContext::iEnterMyCreditCardDetails | CheckoutPage::fillCreditCardForm |
| I complete payment with PayPal | PaymentContext::iCompletePaymentWithPayPal | PayPalPage::completePayment |
| I verify payment confirmation | PaymentContext::iVerifyPaymentConfirmation | ConfirmationPage::verifyPaymentConfirmation |
| I see payment error message | PaymentContext::iSeePaymentErrorMessage | CheckoutPage::getErrorMessage |
| I am shown an upsell offer | UpsellContext::iAmShownAnUpsellOffer | UpsellPage::isUpsellOfferVisible |
| I accept the upsell offer | UpsellContext::iAcceptTheUpsellOffer | UpsellPage::acceptOffer |
| I decline the upsell offer | UpsellContext::iDeclineTheUpsellOffer | UpsellPage::declineOffer |
| I see upsell confirmation | UpsellContext::iSeeUpsellConfirmation | UpsellPage::verifyConfirmation |

## Service Method Usage

| Service | Method | Used By |
|---------|--------|---------|
| BrowserService | visit | All page objects |
| BrowserService | waitForElementVisible | All page objects |
| BrowserService | clickElement | All page objects |
| BrowserService | fillField | CheckoutPage, CartPage, ProductPage |
| BrowserService | selectOption | CheckoutPage, ProductPage |
| BrowserService | getElementText | All page objects |
| BrowserService | isElementVisible | All page objects |
| BrowserService | waitForPageToLoad | All page objects |
| BrowserService | takeScreenshot | FeatureContext |
| ConfigurationService | getEnvironmentConfig | Multiple contexts |
| ConfigurationService | getTestConfig | Multiple contexts |
| ConfigurationService | getFeatureConfig | Multiple contexts |
| LoggingService | logInfo | All contexts and page objects |
| LoggingService | logWarning | All contexts and page objects |
| LoggingService | logError | All contexts and page objects |
| LoggingService | logStep | FeatureContext |
| SharedStateService | set | All contexts |
| SharedStateService | get | All contexts |
| SharedStateService | getAll | Multiple contexts |
| SharedStateService | reset | FeatureContext |
| TestDataService | getTestData | Multiple contexts |
| TestDataService | setTestData | Multiple contexts |
| TestDataService | loadTestDataFromFile | FeatureContext |
| TestDataService | generateRandomData | Multiple contexts |
| ValidationService | validateEmail | ValidationContext, CheckoutContext |
| ValidationService | validatePhone | ValidationContext, CheckoutContext |
| ValidationService | validateCreditCard | ValidationContext, PaymentContext |
| ValidationService | validateAddress | ValidationContext, CheckoutContext |
| ValidationService | getValidationErrors | ValidationContext, CheckoutContext |

## Conclusion

This traceability matrix provides a comprehensive mapping between feature files, step definitions, page objects, and services in the new architecture. It can be used to:

1. Understand the relationships between different components
2. Identify dependencies between components
3. Trace requirements to implementation
4. Plan changes to the system
5. Assess the impact of changes
