<?php

namespace Features\Bootstrap\Tools;

use Symfony\Component\DomCrawler\Crawler;
use GuzzleHttp\Client;
use InvalidArgumentException;

class SelectorUpdater
{
    private Client $client;
    private string $baseUrl;
    private array $selectorMapping;

    public function __construct(string $baseUrl)
    {
        $this->client = new Client([
            'verify' => false,
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'Aeons-Test-Automation/1.0'
            ]
        ]);
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->initializeSelectorMapping();
    }

    /**
     * Scans a page and updates selectors in the page object class
     */
    public function scanAndUpdate(string $url, string $pageObjectPath): array
    {
        $selectors = $this->scanPageSelectors($url);
        $this->validateSelectors($selectors);
        return $this->updatePageObjectSelectors($pageObjectPath, $selectors);
    }

    /**
     * Scans the page and extracts all relevant selectors
     */
    private function scanPageSelectors(string $url): array
    {
        $response = $this->client->get($this->baseUrl . '/' . ltrim($url, '/'));
        $html = (string) $response->getBody();
        $crawler = new Crawler($html);

        $selectors = [];

        foreach ($this->selectorMapping as $type => $config) {
            $elements = $crawler->filter($config['selector']);
            if ($elements->count() > 0) {
                $element = $elements->getNode(0);
                if ($element instanceof \DOMElement) {
                    $selectors[$type] = $this->generateOptimalSelector($element);
                }
            }
        }

        return $selectors;
    }

    /**
     * Validates the extracted selectors
     */
    private function validateSelectors(array $selectors): void
    {
        foreach ($selectors as $type => $selector) {
            if (!isset($this->selectorMapping[$type])) {
                throw new InvalidArgumentException("Unknown selector type: $type");
            }

            if (!$this->isValidCssSelector($selector)) {
                throw new InvalidArgumentException("Invalid CSS selector for $type: $selector");
            }
        }
    }

    /**
     * Updates the page object class file with new selectors
     */
    private function updatePageObjectSelectors(string $pageObjectPath, array $newSelectors): array
    {
        if (!file_exists($pageObjectPath)) {
            throw new \RuntimeException("Page object file not found: $pageObjectPath");
        }

        $content = file_get_contents($pageObjectPath);
        $changes = [];

        if (preg_match('/private\s+const\s+SELECTORS\s*=\s*\[(.*?)\];/s', $content, $matches)) {
            $currentSelectors = $this->parseSelectorsArray($matches[1]);
            
            foreach ($newSelectors as $type => $selector) {
                if (!isset($currentSelectors[$type]) || $currentSelectors[$type] !== $selector) {
                    $changes[$type] = [
                        'old' => $currentSelectors[$type] ?? null,
                        'new' => $selector
                    ];
                    $currentSelectors[$type] = $selector;
                }
            }

            $newSelectorsCode = $this->generateSelectorsArray($currentSelectors);
            $newContent = preg_replace(
                '/private\s+const\s+SELECTORS\s*=\s*\[(.*?)\];/s',
                "private const SELECTORS = [$newSelectorsCode];",
                $content
            );

            file_put_contents($pageObjectPath, $newContent);
        }

        return $changes;
    }

    /**
     * Initializes the mapping between selector types and their identification rules
     */
    private function initializeSelectorMapping(): void
    {
        $this->selectorMapping = [
            'QUANTITY' => [
                'selector' => '.set-quantity.active',
                'attributes' => ['data-value']
            ],
            'PURCHASE_TYPE' => [
                'selector' => '.purchase-option.active .product-variant-label-info',
                'attributes' => ['class']
            ],
            'PRODUCT_NAME' => [
                'selector' => '.content-grid .title',
                'attributes' => ['class']
            ],
            'PRICE' => [
                'selector' => 'button.add-product #product-price',
                'attributes' => ['id']
            ],
            'RESTRICTIONS' => [
                'selector' => '.product-restrictions .warning',
                'attributes' => ['class']
            ],
            'FLAVOR' => [
                'selector' => '.flavor',
                'attributes' => ['data-option-value-code']
            ]
        ];
    }

    /**
     * Generates an optimal CSS selector for an element
     */
    private function generateOptimalSelector(\DOMElement $element): string
    {
        // Try ID first
        if ($element->hasAttribute('id')) {
            return '#' . $element->getAttribute('id');
        }

        // Try data attributes
        foreach ($element->attributes as $attr) {
            if (strpos($attr->name, 'data-') === 0) {
                return "[{$attr->name}=\"{$attr->value}\"]";
            }
        }

        // Use classes
        if ($element->hasAttribute('class')) {
            $classes = array_filter(explode(' ', $element->getAttribute('class')));
            if (!empty($classes)) {
                return '.' . implode('.', $classes);
            }
        }

        // Fallback to tag name and position
        $tag = $element->tagName;
        $parent = $element->parentNode;
        
        if ($parent instanceof \DOMElement) {
            $siblings = $this->getSiblingElements($element);
            if (count($siblings) > 1) {
                $position = array_search($element, $siblings, true) + 1;
                return "{$tag}:nth-child({$position})";
            }
        }

        return $tag;
    }

    /**
     * Gets all sibling elements of the same type
     */
    private function getSiblingElements(\DOMElement $element): array
    {
        $siblings = [];
        $parent = $element->parentNode;
        
        if ($parent) {
            foreach ($parent->childNodes as $child) {
                if ($child instanceof \DOMElement && $child->tagName === $element->tagName) {
                    $siblings[] = $child;
                }
            }
        }
        
        return $siblings;
    }

    /**
     * Checks if a CSS selector is valid
     */
    private function isValidCssSelector(string $selector): bool
    {
        try {
            $crawler = new Crawler('<div></div>');
            $crawler->filter($selector);
            return true;
        } catch (\InvalidArgumentException $e) {
            return false;
        }
    }

    /**
     * Parses the SELECTORS array from the class file
     */
    private function parseSelectorsArray(string $selectorsString): array
    {
        $selectors = [];
        preg_match_all("/'([^']+)'\s*=>\s*'([^']+)'/", $selectorsString, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $selectors[$match[1]] = $match[2];
        }
        
        return $selectors;
    }

    /**
     * Generates the PHP code for the SELECTORS array
     */
    private function generateSelectorsArray(array $selectors): string
    {
        $lines = [];
        foreach ($selectors as $key => $value) {
            $lines[] = "        '$key' => '$value'";
        }
        return "\n" . implode(",\n", $lines) . "\n    ";
    }
} 