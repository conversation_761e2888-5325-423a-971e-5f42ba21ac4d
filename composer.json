{"name": "malaberg/test-framework", "description": "E-commerce test automation framework", "type": "project", "license": "proprietary", "config": {"bin-dir": "vendor/bin", "optimize-autoloader": true, "preferred-stable": true, "minimum-stability": "dev", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "infection/extension-installer": true, "cweagans/composer-patches": true}}, "autoload": {"psr-4": {"Features\\Bootstrap\\": "features/bootstrap/", "Malaberg\\Test\\": "lib/", "App\\": "src/"}, "classmap": ["features/bootstrap/ContainerExtension.php", "features/bootstrap/ContextInitializer.php"]}, "scripts": {"test": "@php bin/run-tests.php", "test:aeons:smoke:stage:golden": "@php bin/run-tests.php --brand=aeons --env=stage --product=golden_harvest --tags=@smoke_one_time", "test:aeons:stage": "@php bin/run-tests.php --brand=aeons --env=stage", "test:aeons:dev": "@php bin/run-tests.php --brand=aeons --env=dev", "test:content:dev": "@php bin/run-tests.php --brand=aeons --env=dev --tags=@content --all-products", "test:content:stage": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@content --all-products", "test:content:prod": "@php bin/run-tests.php --brand=aeons --env=prod --tags=@content --all-products", "test:content:dev:golden": "@php bin/run-tests.php --brand=aeons --env=dev --tags=@content --product=golden_harvest", "test:coupon:dev:golden": "@php bin/run-tests.php --brand=aeons --env=dev --tags=@coupon --product=golden_harvest", "test:content:stage:golden": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@content --product=golden_harvest", "test:content:prod:golden": "@php bin/run-tests.php --brand=aeons --env=prod --tags=@content --product=golden_harvest", "test:paypal:aeons:stage": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@paypal", "test:paypal:aeons:stage:positive": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@paypal,@positive", "test:paypal:aeons:stage:negative": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@paypal,@negative", "test:paypal:aeons:stage:golden": "@php bin/run-tests.php --brand=aeons --env=stage --product=golden_harvest --tags=@paypal", "test:paypal:aeons:stage:golden:positive": "@php bin/run-tests.php --brand=aeons --env=stage --product=golden_harvest --tags=@paypal,@positive", "test:paypal:aeons:stage:golden:negative": "@php bin/run-tests.php --brand=aeons --env=stage --product=golden_harvest --tags=@paypal,@negative", "test:3ds:aeons:stage:golden": "@php bin/run-tests.php --brand=aeons --env=stage --product=golden_harvest --tags=@3ds", "test:subscription:aeons:stage": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@subscription", "test:subscription:aeons:stage:golden": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@subscription --product=golden_harvest", "test:subscription:aeons:stage:smoke": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@subscription,@smoke", "test:coupon:aeons:stage:golden": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@coupon --product=golden_harvest", "test:subscription:aeons:stage:regression": "@php bin/run-tests.php --brand=aeons --env=stage --tags=@subscription,@regression", "test:subscription:aeons:dev": "@php bin/run-tests.php --brand=aeons --env=dev --tags=@subscription", "test:subscription:aeons:dev:smoke": "@php bin/run-tests.php --brand=aeons --env=dev --tags=@subscription,@smoke", "test:subscription:aeons:dev:regression": "@php bin/run-tests.php --brand=aeons --env=dev --tags=@subscription,@regression", "test:order:aeons:stage:golden:positive": "@php bin/run-tests.php --brand=aeons --env=stage --product=golden_harvest --tags=@smoke_one_time", "test:paypal:smoke:stage": ["@test:paypal:aeons:stage:positive", "@test:paypal:aeons:stage:golden:positive"], "test:paypal:regression:stage": ["@test:paypal:aeons:stage", "@test:paypal:aeons:stage:golden"], "test:behat": "behat --colors --format=progress", "test:unit": "phpunit --testdox --colors=always", "test:mutation": "infection --min-msi=80 --min-covered-msi=80 --threads=4", "test:contexts": "phpunit tests/Context", "test:services": "phpunit tests/Service", "test:pages": "phpunit tests/Page", "test:migration": "@php bin/run-tests.php --tags=@context_migration", "test:migration:comprehensive": "@php bin/run-tests-new.php --tags=@context_migration,@comprehensive", "test:verify-migration": "@php bin/verify-migration.php", "test:benchmark": "@php bin/benchmark.php", "test:sales-funnel:headless": "@php bin/run-tests.php --brand=aeons --env=stage --feature=features/salesFunnel.feature --tags=@high-priority", "test:sales-funnel:headless:dry-run": "@php bin/run-tests.php --brand=aeons --env=stage --feature=features/salesFunnel.feature --tags=@high-priority --dry-run", "analyse": ["phpstan analyse -l 8 src features/bootstrap", "psalm --show-info=true", "phpmd src,features/bootstrap text cleancode,codesize,controversial,design,naming,unusedcode"], "test:3ds:regression:stage": ["@test:3ds:aeons:stage"], "test:subscription:smoke": ["@test:subscription:aeons:stage:smoke", "@test:subscription:aeons:dev:smoke"], "check:mess": "phpmd src,features/bootstrap text cleancode,codesize,controversial,design,naming,unusedcode", "check:duplication": "phpcpd src features/bootstrap", "fix:cs": "php-cs-fixer fix src features/bootstrap --rules=@PSR12", "fix-cs": "php-cs-fixer fix src features/bootstrap --rules=@PSR12", "test:subscription:regression": ["@test:subscription:aeons:stage:regression", "@test:subscription:aeons:dev:regression"], "test:abandoned:aeons:stage:golden": "@php bin/run-tests.php --brand=aeons --env=stage --product=golden_harvest --tags=@abandoned_cart_smoke", "check:compatibility": "php scripts/analyze_compatibility.php .", "cs-check": "php-cs-fixer fix --dry-run --diff", "cs-fix": "php-cs-fixer fix", "stan": "phpstan analyse -l 8 src features/bootstrap", "post-install-cmd": [], "post-update-cmd": []}, "require": {"php": "^8.2", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "friends-of-behat/symfony-extension": "^2.4", "friends-of-behat/mink-extension": "^2.7", "behat/mink": "^1.11.0", "guzzlehttp/guzzle": "^7.9", "nyholm/psr7": "^1.8", "php-webdriver/webdriver": "^1.15", "phpseclib/phpseclib": "^3.0", "psr/container": "^2.0", "psr/log": "^3.0", "symfony/browser-kit": "^6.4", "symfony/config": "^6.4", "symfony/console": "^6.4", "symfony/css-selector": "^6.4", "symfony/dependency-injection": "^6.4", "symfony/dom-crawler": "^6.4", "symfony/event-dispatcher": "^6.4", "symfony/http-client": "^6.4", "symfony/process": "^6.4", "symfony/yaml": "^6.4", "vlucas/phpdotenv": "^5.6", "browserstack/browserstack-local": "^1.1"}, "require-dev": {"behat/behat": "^3.18", "behat/mink-browserkit-driver": "^2.2", "behat/mink-selenium2-driver": "^1.7", "cweagans/composer-patches": "^1.7", "friends-of-behat/page-object-extension": "^0.3.2", "friends-of-behat/service-container-extension": "^1.1", "friends-of-behat/test-context": "^1.3", "friendsofphp/php-cs-fixer": "^3.40", "infection/infection": "^0.27", "php-cs-fixer/shim": "^3.49", "php-parallel-lint/php-parallel-lint": "^1.3", "phpmd/phpmd": "^2.15", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^9.6", "psalm/plugin-phpunit": "^0.19", "sensiolabs/behat-page-object-extension": "^2.3", "shipmonk/composer-dependency-analyser": "^1.8", "symfony/routing": "^6.4", "vimeo/psalm": "^5.18"}, "conflict": {"thecodingmachine/safe": "<2.0"}, "platform": {"php": "8.2.0"}, "repositories": {}}