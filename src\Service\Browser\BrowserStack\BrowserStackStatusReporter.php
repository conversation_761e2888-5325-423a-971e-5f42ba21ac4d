<?php

namespace App\Service\Browser\BrowserStack;

use Behat\Mink\Session;
use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

/**
 * Reporter for BrowserStack test status
 */
class BrowserStackStatusReporter
{
    private LoggerInterface $logger;
    private Client $httpClient;

    /**
     * Constructor
     *
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?LoggerInterface $logger = null)
    {
        $this->logger = $logger ?? new NullLogger();
        $this->httpClient = new Client();
    }

    /**
     * Mark test as passed
     *
     * @param Session $session Mink session
     * @param string|null $reason Reason for passing
     * @return bool Whether the status was reported successfully
     */
    public function markTestPassed(Session $session, ?string $reason = null): bool
    {
        return $this->setSessionStatus($session, 'passed', $reason);
    }

    /**
     * Set session status using JavaScript executor
     *
     * @param Session $session Mink session
     * @param string $status Status (passed or failed)
     * @param string|null $reason Reason for status
     * @return bool Whether the status was reported successfully
     */
    private function setSessionStatus(Session $session, string $status, ?string $reason = null): bool
    {
        if (!$session->isStarted()) {
            $this->logger->warning('Cannot set BrowserStack session status: session not started');
            return false;
        }

        try {
            $script = sprintf(
                'browserstack_executor: {"action": "setSessionStatus", "arguments": {"status":"%s"%s}}',
                $status,
                $reason ? ', "reason": "' . addslashes($reason) . '"' : ''
            );

            $session->getDriver()->executeScript($script);

            $this->logger->info(sprintf(
                'BrowserStack session marked as %s%s',
                $status,
                $reason ? ': ' . $reason : ''
            ));

            return true;
        } catch (\Exception $e) {
            $this->logger->error('Failed to set BrowserStack session status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Mark test as failed
     *
     * @param Session $session Mink session
     * @param string|null $reason Reason for failing
     * @return bool Whether the status was reported successfully
     */
    public function markTestFailed(Session $session, ?string $reason = null): bool
    {
        return $this->setSessionStatus($session, 'failed', $reason);
    }

    /**
     * Set session status using REST API
     *
     * @param string $sessionId BrowserStack session ID
     * @param string $status Status (passed or failed)
     * @param string|null $reason Reason for status
     * @return bool Whether the status was reported successfully
     */
    public function setSessionStatusViaApi(string $sessionId, string $status, ?string $reason = null): bool
    {
        // Get BrowserStack credentials from environment
        $username = getenv('BROWSERSTACK_USERNAME');
        $accessKey = getenv('BROWSERSTACK_ACCESS_KEY');

        if (!$username || !$accessKey) {
            $this->logger->warning('BrowserStack credentials not set. Cannot report session status via API.');
            return false;
        }

        try {
            $url = sprintf(
                'https://%s:%<EMAIL>/automate/sessions/%s.json',
                $username,
                $accessKey,
                $sessionId
            );

            $payload = [
                'status' => $status,
            ];

            if ($reason) {
                $payload['reason'] = $reason;
            }

            $response = $this->httpClient->put($url, [
                'json' => $payload,
            ]);

            if ($response->getStatusCode() === 200) {
                $this->logger->info(sprintf(
                    'BrowserStack session %s marked as %s via API%s',
                    $sessionId,
                    $status,
                    $reason ? ': ' . $reason : ''
                ));
                return true;
            } else {
                $this->logger->warning(sprintf(
                    'Failed to set BrowserStack session status via API: %s',
                    $response->getReasonPhrase()
                ));
                return false;
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to set BrowserStack session status via API: ' . $e->getMessage());
            return false;
        }
    }
}
