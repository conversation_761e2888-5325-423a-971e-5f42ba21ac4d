# Step Definition Migration Registry

This document tracks the migration status of Behat step definitions from the legacy structure (`features/bootstrap/Context/` and `features/bootstrap/FeatureContext.php`) to the new architecture (`src/Context/`).

## Migration Status Summary

| Context | Total Steps | Migrated | Partially Migrated | Not Migrated | Duplicates |
|---------|------------|----------|-------------------|--------------|--------------|
| CartContext | 6 | 6 | 0 | 0 | 0 |
| ProductContext | 11 | 9 | 2 | 0 | 0 |
| BrandContext | 2 | 0 | 1 | 1 | 0 |
| TestDataContext | 2 | 1 | 0 | 1 | 0 |
| EmailContext | 7 | 7 | 0 | 0 | 0 |
| CheckoutContext | 13 | 13 | 0 | 0 | 0 |
| UpsellContext | 11 | 11 | 0 | 0 | 0 |
| SalesFunnelContext | 7 | 7 | 0 | 0 | 0 |
| FeatureContext | 19 | 6 | 3 | 10 | 0 |
| ValidationContext | 10 | 6 | 1 | 3 | 0 |
| PaymentContext | 17 | 17 | 0 | 0 | 0 |
| **Total** | 105 | 83 | 7 | 15 | 0 |
| **Progress** | 100% | 79% | 7% | 14% | 0% |

**Migration Progress**: 86% (90/105 steps migrated or partially migrated)

## Migration Status Legend

- ✅ **Migrated**: Step definition has been successfully migrated to the new architecture
- ⚠️ **Partially Migrated**: Step definition exists in new architecture but may need adjustments
- ❌ **Not Migrated**: Step definition has not been migrated yet
- 🔄 **Duplicate**: Step definition exists in both old and new architecture with similar functionality
- 🛑 **Deprecated**: Step definition that should not be migrated (obsolete)

## CartContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iProceedToCheckout()` | ✅ | `src/Context/CartContext.php` | `cartPage->proceedToCheckout()` | Implementation is similar, using PageFactory instead of direct page object injection |
| `addToCart()` | ✅ | `src/Context/ProductContext.php::iAddTheProductToCart()` | `productPage->addToCart()` | Migrated to ProductContext where it belongs |
| `iViewMyCart()` | ✅ | `src/Context/CartContext.php::iAmOnTheCartPage()` | `cartPage->open()` | Implementation supports both step texts |
| `iRemoveItemFromCart()` | ✅ | `src/Context/CartContext.php::iRemoveItemFromTheCart()` | `cartPage->removeFirstItem()` | Implementation supports both removing first item and by index |
| `theCartShouldBeEmpty()` | ✅ | `src/Context/CartContext.php` | `cartPage->isEmpty()` | Implementation supports negation |
| `theCartShouldContainItems()` | ✅ | `src/Context/CartContext.php::iShouldSeeItemsInTheCart()` | `cartPage->getItemCount()` | Implementation supports both step texts |

## ProductContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iSelectFlavor()` | ✅ | `src/Context/ProductContext.php` | `productPage->selectFlavor()` | Implementation supports both step text patterns |
| `iAmOnTheSpecificProductPage()` | ✅ | `src/Context/ProductContext.php::iAmViewingTheProduct()` | `productPage->open()` or `productPage->loadWithName()` | Implementation supports both product slug and name |
| `iSelectTheQuantityOption()` | ✅ | `src/Context/ProductContext.php::iSelectQuantity()` | `productPage->selectQuantity()` | Implementation handles both string and numeric quantities |
| `iSetTheSupplyTo()` | ✅ | `src/Context/ProductContext.php::iSetTheSupplyTo()` | `productPage->selectSupplyDuration()` | Implemented with proper frequency mapping |
| `userSubscribesToProduct()` | ✅ | `src/Context/ProductContext.php::iSubscribeToProduct()` | `productPage->selectPurchaseType('Subscribe & Save')` | Implemented |
| `userSelectsPurchaseType()` | ✅ | `src/Context/ProductContext.php::iSelectPurchaseType()` | `productPage->selectPurchaseType()` | Implementation handles different purchase type formats |
| `expectedSumOfProductsShouldBeCalculatedCorrectly()` | ✅ | `src/Context/ProductContext.php` | `productPage->getPrice()` | Implemented with price calculation and verification |
| `iVerifyProductContentMatchesConfiguration()` | ✅ | `src/Context/ProductContext.php` | `productPage->hasProductName()`, `productPage->hasContentText()` | Implemented with comprehensive content verification |
| `iSelectThePurchaseType()` | ✅ | `src/Context/ProductContext.php::iSelectPurchaseType()` | `productPage->selectPurchaseType()` | Merged into single implementation with multiple patterns |
| `iSelectQuantity()` | ✅ | `src/Context/ProductContext.php::iSelectQuantity()` | `productPage->selectQuantity()` | Implementation handles both text and numeric inputs |
| `iAmOnTheProductPage()` | ⚠️ | `src/Context/ProductContext.php::iAmViewingTheProduct()` | `productPage->loadWithName()` | Implementation supports no-parameter version but needs current product data |

## BrandContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iAmUsingTheBrand()` | ⚠️ | `src/Context/BrandContext.php::iAmUsingTheDefaultBrandAndEnvironment()` | N/A | Different implementation. New version sets both brand and environment |
| `theBrandHasFeatureEnabled()` | ❌ | N/A | N/A | Not migrated yet. Verifies brand features |

## TestDataContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iLoadTestDataForProduct()` | ✅ | `src/Context/TestDataContext.php` | N/A | Implementation is similar |
| `iLoadPaymentMethods()` | ❌ | N/A | N/A | Not migrated yet. Loads payment method data |

## EmailContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iShouldReceiveAbandonedCartEmailWithinHour()` | ✅ | `src/Context/EmailContext.php` | N/A | Implementation is similar, uses HTTP client directly instead of EmailVerificationTool |
| `iShouldNotReceiveAnyMoreRecoveryEmails()` | ✅ | `src/Context/EmailContext.php` | N/A | Implementation is similar, checks for emails after a specific time point |
| `iClickTheRecoveryLinkInTheEmail()` | ✅ | `src/Context/EmailContext.php` | `browserService->visit()` | Implementation extracts and follows recovery link from email |
| `iFollowTheLinkInTheEmail()` | ✅ | `src/Context/EmailContext.php` | `browserService->visit()` | Implementation extracts and follows any link matching the requested text |
| `iVerifyTheWelcomeEmailContainsAccountCredentials()` | ✅ | `src/Context/EmailContext.php` | N/A | Implementation verifies welcome email content contains login info |
| `iVerifyTheSubscriptionConfirmationEmail()` | ✅ | `src/Context/EmailContext.php` | N/A | Implementation verifies subscription email content with proper validations |
| `iShouldReceiveAnOrderConfirmationEmail()` | ✅ | `src/Context/EmailContext.php` | N/A | Implementation is enhanced to support order number pattern |

## CheckoutContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iFillInTheShippingInformationWith()` | ✅ | `src/Context/CheckoutContext.php` | `checkoutPage->fillShippingInformation()` | Implementation is similar |
| `iChooseToUseTheSameAddressForBilling()` | ✅ | `src/Context/CheckoutContext.php` | `checkoutPage->fillBillingInformation()` | Implementation handles setting billing same as shipping |
| `iEnterThePaymentDetails()` | ✅ | `src/Context/CheckoutContext.php` | `checkoutPage->fillPaymentInformation()` | Implementation handles different payment types |
| `iCompleteThePurchase()` | ✅ | `src/Context/CheckoutContext.php` | `checkoutPage->placeOrder()` | Method is renamed from iConfirmTheOrder() but keeps same step definition |
| `iAmRedirectedToThePayPalSandboxPage()` | ✅ | `src/Context/CheckoutContext.php` | `browserService->waitForUrlContains()` | Implementation checks for PayPal URL |
| `iShouldSeeTheCorrectPaymentAmountInPayPal()` | ✅ | `src/Context/CheckoutContext.php` | `browserService->findElement()` | Implementation verifies amount in PayPal |
| `iVerifyTheProductDetailsAndPricingAreCorrect()` | ✅ | `src/Context/CheckoutContext.php` | N/A | Placeholder implementation for future use |
| `iShouldSeeAnErrorMessageIndicatingTheCardHasExpired()` | ✅ | `src/Context/CheckoutContext.php` | N/A | Placeholder implementation for future use |
| `iVerifyTheOrderTotalIsCorrect()` | ✅ | `src/Context/CheckoutContext.php` | N/A | Placeholder implementation for future use |
| `iShouldRemainOnCheckoutPage()` | ✅ | `src/Context/CheckoutContext.php` | N/A | Placeholder implementation for future use |
| `iLeaveTheCheckoutPage()` | ✅ | `src/Context/CheckoutContext.php` | N/A | Placeholder implementation for future use |
| `iShouldBeOnTheCheckoutPage()` | ✅ | `src/Context/CheckoutContext.php` | `checkoutPage->open()` | Method is renamed from iAmOnTheCheckoutPage() but keeps same step definition |
| `iShouldSeeThatTheCouponIsApplied()` | ✅ | `src/Context/CheckoutContext.php` | N/A | Placeholder implementation for future use |

## UpsellContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iAcceptTheUpsellOffer()` | ✅ | `src/Context/UpsellContext.php` | `upsellPage->acceptOffer()` | Implementation is similar, using PageFactory instead of direct page object injection |
| `iDeclineTheUpsellOffer()` | ✅ | `src/Context/UpsellContext.php` | `upsellPage->declineOffer()` | Implementation is similar |
| `iShouldSeeTheUpsellMessage()` | ✅ | `src/Context/UpsellContext.php` | `upsellPage->getUpsellMessage()` | Implementation verifies upsell message is not empty |
| `iAcceptTheFirstUpsellOffer()` | ✅ | `src/Context/UpsellContext.php` | Calls `iAcceptTheUpsellOffer()` | Implementation sets additional state for first upsell |
| `iAcceptTheSecondUpsellOffer()` | ✅ | `src/Context/UpsellContext.php` | Calls `iAcceptTheUpsellOffer()` | Implementation sets additional state for second upsell |
| `iDeclineTheFirstUpsellOffer()` | ✅ | `src/Context/UpsellContext.php` | Calls `iDeclineTheUpsellOffer()` | Implementation sets additional state for first upsell |
| `iDeclineTheSecondUpsellOffer()` | ✅ | `src/Context/UpsellContext.php` | Calls `iDeclineTheUpsellOffer()` | Implementation sets additional state for second upsell |
| `iClickTheAcceptButtonMultipleTimes()` | ✅ | `src/Context/UpsellContext.php` | `browserService->clickElement()` | Implementation clicks 3 times with short delay between clicks |
| `iVerifyDietaryRestrictionWarningsAreDisplayed()` | ✅ | `src/Context/UpsellContext.php` | `upsellPage->getProductRestrictions()` | Implementation checks each warning from funnel data against displayed restrictions |
| `iShouldBeRedirectedToTheUpsellPage()` | ✅ | `src/Context/UpsellContext.php` | `upsellPage->isOpen()` | Implementation supports position parameter for multi-step funnels |
| `iVerifyOnlyOneUpsellProductIsInTheOrder()` | ✅ | `src/Context/SalesFunnelContext.php` | `confirmationPage->getOrderedProducts()` | Implemented in SalesFunnelContext instead since it requires confirmation page access |

## SalesFunnelContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iAmOnTheMultiStepFunnelPage()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->visit()` | Implementation is similar but uses browserService instead of getSession() |
| `iAmOnTheSubscriptionFunnelPage()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->visit()` | Implementation is similar but uses browserService instead of getSession() |
| `iAmOnTheHighValueFunnelPage()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->visit()` | Implementation is similar but uses browserService instead of getSession() |
| `iVerifyTheFunnelProductDetails()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->getElementText()` | Implementation verifies product name and price from funnel data |
| `theUpsellPageFailsToLoad()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->executeScript("window.stop()")` | Implementation simulates interrupting page load |
| `iCloseTheBrowserWithoutCompletingPayment()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->clearCookies()` | Implementation uses modern approach to simulate browser closing |
| `iCompleteThePurchaseFlowForWith()` | ✅ | `src/Context/SalesFunnelContext.php` | Multiple | New implementation that combines multiple steps into one flow |
| `iVerifyOnlyOneUpsellProductIsInTheOrder()` | ✅ | `src/Context/SalesFunnelContext.php` | `confirmationPage->getOrderedProducts()` | Implemented in SalesFunnelContext instead since it requires confirmation page access |
| `iVerifyBothProductsAreInTheOrder()` | ✅ | `src/Context/SalesFunnelContext.php` | `confirmationPage->getOrderedProducts()` | New implementation added to verify both initial and upsell products |
| `iVerifyTheOrderContainsAllThreeProducts()` | ✅ | `src/Context/SalesFunnelContext.php` | `confirmationPage->getOrderedProducts()` | New implementation added to verify all three products in multi-step funnels |
| `iVerifyTheProductInstructionsContainAllWarnings()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->getElementText()` | New implementation added to verify product warnings |
| `iSelectTheSubscriptionFrequency()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->selectOptionByText()` | New implementation added for subscription frequency selection |
| `iVerifyTheSubscriptionFrequencyIs()` | ✅ | `src/Context/SalesFunnelContext.php` | State verification | New implementation added to verify selected subscription frequency |
| `iHaveAValidDiscountCode()` | ✅ | `src/Context/SalesFunnelContext.php` | State storage | New implementation added to store discount code |
| `iEnterTheDiscountCode()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->fillField()` | New implementation added to enter discount code |
| `theDiscountShouldBeAppliedToTheOrder()` | ✅ | `src/Context/SalesFunnelContext.php` | `browserService->findElement()` | New implementation added to verify discount application |

## FeatureContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iCheckTheDriverType()` | ❌ | N/A | N/A | Not migrated yet. Utility step for debugging |
| `iLoadBrandConfiguration()` | ⚠️ | `src/Context/BrandContext.php::iAmUsingTheDefaultBrandAndEnvironment()` | N/A | Similar functionality but different implementation |
| `iLoadProductData()` | ⚠️ | `src/Context/TestDataContext.php::iLoadTestDataForProduct()` | N/A | Similar functionality but different implementation |
| `iShouldBeRedirectedToTheUpsellPage()` | ❌ | N/A | N/A | Not migrated yet. Verifies redirect to upsell page |
| `iAcceptTheUpsellOffer()` | ❌ | N/A | N/A | Not migrated yet. Placeholder in FeatureContext |
| `iVerifyTheOrderDetailsAreCorrect()` | ❌ | N/A | N/A | Not migrated yet. Placeholder in FeatureContext |
| `verifyBrowserSession()` | ❌ | N/A | N/A | Not migrated yet. Verifies browser session |
| `verifyBrowserStackSession()` | ❌ | N/A | N/A | Not migrated yet. Verifies BrowserStack session |
| `iPressTheButton()` | ❌ | N/A | N/A | Not migrated yet. Handles Stripe 3DS buttons |
| `iDontCompleteTheOrder()` | ❌ | N/A | N/A | Not migrated yet. Abandons order flow |
| `iAmUsingTheBrand()` | ⚠️ | `src/Context/BrandContext.php::iAmUsingTheDefaultBrandAndEnvironment()` | N/A | Similar functionality but different implementation |
| `iLoadTestDataFor()` | ❌ | N/A | N/A | Not migrated yet. Loads generic test data |
| `iLoadProductDataFor()` | ⚠️ | `src/Context/TestDataContext.php::iLoadTestDataForProduct()` | N/A | Similar functionality but different implementation |
| `iAmOnTheHomepage()` | ✅ | `src/Context/FeatureContext.php` | `browserService->visit()` | Implementation is similar but uses browserService instead of getSession() |
| `iTakeAScreenshotNamed()` | ✅ | `src/Context/FeatureContext.php` | `browserService->takeScreenshot()` | Implementation is similar but uses browserService instead of getSession() |
| `iShouldSee()` | ✅ | `src/Context/FeatureContext.php` | `page->hasContent()` | Implementation is similar but uses browserService instead of getSession() |
| `iClickOn()` | ✅ | `src/Context/FeatureContext.php` | `element->click()` | Implementation is similar but uses browserService instead of getSession() |
| `iWaitForSeconds()` | ✅ | `src/Context/FeatureContext.php` | `sleep()` | Implementation is identical |
| `iWaitForElement()` | ✅ | `src/Context/FeatureContext.php` | `browserService->waitForElement()` | Implementation is similar but uses browserService instead of getSession() |

## ValidationContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `handlePageTitle()` | ❌ | N/A | N/A | Not migrated yet. Verifies page title |
| `iVerifyTheCartContainsTheCorrectProductDetails()` | ✅ | `src/Context/ValidationContext.php::iVerifyCartContainsCorrectProductDetails()` | `cartPage->verifyProductName()`, `cartPage->verifyProductQuantity()`, `cartPage->verifyPurchaseType()` | Implementation is similar but with more detailed verification |
| `iVerifyTheOrderTotalIsCorrect()` | ✅ | `src/Context/ValidationContext.php` | `checkoutPage->getOrderTotal()` | Implementation is similar |
| `iVerifyProductContentMatches()` | ✅ | `src/Context/ValidationContext.php` | `productPage->getContentText()` | Implementation is similar but with more robust error handling |
| `calculateExpectedTotal()` | ⚠️ | `src/Context/ValidationContext.php::calculateExpectedTotal()` | N/A | Similar functionality but different implementation. New version uses product data from test data service |
| `iVerifyTheOrderDetailsAreCorrect()` | ✅ | `src/Context/ValidationContext.php` | `confirmationPage->getShippingAddress()` | Implementation is similar but with more robust error handling |
| `handleProductDetails()` | ❌ | N/A | N/A | Not migrated yet. Verifies product details |
| `iVerifyProductContentMatchesConfiguration()` | ❌ | N/A | N/A | Not migrated yet. Complex validation of product content against configuration |
| `iVerifyTheMixedCartOrderDetailsAreCorrect()` | ❌ | N/A | N/A | Not migrated yet. Verifies mixed cart order details |
| `iVerifyTheUrlIs()` | ✅ | `src/Context/ValidationContext.php` | `basePage->getCurrentUrl()` | New implementation in ValidationContext |

## PaymentContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iSelectPaymentMethod()` | ✅ | `src/Context/PaymentContext.php` | `paymentPage->selectPaymentMethod()` | Fully implemented |
| `iFillInCreditCardInformationWith()` | ✅ | `src/Context/PaymentContext.php` | `paymentPage->fillCreditCardInformation()` | Fully implemented with card data loading |
| `iCompleteThePayPalCheckout()` | ✅ | `src/Context/PaymentContext.php` | `paymentPage->completePayPalCheckout()` | Fully implemented |
| `iHandle3DSecureAuthentication()` | ✅ | `src/Context/PaymentContext.php` | `paymentPage->handle3DSecureAuthentication()` | Fully implemented |
| `iSelectThePaymentMethod()` | ✅ | `src/Context/PaymentContext.php` | Calls existing `iSelectPaymentMethod()` | Fully implemented - uses regex pattern |
| `iLogInToPayPalWithCredentials()` | ✅ | `src/Context/PaymentContext.php` | `paymentPage->loginToPayPal()` | Fully implemented with credential loading |
| `iChooseToPayWithPayPal()` | ✅ | `src/Context/PaymentContext.php` | `checkoutPage->choosePayPalPayment()` + `paypalPage->waitForRedirectToPayPal()` | Migrated from legacy FeatureContext |
| `iAmRedirectedToThePayPalSandboxPage()` | ✅ | `src/Context/PaymentContext.php` | `paypalPage->waitForRedirectToPayPal()` | Migrated from legacy FeatureContext |
| `iShouldSeeTheCorrectPaymentAmountInPayPal()` | ✅ | `src/Context/PaymentContext.php` | `paypalPage->getDisplayedAmount()` | Migrated from legacy FeatureContext |
| `iConfirmThePayPalPayment()` | ✅ | `src/Context/PaymentContext.php` | `paypalPage->confirmPayment()` | Migrated from legacy FeatureContext |
| `thePayPalPaymentShouldBeSuccessful()` | ✅ | `src/Context/PaymentContext.php` | `checkoutPage->verifyPayPalPaymentSuccess()` | Migrated from legacy FeatureContext |
| `iShouldSeeAPayPalLoginErrorMessage()` | ✅ | `src/Context/PaymentContext.php` | `paypalPage->hasLoginError()` | Migrated from legacy FeatureContext |
| `iShouldSeeThe3DSPage()` | ✅ | `src/Context/PaymentContext.php` | `stripe3DSPage->is3DSPageDisplayed()` | Migrated from legacy FeatureContext |
| `iPressTheButton()` | ✅ | `src/Context/PaymentContext.php` | `stripe3DSPage->clickCompleteButton()` or `stripe3DSPage->clickFailButton()` | Migrated from legacy FeatureContext |
| `iShouldSeeThePayPalLoginError()` | ✅ | `src/Context/PaymentContext.php` | Calls existing `iShouldSeeAPayPalLoginErrorMessage()` | Migrated from legacy FeatureContext |
| `iLogInToPayPalSandboxWithCredentials()` | ✅ | `src/Context/PaymentContext.php` | `paypalPage->login()` | Migrated from legacy FeatureContext |
| `iEnterPaymentDetails()` | ✅ | `src/Context/PaymentContext.php` | `paymentPage->fillPaymentForm()` | Migrated from legacy FeatureContext |

## General Observations and Recommendations

1. **Inconsistent Step Text**: There are inconsistencies in step text between legacy and new implementations. For example, `iViewMyCart()` vs `iAmOnTheCartPage()`. Consider standardizing step text across the codebase.

2. **Page Object Usage**: 
   - Legacy contexts directly inject page objects
   - New contexts use PageFactory to get page objects
   - Ensure all page object method calls are consistent between old and new implementations

3. **Error Handling**:
   - Both implementations use try/catch blocks
   - New implementation has more standardized error handling
   - Ensure all exceptions are properly caught and logged

4. **State Management**:
   - Legacy uses `stateService->set()`
   - New also uses `stateService->set()`
   - Ensure state keys are consistent between implementations

5. **Migration Priority**:
   - Focus on migrating core step definitions first (cart, product, checkout)
   - Then migrate supporting step definitions (brand, test data)
   - Finally migrate utility step definitions (validation, browser verification)

6. **Duplicate Functionality**:
   - Several step definitions have duplicate functionality with different implementations
   - Standardize on one implementation and remove duplicates

7. **Missing Step Definitions**:
   - Several step definitions from legacy contexts are not yet migrated
   - Create tickets to track migration of these step definitions

## Next Steps

1. Complete the migration of steps from BrandContext and TestDataContext
2. Implement missing steps in CheckoutContext and BrowserContext
3. Standardize step text and implementation across the codebase
4. Remove duplicate step definitions
5. Update feature files to use standardized step text
