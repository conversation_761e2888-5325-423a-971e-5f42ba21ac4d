<?php

namespace Features\Bootstrap;

use App\Service\Data\SimpleCriticalTestDataService;
use App\Service\Data\TestDataServiceInterface;
use Behat\Behat\Context\Context;

/**
 * Context for managing test data
 */
class TestDataContext implements Context
{
    private array $testData = [];
    private ?TestDataServiceInterface $testDataService = null;

    /**
     * Register test data
     *
     * @param string $type Data type
     * @param string $key Data key
     * @param array $data Data
     */
    public function registerTestData(string $type, string $key, array $data): void
    {
        $dataKey = sprintf('%s.%s', $type, $key);
        $this->testData[$dataKey] = $data;
        
        // Also register with the service if available
        if ($this->testDataService !== null) {
            $this->testDataService->registerData($dataKey, $data);
        }
    }

    /**
     * Get test data
     *
     * @param string $type Data type
     * @param string $key Data key
     * @return array|null Test data or null if not found
     */
    public function getTestData(string $type, string $key): ?array
    {
        $dataKey = sprintf('%s.%s', $type, $key);
        
        if (isset($this->testData[$dataKey])) {
            return $this->testData[$dataKey];
        }
        
        // Try to get from the service if available
        if ($this->testDataService !== null && $this->testDataService->hasData($dataKey)) {
            return $this->testDataService->getData($dataKey);
        }
        
        return null;
    }

    /**
     * Set the test data service
     *
     * @param TestDataServiceInterface $testDataService Test data service
     */
    public function setTestDataService(TestDataServiceInterface $testDataService): void
    {
        $this->testDataService = $testDataService;
        
        // Register existing data with the service
        foreach ($this->testData as $key => $data) {
            $testDataService->registerData($key, $data);
        }
    }
}
