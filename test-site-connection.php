<?php
/**
 * Test script to diagnose connectivity to the target website
 * This script performs a detailed analysis of the connection to the test website,
 * including DNS resolution, TCP connectivity, HTTP response, redirects, and security policies.
 */

// Site URL Configuration
$testUrl = getenv('TEST_BASE_URL') ?: 'https://aeonstest.info';
$specificPaths = [
    'root' => '/',
    'funnel' => '/specials/start/natures-gift-basic',
    'sitemap' => '/sitemap.xml',
    'robots' => '/robots.txt'
];

// Timing and retry settings
$maxRetries = 3;
$connectTimeout = 10;
$requestTimeout = 30;

echo "Website Connection Diagnostic Tool\n";
echo "=================================\n\n";
echo "Target site: {$testUrl}\n\n";

// Step 1: DNS Resolution
echo "STEP 1: DNS Resolution\n";
echo "---------------------\n";

$domain = parse_url($testUrl, PHP_URL_HOST);
echo "Resolving hostname: {$domain}...\n";

$dnsStartTime = microtime(true);
$ipAddresses = gethostbynamel($domain);
$dnsTime = round((microtime(true) - $dnsStartTime) * 1000);

if ($ipAddresses === false) {
    echo "❌ DNS resolution failed! Could not resolve {$domain}\n";
    echo "   This indicates a DNS configuration issue or network connectivity problem.\n";
    echo "   Troubleshooting:\n";
    echo "   - Check if the domain name is correct\n";
    echo "   - Verify network connectivity\n";
    echo "   - Try using a different DNS server\n";
    exit(1);
} else {
    echo "✅ DNS resolution successful in {$dnsTime}ms\n";
    echo "   IP Addresses:\n";
    foreach ($ipAddresses as $ip) {
        echo "   - {$ip}\n";
    }
}

echo "\n";

// Step 2: TCP Connectivity
echo "STEP 2: TCP Connectivity\n";
echo "----------------------\n";

$scheme = parse_url($testUrl, PHP_URL_SCHEME);
$port = ($scheme === 'https') ? 443 : 80;

echo "Testing TCP connection to {$domain}:{$port}...\n";

$tcpStartTime = microtime(true);
$socket = @fsockopen($domain, $port, $errno, $errstr, $connectTimeout);
$tcpTime = round((microtime(true) - $tcpStartTime) * 1000);

if (!$socket) {
    echo "❌ TCP connection failed: {$errstr} (Error #{$errno})\n";
    echo "   This indicates a network connectivity issue or server problem.\n";
    echo "   Troubleshooting:\n";
    echo "   - Check if the server is online\n";
    echo "   - Verify network connectivity\n";
    echo "   - Check firewall settings\n";
    exit(1);
} else {
    echo "✅ TCP connection established in {$tcpTime}ms\n";
    fclose($socket);
}

echo "\n";

// Step 3: Basic HTTP Request
echo "STEP 3: Basic HTTP Request\n";
echo "------------------------\n";

echo "Sending HTTP request to {$testUrl}...\n";

$ch = curl_init($testUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HEADER => true,
    CURLOPT_NOBODY => false,
    CURLOPT_FOLLOWLOCATION => false, // Don't follow redirects to analyze them
    CURLOPT_CONNECTTIMEOUT => $connectTimeout,
    CURLOPT_TIMEOUT => $requestTimeout,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_SSL_VERIFYHOST => 2,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
]);

$httpStartTime = microtime(true);
$response = curl_exec($ch);
$httpTime = round((microtime(true) - $httpStartTime) * 1000);

if ($response === false) {
    $error = curl_error($ch);
    $errno = curl_errno($ch);
    echo "❌ HTTP request failed: {$error} (Error #{$errno})\n";
    echo "   This indicates a problem with the HTTP connection or SSL configuration.\n";
    echo "   Troubleshooting:\n";
    echo "   - Check if the site is using HTTPS correctly\n";
    echo "   - Verify the server is running a web server\n";
    echo "   - Check for SSL certificate issues\n";
    curl_close($ch);
    exit(1);
} else {
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);

    echo "✅ HTTP request completed in {$httpTime}ms\n";
    echo "   Status code: {$statusCode}\n";

    // Check for redirects
    if ($statusCode >= 300 && $statusCode < 400) {
        $location = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
        echo "   ⚠️ Redirect detected to: {$location}\n";

        // Follow the redirect chain manually
        echo "\n   Analyzing redirect chain:\n";
        $redirectUrl = $location;
        $redirectCount = 1;
        $maxRedirects = 10;

        while ($redirectCount < $maxRedirects && $redirectUrl) {
            echo "   Redirect #{$redirectCount}: {$redirectUrl}\n";

            curl_setopt($ch, CURLOPT_URL, $redirectUrl);
            $redirectResponse = curl_exec($ch);
            $redirectStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            echo "   Status code: {$redirectStatusCode}\n";

            if ($redirectStatusCode >= 300 && $redirectStatusCode < 400) {
                $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
                $redirectCount++;
            } else {
                $redirectUrl = null;
            }
        }

        if ($redirectCount >= $maxRedirects) {
            echo "   ⚠️ Too many redirects detected (> {$maxRedirects})\n";
        }
    }

    // Check for security headers
    echo "\n   Security Headers:\n";
    $securityHeaders = [
        'Strict-Transport-Security',
        'Content-Security-Policy',
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection',
        'Referrer-Policy'
    ];

    $headerLines = explode("\n", $headers);
    $foundSecurityHeaders = [];

    foreach ($headerLines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        foreach ($securityHeaders as $secHeader) {
            if (stripos($line, $secHeader . ':') === 0) {
                $foundSecurityHeaders[$secHeader] = $line;
                break;
            }
        }
    }

    foreach ($securityHeaders as $secHeader) {
        if (isset($foundSecurityHeaders[$secHeader])) {
            echo "   ✓ {$foundSecurityHeaders[$secHeader]}\n";
        } else {
            echo "   ✗ {$secHeader} not found\n";
        }
    }

    // Content type and server info
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    echo "\n   Content Type: {$contentType}\n";

    // Extract server header
    $serverHeader = '';
    foreach ($headerLines as $line) {
        if (stripos($line, 'Server:') === 0) {
            $serverHeader = $line;
            break;
        }
    }

    if ($serverHeader) {
        echo "   {$serverHeader}\n";
    } else {
        echo "   Server header not found\n";
    }
}

curl_close($ch);
echo "\n";

// Step 4: Testing specific paths
echo "STEP 4: Testing Specific Paths\n";
echo "---------------------------\n";

foreach ($specificPaths as $name => $path) {
    $fullUrl = rtrim($testUrl, '/') . $path;
    echo "Testing path: {$name} ({$fullUrl})\n";

    // Test with retries
    $success = false;
    $lastError = '';
    $lastStatusCode = 0;
    $lastResponseTime = 0;
    $lastRedirectUrl = '';

    for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
        if ($attempt > 1) {
            echo "   Retry #{$attempt}...\n";
            // Add increasing delay between retries
            sleep($attempt);
        }

        $ch = curl_init($fullUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => true,
            CURLOPT_NOBODY => false,
            CURLOPT_FOLLOWLOCATION => false, // Don't follow redirects
            CURLOPT_CONNECTTIMEOUT => $connectTimeout,
            CURLOPT_TIMEOUT => $requestTimeout,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        ]);

        $startTime = microtime(true);
        $response = curl_exec($ch);
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000);
        $lastResponseTime = $responseTime;

        if ($response === false) {
            $lastError = curl_error($ch);
            $lastStatusCode = 0;
        } else {
            $lastStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $lastRedirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);

            // Accept 2xx, 3xx as success
            if (($lastStatusCode >= 200 && $lastStatusCode < 400) || $lastStatusCode == 401) {
                $success = true;
                break;
            } else {
                $lastError = "HTTP status {$lastStatusCode}";
            }
        }

        curl_close($ch);
    }

    if ($success) {
        echo "   ✅ Path accessible (status: {$lastStatusCode}) in {$lastResponseTime}ms\n";
        if ($lastRedirectUrl) {
            echo "   ⚠️ Redirects to: {$lastRedirectUrl}\n";
        }
    } else {
        echo "   ❌ Failed to access path after {$maxRetries} attempts: {$lastError}\n";
        if ($lastStatusCode > 0) {
            echo "   Last status code: {$lastStatusCode}\n";
        }
    }

    echo "\n";
}

// Step 5: Performance Analysis
echo "STEP 5: Performance Analysis\n";
echo "-------------------------\n";

echo "Testing performance with full page load...\n";

$ch = curl_init($testUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HEADER => true,
    CURLOPT_NOBODY => false,
    CURLOPT_FOLLOWLOCATION => true, // Now follow redirects
    CURLOPT_MAXREDIRS => 5,
    CURLOPT_CONNECTTIMEOUT => $connectTimeout,
    CURLOPT_TIMEOUT => $requestTimeout,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_SSL_VERIFYHOST => 2,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
]);

// Add timing info
curl_setopt($ch, CURLINFO_HEADER_OUT, true);

$startTime = microtime(true);
$response = curl_exec($ch);
$totalTime = round((microtime(true) - $startTime) * 1000);

$info = curl_getinfo($ch);
curl_close($ch);

if ($response !== false) {
    echo "✅ Full page load completed in {$totalTime}ms\n";
    echo "   DNS Lookup Time: " . round($info['namelookup_time'] * 1000) . "ms\n";
    echo "   Connect Time: " . round($info['connect_time'] * 1000) . "ms\n";
    echo "   TLS Handshake: " . round(($info['appconnect_time'] - $info['connect_time']) * 1000) . "ms\n";
    echo "   Time to First Byte: " . round($info['starttransfer_time'] * 1000) . "ms\n";
    echo "   Download Time: " . round(($info['total_time'] - $info['starttransfer_time']) * 1000) . "ms\n";
    echo "   Total Size: " . round($info['size_download'] / 1024, 2) . " KB\n";
}

echo "\nDiagnostics completed.\n";

// Summary
echo "\nSUMMARY\n";
echo "=======\n";
echo "✅ DNS Resolution: {$dnsTime}ms\n";
echo "✅ TCP Connection: {$tcpTime}ms\n";
echo "✅ HTTP Response: {$httpTime}ms\n";
echo "✅ Full Page Load: {$totalTime}ms\n";
echo "\n";

// Optional recommendations
echo "RECOMMENDATIONS\n";
echo "===============\n";

if ($dnsTime > 200) {
    echo "⚠️ DNS resolution time is high. Consider improving DNS configuration.\n";
}

if ($tcpTime > 300) {
    echo "⚠️ TCP connection time is high. This may indicate network latency issues.\n";
}

if ($httpTime > 1000) {
    echo "⚠️ HTTP response time is high. The server may be overloaded or have performance issues.\n";
}

if ($totalTime > 3000) {
    echo "⚠️ Total page load time is high. Consider performance optimizations.\n";
}

// Specific analysis for Selenium
echo "\nSELENIUM INTEGRATION ANALYSIS\n";
echo "===========================\n";
echo "Based on the above diagnostics, here are potential issues affecting Selenium connectivity:\n\n";

if ($dnsTime > 500 || $tcpTime > 500) {
    echo "1. Network Latency: High connection times may cause Selenium timeouts.\n";
    echo "   - Consider increasing timeouts in WebDriver configuration\n";
    echo "   - Ensure Docker containers have stable network connectivity\n\n";
}

if (strpos($headers, 'X-Frame-Options: DENY') !== false || strpos($headers, 'X-Frame-Options: SAMEORIGIN') !== false) {
    echo "2. X-Frame-Options Restriction: The site restricts framing which might affect Selenium.\n";
    echo "   - Add ChromeOptions to bypass frame restrictions in WebDriver\n\n";
}

if (strpos($headers, 'Content-Security-Policy') !== false) {
    echo "3. Content Security Policy: The site has CSP which might block resources in Selenium.\n";
    echo "   - Add '--disable-web-security' to ChromeOptions\n\n";
}

echo "4. General Selenium WebDriver recommendations:\n";
echo "   - Increase page load timeout: \$driver->manage()->timeouts()->pageLoadTimeout(30, TimeUnit::SECONDS)\n";
echo "   - Add robust wait conditions before interactions\n";
echo "   - Use explicit waits instead of implicit waits\n";
echo "   - Consider adding retries for flaky elements\n";
echo "   - Log detailed information about each WebDriver step\n";
echo "   - Add crash detection and session recovery logic\n";
echo "\n";

echo "Diagnostics completed. Use this information to adjust your Selenium configuration.\n"; 