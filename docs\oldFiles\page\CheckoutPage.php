<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\ElementNotFoundException;

/**
 * CheckoutPage handles actions on the checkout page.
 */
class CheckoutPage extends BasePage
{
    /**
     * The path of the checkout page.
     *
     * @var string
     */
    protected $path = '/checkout';

    private const SELECTORS = [
        'PAYPAL_OPTION' => '#paypal-payment-option',
        'PAYPAL_BUTTON' => '#paypal-button',
        'PAYPAL_RADIO' => 'input[name="app_one_page_checkout[payments][0][method]"][value="paypal"]',
        'PAYPAL_LABEL' => 'label:has(input[name="app_one_page_checkout[payments][0][method]"][value="paypal"])',
        'PAYMENT_COMPONENT' => '.payment-component',
        'COMPLETE_PURCHASE_BUTTON' => 'button[type="submit"][form="app_one_page_checkout"]',
        'PAYMENT_SUCCESS' => '.payment-success-message',
        // Billing Form Fields
        'EMAIL' => '#app_one_page_checkout_customer_email',
        'FIRST_NAME' => '#app_one_page_checkout_billingAddress_firstName',
        'LAST_NAME' => '#app_one_page_checkout_billingAddress_lastName',
        'PHONE' => '#app_one_page_checkout_billingAddress_phoneNumber',
        'ADDRESS' => '#app_one_page_checkout_billingAddress_street',
        'CITY' => '#app_one_page_checkout_billingAddress_city',
        'POSTCODE' => '#app_one_page_checkout_billingAddress_postcode',
        'COUNTRY' => '#app_one_page_checkout_billingAddress_countryCode',
        // Checkout Form
        'SAME_ADDRESS_CHECKBOX' => '#app_one_page_checkout_differentShippingAddress_0',
        'DIFFERENT_ADDRESS_CHECKBOX' => '#app_one_page_checkout_differentShippingAddress_1',
        // Shipping Form Fields
        'SHIPPING_FIRST_NAME' => '#app_one_page_checkout_shippingAddress_firstName',
        'SHIPPING_LAST_NAME' => '#app_one_page_checkout_shippingAddress_lastName',
        'SHIPPING_PHONE' => '#app_one_page_checkout_shippingAddress_phoneNumber',
        'SHIPPING_ADDRESS' => '#app_one_page_checkout_shippingAddress_street',
        'SHIPPING_CITY' => '#app_one_page_checkout_shippingAddress_city',
        'SHIPPING_POSTCODE' => '#app_one_page_checkout_shippingAddress_postcode',
        'SHIPPING_COUNTRY' => '#app_one_page_checkout_shippingAddress_countryCode',
        // Stripe Card Fields
        'CARD_NUMBER' => 'input[data-elements-stable-field-name="cardNumber"]',
        'CARD_EXPIRY' => 'input[data-elements-stable-field-name="cardExpiry"]',
        'CARD_CVC' => 'input[data-elements-stable-field-name="cardCvc"]',
        // Order Summary
        'SHIPPING_COST' => '.order-summary-component .ch-shipping-value span',
        'ORDER_TOTAL' => '.order-summary-component .ch-total-value span',
        // Submit Button
        'SUBMIT_BUTTON' => 'button[type="submit"]',
        // Page Elements
        'CHECKOUT_TITLE' => '//*[@class="checkout-title"]',
        'CHECKOUT_FORM' => 'form[name="app_one_page_checkout"]',
        'APP_CHECKOUT' => '//*[@id="app_one_page_checkout"]//h2',
        'PROCESSING_ICON' => '//*[@class="PROCESSING_ICON_SELECTOR"]',
        // Shipping Methods
        'SHIPPING_METHODS_LIST' => '#app-checkout-shipping-methods',
        'SELECTED_SHIPPING_METHOD' => "//ul[@id='app-checkout-shipping-methods']//input[@type='radio' and @checked]/../span[@class='ch-custom-label']/span",
        'ERROR_MESSAGE' => '.alert.alert-danger',
        'ABANDON_CHECKOUT_BUTTON' => '.abandon-checkout-btn'
    ];

    /**
     * Verifies that we're on the expected page.
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        $this->waitForElementVisible(self::SELECTORS['PAYMENT_COMPONENT']);
    }

    /**
     * Gets the URL for the checkout page.
     *
     * @return string The complete URL
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Waits for the checkout page to load completely.
     * @param int $timeout The maximum time to wait in milliseconds.
     * @throws ElementNotFoundException
     */
    public function waitForPageToLoad(int $timeout = 10000): void
    {
        parent::waitForPageToLoad($timeout);
        $this->waitForElementVisible(self::SELECTORS['CHECKOUT_TITLE'], $timeout);
    }

    /**
     * Fills in the checkout form with provided user data.
     *
     * @param array $userData User data from test_data.yml
     * @throws ElementNotFoundException
     */
    public function fillInCheckoutForm(array $userData): void
    {
        // Validate required fields
        $requiredFields = ['email', 'first_name', 'last_name', 'phone', 'address', 'city', 'postcode', 'country'];
        foreach ($requiredFields as $field) {
            if (empty($userData[$field])) {
                throw new \InvalidArgumentException("Missing required field: $field");
            }
        }

        // Map test data fields to form field selectors
        $fieldMapping = [
            'email' => self::SELECTORS['EMAIL'],
            'first_name' => self::SELECTORS['FIRST_NAME'],
            'last_name' => self::SELECTORS['LAST_NAME'],
            'phone' => self::SELECTORS['PHONE'],
            'address' => self::SELECTORS['ADDRESS'],
            'city' => self::SELECTORS['CITY'],
            'postcode' => self::SELECTORS['POSTCODE'],
            'country' => self::SELECTORS['COUNTRY'],
        ];

        // Wait for form to be ready
        $this->waitForCheckoutForm();

        // Fill in each field
        foreach ($fieldMapping as $dataKey => $selector) {
            $element = $this->findElement($selector);
            $element->setValue($userData[$dataKey]);

            // Special handling for country dropdown
            if ($dataKey === 'country') {
                $this->waitForAjaxToComplete();
                $this->selectDropdownOption($selector, $userData['country']);
            }
        }
    }

    /**
     * Waits for the checkout form to be visible.
     *
     * @param int $timeout The maximum time to wait in milliseconds.
     * @throws ElementNotFoundException
     */
    public function waitForCheckoutForm(int $timeout = 10000): void
    {
        $this->waitForElementVisible(self::SELECTORS['EMAIL'], $timeout);
    }

    /**
     * Ensures that the same address is used for both billing and shipping.
     *
     * @throws ElementNotFoundException
     */
    public function useSameAddressForBillingAndShipping(): void
    {
        if (!$this->isCheckboxChecked(self::SELECTORS['SAME_ADDRESS_CHECKBOX'])) {
            $this->findElement(self::SELECTORS['SAME_ADDRESS_CHECKBOX'])->click();
        }
    }

    /**
     * Checks if a checkbox is checked.
     *
     * @param string $selector The CSS selector of the checkbox.
     * @return bool True if checked, false otherwise.
     * @throws ElementNotFoundException
     */
    public function isCheckboxChecked(string $selector): bool
    {
        $element = $this->findElement($selector);
        return $element->isSelected();
    }

    /**
     * Enters payment details into Stripe iframe fields.
     *
     * @param array $paymentDetails Array containing payment details.
     * @throws \Exception
     */
    public function enterPaymentDetails(array $paymentDetails): void
    {
        // Switch to card number iframe and enter card number
        $this->switchToStripeFrame('Secure card number input frame');
        $this->findElement(self::SELECTORS['CARD_NUMBER'])->setValue($paymentDetails['cardNumber']);
        $this->session->switchToIFrame(null);

        // Switch to expiry date iframe and enter expiry date
        $this->switchToStripeFrame('Secure expiration date input frame');
        $this->findElement(self::SELECTORS['CARD_EXPIRY'])->setValue($paymentDetails['cardExpiry']);
        $this->session->switchToIFrame(null);

        // Switch to CVC iframe and enter CVC
        $this->switchToStripeFrame('Secure CVC input frame');
        $this->findElement(self::SELECTORS['CARD_CVC'])->setValue($paymentDetails['cardCvc']);
        $this->session->switchToIFrame(null);
    }

    /**
     * Selects a shipping method.
     *
     * @param string $method The shipping method to select (e.g., "Domestic tracked")
     * @throws \Exception If the shipping method is not found or cannot be selected.
     */
    public function selectShippingMethod(string $method): void
    {
        $selector = "//span[contains(text(), '$method')]/../../input[@type='radio']";
        $element = $this->getSession()->getPage()->find('xpath', $selector);

        if (!$element) {
            throw new \Exception("Shipping method not found: $method");
        }

        $element->click();
    }

    /**
     * Retrieves the selected shipping method.
     *
     * @return string|null The selected shipping method, or null if none selected.
     */
    public function getSelectedShippingMethod(): ?string
    {
        $element = $this->getSession()->getPage()->find('xpath', self::SELECTORS['SELECTED_SHIPPING_METHOD']);

        if (!$element) {
            error_log("[CheckoutPage] No shipping method element found with selector: " . self::SELECTORS['SELECTED_SHIPPING_METHOD']);
            return null;
        }

        return trim($element->getText());
    }

    /**
     * Retrieves the displayed shipping cost.
     *
     * @return string The shipping cost (e.g., "£5.00").
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getShippingCost(): string
    {
        $element = $this->findElement(self::SELECTORS['SHIPPING_COST']);
        return $element->getText();
    }

    /**
     * Retrieves the order total from the checkout page.
     *
     * @return float The order total amount.
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getOrderTotal(): float
    {
        $element = $this->findElement(self::SELECTORS['ORDER_TOTAL']);
        $totalText = $element->getText();
        return $this->parsePrice($totalText);
    }

    /**
     * Parses a price string and returns its numeric value.
     *
     * @param string $priceText The price text (e.g., "£5.00").
     * @return float The numeric value.
     */
    private function parsePrice(string $priceText): float
    {
        $price = str_replace(['£', ','], '', $priceText);
        return (float) trim($price);
    }

    /**
     * Completes the purchase by clicking the submit button.
     *
     * @throws \RuntimeException
     * @throws ElementNotFoundException
     */
    public function completePurchase(): void
    {
        $button = $this->findElement(self::SELECTORS['SUBMIT_BUTTON']);
        $button->click();
    }

    /**
     * Verifies that the current page is the checkout page by checking the presence of the checkout header.
     *
     * @return bool True if on the checkout page, false otherwise.
     */
    public function isCheckoutPage(): bool
    {
        return $this->isElementVisible(self::SELECTORS['CHECKOUT_TITLE']) && 
               $this->isElementPresent(self::SELECTORS['CHECKOUT_FORM']);
    }

    public function getPageTitle(): string
    {
        return $this->session->getPage()->find('xpath', self::SELECTORS['APP_CHECKOUT'])->getText();
    }

    public function isProcessingPageDisplayed(): bool
    {
        return $this->isElementVisible(self::SELECTORS['PROCESSING_ICON']);
    }

    /**
     * Helper method to find Stripe iframe by type
     *
     * @param string $type The type of frame to find (number, expiry, cvc)
     * @return NodeElement|null
     */
    protected function findStripeFrame(string $type): ?NodeElement
    {
        // Wait for iframes to be present
        $this->getSession()->wait(5000, "document.querySelector('iframe[name^=\"__privateStripeFrame\"]') !== null");

        // Find all Stripe iframes
        $frames = $this->getSession()->getPage()->findAll('css', 'iframe[name^="__privateStripeFrame"]');

        // Use a more reliable way to identify frames
        foreach ($frames as $frame) {
            try {
                // First verify the frame is actually accessible
                if (!$frame->isValid()) {
                    continue;
                }

                // Get frame attributes using a more reliable method
                $frameAttributes = [
                    'name' => $frame->getAttribute('name'),
                    'title' => $frame->getAttribute('title')
                ];

                // Skip if we couldn't get the attributes
                if (empty($frameAttributes['title'])) {
                    continue;
                }

                $frameTitle = strtolower($frameAttributes['title']);

                // Match frame by type
                switch ($type) {
                    case 'number':
                        if (strpos($frameTitle, 'card number') !== false) {
                            return $frame;
                        }
                        break;
                    case 'expiry':
                        if (strpos($frameTitle, 'expiry') !== false) {
                            return $frame;
                        }
                        break;
                    case 'cvc':
                        if (strpos($frameTitle, 'security code') !== false ||
                            strpos($frameTitle, 'cvc') !== false) {
                            return $frame;
                        }
                        break;
                }
            } catch (\Exception $e) {
                // Log the error but continue checking other frames
                error_log("Error checking frame: " . $e->getMessage());
                continue;
            }
        }

        return null;
    }

    /**
     * Alternative method using XPath to find Stripe frames
     * This might be more reliable in some cases
     */
    protected function findStripeFrameByXPath(string $type): ?NodeElement
    {
        $xpathMap = [
            'number' => "//iframe[contains(@name, '__privateStripeFrame')][contains(@title, 'card number')]",
            'expiry' => "//iframe[contains(@name, '__privateStripeFrame')][contains(@title, 'expiry')]",
            'cvc' => "//iframe[contains(@name, '__privateStripeFrame')][contains(@title, 'security code')]"
        ];

        if (!isset($xpathMap[$type])) {
            throw new \InvalidArgumentException("Invalid frame type: $type");
        }

        return $this->getSession()->getPage()->find('xpath', $xpathMap[$type]);
    }

    /**
     * Selects PayPal as the payment method and completes the purchase.
     */
    public function choosePayPalPayment(): void
    {
        try {
            $this->clickElement(self::SELECTORS['PAYPAL_RADIO']);
            
            // Verify the radio input is checked
            $paypalRadio = $this->findElement(self::SELECTORS['PAYPAL_RADIO']);
            if (!$paypalRadio->isChecked()) {
                throw new \RuntimeException('PayPal radio button was not successfully checked after clicking the label');
            }
            $this->clickElement(self::SELECTORS['COMPLETE_PURCHASE_BUTTON']);
            
            // Wait for form submission
            $this->waitForAjaxToComplete();
            
        } catch (ElementNotFoundException $e) {
            throw new \RuntimeException("Failed to select PayPal payment method: " . $e->getMessage());
        }
    }

    /**
     * Verifies if the PayPal payment was successful.
     */
    public function verifyPayPalPaymentSuccess(): bool
    {
        try {
            $this->waitForElementVisible(self::SELECTORS['PAYMENT_SUCCESS']);
            return true;
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Gets the current error message displayed on the page
     */
    public function getErrorMessage(): string
    {
        $element = $this->findElement(self::SELECTORS['ERROR_MESSAGE']);
        return $element->getText();
    }

    /**
     * Checks if the checkout can be abandoned
     */
    public function canAbandonCheckout(): bool
    {
        try {
            $button = $this->findElement(self::SELECTORS['ABANDON_CHECKOUT_BUTTON']);
            return $button->isVisible() && !$button->hasClass('disabled');
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Checks if the checkout form is locked (cannot be abandoned)
     */
    public function isCheckoutFormLocked(): bool
    {
        $form = $this->findElement(self::SELECTORS['CHECKOUT_FORM']);
        return $form->hasClass('locked') || $form->hasAttribute('data-locked');
    }
}
