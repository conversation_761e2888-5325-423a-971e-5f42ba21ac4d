<?php

namespace App\Context;

use App\Service\Browser\BrowserStackBrowserService;
use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\AfterScenarioScope;
use Behat\Behat\Hook\Scope\AfterStepScope;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Behat\Testwork\Tester\Result\TestResult;
use Psr\Log\LoggerInterface;

/**
 * Context for BrowserStack integration
 */
class BrowserStackContext implements Context
{
    private BrowserStackBrowserService $browserService;
    private LoggerInterface $logger;
    private string $scenarioName;
    private bool $scenarioFailed = false;
    private string $failureReason = '';

    /**
     * Constructor
     *
     * @param BrowserStackBrowserService $browserService BrowserStack browser service
     * @param LoggerInterface $logger Logger
     */
    public function __construct(
        BrowserStackBrowserService $browserService,
        LoggerInterface            $logger
    )
    {
        $this->browserService = $browserService;
        $this->logger = $logger;
    }

    /**
     * @BeforeScenario
     */
    public function beforeScenario(BeforeScenarioScope $scope): void
    {
        $this->scenarioName = $scope->getScenario()->getTitle();
        $this->scenarioFailed = false;
        $this->failureReason = '';

        $this->logger->info(sprintf('Starting scenario: %s', $this->scenarioName));
    }

    /**
     * @AfterStep
     */
    public function afterStep(AfterStepScope $scope): void
    {
        // Check if step failed
        if ($scope->getTestResult()->getResultCode() === TestResult::FAILED) {
            $this->scenarioFailed = true;
            $this->failureReason = $scope->getTestResult()->getMessage() ?? 'Step failed';

            // Take a screenshot on failure
            try {
                $this->browserService->takeScreenshot('failed_step');
            } catch (\Exception $e) {
                $this->logger->error('Failed to take screenshot after failed step: ' . $e->getMessage());
            }
        }
    }

    /**
     * @AfterScenario
     */
    public function afterScenario(AfterScenarioScope $scope): void
    {
        // Check if scenario failed
        if ($scope->getTestResult()->getResultCode() === TestResult::FAILED) {
            $this->scenarioFailed = true;
            $this->failureReason = $scope->getTestResult()->getMessage() ?? 'Scenario failed';
        }

        // Report status to BrowserStack
        if ($this->scenarioFailed) {
            $this->logger->info(sprintf('Marking scenario as failed in BrowserStack: %s', $this->scenarioName));
            $this->browserService->markTestFailed($this->failureReason);
        } else {
            $this->logger->info(sprintf('Marking scenario as passed in BrowserStack: %s', $this->scenarioName));
            $this->browserService->markTestPassed('Scenario passed successfully');
        }
    }

    /**
     * @Then I verify BrowserStack integration
     */
    public function iVerifyBrowserStackIntegration(): void
    {
        if (!$this->browserService->isBrowserStackSession()) {
            throw new \RuntimeException('Not running in a BrowserStack session');
        }

        $this->logger->info('BrowserStack integration verified');
    }

    /**
     * @Then I take a BrowserStack screenshot named :name
     */
    public function iTakeABrowserStackScreenshot(string $name): void
    {
        $this->browserService->takeScreenshot($name);
    }
}
