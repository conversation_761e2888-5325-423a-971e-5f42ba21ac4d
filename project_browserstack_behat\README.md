# Project BrowserStack + Behat Scaffold

This repository provides a ready-to-use scaffold for running Behat tests with <PERSON><PERSON> and BrowserStack, using Symfony
Dependency Injection and the FriendsOfBehat PageObjectExtension.

## Structure

- [composer.json](composer.json) - Project dependencies and scripts
- [.env.example](.env.example) - Example environment variable definitions
- [behat.yml](behat.yml) - Behat configuration profiles
- [features](features) - Feature files, fixtures, and bootstrap script
- [src](src) - Context classes, page objects, and service adapters
- [docs](docs) - Documentation and architectural overview
- [.gitlab-ci.yml](.gitlab-ci.yml) - GitLab CI pipeline configuration
- [tests/Dockerfile](tests/Dockerfile) - Dockerfile for test environment

## Documentation

See the [docs](docs) directory for detailed guides:

- [01-overview](docs/01-overview.md)
- [02-architecture](docs/02-architecture.md)
- [03-configuration](docs/03-configuration.md)
- [04-running-tests](docs/04-running-tests.md)
- [05-ci-setup](docs/05-ci-setup.md)
- [06-docker-usage](docs/06-docker.md)
- [07-compatibility](docs/07-compatibility.md)

---

Feel free to drop your existing Contexts into `src/Context` and Page Objects into `src/Page`.  
Install dependencies via:

```bash
composer install
```

Run tests locally:

```bash
vendor/bin/behat --strict
```

Run tests on BrowserStack:

```bash
vendor/bin/behat --profile=browserstack
``` 