# Test Framework Simplification Plan

## Overview
This document outlines the plan to simplify and refocus the e-commerce test automation framework to prioritize functional verifications of the Sylius CRM system without visual testing or BrowserStack integration. Instead, the framework will use local browser drivers when browser interaction is required.

## Version: 1.2
**Last Updated**: 2025-03-27

## Goals
1. Remove BrowserStack dependencies and configurations
2. Configure local browser testing with Selenium WebDriver
3. Refocus test scenarios on functional verification
4. Simplify environment configuration
5. Update documentation to reflect changes

## Progress Tracker

| Task | Status | Description | Completion Date |
|------|--------|-------------|-----------------|
| Update `behat.yml` | ✅ | Replace BrowserStack with local WebDriver | 2025-03-27 |
| Update `behat-bootstrap.php` | ✅ | Remove BrowserStack environment variables | 2025-03-27 |
| Update documentation | ✅ | Update framework documentation | 2025-03-27 |
| Remove BrowserStack contexts | ✅ | Remove BrowserStackContext and BrowserStackLogsContext | 2025-03-27 |
| Remove BrowserStack tools | ✅ | Remove BrowserStack verification tools | 2025-03-27 |
| Setup local WebDriver | ✅ | Create script to download and setup ChromeDriver | 2025-03-27 |
| Update context classes | ✅ | Update context classes to use local browser driver | 2025-03-27 |
| Update configuration files | ✅ | Update remaining config files to remove BrowserStack | 2025-03-27 |
| Create test helpers | ✅ | Create scripts for Selenium setup and test running | 2025-03-27 |
| Test refactoring | 🔄 | Verify tests run with local browser setup | - |
| CI/CD integration | 🔄 | Update CI/CD pipeline for local browser testing | - |

## Implementation Details

### 1. Local Browser Testing Configuration

#### Required Components
- **ChromeDriver**: For Chrome browser automation
- **GeckoDriver**: For Firefox browser automation (optional)
- **Selenium Server**: For centralized browser automation (optional)

#### Setup Instructions
1. **ChromeDriver**:
   - Automatic setup using provided script: `php bin/setup-webdriver.php`
   - The script will download the latest compatible ChromeDriver version
   - ChromeDriver will be placed in `tools/webdriver` directory

2. **GeckoDriver** (optional):
   - Download from [GeckoDriver Releases](https://github.com/mozilla/geckodriver/releases)
   - Add to system PATH
   - Verify with `geckodriver --version`

3. **Selenium Server** (optional):
   - Download from [Selenium Website](https://www.selenium.dev/downloads/)
   - Start with `java -jar selenium-server-standalone-x.xx.x.jar`
   - Verify by accessing `http://localhost:4444/wd/hub`

### 2. Running Tests Locally

We've created a test runner script that handles the setup and execution of tests with the local WebDriver:

```bash
# Run all tests
php bin/run-test-local.php

# Run a specific feature
php bin/run-test-local.php -f features/purchase.feature

# Run tests with specific tags
php bin/run-test-local.php -t smoke_one_time

# Enable debug logging
php bin/run-test-local.php -d
```

The test runner script:
1. Verifies all dependencies are installed
2. Starts Selenium Server with ChromeDriver
3. Executes the specified Behat tests
4. Cleans up processes when done

### 3. Completed Configuration Changes

#### Environment Variables
| Variable | Purpose | Default Value |
|----------|---------|---------------|
| TEST_BASE_URL | Base URL for testing | https://aeonstest.info |
| BROWSER_NAME | Browser for testing | chrome |
| WEBDRIVER_HOST | WebDriver host address | http://localhost:4444/wd/hub |

#### Updated Configuration Files
- `behat.yml`: Updated to use local browser driver
- `behat-bootstrap.php`: Removed BrowserStack variables
- `features/bootstrap/bootstrap.php`: Updated default values
- `features/bootstrap/Tools/config.php`: Updated configuration parameters

### 4. Completed Code Refactoring

#### BrowserStack Context Removal
- Removed context classes:
  - `Features\Bootstrap\Context\BrowserStackContext`
  - `Features\Bootstrap\Context\BrowserStackLogsContext`
- Backup copies preserved in `backup/Context` directory

#### Cleanup Related Tools
- Removed tool files:
  - `features/bootstrap/Tools/test_browserstack_connection.php`
  - `features/bootstrap/Tools/verify_browserstack.php`
- Backup copies preserved in `backup/Tools` directory

#### Updated Step Definitions
- Replaced `verifyBrowserStackSession()` with a generic `verifyBrowserSession()` method
- Updated any references to BrowserStack in other files
- Made sure all functionality not related to BrowserStack is preserved

### 5. Future Enhancements
- Add headless browser support
- Implement parallel test execution
- Add screenshot capture for test failures
- Add video recording for critical tests
- Implement browser session reuse for performance

## Reference

### Key Feature Files
- `features/abandoned_cart.feature`
- `features/abandoned_cart_extended.feature`
- `features/mixedCart.feature`
- `features/productPage.feature`
- `features/purchase.feature`
- `features/salesFunnel.feature`
- `features/subscription_reorder.feature`

### Important Configuration Files
- `behat.yml`
- `behat-bootstrap.php`
- `composer.json`
- `.env.example`
