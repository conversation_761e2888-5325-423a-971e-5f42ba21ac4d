<?php

namespace Features\Bootstrap\Core;

use Behat\Behat\Context\Context;
use RuntimeException;

/**
 * Central registry for all context instances
 * Provides a consistent way to access contexts and their functionality
 */
class ContextManager
{
    /**
     * @var array<string, Context> Registered contexts
     */
    private array $contexts = [];

    /**
     * Register a context instance
     *
     * @param string $contextName Class name of the context
     * @param Context $context Context instance
     * @return void
     */
    public function registerContext(string $contextName, Context $context): void
    {
        $this->contexts[$contextName] = $context;
    }

    /**
     * Get a registered context by name
     *
     * @param string $contextName Class name of the context
     * @return Context The requested context
     * @throws RuntimeException If context is not registered
     */
    public function getContext(string $contextName): Context
    {
        if (!isset($this->contexts[$contextName])) {
            throw new RuntimeException("Context '{$contextName}' not registered.");
        }

        return $this->contexts[$contextName];
    }

    /**
     * Check if a context is registered
     *
     * @param string $contextName Class name of the context
     * @return bool True if context is registered, false otherwise
     */
    public function hasContext(string $contextName): bool
    {
        return isset($this->contexts[$contextName]);
    }

    /**
     * Get all registered contexts
     *
     * @return array<string, Context> All registered contexts
     */
    public function getAllContexts(): array
    {
        return $this->contexts;
    }
} 