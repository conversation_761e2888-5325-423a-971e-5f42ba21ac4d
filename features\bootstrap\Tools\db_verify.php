<?php

// Load composer autoloader
require_once __DIR__ . '/../../../vendor/autoload.php';
require_once __DIR__ . '/../Helper/SSHTunnel.php';
require_once __DIR__ . '/../Helper/DatabaseHelper.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check for debug flag in command line arguments
$debug = in_array('--debug', $argv) || in_array('-d', $argv);

function log_debug(string $message): void
{
    global $debug;
    if ($debug) {
        echo "[DEBUG] $message\n";
    }
}

try {
    echo "Starting database connection verification...\n\n";

    if ($debug) {
        echo "Debug mode enabled\n\n";
    }

    // Configuration
    $config = [
        'ssh' => [
            'host' => '**************',
            'user' => 'ec2-user',
            'private_key' => "-----B<PERSON>IN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEAgvcnT5ne+tf+6iWojP+nPXXIvYgR0n/Ua6c9NRsSIGdG0hHp
QseWOb4XHG71l0/KBlXj2YzF+hhIRZq7qXJ12VfRnBim7XiwMeUyV/jmZ9gA1cDj
aoC5DDDL3geufk4jgZQqhQy3uAoUcqHa8ufAJ4Onoe++Vu2PO0bIccjMS1s1zOcp
G+5qv/tGcFHYvWwLYhpLVSuGgiRuvIn60dSGxl5LICkLKmiX42RwZQ032arZja7+
KpMZiLORVNPny/BncM/RvyqGo2+vXidrYXigZedSlrMbzFnsolHbPVHTQO+lx+T4
ZzhxDzGjbj1iOOhYPTUA9fZ+d08CsCoR0VZwTwIDAQABAoIBABPFBV0hyCQrmOJ1
ntTx/qfrcMsTkEUrYMCDdVzf4kOb83fglkqrYEUFKe3z3gTiWYfNTWDYvEX9hmNj
cTKqAZo2hIDAw+oTDt3pUEkxXG6Oj8m0nV/FYKmzcALrO+77EqcL28NL4SvZ8a5A
OpHyY/JTEGoEi8IJiFaVX8q1WgmB2k54WW0e9jjwvUfGkN4F6zrFGBu1tbwUbXuM
GTARnT6J6UM1QZixoHJpgJZtz6XZRC67bxK4OMgNxuOqxNpcWmS0V6OM05S5Mvbn
VrYZ+U9kwid9zfIDBTCX3xk/1hh8H0NNXRVdkqi/Ryt5u/FZDs3XNZcesjZ21fc/
Cf09wuECgYEA1iORSB8KJCXYOGYAzH+oWKewZ80q0C46L/rKFAplXr9e1EccYzyL
E1ydMlA0CaQOept7wEoG/15VXYTrTo2+S9O4DrepQK0dpz8+YcqneFYCVa0upWL9
N9Jj9wRyUlabNwsN8/attSDV6jg9657gcRfnA/j5XeifUINJ8Wl+1p8CgYEAnJE6
JDrOFyV8JKtTl85ReJdDsppn7HEZldVMKTn/DLKIIA2/SgXzboe2D21c5FBYB3t2
CPR2/+SxZeX268LPNnjsfJ8AiEvZZtp1RjUP8vQF939YfIX0/I2NsHB78j9u6UDz
eNChtk65teESjLLLhzkFwTRBH34FNT1VQ+AmeFECgYEAp63WN3lGwaN4ukD1Yyyb
MECu27lGSxnHqoZg8mxpkFWd9e9xBSwFYzXkSd+Pv/Tc+ZkCwEG3VQQTKFXKxAot
9sZkm/Go8OIeFDZrrLu5g3sl/NzNZ+gpjCqM6IJs2pjClclsNZr1zp1VpEBCDFyu
NyCWXv/4lFZxWeRUZFtNXNUCgYBv/vK4N9h0djv3fxas4Mkmmg2zFQCCAeVraMiy
NOpdCtiVFUBgkyEvT6TMJE7ugwp1T7t/bGAzbEP5wCaBPzk6ngWMn7ChzqAVEvAQ
6KQgBq69/Dov2SMGBbv1GoGPKBEOiYegRJ1o66eskvPR8ZPlhsS3D8oF3P+cteLo
Ss86AQKBgQDQY7oHhcU2ctolZhVM2yJTaPGNYSZ6f+jX6lKBQ9BatHg4OqD69fWu
bj8ApvK3RZ+512bpBevUKzz51lw/vbKDz5eya/NHTpCCdIJwlWS59S6WbU7ulmWN
ijGSTbkCQnA0jGlsVz6SyEHThDK0WNW3nZYvD5M+cePft8J7qLDDzw==
-----END RSA PRIVATE KEY-----"
        ],
        'db' => [
            'host' => '*************',
            'port' => 3306,
            'database' => 'origins_diet',
            'user' => 'aeons_remote',
            'password' => 'K1QtsqYseONO7j9Z'
        ]
    ];

    log_debug("Using configuration:");
    log_debug("SSH Host: {$config['ssh']['host']}");
    log_debug("SSH User: {$config['ssh']['user']}");
    log_debug("DB Host: {$config['db']['host']}");
    log_debug("DB Port: {$config['db']['port']}");
    log_debug("DB Name: {$config['db']['database']}");
    log_debug("DB User: {$config['db']['user']}");

    // Create database helper with SSH tunnel
    $dbHelper = new Features\Bootstrap\Helper\DatabaseHelper($config);

    // Get connection
    $connection = $dbHelper->getConnection();

    // Run diagnostic tests
    echo "\nRunning diagnostic tests...\n";

    // Test 1: Basic connectivity
    echo "\nTest 1: Basic Connectivity\n";
    echo str_repeat('-', 50) . "\n";
    $connection->query('SELECT 1');
    echo "✓ Basic connectivity test passed\n";

    // Test 2: Server version
    echo "\nTest 2: Server Version\n";
    echo str_repeat('-', 50) . "\n";
    $version = $connection->getAttribute(PDO::ATTR_SERVER_VERSION);
    echo "✓ MySQL Version: $version\n";

    // Test 3: Database selection
    echo "\nTest 3: Database Selection\n";
    echo str_repeat('-', 50) . "\n";
    $result = $connection->query('SELECT DATABASE() as db')->fetch();
    echo "✓ Current database: {$result['db']}\n";

    // Test 4: Table access
    echo "\nTest 4: Table Access\n";
    echo str_repeat('-', 50) . "\n";
    $result = $connection->query('SELECT COUNT(*) as count FROM sylius_order')->fetch();
    echo "✓ Sylius order table accessible (Total orders: {$result['count']})\n";

    echo "\nAll tests completed successfully!\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    if ($debug) {
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    exit(1);
} 