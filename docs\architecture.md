# MalabergTest Architecture

## Overview

The MalabergTest framework is a BDD-based test automation framework built on top of Behat, Mink, and BrowserStack. It follows a modular architecture with clear separation of concerns between different layers.

## Key Components

### 1. Dependency Injection

The framework uses Symfony's Dependency Injection Container to manage services and dependencies. The container is configured in the `config/services.yml` file and its imports.

Key features:
- Autowiring and autoconfiguration for services
- Service tagging for contexts and page objects
- Interface-based service resolution

### 2. Service Layer

Services provide core functionality to the framework and are organized in the `src/Service` directory.

Key services:
- `BrowserServiceInterface` → `BrowserStackBrowserService`: Handles browser interactions
- `SharedStateServiceInterface` → `SharedStateService`: Manages state between steps
- `ConfigurationServiceInterface` → `ConfigurationService`: Provides access to configuration
- `TestDataServiceInterface` → `TestDataService`: Manages test data
- `CacheServiceInterface` → `FilesystemCacheService`: Provides caching functionality
- `PageFactoryInterface` → `PageFactory`: Creates page objects

### 3. Context Layer

Contexts contain step definitions and are organized in the `src/Context` directory.

Key features:
- Contexts extend `BaseContext` which provides access to services
- Contexts are tagged with `context.service` for automatic registration
- Contexts are autowired with their dependencies

### 4. Page Object Layer

Page objects encapsulate interactions with web pages and are organized in the `src/Page` directory.

Key features:
- Page objects implement `BasePageInterface`
- Page objects are tagged with `page.service` for automatic registration
- Page objects are autowired with their dependencies

### 5. Configuration

The framework uses YAML files for configuration and environment variables for environment-specific settings.

Key configuration files:
- `behat.yml`: Behat configuration with profiles for different environments
- `config/services.yml`: Service configuration
- `config/services/*.yml`: Specific service configurations
- `.env`: Environment variables

## Execution Flow

1. Behat loads the bootstrap script (`features/bootstrap/bootstrap.php`)
2. The bootstrap script loads environment variables and sets up paths
3. The Symfony Extension creates the kernel and loads the container
4. Behat creates contexts and injects dependencies
5. The PageObjectExtension registers page objects
6. Behat executes the scenarios

## BrowserStack Integration

The framework uses BrowserStack for browser automation. The integration is configured in the `behat.yml` file and implemented in the `BrowserStackBrowserService` class.

Key features:
- BrowserStack credentials are stored in environment variables
- BrowserStack capabilities are configured in the `behat.yml` file
- The `BrowserStackBrowserService` handles session creation and management

## Running Tests

Tests can be run using the `run-browserstack-tests.ps1` script or directly with Behat:

```powershell
# Using the script
.\run-browserstack-tests.ps1 -Feature "features/salesFunnel.feature" -Tags "@high-priority"

# Using Behat directly
vendor/bin/behat --profile=browserstack features/salesFunnel.feature --tags="@high-priority"
```

## CI/CD Integration

The framework includes a GitLab CI configuration in the `.gitlab-ci.yml` file. It defines jobs for building the Docker image, installing dependencies, running tests, and generating reports.

Key features:
- Docker image for consistent test execution
- Caching of dependencies for faster builds
- Artifact collection for screenshots and logs
- JUnit report generation for test results
