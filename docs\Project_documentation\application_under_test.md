# Application Under Test

## Overview

The application under test is a CRM-like e-commerce platform based on **Sylius** (a PHP-based framework built on Symfony). The platform serves two primary user types:

1. **End Customers** using the webstore for shopping
2. **Admin Users** managing products and processing orders

## Key Features

The e-commerce platform includes the following key features that are covered by the test automation framework:

### Shopping Experience

- **Product Browsing**: Customers can browse products by category, search for specific products, and view product details.
- **Product Details**: Detailed product information, including descriptions, prices, images, and FAQs.
- **Cart Management**: Adding products to cart, updating quantities, and removing items.
- **Checkout Process**: Multi-step checkout with shipping information, billing information, and payment processing.
- **Order Confirmation**: Confirmation page with order details and tracking information.

### Subscription Management

- **Subscription Options**: Customers can choose between one-time purchases and subscription-based purchases.
- **Frequency Selection**: Customers can select the frequency of subscription deliveries.
- **Subscription Management**: Customers can manage their subscriptions, including changing frequency, skipping deliveries, and canceling subscriptions.

### User Account Management

- **Registration**: New customers can create accounts.
- **Login/Logout**: Customers can log in and out of their accounts.
- **Profile Management**: Customers can update their profile information, including shipping and billing addresses.
- **Order History**: Customers can view their order history and track current orders.

### Admin Features

- **Product Management**: Admins can add, edit, and remove products.
- **Order Processing**: Admins can view and process orders.
- **Customer Management**: Admins can view and manage customer accounts.
- **Reporting**: Admins can generate reports on sales, inventory, and customer activity.

## Critical Workflows

The test automation framework focuses on the following critical workflows:

### 1. Product Purchase Flow

The basic flow of a customer purchasing a product:

1. Customer navigates to the product page
2. Customer selects product options (quantity, flavor, etc.)
3. Customer adds the product to the cart
4. Customer proceeds to checkout
5. Customer enters shipping and billing information
6. Customer selects a payment method
7. Customer completes the order
8. Customer receives order confirmation

### 2. Abandoned Cart Flow

The flow when a customer adds items to the cart but does not complete the purchase:

1. Customer adds products to the cart
2. Customer begins the checkout process
3. Customer abandons the checkout process
4. System sends abandoned cart email reminders
5. Customer returns to complete the purchase (optional)

### 3. Sales Funnel Flow

The flow of a customer through the sales funnel:

1. Customer lands on the homepage
2. Customer navigates to a product page
3. Customer adds the product to the cart
4. Customer proceeds to checkout
5. Customer completes the purchase
6. Customer is presented with upsell offers
7. Customer accepts or declines upsell offers
8. Customer receives order confirmation

### 4. Subscription Reorder Flow

The flow of a subscription reorder:

1. System initiates a reorder based on the subscription frequency
2. System processes the payment using the stored payment method
3. System creates a new order
4. System sends order confirmation to the customer
5. System ships the order to the customer

## Technical Implementation

The e-commerce platform is implemented using the following technologies:

- **Sylius**: A PHP-based e-commerce framework built on Symfony
- **Symfony**: A PHP framework for web applications
- **Doctrine**: An object-relational mapper (ORM) for database interactions
- **Twig**: A template engine for rendering HTML
- **JavaScript/jQuery**: For client-side interactions
- **SCSS/CSS**: For styling
- **MySQL**: For database storage

## Testing Challenges

Testing this e-commerce platform presents several challenges:

1. **Complex Workflows**: The purchase and checkout flows involve multiple steps and dependencies.
2. **State Management**: Tests need to maintain state across multiple steps and contexts.
3. **Data Integrity**: Tests need to verify that data is correctly processed and stored.
4. **Dynamic Content**: The platform includes dynamic content that changes based on user interactions.
5. **Cross-Browser Compatibility**: The platform needs to work correctly across different browsers and devices.
6. **Performance**: The platform needs to perform well under load, especially during checkout.

## Test Data Management

Test data for the e-commerce platform is managed through YAML-based fixtures organized by brand. This allows for:

1. **Brand-Specific Testing**: Tests can be run against different brands with different products and configurations.
2. **Consistent Test Data**: Tests use consistent data across different test runs.
3. **Maintainable Test Data**: Test data can be easily updated and maintained in a central location.
4. **Realistic Test Scenarios**: Test data reflects real-world scenarios and edge cases.

## Continuous Integration

The test automation framework is integrated with GitLab CI/CD for continuous integration and delivery. This ensures that:

1. **Tests Run Automatically**: Tests run automatically on code changes.
2. **Fast Feedback**: Developers receive fast feedback on the impact of their changes.
3. **Regression Prevention**: Regressions are caught early in the development process.
4. **Release Confidence**: Releases are made with confidence that the application works as expected.
