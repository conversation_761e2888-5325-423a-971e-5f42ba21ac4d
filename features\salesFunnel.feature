Feature: Core Sales Funnel Flows
  As an e-commerce store owner
  I want customers to complete purchases through sales funnels
  So that I can increase average order value and conversions

        Background:
            Given I load brand configuration

  @funnel @sales-funnel-high-priority @high-priority @smoke
        Scenario: Basic funnel flow with successful upsell
    Given I am on the sales funnel page "total-harmony-funnel"
    And I am on the checkout page
    When I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
    And I verify the shipping cost is "£2.95"
             When I enter "stripe_valid" payment details
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the order details are correct
              And I verify the order confirmation email
    And I verify the welcome email contains account credentials

        @funnel @regression @shipping
        Scenario Outline: Verify free shipping threshold with different funnel combinations
            Given I am on the sales funnel page "<funnel_id>"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
              And I verify the shipping cost is "<initial_shipping>"
             When I enter "stripe_valid" payment details
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the shipping cost is "<final_shipping>"
          And I validate both initial and upsell products are in the order

        Examples:
          | funnel_id            | initial_shipping | final_shipping |
          | total-harmony-funnel | £2.95            | FREE           |
          | ancient-roots-small  | £2.95            | £2.95          |

  @funnel @paypal @payment
  Scenario: Complete funnel purchase using PayPal
    Given I am on the sales funnel page "total-harmony-funnel"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I use the same address for billing
    Then The shipping method "Domestic tracked" should be selected
    When I select the "PayPal" payment method
    And I complete the purchase
    Then I should be redirected to the PayPal login page
    When I log in to PayPal with "valid" credentials
    And I confirm the PayPal payment
    Then I should be redirected to the upsell page
    When I accept the upsell offer
    Then I wait for the order confirmation page to load
    And I verify the order details are correct
    And I verify the order confirmation email

        @funnel @negative @payment
        Scenario: Handle expired card in funnel checkout
          Given I am on the sales funnel page "total-harmony-funnel"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I use the same address for billing
             Then The shipping method "Domestic tracked" should be selected
             When I enter "stripe_expired" payment details
              And I complete the purchase
             Then I should see an error message indicating the card has expired
              And I should remain on the checkout page

        @funnel @back-button @regression
        Scenario: Handle browser back button during funnel flow
          Given I am on the sales funnel page "total-harmony-funnel"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I navigate back in browser
             Then I should be redirected to the upsell page
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the order details are correct

  @funnel @compatibility
        Scenario: Verify product combination restrictions
            Given I am on the sales funnel page "natures-gift-basic"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I complete the purchase
             Then I should be redirected to the upsell page
    And I validate all dietary restriction warnings are displayed
             When I accept the upsell offer
             Then I wait for the order confirmation page to load
              And I verify the product instructions contain all warnings

        @funnel @duplicate-prevention @regression
        Scenario: Prevent duplicate upsell submissions
          Given I am on the sales funnel page "total-harmony-funnel"
             When I proceed to checkout
              And I fill in the shipping information with "default" user data
              And I complete the purchase
             Then I should be redirected to the upsell page
             When I click the accept button multiple times
             Then I wait for the order confirmation page to load
              And I verify only one upsell product is in the order