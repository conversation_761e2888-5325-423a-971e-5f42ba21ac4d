Feature: Sales Funnel Variations and Special Cases
  As an e-commerce store owner
  I want to support different sales funnel configurations and edge cases
  So that I can target different customer segments and maximize revenue

  Background:
    Given I load brand configuration
    And I load product data

  @funnel @multi-step
  Scenario: Multi-step funnel with multiple upsell pages
    Given I am on the multi-step funnel page "premium-journey"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I use the same address for billing
    And I enter "stripe_valid" payment details
    And I complete the purchase
    Then I should be redirected to the first upsell page
    When I accept the first upsell offer
    Then I should be redirected to the second upsell page
    When I accept the second upsell offer
    Then I wait for the order confirmation page to load
    And I verify the order contains all three products
    And I verify the order confirmation email contains all products

  @funnel @discount
  Scenario: Funnel with applied discount code
    Given I am on the sales funnel page "total-harmony-funnel"
    And I have a valid discount code "FUNNEL20"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I enter the discount code
    Then the discount should be applied to the order
    When I enter "stripe_valid" payment details
    And I complete the purchase
    Then I should be redirected to the upsell page
    When I accept the upsell offer
    Then I wait for the order confirmation page to load
    And I verify the discount was applied to the initial product only
    And I verify the order confirmation email shows the discount

  @funnel @international
  Scenario: International shipping in sales funnel
    Given I am on the sales funnel page "total-harmony-funnel"
    When I proceed to checkout
    And I fill in the shipping information with "international" user data
    And I use the same address for billing
    Then the international shipping methods should be available
    When I select the "International tracked" shipping method
    And I verify the international shipping cost
    And I enter "stripe_valid" payment details
    And I complete the purchase
    Then I should be redirected to the upsell page
    When I accept the upsell offer
    Then I wait for the order confirmation page to load
    And I verify the international shipping cost is correct
    And I verify the order confirmation email contains international shipping details

  @funnel @subscription
  Scenario: Funnel with subscription product
    Given I am on the subscription funnel page "monthly-harmony"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I select the subscription frequency "30 days"
    And I enter "stripe_valid" payment details
    And I complete the purchase
    Then I should be redirected to the upsell page
    When I accept the upsell offer
    Then I wait for the order confirmation page to load
    And I verify the order contains a subscription product
    And I verify the subscription frequency is "30 days"
    And I verify the subscription confirmation email was sent

  @funnel @decline-upsell
  Scenario: Decline upsell offer in funnel
    Given I am on the sales funnel page "total-harmony-funnel"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I enter "stripe_valid" payment details
    And I complete the purchase
    Then I should be redirected to the upsell page
    When I decline the upsell offer
    Then I wait for the order confirmation page to load
    And I verify the order contains only the initial product
    And I verify the order confirmation email

  @funnel @cross-sell @high-value
  Scenario: High-value funnel with multiple cross-sell opportunities
    Given I am on the high-value funnel page "premium-package"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I enter "stripe_valid" payment details
    And I complete the purchase
    Then I should be redirected to the first cross-sell page
    When I accept the first cross-sell offer
    Then I should be redirected to the second cross-sell page
    When I decline the second cross-sell offer
    Then I wait for the order confirmation page to load
    And I verify the order contains the initial product and first cross-sell
    And the order total should exceed the high-value threshold
    And I verify shipping is "FREE"