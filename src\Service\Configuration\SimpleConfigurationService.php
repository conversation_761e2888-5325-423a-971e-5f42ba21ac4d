<?php

namespace App\Service\Configuration;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Simple configuration service that works with existing files
 */
class SimpleConfigurationService extends AbstractService implements ConfigurationServiceInterface
{
    private string $configDir;
    private string $brand;
    private string $environment;
    private array $brandConfig = [];
    private array $envConfig = [];

    /**
     * Constructor
     *
     * @param string $configDir Configuration directory
     * @param string $brand Brand identifier
     * @param string $environment Environment identifier
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(string $configDir, string $brand, string $environment, ?LoggerInterface $logger = null)
    {
        parent::__construct($logger);

        $this->configDir = $configDir;
        $this->brand = $brand;
        $this->environment = $environment;

        $this->logInfo("SimpleConfigurationService initialized with brand: $brand, environment: $environment");

        // Load brand configuration from products.yml
        $this->loadBrandConfig();

        // Set default environment configuration
        $this->envConfig = [
            'url' => getenv('TEST_BASE_URL') ?: 'https://example.com',
            'api_url' => getenv('TEST_API_URL') ?: 'https://api.example.com',
            'admin_url' => getenv('TEST_ADMIN_URL') ?: 'https://admin.example.com',
        ];
    }

    /**
     * Load brand configuration from products.yml
     *
     * @throws RuntimeException When configuration cannot be loaded
     */
    private function loadBrandConfig(): void
    {
        $productsFile = sprintf('%s/brands/%s/products.yml', $this->configDir, $this->brand);

        if (!file_exists($productsFile)) {
            $this->logError("Products file not found: $productsFile");
            throw new RuntimeException("Products file not found: $productsFile");
        }

        try {
            $products = Yaml::parseFile($productsFile);

            if (!is_array($products) || empty($products)) {
                $this->logError("Invalid or empty products configuration");
                throw new RuntimeException("Invalid or empty products configuration");
            }

            // Set brand configuration
            $this->brandConfig = [
                'name' => ucfirst($this->brand),
                'slug' => $this->brand,
                'products' => $products
            ];

            $this->logInfo("Loaded brand configuration with " . count($products) . " products");
        } catch (\Exception $e) {
            $this->logError("Failed to parse products file", $e);
            throw new RuntimeException("Failed to parse products file: " . $e->getMessage());
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentBrand(): string
    {
        return $this->brand;
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentEnvironment(): string
    {
        return $this->environment;
    }

    /**
     * {@inheritdoc}
     */
    public function getBrandConfig(string $key)
    {
        if (!isset($this->brandConfig[$key])) {
            throw new RuntimeException(
                sprintf('Brand configuration not found: %s', $key)
            );
        }

        return $this->brandConfig[$key];
    }

    /**
     * {@inheritdoc}
     */
    public function getEnvironmentConfig(string $key)
    {
        // Special handling for base_url
        if ($key === 'base_url') {
            return $this->envConfig['url'];
        }

        if (!isset($this->envConfig[$key])) {
            throw new RuntimeException(
                sprintf('Environment configuration not found: %s', $key)
            );
        }

        return $this->envConfig[$key];
    }

    /**
     * {@inheritdoc}
     */
    public function getAllBrandConfig(): array
    {
        return $this->brandConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function getAllEnvironmentConfig(): array
    {
        return $this->envConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function setBrand(string $brand): void
    {
        $this->brand = $brand;
        $this->loadBrandConfig();
        $this->logInfo("Brand changed to: $brand");
    }

    /**
     * {@inheritdoc}
     */
    public function setEnvironment(string $environment): void
    {
        $this->environment = $environment;
        $this->logInfo("Environment changed to: $environment");
    }
}
