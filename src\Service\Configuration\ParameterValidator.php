<?php

namespace App\Service\Configuration;

/**
 * Service for validating parameters
 */
class ParameterValidator implements ParameterValidatorInterface
{
    /**
     * {@inheritdoc}
     */
    public function validateParameters(array $parameters): bool
    {
        $requiredParameters = [
            'app.project_root',
            'app.config_dir',
            'app.fixtures_dir',
            'app.cache_dir',
            'app.logs_dir'
        ];

        foreach ($requiredParameters as $parameter) {
            if (!isset($parameters[$parameter])) {
                throw new \RuntimeException("Missing required parameter: {$parameter}");
            }
        }

        return true;
    }
}