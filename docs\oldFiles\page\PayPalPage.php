<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Exception\ElementNotFoundException;

/**
 * PayPalPage handles actions on the PayPal payment page.
 */
class PayPalPage extends BasePage
{
    /**
     * The path of the PayPal page.
     *
     * @var string
     */
    protected $path = '/paypal';

    private const SELECTORS = [
        'EMAIL_FIELD' => '#email',
        'PASSWORD_FIELD' => '#password',
        'NEXT_BUTTON' => 'button[data-testid="next-button"]',
        'LOGIN_BUTTON' => 'button[data-testid="login-button"]',
        'AMOUNT' => '.test_transaction-amount',
        'CONFIRM_BUTTON' => '#confirmButtonTop',
        'PAYPAL_PAYMENT_OPTION' => '#paypal-payment-option',
        'PAYPAL_BUTTON' => '#paypal-button',
        'ERROR_MESSAGE' => '.notification-critical',
        'SUCCESS_MESSAGE' => '.payment-success-message'
    ];

    /**
     * Verifies that we're on the expected page.
     *
     * @throws ElementNotFoundException If required elements are not found
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        try {
            $this->waitForElementVisible(self::SELECTORS['EMAIL_FIELD']);
        } catch (ElementNotFoundException $e) {
            error_log("[PayPalPage] Failed to verify page: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Gets the URL for the PayPal page.
     *
     * @return string The complete URL
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Login to PayPal with provided credentials
     *
     * @param string $username PayPal username/email
     * @param string $password PayPal password
     * @throws \RuntimeException If login process fails
     */
    public function login(string $username, string $password): void
    {
        try {
            error_log("[PayPalPage] Attempting to login with username: $username");
            
            // Enter email and click next
            $this->waitForElementVisible(self::SELECTORS['EMAIL_FIELD']);
            $this->enterText(self::SELECTORS['EMAIL_FIELD'], $username);
            $this->clickElement(self::SELECTORS['NEXT_BUTTON']);
            
            // Enter password and click login
            $this->waitForElementVisible(self::SELECTORS['PASSWORD_FIELD']);
            $this->enterText(self::SELECTORS['PASSWORD_FIELD'], $password);
            $this->clickElement(self::SELECTORS['LOGIN_BUTTON']);
            
            error_log("[PayPalPage] Login attempt completed");
        } catch (\Exception $e) {
            $error = sprintf('PayPal login failed: %s', $e->getMessage());
            error_log("[PayPalPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Get the displayed transaction amount
     *
     * @return string The displayed amount
     * @throws ElementNotFoundException If amount element is not found
     */
    public function getDisplayedAmount(): string
    {
        try {
            return $this->getElementText(self::SELECTORS['AMOUNT']);
        } catch (ElementNotFoundException $e) {
            error_log("[PayPalPage] Failed to get displayed amount: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Confirm the PayPal payment
     *
     * @throws \RuntimeException If confirmation fails
     */
    public function confirmPayment(): void
    {
        try {
            error_log("[PayPalPage] Attempting to confirm payment");
            $this->waitForElementVisible(self::SELECTORS['CONFIRM_BUTTON']);
            $this->clickElement(self::SELECTORS['CONFIRM_BUTTON']);
            error_log("[PayPalPage] Payment confirmation completed");
        } catch (\Exception $e) {
            $error = sprintf('Failed to confirm PayPal payment: %s', $e->getMessage());
            error_log("[PayPalPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Wait for redirect to PayPal
     *
     * @throws \RuntimeException If redirect timeout occurs
     */
    public function waitForRedirectToPayPal(): void
    {
        try {
            error_log("[PayPalPage] Waiting for redirect to PayPal");
            $this->waitForUrlContains('sandbox.paypal.com');
            error_log("[PayPalPage] Successfully redirected to PayPal");
        } catch (\Exception $e) {
            $error = sprintf('Failed to redirect to PayPal: %s', $e->getMessage());
            error_log("[PayPalPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Check if there's a login error
     *
     * @return bool True if login error is present
     */
    public function hasLoginError(): bool
    {
        try {
            $this->waitForElementVisible(self::SELECTORS['ERROR_MESSAGE']);
            $errorText = $this->getElementText(self::SELECTORS['ERROR_MESSAGE']);
            $hasError = strpos($errorText, 'Some of your info is not correct') !== false;
            error_log("[PayPalPage] Login error status: " . ($hasError ? 'true' : 'false'));
            return $hasError;
        } catch (ElementNotFoundException $e) {
            error_log("[PayPalPage] No login error found");
            return false;
        }
    }

    /**
     * Wait for URL to contain specific string
     *
     * @param string $url URL substring to wait for
     */
    public function waitForUrlContains(string $url): void
    {
        $this->getSession()->wait(10000, 
            "window.location.href.indexOf('$url') !== -1");
    }

    /**
     * Select PayPal as payment method
     *
     * @throws \RuntimeException If selection fails
     */
    public function selectPayPalPayment(): void
    {
        try {
            error_log("[PayPalPage] Attempting to select PayPal payment");
            $this->waitForElementVisible(self::SELECTORS['PAYPAL_PAYMENT_OPTION']);
            $this->clickElement(self::SELECTORS['PAYPAL_PAYMENT_OPTION']);
            $this->waitForElementVisible(self::SELECTORS['PAYPAL_BUTTON']);
            $this->clickElement(self::SELECTORS['PAYPAL_BUTTON']);
            error_log("[PayPalPage] Successfully selected PayPal payment");
        } catch (\Exception $e) {
            $error = sprintf('Failed to select PayPal payment: %s', $e->getMessage());
            error_log("[PayPalPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Check if payment was successful
     *
     * @return bool True if payment was successful
     */
    public function isPaymentSuccessful(): bool
    {
        try {
            $this->waitForElementVisible(self::SELECTORS['SUCCESS_MESSAGE']);
            error_log("[PayPalPage] Payment successful");
            return true;
        } catch (ElementNotFoundException $e) {
            error_log("[PayPalPage] Payment not successful: " . $e->getMessage());
            return false;
        }
    }
} 