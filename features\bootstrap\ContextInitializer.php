<?php

use App\Context\Base\ServiceAwareContext;
use Behat\Behat\Context\Context;
use Behat\Behat\Context\Initializer\ContextInitializer as BehatContextInitializer;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

/**
 * Custom context initializer that injects the service container into contexts
 */
class ContextInitializer implements BehatContextInitializer
{
    private static ?ContainerBuilder $container = null;

    /**
     * Initialize the context
     *
     * @param Context $context
     */
    public function initializeContext(Context $context): void
    {
        // Only initialize ServiceAwareContext instances
        if (!$context instanceof ServiceAwareContext) {
            return;
        }

        // Initialize container if not already done
        if (self::$container === null) {
            self::$container = $this->buildContainer();
        }

        // Set the container on the context using reflection
        $reflection = new ReflectionClass($context);
        if ($reflection->hasProperty('container')) {
            $property = $reflection->getProperty('container');
            $property->setAccessible(true);
            $property->setValue($context, self::$container);
        }
    }

    /**
     * Build the service container
     *
     * @return ContainerBuilder
     */
    private function buildContainer(): ContainerBuilder
    {
        $container = new ContainerBuilder();

        // Set base parameters
        $projectRoot = dirname(dirname(__DIR__));
        $container->setParameter('kernel.project_dir', $projectRoot);
        $container->setParameter('paths.base', $projectRoot);
        $container->setParameter('app.project_root', $projectRoot);
        $container->setParameter('app.config_dir', $projectRoot . '/config');
        $container->setParameter('app.fixtures_dir', $projectRoot . '/features/fixtures');
        $container->setParameter('app.cache_dir', $projectRoot . '/var/cache');
        $container->setParameter('app.logs_dir', $projectRoot . '/var/logs');
        $container->setParameter('app.downloads_dir', $projectRoot . '/downloads');
        $container->setParameter('kernel.debug', true);

        // Load service configuration
        $loader = new YamlFileLoader($container, new FileLocator($projectRoot . '/config'));
        $loader->load('services.yml');

        // Compile container
        $container->compile();

        return $container;
    }
}
