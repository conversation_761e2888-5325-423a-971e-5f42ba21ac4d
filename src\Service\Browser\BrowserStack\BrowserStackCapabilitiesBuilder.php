<?php

namespace App\Service\Browser\BrowserStack;

use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

/**
 * Builder for BrowserStack capabilities
 */
class BrowserStackCapabilitiesBuilder
{
    private array $capabilities = [];
    private LoggerInterface $logger;

    /**
     * Constructor
     *
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?LoggerInterface $logger = null)
    {
        $this->logger = $logger ?? new NullLogger();

        // Set default capabilities using the format expected by MinkExtension BrowserStackFactory
        $this->capabilities = [
            'browserName' => 'Chrome',
            'browserVersion' => 'latest',
            'os' => 'Windows',
            'os_version' => '11',
            'resolution' => '1920x1080',
            'project' => 'Malaberg E-Commerce',
            'build' => 'Local Build',
            'name' => 'Test Session',
            'browserstack-debug' => true,
            'browserstack-tunnel' => false,
            'extra_capabilities' => [
                'acceptSslCerts' => true,
                'javascriptEnabled' => true,
                'consoleLogs' => 'verbose',
                'networkLogs' => true
            ]
        ];
    }

    /**
     * Set screen resolution
     *
     * @param string $resolution Screen resolution (e.g., 1920x1080)
     * @return self
     */
    public function setResolution(string $resolution): self
    {
        $this->capabilities['resolution'] = $resolution;
        return $this;
    }

    /**
     * Set custom capability
     *
     * @param string $name Capability name
     * @param mixed $value Capability value
     * @return self
     */
    public function setCapability(string $name, $value): self
    {
        // Convert bstack:options format to individual capabilities
        if (strpos($name, 'bstack:') === 0) {
            $key = substr($name, 7);
            // Map to legacy format keys
            switch ($key) {
                case 'os':
                    $this->capabilities['os'] = $value;
                    break;
                case 'osVersion':
                    $this->capabilities['os_version'] = $value;
                    break;
                case 'local':
                    $this->capabilities['browserstack-local'] = $value;
                    break;
                case 'debug':
                    $this->capabilities['browserstack-debug'] = $value;
                    break;
                case 'consoleLogs':
                    if (!isset($this->capabilities['extra_capabilities'])) {
                        $this->capabilities['extra_capabilities'] = [];
                    }
                    $this->capabilities['extra_capabilities']['consoleLogs'] = $value;
                    break;
                case 'networkLogs':
                    if (!isset($this->capabilities['extra_capabilities'])) {
                        $this->capabilities['extra_capabilities'] = [];
                    }
                    $this->capabilities['extra_capabilities']['networkLogs'] = $value;
                    break;
                case 'buildName':
                    $this->capabilities['build'] = $value;
                    break;
                case 'projectName':
                    $this->capabilities['project'] = $value;
                    break;
                case 'sessionName':
                    $this->capabilities['name'] = $value;
                    break;
                default:
                    // For any other bstack options, convert to browserstack-{key}
                    $this->capabilities['browserstack-' . $key] = $value;
            }
        } else {
            // Special handling for certain capability names
            if ($name === 'consoleLogs' || $name === 'networkLogs') {
                if (!isset($this->capabilities['extra_capabilities'])) {
                    $this->capabilities['extra_capabilities'] = [];
                }
                $this->capabilities['extra_capabilities'][$name] = $value;
            } else {
                $this->capabilities[$name] = $value;
            }
        }
        return $this;
    }

    /**
     * Load capabilities from environment variables
     *
     * @return self
     */
    public function loadFromEnvironment(): self
    {
        // Browser configuration
        if (getenv('BROWSER_NAME')) {
            $this->setBrowser(getenv('BROWSER_NAME'));
        }

        if (getenv('BROWSER_VERSION')) {
            $this->setBrowserVersion(getenv('BROWSER_VERSION'));
        }

        // Platform configuration
        if (getenv('PLATFORM')) {
            $this->setOS(getenv('PLATFORM'));
        }

        if (getenv('PLATFORM_VERSION')) {
            $this->setOSVersion(getenv('PLATFORM_VERSION'));
        }

        // Build information
        if (getenv('BUILD_NUMBER')) {
            $this->setBuildName('Build ' . getenv('BUILD_NUMBER'));
        }

        // Project information
        if (getenv('TEST_BRAND')) {
            $projectName = 'Malaberg E-Commerce';
            if (getenv('TEST_BRAND')) {
                $projectName .= ' - ' . getenv('TEST_BRAND');
            }
            $this->setProjectName($projectName);
        }

        // Debug configuration
        if (getenv('BROWSERSTACK_DEBUG') === 'true') {
            $this->setDebug(true);
        }

        // Console logs
        if (getenv('BROWSERSTACK_CONSOLE_LOGS')) {
            $this->setConsoleLogLevel(getenv('BROWSERSTACK_CONSOLE_LOGS'));
        }

        // Network logs
        if (getenv('BROWSERSTACK_NETWORK_LOGS') === 'true') {
            $this->setNetworkLogs(true);
        }

        // Local testing
        if (getenv('BROWSERSTACK_LOCAL') === 'true') {
            $this->setLocalTesting(true);
        }

        // Test name - use timestamp for uniqueness if not provided
        $this->setTestName('Test ' . date('Y-m-d H:i:s'));

        $this->logger->debug('Loaded BrowserStack capabilities from environment', $this->capabilities);

        return $this;
    }

    /**
     * Set browser name
     *
     * @param string $browserName Browser name (Chrome, Firefox, Safari, Edge, etc.)
     * @return self
     */
    public function setBrowser(string $browserName): self
    {
        $this->capabilities['browserName'] = $browserName;
        return $this;
    }

    /**
     * Set browser version
     *
     * @param string $version Browser version
     * @return self
     */
    public function setBrowserVersion(string $version): self
    {
        $this->capabilities['browserVersion'] = $version;
        return $this;
    }

    /**
     * Set operating system
     *
     * @param string $os Operating system (Windows, OS X, etc.)
     * @return self
     */
    public function setOS(string $os): self
    {
        $this->capabilities['os'] = $os;
        return $this;
    }

    /**
     * Set operating system version
     *
     * @param string $osVersion Operating system version
     * @return self
     */
    public function setOSVersion(string $osVersion): self
    {
        $this->capabilities['os_version'] = $osVersion;
        return $this;
    }

    /**
     * Set build name
     *
     * @param string $buildName Build name
     * @return self
     */
    public function setBuildName(string $buildName): self
    {
        $this->capabilities['build'] = $buildName;
        return $this;
    }

    /**
     * Set project name
     *
     * @param string $projectName Project name
     * @return self
     */
    public function setProjectName(string $projectName): self
    {
        $this->capabilities['project'] = $projectName;
        return $this;
    }

    /**
     * Enable debug mode
     *
     * @param bool $enabled Whether to enable debug mode
     * @return self
     */
    public function setDebug(bool $enabled): self
    {
        $this->capabilities['browserstack-debug'] = $enabled ? 'true' : 'false';
        return $this;
    }

    /**
     * Set console log level
     *
     * @param string $level Console log level (disable, errors, warnings, info, verbose)
     * @return self
     */
    public function setConsoleLogLevel(string $level): self
    {
        if (!isset($this->capabilities['extra_capabilities'])) {
            $this->capabilities['extra_capabilities'] = [];
        }
        $this->capabilities['extra_capabilities']['consoleLogs'] = $level;
        return $this;
    }

    /**
     * Enable network logs
     *
     * @param bool $enabled Whether to enable network logs
     * @return self
     */
    public function setNetworkLogs(bool $enabled): self
    {
        if (!isset($this->capabilities['extra_capabilities'])) {
            $this->capabilities['extra_capabilities'] = [];
        }
        $this->capabilities['extra_capabilities']['networkLogs'] = $enabled;
        return $this;
    }

    /**
     * Enable BrowserStack Local testing
     *
     * @param bool $enabled Whether to enable local testing
     * @return self
     */
    public function setLocalTesting(bool $enabled): self
    {
        $this->capabilities['browserstack-local'] = $enabled ? 'true' : 'false';
        return $this;
    }

    /**
     * Set test name
     *
     * @param string $testName Test name
     * @return self
     */
    public function setTestName(string $testName): self
    {
        $this->capabilities['name'] = $testName;
        return $this;
    }

    /**
     * Build capabilities array
     *
     * @return array
     */
    public function build(): array
    {
        return $this->capabilities;
    }

    /**
     * Enable/disable screenshots
     *
     * @param bool $enabled Whether to enable screenshots
     * @return self
     */
    public function withScreenshots(bool $enabled): self
    {
        $this->capabilities['browserstack-debug'] = $enabled;
        return $this;
    }
}
