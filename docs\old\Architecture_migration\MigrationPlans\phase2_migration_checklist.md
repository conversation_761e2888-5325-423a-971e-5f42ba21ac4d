# Phase 2: Service Definition Refactoring Checklist

This document provides a checklist for Phase 2 of the architecture migration: Service Definition Refactoring.

## Overview

Phase 2 focuses on defining contexts as services in the Symfony container and configuring Behat to use these services.
This eliminates the need for global state and mock services, making the codebase more maintainable and reliable.

## Completed Tasks

- [x] Install friends-of-behat/service-container-extension
- [x] Configure behat.yml to use service IDs instead of class names
- [x] Define contexts as services in config/services/contexts.yml
- [x] Create bin/verify-container.php script
- [x] Update SalesFunnelContext to use proper dependency injection
- [x] Remove mock service creation methods
- [x] Document the context service pattern
- [x] Create dependency injection guidelines

## Remaining Tasks

- [ ] Test the changes with a simple feature
- [ ] Update other contexts to use proper dependency injection
- [ ] Remove mock service creation methods from other contexts
- [ ] Verify that all contexts can be instantiated with their dependencies

## Context Service Definition Checklist

For each context:

- [x] Define the context as a service in config/services/contexts.yml
- [x] Use a consistent service ID pattern (behat.context.{name})
- [x] Make the service public
- [x] Inject the container and specific services
- [x] Tag the service with 'context.service'

## Context Implementation Checklist

For each context:

- [x] SalesFunnelContext
    - [x] Update constructor to accept specific dependencies
    - [x] Remove mock service creation methods
    - [x] Remove fallback logic for mock services
    - [x] Use type hints for all dependencies
    - [x] Update PHPDoc comments

- [ ] FeatureContext
- [ ] BrandContext
- [ ] ProductContext
- [ ] CartContext
- [ ] CheckoutContext
- [ ] PaymentContext
- [ ] TestDataContext
- [ ] EmailContext
- [ ] UpsellContext
- [ ] AdminCommandContext
- [ ] ValidationContext
- [ ] SSHContext
- [ ] AbandonedCartContext
- [ ] DatabaseContext

## Testing Checklist

- [ ] Run bin/verify-container.php to verify service configuration
- [ ] Run a simple feature test to verify functionality
- [ ] Run a comprehensive test suite to verify all features
- [ ] Verify that no contexts fall back to mock services
- [ ] Check for any performance regressions

## Documentation Checklist

- [x] Document the context service pattern
- [x] Create dependency injection guidelines
- [x] Create Phase 2 migration checklist
- [ ] Update README with new architecture information

## Verification Steps

1. **Verify container configuration**
   ```bash
   php bin/verify-container.php
   ```

2. **Run a simple feature test**
   ```bash
   php bin/run-tests.php --brand=aeons --env=stage --feature=features/salesFunnel.feature --tags=@high-priority
   ```

3. **Verify that SalesFunnelContext uses real services**
    - Check logs for any fallback to mock services
    - Verify that the context is properly instantiated with its dependencies

4. **Verify documentation**
    - Check that the context service pattern is documented
    - Verify that dependency injection guidelines are clear
    - Ensure that the migration checklist is complete
