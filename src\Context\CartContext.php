<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Page\Base\BasePageInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for cart-related functionality
 */
class CartContext extends BaseContext
{
    private PageFactoryInterface $pageFactory;
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param PageFactoryInterface|null $pageFactory Page factory service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface          $container = null,
        ?PageFactoryInterface        $pageFactory = null,
        ?SharedStateServiceInterface $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Check if we're forcing real services
            if (getenv('FORCE_REAL_SERVICES') === 'true') {
                // Try to get the global container
                if (isset($GLOBALS['service_container'])) {
                    $container = $GLOBALS['service_container'];
                    $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
                    $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
                } else {
                    // Fall back to mock services if global container is not available
                    $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
                    $this->stateService = $stateService ?? $this->createMockStateService();
                }
            } else {
                // Create mock services if container is not available and not forcing real services
                $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
                $this->stateService = $stateService ?? $this->createMockStateService();
            }
        }

        $this->logInfo("CartContext initialized with " . ($container !== null ? "container" : "no container") .
            " and " . (get_class($this->pageFactory)) . " page factory");
    }

    /**
     * Create a mock page factory for testing
     *
     * @return PageFactoryInterface
     */
    private function createMockPageFactory(): PageFactoryInterface
    {
        return new class implements PageFactoryInterface {
            public function createPage(string $pageClass, array $parameters = []): BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot create pages');
            }

            public function getPage(string $pageName, array $parameters = []): BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot get pages');
            }

            public function hasPage(string $pageName): bool
            {
                return false;
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @Given I am on the cart page
     * @Given I view my cart
     */
    public function iAmOnTheCartPage(): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $cartPage->open();

            // Store cart state in shared data
            $isEmpty = $cartPage->isEmpty();
            $this->stateService->set('cart.is_empty', $isEmpty);

            if (!$isEmpty) {
                $itemCount = $cartPage->getItemCount();
                $total = $cartPage->getCartTotal();

                $this->stateService->set('cart.item_count', $itemCount);
                $this->stateService->set('cart.total', $total);

                $this->logInfo(sprintf('Cart has %d items with total %s', $itemCount, $total));
            } else {
                $this->logInfo('Cart is empty');
            }

            $this->stateService->set('page.current', 'cart');
            $this->logInfo("Navigated to cart page");
        } catch (\Throwable $e) {
            $this->logError("Failed to navigate to cart page", $e);
            throw $e;
        }
    }

    /**
     * @When I proceed to checkout
     */
    public function iProceedToCheckout(): void
    {
        try {
            // Check if we're already on the checkout page (due to a redirect)
            if ($this->stateService->get('page.current') === 'checkout') {
                $this->logInfo("Already on checkout page due to redirect");
                return;
            }

            $cartPage = $this->pageFactory->getPage('CartPage');

            // Check if cart is empty
            if ($cartPage->isEmpty()) {
                throw new \RuntimeException('Cannot proceed to checkout with empty cart');
            }

            // Proceed to checkout
            $cartPage->proceedToCheckout();

            $this->stateService->set('cart.checkout_started', true);
            $this->stateService->set('page.current', 'checkout');

            $this->logInfo("Proceeded to checkout");
        } catch (\Throwable $e) {
            $this->logError("Failed to proceed to checkout", $e);
            throw $e;
        }
    }

    /**
     * @When I update the quantity of item :index to :quantity
     */
    public function iUpdateTheQuantityOfItemTo(int $index, int $quantity): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $cartPage->updateItemQuantity($index, $quantity);

            // Store updated quantity in state for verification
            $this->stateService->set('cart.item.' . $index . '.quantity', $quantity);

            $this->logInfo("Updated quantity of item $index to $quantity");
        } catch (\Throwable $e) {
            $this->logError("Failed to update quantity of item $index to $quantity", $e);
            throw $e;
        }
    }

    /**
     * @When I remove item :index from the cart
     * @When I remove item from cart
     */
    public function iRemoveItemFromTheCart(int $index = 1): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');

            $itemCountBefore = $cartPage->getItemCount();

            if ($index === 1) {
                $cartPage->removeFirstItem();
            } else {
                $cartPage->removeItemByIndex($index);
            }

            $itemCountAfter = $cartPage->getItemCount();

            // Update cart items in shared state
            $cartItems = $this->stateService->get('cart.items', 'scenario') ?? [];
            if (isset($cartItems[$index - 1])) {
                array_splice($cartItems, $index - 1, 1);
                $this->stateService->set('cart.items', $cartItems);
            }

            $this->stateService->set('cart.item_count', $itemCountAfter);

            $this->logInfo(sprintf("Removed item %d from cart. Items before: %d, after: %d",
                $index, $itemCountBefore, $itemCountAfter));
        } catch (\Throwable $e) {
            $this->logError("Failed to remove item $index from cart", $e);
            throw $e;
        }
    }

    /**
     * @Then I should see :count item(s) in the cart
     * @Then the cart should contain :count item(s)
     */
    public function iShouldSeeItemsInTheCart(int $count): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $actualCount = $cartPage->getItemCount();

            if ($actualCount !== $count) {
                throw new \RuntimeException(
                    sprintf('Expected %d items in cart, but found %d', $count, $actualCount)
                );
            }

            $this->logInfo("Verified cart contains $count items");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify cart item count", $e);
            throw $e;
        }
    }

    /**
     * @Then the cart should contain :count subscription items
     */
    public function theCartShouldContainSubscriptionItemsCount(int $count): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $subscriptionCount = $cartPage->getSubscriptionItemCount();

            if ($subscriptionCount !== $count) {
                throw new \RuntimeException(
                    sprintf('Expected %d subscription items in cart, but found %d', $count, $subscriptionCount)
                );
            }

            $this->logInfo("Verified cart contains $count subscription items");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify subscription item count", $e);
            throw $e;
        }
    }

    /**
     * @Then the cart should contain :count one-time purchase items
     */
    public function theCartShouldContainOneTimePurchaseItemsCount(int $count): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $oneTimeCount = $cartPage->getOneTimePurchaseItemCount();

            if ($oneTimeCount !== $count) {
                throw new \RuntimeException(
                    sprintf('Expected %d one-time purchase items in cart, but found %d', $count, $oneTimeCount)
                );
            }

            $this->logInfo("Verified cart contains $count one-time purchase items");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify one-time purchase item count", $e);
            throw $e;
        }
    }

    /**
     * @Then the subscription items should have correct frequencies
     */
    public function theSubscriptionItemsShouldHaveCorrectFrequenciesVerify(): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $frequencies = $cartPage->getSubscriptionItemFrequencies();

            // Get expected frequencies from state
            $expectedFrequencies = $this->stateService->get('cart.subscription_frequencies') ?? [];

            foreach ($expectedFrequencies as $index => $expectedFrequency) {
                if (!isset($frequencies[$index]) || $frequencies[$index] !== $expectedFrequency) {
                    throw new \RuntimeException(
                        sprintf(
                            'Expected subscription frequency "%s" for item %d, but found "%s"',
                            $expectedFrequency,
                            $index,
                            $frequencies[$index] ?? 'none'
                        )
                    );
                }
            }

            $this->logInfo("Verified subscription frequencies are correct");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify subscription frequencies", $e);
            throw $e;
        }
    }

    /**
     * @Then the cart should be empty
     * @Then the cart should not be empty
     */
    public function theCartShouldBeEmpty($not = null): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $isEmpty = $cartPage->isEmpty();

            if ($not === null && !$isEmpty) {
                throw new \RuntimeException('Expected cart to be empty, but it contains items');
            } elseif ($not !== null && $isEmpty) {
                throw new \RuntimeException('Expected cart to contain items, but it is empty');
            }

            $this->logInfo("Verified cart is " . ($not === null ? "empty" : "not empty"));
        } catch (\Throwable $e) {
            $this->logError("Failed to verify cart emptiness", $e);
            throw $e;
        }
    }

    /**
     * @Then the cart should contain :count subscription items
     */
    public function theCartShouldContainSubscriptionItems(int $count): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $subscriptionItems = $cartPage->getSubscriptionItemCount();

            if ($subscriptionItems !== $count) {
                throw new \RuntimeException(
                    sprintf('Expected %d subscription items in cart, but found %d', $count, $subscriptionItems)
                );
            }

            $this->logInfo(sprintf("Verified cart contains %d subscription items", $count));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to verify subscription item count: %d", $count), $e);
            throw $e;
        }
    }

    /**
     * @Then the cart should contain :count one-time purchase items
     */
    public function theCartShouldContainOneTimePurchaseItems(int $count): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $oneTimeItems = $cartPage->getOneTimeItemCount();

            if ($oneTimeItems !== $count) {
                throw new \RuntimeException(
                    sprintf('Expected %d one-time purchase items in cart, but found %d', $count, $oneTimeItems)
                );
            }

            $this->logInfo(sprintf("Verified cart contains %d one-time purchase items", $count));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to verify one-time purchase item count: %d", $count), $e);
            throw $e;
        }
    }

    /**
     * @Then the subscription items should have correct frequencies
     */
    public function theSubscriptionItemsShouldHaveCorrectFrequencies(): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $cartItems = $this->stateService->get('cart.items', 'scenario') ?? [];

            $subscriptionItems = array_filter($cartItems, function ($item) {
                return isset($item['purchase_type']) && $item['purchase_type'] === 'subscription';
            });

            foreach ($subscriptionItems as $item) {
                $expectedFrequency = $item['subscription_frequency_display'] ?? null;
                if (!$expectedFrequency) {
                    continue;
                }

                $itemName = $item['product'];
                $actualFrequency = $cartPage->getSubscriptionFrequencyForProduct($itemName);

                if ($actualFrequency !== $expectedFrequency) {
                    throw new \RuntimeException(
                        sprintf('Expected frequency "%s" for product "%s", but got "%s"',
                            $expectedFrequency, $itemName, $actualFrequency)
                    );
                }
            }

            $this->logInfo("Verified all subscription items have correct frequencies");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify subscription frequencies", $e);
            throw $e;
        }
    }

    /**
     * @Then my cart should be restored
     */
    public function myCartShouldBeRestored(): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');
            $abandonedItems = $this->stateService->get('abandoned_cart.items');

            if (!$abandonedItems || !is_array($abandonedItems)) {
                throw new \RuntimeException('No abandoned cart items found in state');
            }

            $currentItems = $cartPage->getCartItems();

            // Check item count first
            if (count($currentItems) !== count($abandonedItems)) {
                throw new \RuntimeException(
                    sprintf('Expected %d items in restored cart, but found %d',
                        count($abandonedItems),
                        count($currentItems)
                    )
                );
            }

            // Check each item matches the abandoned cart
            foreach ($abandonedItems as $i => $abandonedItem) {
                if (!isset($currentItems[$i]) ||
                    $currentItems[$i]['product'] !== $abandonedItem['product'] ||
                    $currentItems[$i]['quantity'] !== $abandonedItem['quantity']) {
                    throw new \RuntimeException(
                        sprintf('Mismatch in restored cart item %d', $i + 1)
                    );
                }
            }

            $this->logInfo("Verified cart has been correctly restored with all expected items");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify cart restoration", $e);
            throw $e;
        }
    }

    /**
     * @Then my cart should be restored with original items
     */
    public function myCartShouldBeRestoredWithOriginalItems(): void
    {
        try {
            $cartPage = $this->pageFactory->getPage('CartPage');

            // Get original cart items from state
            $originalItems = $this->stateService->get('cart.original_items') ?? [];

            // Get current cart items
            $currentItems = $cartPage->getCartItems();

            // Compare items
            if (count($originalItems) !== count($currentItems)) {
                throw new \RuntimeException(
                    sprintf(
                        'Expected %d items in restored cart, but found %d',
                        count($originalItems),
                        count($currentItems)
                    )
                );
            }

            foreach ($originalItems as $index => $originalItem) {
                if (!isset($currentItems[$index])) {
                    throw new \RuntimeException(
                        sprintf('Expected item %d in restored cart, but it is missing', $index)
                    );
                }

                // Compare product, quantity, and purchase type
                foreach (['product', 'quantity', 'purchase_type'] as $property) {
                    if ($originalItem[$property] !== $currentItems[$index][$property]) {
                        throw new \RuntimeException(
                            sprintf(
                                'Expected %s "%s" for item %d in restored cart, but found "%s"',
                                $property,
                                $originalItem[$property],
                                $index,
                                $currentItems[$index][$property]
                            )
                        );
                    }
                }
            }

            $this->logInfo("Verified cart has been restored");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify cart restoration", $e);
            throw $e;
        }
    }
}
