<?php

namespace Features\Bootstrap\Context;

use Behat\Behat\Context\Context;
use Features\Bootstrap\Core\ContextManager;
use App\Service\State\SharedStateServiceInterface;
use Throwable;

abstract class BaseContext implements Context
{
    protected ?ContextManager $contextManager = null;
    protected SharedStateServiceInterface $stateService;

    public function __construct(SharedStateServiceInterface $stateService = null)
    {
        $this->stateService = $stateService;
    }

    public function setContextManager(ContextManager $contextManager): void
    {
        $this->contextManager = $contextManager;
    }

    protected function logInfo(string $message): void
    {
        // Standard logging implementation
        // Can be enhanced with proper logger integration
        error_log("[INFO] " . $message);
    }

    protected function logError(string $message, ?Throwable $exception = null): void
    {
        // Standard error logging
        $logMessage = "[ERROR] " . $message;
        if ($exception) {
            $logMessage .= " - " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
        }
        error_log($logMessage);
    }
}
