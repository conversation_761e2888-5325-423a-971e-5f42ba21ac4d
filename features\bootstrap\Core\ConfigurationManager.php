<?php

namespace Features\Bootstrap\Core;

use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Manages brand and environment configurations
 */
class ConfigurationManager
{
    private array $brandConfigs = [];
    private array $envConfigs = [];
    private string $configPath;
    private string $currentBrand;
    private string $currentEnv;

    public function __construct(string $configPath = null, ?string $brand = null, ?string $environment = null)
    {
        $this->configPath = $configPath ?? dirname(__DIR__, 3) . '/config';
        
        // Set default or explicitly provided brand
        $this->currentBrand = $brand ?? getenv('TEST_BRAND') ?? 'aeons';
        $this->currentEnv = $environment ?? getenv('TEST_ENV') ?? 'stage';
        
        // Load configs
        $this->loadBrandConfig($this->currentBrand);
        $this->loadEnvironmentConfig($this->currentBrand, $this->currentEnv);
    }

    /**
     * Sets the current brand and loads its configuration
     *
     * @param string $brand Brand identifier
     * @throws RuntimeException When brand configuration cannot be loaded
     */
    public function setBrand(string $brand): void
    {
        if (!isset($this->brandConfigs[$brand])) {
            $this->loadBrandConfig($brand);
        }
        $this->currentBrand = $brand;
    }

    /**
     * Sets the current environment and loads its configuration
     *
     * @param string $env Environment identifier
     * @throws RuntimeException When environment configuration cannot be loaded
     */
    public function setEnvironment(string $env): void
    {
        if (!isset($this->envConfigs[$this->currentBrand][$env])) {
            $this->loadEnvironmentConfig($this->currentBrand, $env);
        }
        $this->currentEnv = $env;
    }

    /**
     * Gets brand configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     * @throws RuntimeException When configuration not found
     */
    public function getBrandConfig(string $key)
    {
        if (!isset($this->brandConfigs[$this->currentBrand][$key])) {
            throw new RuntimeException(
                sprintf('Brand configuration not found: %s', $key)
            );
        }
        return $this->brandConfigs[$this->currentBrand][$key];
    }

    /**
     * Gets environment configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     * @throws RuntimeException When configuration not found
     */
    public function getEnvironmentConfig(string $key)
    {
        // Special handling for base_url
        if ($key === 'base_url') {
            return $this->envConfigs[$this->currentBrand][$this->currentEnv]['url'];
        }

        if (!isset($this->envConfigs[$this->currentBrand][$this->currentEnv][$key])) {
            throw new RuntimeException(
                sprintf('Environment configuration not found: %s', $key)
            );
        }
        return $this->envConfigs[$this->currentBrand][$this->currentEnv][$key];
    }

    /**
     * Gets the current brand identifier
     *
     * @return string Current brand
     * @throws RuntimeException When no brand is set
     */
    public function getCurrentBrand(): string
    {
        if (!isset($this->currentBrand)) {
            throw new RuntimeException('No brand is currently set');
        }
        return $this->currentBrand;
    }

    /**
     * Gets the current environment identifier
     *
     * @return string Current environment
     * @throws RuntimeException When no environment is set
     */
    public function getCurrentEnvironment(): string
    {
        if (!isset($this->currentEnv)) {
            throw new RuntimeException('No environment is currently set');
        }
        return $this->currentEnv;
    }

    /**
     * Loads brand configuration from file
     *
     * @param string $brand Brand identifier
     * @throws RuntimeException When configuration cannot be loaded
     */
    private function loadBrandConfig(string $brand): void
    {
        error_log(sprintf("[ConfigurationManager] Loading brand config for: %s", $brand));
        // Check both formats - combined file or environment-specific
        $pathOptions = [
            sprintf('%s/brands/%s/%s.yml', $this->configPath, $brand, $this->currentEnv ?? 'stage'),
            sprintf('%s/brands/%s/config.yml', $this->configPath, $brand),
        ];
        
        $loaded = false;
        foreach ($pathOptions as $path) {
            if (file_exists($path)) {
                error_log(sprintf("[ConfigurationManager] Loading from path: %s", $path));
                $config = Yaml::parseFile($path);
                $this->validateBrandConfig($config);
                $this->brandConfigs[$brand] = $config['brand'];
                $loaded = true;
                break;
            }
        }
        
        if (!$loaded) {
            throw new RuntimeException(
                sprintf('Brand configuration file not found. Tried: %s', implode(', ', $pathOptions))
            );
        }
    }

    /**
     * Loads environment configuration from file
     *
     * @param string $brand Brand identifier
     * @param string $env Environment identifier
     * @throws RuntimeException When configuration cannot be loaded
     */
    private function loadEnvironmentConfig(string $brand, string $env): void
    {
        error_log(sprintf("[ConfigurationManager] Loading environment config for brand: %s, env: %s", $brand, $env));
        
        // Check both formats - combined file or environment-specific
        $pathOptions = [
            sprintf('%s/brands/%s/%s.yml', $this->configPath, $brand, $env),
            sprintf('%s/environments/%s/%s.yml', $this->configPath, $brand, $env),
            sprintf('%s/brands/%s/config.yml', $this->configPath, $brand),
        ];
        
        $loaded = false;
        foreach ($pathOptions as $path) {
            if (file_exists($path)) {
                error_log(sprintf("[ConfigurationManager] Loading from path: %s", $path));
                
                if (!isset($this->envConfigs[$brand])) {
                    $this->envConfigs[$brand] = [];
                }

                $config = Yaml::parseFile($path);
                
                // Handle different file formats
                if (isset($config['environments'][$env])) {
                    // Format with environments key
                    $this->validateEnvironmentConfig($config['environments'][$env]);
                    $this->envConfigs[$brand][$env] = $config['environments'][$env];
                } else if (isset($config['brand'])) {
                    // Format with brand key
                    $this->validateEnvironmentConfig($config['brand']);
                    $this->envConfigs[$brand][$env] = $config['brand'];
                } else {
                    // Direct format
                    $this->validateEnvironmentConfig($config);
                    $this->envConfigs[$brand][$env] = $config;
                }
                
                $loaded = true;
                break;
            }
        }
        
        if (!$loaded) {
            throw new RuntimeException(
                sprintf('Environment configuration file not found. Tried: %s', implode(', ', $pathOptions))
            );
        }
    }

    /**
     * Validates brand configuration structure
     *
     * @param array $config Configuration to validate
     * @throws RuntimeException When validation fails
     */
    private function validateBrandConfig(array $config): void
    {
        if (!isset($config['brand'])) {
            throw new RuntimeException('Missing brand configuration section');
        }

        $brandConfig = $config['brand'];
        $requiredFields = [
            'name',
            'code',
            'url',
            'currency',
            'features'
        ];

        foreach ($requiredFields as $field) {
            if (!isset($brandConfig[$field])) {
                throw new RuntimeException(
                    sprintf('Missing required brand configuration field: %s', $field)
                );
            }
        }

        if (!is_array($brandConfig['features'])) {
            throw new RuntimeException('Features must be an array');
        }
    }

    /**
     * Validates environment configuration structure
     *
     * @param array $config Configuration to validate
     * @throws RuntimeException When validation fails
     */
    private function validateEnvironmentConfig(array $config): void
    {
        $requiredFields = [
            'url',
        ];

        foreach ($requiredFields as $field) {
            if (!isset($config[$field])) {
                throw new RuntimeException(
                    sprintf('Missing required environment configuration field: %s', $field)
                );
            }
        }
    }
} 