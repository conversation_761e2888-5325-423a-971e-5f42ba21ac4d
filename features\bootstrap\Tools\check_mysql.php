<?php

require_once __DIR__ . '/../../../vendor/autoload.php';

use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Net\SSH2;

try {
    echo "Checking MySQL service status...\n\n";

    $ssh = new SSH2('18.170.243.171');
    $key = PublicKeyLoader::load("**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");

    if (!$ssh->login('ec2-user', $key)) {
        throw new RuntimeException('SSH authentication failed');
    }

    // Check MySQL service status
    echo "1. MySQL Service Status:\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('sudo systemctl status mysqld 2>/dev/null || sudo systemctl status mysql 2>/dev/null || sudo service mysqld status 2>/dev/null || sudo service mysql status 2>/dev/null');
    echo $result . "\n\n";

    // Check MySQL process
    echo "2. MySQL Process Check:\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('ps aux | grep mysql | grep -v grep');
    echo $result . "\n\n";

    // Check MySQL port
    echo "3. MySQL Port Check:\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('sudo lsof -i :3306');
    echo $result . "\n\n";

    // Check MySQL error log
    echo "4. Recent MySQL Errors:\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('sudo tail -n 20 /var/log/mysqld.log 2>/dev/null || sudo tail -n 20 /var/log/mysql/error.log 2>/dev/null');
    echo $result . "\n\n";

    // Try direct MySQL connection
    echo "5. Testing MySQL Connection:\n";
    echo str_repeat('-', 50) . "\n";
    $result = $ssh->exec('mysql -h 35.177.77.173 -u aeons_remote -pK1QtsqYseONO7j9Z -e "SELECT VERSION();" 2>&1');
    echo $result . "\n\n";

    echo "MySQL checks completed.\n";

} catch (Exception $e) {
    echo "Error during checks: " . $e->getMessage() . "\n";
    exit(1);
} 