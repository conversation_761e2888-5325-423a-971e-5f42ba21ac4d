<?php

namespace App\Page\Element;

use App\Service\Browser\BrowserServiceInterface;

/**
 * SearchForm element for handling search functionality.
 */
class SearchForm
{
    /**
     * CSS selector for this element
     */
    private string $selector = 'form.search-form';

    /**
     * Browser service
     *
     * @var BrowserServiceInterface
     */
    private BrowserServiceInterface $browserService;

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     */
    public function __construct(BrowserServiceInterface $browserService)
    {
        $this->browserService = $browserService;
    }

    /**
     * Performs a search using the search form
     *
     * @param string $keywords Search keywords to enter
     * @return void
     */
    public function search(string $keywords): void
    {
        $this->browserService->fillField($this->selector . ' input[name="q"]', $keywords);
        $this->browserService->clickElement($this->selector . ' button[type="submit"]');
        $this->browserService->waitForPageToLoad();
    }

    /**
     * Check if the search form is visible
     *
     * @return bool
     */
    public function isVisible(): bool
    {
        return $this->browserService->elementExists($this->selector);
    }

    /**
     * Get the search input value
     *
     * @return string
     */
    public function getSearchValue(): string
    {
        return $this->browserService->getElementAttribute($this->selector . ' input[name="q"]', 'value');
    }
}
