<?php

namespace Features\Bootstrap\Helper;

use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Net\SSH2;
use RuntimeException;

class SSHTunnel
{
    private SSH2 $ssh;
    private array $config;
    private int $localPort;
    private bool $isConnected = false;
    private ?int $tunnelPid = null;

    public function __construct(array $config)
    {
        $this->validateConfig($config);
        $this->config = $config;
        $this->localPort = $this->findAvailablePort();
    }

    private function validateConfig(array $config): void
    {
        if (empty($config['ssh']['host'])) {
            throw new RuntimeException('SSH host is required');
        }
        if (empty($config['ssh']['user'])) {
            throw new RuntimeException('SSH user is required');
        }
        if (empty($config['ssh']['private_key']) && empty($config['ssh']['password'])) {
            throw new RuntimeException('Either SSH private key or password is required');
        }
    }

    private function findAvailablePort(int $start = 49152, int $end = 65535): int
    {
        for ($port = $start; $port <= $end; $port++) {
            $sock = @fsockopen('127.0.0.1', $port, $errno, $errstr, 0.1);
            if (!$sock) {
                return $port;
            }
            fclose($sock);
        }
        throw new RuntimeException('No available ports found');
    }

    public function connect(): void
    {
        try {
            echo "Establishing SSH connection...\n";
            echo "SSH Host: {$this->config['ssh']['host']}\n";
            echo "SSH User: {$this->config['ssh']['user']}\n";

            // Initialize SSH connection with extended timeout
            $this->ssh = new SSH2($this->config['ssh']['host']);
            $this->ssh->setTimeout(30);

            // Enable verbose debugging
            $this->ssh->enableQuietMode(false);

            // Authenticate
            if (isset($this->config['ssh']['private_key'])) {
                $key = $this->loadPrivateKey($this->config['ssh']['private_key']);
                if (!$this->ssh->login($this->config['ssh']['user'], $key)) {
                    throw new RuntimeException('SSH authentication failed with key');
                }
            } else {
                if (!$this->ssh->login($this->config['ssh']['user'], $this->config['ssh']['password'])) {
                    throw new RuntimeException('SSH authentication failed with password');
                }
            }

            echo "SSH connection established successfully.\n";

            // Set up port forwarding
            $this->setupPortForwarding();

            $this->isConnected = true;

        } catch (\Exception $e) {
            throw new RuntimeException('SSH connection failed: ' . $e->getMessage());
        }
    }

    private function loadPrivateKey(string $key): \phpseclib3\Crypt\Common\PrivateKey
    {
        // If the key starts with ----- it's the actual key content
        if (str_starts_with(trim($key), '-----BEGIN')) {
            return PublicKeyLoader::load($key);
        }

        // Otherwise treat it as a file path
        if (!file_exists($key)) {
            throw new RuntimeException("SSH key file not found: $key");
        }

        $keyContent = file_get_contents($key);
        if ($keyContent === false) {
            throw new RuntimeException("Unable to read key file: $key");
        }

        return PublicKeyLoader::load($keyContent);
    }

    private function setupPortForwarding(): void
    {
        $remoteHost = $this->config['db']['host'];
        $remotePort = $this->config['db']['port'];

        echo "Setting up port forwarding...\n";
        echo "Local port: {$this->localPort}\n";
        echo "Remote: {$remoteHost}:{$remotePort}\n";

        // First, verify we can reach the MySQL server
        $checkResult = $this->ssh->exec("nc -zv {$remoteHost} {$remotePort} 2>&1");
        echo "MySQL connectivity check: $checkResult\n";

        if (strpos($checkResult, 'Connected') === false) {
            throw new RuntimeException("Cannot connect to MySQL server: $checkResult");
        }

        // Kill any existing processes using our port
        $this->ssh->exec("pkill -f 'socat.*{$this->localPort}' 2>/dev/null");
        $this->ssh->exec("pkill -f 'nc.*{$this->localPort}' 2>/dev/null");

        // Try socat first (more reliable)
        $command = sprintf(
            'socat TCP-LISTEN:%d,fork,reuseaddr TCP:%s:%d > /dev/null 2>&1 & echo $!',
            $this->localPort,
            $remoteHost,
            $remotePort
        );

        $pid = trim($this->ssh->exec($command));

        if (!is_numeric($pid)) {
            // Fallback to netcat if socat fails
            $command = sprintf(
                'while true; do nc -l %d -c "nc %s %d"; done > /dev/null 2>&1 & echo $!',
                $this->localPort,
                $remoteHost,
                $remotePort
            );

            $pid = trim($this->ssh->exec($command));

            if (!is_numeric($pid)) {
                throw new RuntimeException("Failed to start port forwarding process");
            }
        }

        $this->tunnelPid = (int)$pid;

        // Give the process a moment to start
        sleep(2);

        // Verify the tunnel is working
        echo "Verifying tunnel...\n";
        $check = @fsockopen('127.0.0.1', $this->localPort, $errno, $errstr, 5);
        if (!$check) {
            $this->cleanup();
            throw new RuntimeException("Port forwarding failed: $errstr ($errno)");
        }
        fclose($check);

        echo "Port forwarding established successfully.\n";
    }

    private function cleanup(): void
    {
        if ($this->tunnelPid !== null) {
            $this->ssh->exec("kill {$this->tunnelPid} 2>/dev/null");
            $this->ssh->exec("pkill -f 'socat.*{$this->localPort}' 2>/dev/null");
            $this->ssh->exec("pkill -f 'nc.*{$this->localPort}' 2>/dev/null");
        }
    }

    public function getLocalPort(): int
    {
        if (!$this->isConnected) {
            throw new RuntimeException('SSH tunnel not connected');
        }
        return $this->localPort;
    }

    public function __destruct()
    {
        if ($this->isConnected) {
            $this->cleanup();
            $this->ssh->disconnect();
        }
    }
} 