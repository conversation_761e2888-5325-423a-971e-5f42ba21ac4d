<?php

namespace App\Factory;

use App\Service\Cache\CacheServiceInterface;
use Psr\Log\LoggerInterface;

/**
 * Factory for creating decorated service instances with caching
 */
class ServiceDecoratorFactory
{
    private CacheServiceInterface $cacheService;
    private LoggerInterface $logger;
    private array $cacheLifetimes;

    public function __construct(
        CacheServiceInterface $cacheService,
        LoggerInterface       $logger,
        array                 $cacheLifetimes = []
    )
    {
        $this->cacheService = $cacheService;
        $this->logger = $logger;
        $this->cacheLifetimes = array_merge([
            'configuration' => 3600,  // 1 hour
            'browser' => 1800,       // 30 minutes
            'api' => 300,           // 5 minutes
            'data' => 600,          // 10 minutes
        ], $cacheLifetimes);
    }

    /**
     * Create a cached decorator for a service
     *
     * @template T
     * @param T $service The service to decorate
     * @param string $type The service type (configuration, browser, api, data)
     * @param string|null $prefix Optional cache key prefix
     * @return T The decorated service
     */
    public function createCachedDecorator($service, string $type, ?string $prefix = null)
    {
        $decoratorClass = $this->getDecoratorClass($service);
        if (!$decoratorClass) {
            $this->logger->warning(sprintf(
                'No decorator found for service of class %s, returning original service',
                get_class($service)
            ));
            return $service;
        }

        return new $decoratorClass(
            $service,
            $this->cacheService,
            $this->cacheLifetimes[$type] ?? 3600,
            $prefix
        );
    }

    /**
     * Get the appropriate decorator class for a service
     *
     * @param object $service The service to get a decorator for
     * @return string|null The decorator class name or null if none found
     */
    private function getDecoratorClass($service): ?string
    {
        $serviceClass = get_class($service);
        $decoratorClass = str_replace('\\Service\\', '\\Service\\Decorator\\', $serviceClass) . 'Decorator';

        if (class_exists($decoratorClass)) {
            return $decoratorClass;
        }

        // Try common interfaces
        foreach (class_implements($service) as $interface) {
            $decoratorClass = str_replace('\\Service\\', '\\Service\\Decorator\\', $interface) . 'Decorator';
            if (class_exists($decoratorClass)) {
                return $decoratorClass;
            }
        }

        return null;
    }
} 