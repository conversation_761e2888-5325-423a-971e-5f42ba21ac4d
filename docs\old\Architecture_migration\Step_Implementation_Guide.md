# Step Definition Implementation Guide

This guide provides instructions for implementing missing step definitions in the new service-oriented architecture.
Follow these guidelines to ensure consistency across the codebase.

## Step Implementation Workflow

1. **Identify the Target Context**
    - Use the domain of the step to determine which context it belongs to
    - Review the `steps_definition_registry.md` to see similar steps

2. **Analyze Legacy Implementation**
    - If the step exists in a legacy file, review its implementation
    - Understand what services and page objects it uses
    - Note any dependencies on shared data or configuration

3. **Implement the Step**
    - Create or update the method in the appropriate context file
    - Use injected services instead of direct dependencies
    - Follow the error handling pattern in the context

4. **Test the Implementation**
    - Run feature files that use the step to verify functionality
    - Compare behavior with the legacy implementation
    - Ensure all edge cases are handled

5. **Update Documentation**
    - Update `steps_definition_registry.md` with the new step
    - Mark the step as implemented in `Step_Migration_Tracker.md`
    - Create a pull request following the template

## Implementation Template

```php
/**
 * @When I perform some action with :parameter
 */
public function iPerformSomeActionWith(string $parameter): void
{
    try {
        // Get necessary services or page objects
        $page = $this->pageFactory->getPage('SomePage');
        
        // Perform the action
        $page->someAction($parameter);
        
        // Store state if needed
        $this->stateService->set('some.key', $parameter);
        
        // Log success
        $this->logInfo(sprintf("Successfully performed action with %s", $parameter));
    } catch (\Throwable $e) {
        // Log error
        $this->logError(sprintf("Failed to perform action with %s", $parameter), $e);
        // Re-throw the exception
        throw $e;
    }
}
```

## Common Patterns

### Page Object Access

Always use the PageFactory service to get page objects:

```php
$page = $this->pageFactory->getPage('PageName');
```

### State Management

Use the SharedStateService for state management:

```php
// Set state
$this->stateService->set('domain.key', $value);

// Get state
$value = $this->stateService->get('domain.key');

// Check if state exists
if ($this->stateService->has('domain.key')) {
    // Do something
}
```

### Configuration Access

Use the ConfigurationService for configuration access:

```php
$brand = $this->getConfigService()->getCurrentBrand();
$environment = $this->getConfigService()->getCurrentEnvironment();
$config = $this->getConfigService()->getBrandConfig('some.key');
```

### Test Data Access

Use the TestDataService for test data access:

```php
$brand = $this->getConfigService()->getCurrentBrand();
$data = $this->dataService->loadTestData($brand, 'data_type', 'optional_key');
```

### Error Handling

Always wrap implementation in try/catch blocks:

```php
try {
    // Implementation
    $this->logInfo("Success message");
} catch (\Throwable $e) {
    $this->logError("Error message", $e);
    throw $e;
}
```

### Parameter Validation

Validate parameters before using them:

```php
if (empty($parameter)) {
    throw new \InvalidArgumentException("Parameter cannot be empty");
}
```

## Step Definition Annotations

Use the appropriate annotation for each step:

- `@Given` - For setup steps that establish a state
- `@When` - For actions that the user performs
- `@Then` - For assertions/verifications

For steps with parameters, use either:

- Named parameters: `@When I select :quantity items`
- Regex parameters: `@When /^I select (\d+) items$/`

## Context Responsibilities

Follow these guidelines for context responsibilities:

- **FeatureContext**: Generic browser interactions, navigation, waiting
- **BrandContext**: Brand selection, configuration, environment setup
- **ProductContext**: Product selection, configuration, content validation
- **CartContext**: Cart manipulation, item management
- **CheckoutContext**: Checkout process, shipping, billing
- **PaymentContext**: Payment methods, credit cards, PayPal
- **ValidationContext**: Content verification, price validation
- **EmailContext**: Email verification, link following
- **TestDataContext**: Test data loading and management
- **UpsellContext**: Upsell offers, acceptance/rejection
- **SalesFunnelContext**: Sales funnel navigation and validation

## Example Implementation

Here's an example of migrating a payment step:

**Legacy Implementation:**

```php
/**
 * @When I enter payment information for :cardType
 */
public function iEnterPaymentInformation($cardType)
{
    $data = SharedDataContext::getInstance()->getPaymentData($cardType);
    $this->getPage('CheckoutPage')->fillPaymentForm($data);
}
```

**New Implementation:**

```php
/**
 * @When I enter payment information for :cardType
 */
public function iEnterPaymentInformation(string $cardType): void
{
    try {
        // Get card data
        $brand = $this->getConfigService()->getCurrentBrand();
        $paymentData = $this->dataService->loadTestData($brand, 'payment_methods');
        
        if (!isset($paymentData['credit_cards'][$cardType])) {
            throw new \RuntimeException(sprintf('Credit card data for type "%s" not found', $cardType));
        }
        
        $cardData = $paymentData['credit_cards'][$cardType];
        
        // Fill payment form
        $paymentPage = $this->pageFactory->getPage('PaymentPage');
        $paymentPage->fillPaymentForm($cardData);
        
        // Store state
        $this->stateService->set('payment.card_type', $cardType);
        
        $this->logInfo(sprintf("Entered payment information for %s card", $cardType));
    } catch (\Throwable $e) {
        $this->logError(sprintf("Failed to enter payment information for %s card", $cardType), $e);
        throw $e;
    }
}
```

## Additional Resources

- [Architecture Migration Overview](MigrationPlans/Architecture_migration.md)
- [Phase 3: Context Migration](MigrationPlans/Phase3_Context_Migration.md)
- [Phase 6: Cleanup and Optimization](MigrationPlans/Phase6_Cleanup_and_Optimization.md)
- [Service Catalog](Service_Catalog.md) 