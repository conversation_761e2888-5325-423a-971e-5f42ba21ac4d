# Page Object Interfaces

This directory contains the interfaces for the page object pattern implementation.

## Interface Structure

The page object interfaces follow a hierarchical structure:

- `BasePageInterface`: Common methods for all pages
- Specialized interfaces for specific page types:
  - `PaymentPageInterface`: Methods for payment-related pages
  - `ProductPageInterface`: Methods for product-related pages

## Interface Usage

When creating a new page class, implement the appropriate interfaces based on the page's functionality:

```php
// Basic page with no specialized functionality
class BasicPage extends BasePage {
    // Implements only BasePageInterface methods
}

// Product page with product-related functionality
class ProductPage extends BasePage implements ProductPageInterface {
    // Implements BasePageInterface methods
    // Implements ProductPageInterface methods
}

// Checkout page with payment functionality
class CheckoutPage extends BasePage implements PaymentPageInterface {
    // Implements BasePageInterface methods
    // Implements PaymentPageInterface methods
}

// Page with multiple types of functionality
class ComplexPage extends BasePage implements ProductPageInterface, PaymentPageInterface {
    // Implements BasePageInterface methods
    // Implements ProductPageInterface methods
    // Implements PaymentPageInterface methods
}
```

## Interface Descriptions

### BasePageInterface

Contains methods common to all pages:

- `open()`: Opens the page
- `getUrl()`: Gets the URL of the page
- `isOpen()`: Checks if the page is open
- `waitForPageToLoad()`: Waits for the page to load
- `getTitle()`: Gets the page title
- `takeScreenshot()`: Takes a screenshot of the page

### PaymentPageInterface

Contains methods for payment-related functionality:

- `completePayPalCheckout()`: Completes the PayPal checkout process
- `handle3DSecureAuthentication()`: Handles 3D Secure authentication

### ProductPageInterface

Contains methods for product-related functionality:

- `selectSupplyDuration()`: Selects the supply duration/frequency
- `selectQuantity()`: Selects the product quantity

## Design Principles

This interface structure follows the Interface Segregation Principle (ISP) from SOLID principles:

> "Clients should not be forced to depend upon interfaces that they do not use."

By separating specialized methods into specific interfaces, we ensure that page classes only need to implement the methods that are relevant to their functionality.
