composer.phar
local.log
vendor/
bin/
.env
.env.local
.env.*
!.env.example
.idea
/.github/
/.vscode/

logs/

# SSH files
/config/ssh/*
!/config/ssh/.gitkeep
/docker/ssh/id_rsa
/docker/ssh/*.key
/docker/ssh/*.pem

# Logs
/logs/ssh/*
!/logs/ssh/.gitkeep

# windsurf rules
.windsurfrules
/backup/

# Added by <PERSON> Task Master
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Task files
tasks.json
tasks/ 