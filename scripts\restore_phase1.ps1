# Set error handling
$ErrorActionPreference = "Stop"

# Check if backup directory is provided
param(
    [Parameter(Mandatory=$true)]
    [string]$BackupDir
)

# Verify backup directory exists
if (-not (Test-Path $BackupDir)) {
    Write-Error "Error: Backup directory $BackupDir not found"
    exit 1
}

# Verify backup structure
$requiredDirs = @(
    "page_objects\original",
    "config",
    "documentation",
    "metadata"
)

foreach ($dir in $requiredDirs) {
    if (-not (Test-Path (Join-Path $BackupDir $dir))) {
        Write-Error "Error: Required directory $dir not found in backup"
        exit 1
    }
}

# Create temporary directory for safety
$TEMP_DIR = Join-Path $env:TEMP ([System.Guid]::NewGuid().ToString())
New-Item -ItemType Directory -Force -Path $TEMP_DIR | Out-Null
Write-Host "Created temporary directory: $TEMP_DIR"

# Function to cleanup on exit
try {
    # Verify backup integrity
    Write-Host "Verifying backup integrity..."
    $backupInfoPath = Join-Path $BackupDir "metadata\backup_info.json"
    if (Test-Path $backupInfoPath) {
        Copy-Item $backupInfoPath -Destination $TEMP_DIR
    } else {
        Write-Warning "backup_info.json not found"
    }

    # Restore page objects
    Write-Host "Restoring page objects..."
    $originalPageObjects = Join-Path $BackupDir "page_objects\original"
    if (Test-Path $originalPageObjects) {
        New-Item -ItemType Directory -Force -Path "features\bootstrap\Page" | Out-Null
        Copy-Item "$originalPageObjects\*" -Destination "features\bootstrap\Page" -Recurse -Force
    } else {
        Write-Error "Error: Original page objects not found in backup"
        exit 1
    }

    # Restore configuration
    Write-Host "Restoring configuration files..."
    $behatConfig = Join-Path $BackupDir "config\behat.yml"
    if (Test-Path $behatConfig) {
        Copy-Item $behatConfig -Destination "."
    } else {
        Write-Warning "behat.yml not found in backup"
    }

    $composerJson = Join-Path $BackupDir "config\composer.json"
    if (Test-Path $composerJson) {
        Copy-Item $composerJson -Destination "."
    } else {
        Write-Warning "composer.json not found in backup"
    }

    # Restore documentation
    Write-Host "Restoring documentation..."
    $pageObjectRules = Join-Path $BackupDir "documentation\page_object_rules.md"
    if (Test-Path $pageObjectRules) {
        New-Item -ItemType Directory -Force -Path "docs" | Out-Null
        Copy-Item $pageObjectRules -Destination "docs"
    } else {
        Write-Warning "page_object_rules.md not found in backup"
    }

    $extensionInstructions = Join-Path $BackupDir "documentation\page_object_extennsion_instructions.md"
    if (Test-Path $extensionInstructions) {
        New-Item -ItemType Directory -Force -Path "docs" | Out-Null
        Copy-Item $extensionInstructions -Destination "docs"
    } else {
        Write-Warning "page_object_extennsion_instructions.md not found in backup"
    }

    # Reinstall dependencies
    Write-Host "Reinstalling dependencies..."
    if (Get-Command "composer" -ErrorAction SilentlyContinue) {
        composer install
    } else {
        Write-Warning "Composer not found, skipping dependency installation"
    }

    # Verify restoration
    Write-Host "Verifying restoration..."
    if (Test-Path "vendor\bin\behat") {
        try {
            vendor\bin\behat --tags=@smoke
        } catch {
            Write-Warning "Smoke tests failed"
        }
    } else {
        Write-Warning "Behat not found, skipping verification"
    }

    Write-Host "Restoration completed"
    Write-Host "Please verify the restored files and run tests manually"
}
finally {
    # Cleanup
    Write-Host "Cleaning up temporary directory..."
    Remove-Item -Path $TEMP_DIR -Recurse -Force -ErrorAction SilentlyContinue
} 