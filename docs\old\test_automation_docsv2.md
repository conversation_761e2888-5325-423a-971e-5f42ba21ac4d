# **1. Executive Summary**

- **Product Overview**:  
  This project focuses on testing a CRM-like e-commerce platform based on **Sylius** (a PHP-based framework built on
  Symfony). The testing framework incorporates **Behat** for BDD-style tests, **MinkExtension** for UI interactions, and
  local compatibility testing. The framework now includes robust data sharing mechanisms through `SharedDataContext`,
  enhanced product content verification, quantity management, and data integrity features.

- **Objectives**:
    - **Backend Testing**: Validate critical workflows such as adding products to the cart, checkout flow, and order
      processing by executing predefined **Sylius commands**.
    - **UI Testing**: Ensure UI functions correctly across various devices (both mobile and desktop) through *
      *BrowserStack**.
    - **Content Verification**: Validate product information, FAQs, and dynamic content updates.
    - **State Management**: Ensure proper data sharing between test steps and contexts using `SharedDataContext`.
    - **Quantity Management**: Verify correct price updates with quantity changes.
    - **Data Integrity**: Enhance validation and caching of test data through `TestDataContext`.

- **Target Audience**:
    - **End Customers** using the webstore for shopping.
    - **Admin Users** managing products and processing orders.

- **Tech Stack**:
    - **Languages/Tools**: PHP/Behat, Mink, BrowserStack, Sylius for backend commands.
    - **CI/CD**: GitLab CI/CD with parallel test execution.
    - **Test Data Management**: YAML-based fixtures per brand.
    - **Cross-browser Testing**: BrowserStack integration with self-healing capabilities.
    - **State Management**: `SharedDataContext` singleton pattern.
    - **Data Validation**: Enhanced `TestDataContext` with caching.
    - **Price Verification**: Dedicated price comparison utilities.
    - **Data Caching**: Implemented in `TestDataContext` for performance.
    - **Content Verification**: Structured validation for product content.

---

# **2. Product Vision**

- **Long-Term Vision**:  
  Build scalable automated test coverage adaptable to future feature expansions (e.g., API testing, mobile app testing),
  ensuring stability and consistency across various environments.

- **Alignment with Strategy**:  
  Provide comprehensive test coverage for both backend (business logic) and frontend (UI and UX) elements across
  devices, reducing risks during product updates and deployments.

- **Architecture Vision**:
    - Maintain singleton pattern for shared state.
    - Implement robust data validation.
    - Support dynamic content verification.
    - Enable flexible quantity management.

- **Test Coverage Vision**:
    - Comprehensive product content verification.
    - Dynamic price updates validation.
    - FAQ and product details validation.
    - Cross-step data sharing.

---

# **3. Target Persona**

- **End User Personas**:
    - **Shoppers**: Individuals purchasing products via the webstore.
    - **Admin Users**: Managing products and orders to ensure smooth operations.

- **Pain Points**:
    - Ensuring a consistent checkout experience across all devices and browsers.
    - Verifying backend workflows align with the UI state.

---

# **4. Problem Statement**

The project addresses the challenge of ensuring that **backend workflows** function correctly and consistently while
ensuring the **UI behaves properly** across mobile and desktop devices. Testing across multiple environments is
essential to eliminate discrepancies.

---

# **5. Product Goals and Success Metrics**

- **Goals**:
    - Automate core e-commerce workflows like product purchases, cart updates, and checkout processes.
    - Perform cross-browser and cross-device testing to verify consistent functionality across platforms.

- **Success Metrics (KPIs)**:
    - **Bug Detection Rate** across different devices and browsers.
    - **Test Execution Time**: Optimize tests for efficiency across all targeted platforms.
    - **Email Verification**: Ensure order confirmation emails are correctly sent and verified for each purchase.

---

# **6. Features and Requirements**

## **6.1 Core Features**

### **Multi-Brand Support**

- Separate configurations and test data for each brand:
    - Aeons Labs (aeons)
    - Dr Sister Skincare (dss)
    - Origins Diet (odd)
    - Your Pet Nutrition (ypn)
    - Apex Laboratories (apex)
- Environment-specific configurations (staging/production).
- Each brand has a unique Mailtrap inbox configuration for email verification.
- Brand-specific product catalogs and pricing.
- Brand-specific configuration management.
- Environment-specific test data handling.

### **Test Data Management and Sharing**

- YAML-based fixtures organized by brand.
- Separate test data for different environments.
- Reusable test user profiles and payment methods.
- Product-specific configurations, including variants and pricing.
- Implemented `TestDataContext` for managing brand-specific test data.
- Cached test data loading for performance.
- Normalized product slug handling.
- Enhanced validation and caching in `TestDataContext`.

### **State Management and Data Sharing**

- Implemented `SharedDataContext` singleton pattern.
- Data sharing across test steps and contexts.
- Stores shared data (e.g., product details, shipping info, order numbers).
- Enhanced `SharedDataContext` with cleanup hooks.
- Ensures proper data sharing between test steps and contexts.
- Enhanced error handling in data sharing.

### **Purchase Workflows**

- One-time purchase flow with various payment methods.
- Subscription-based purchases with supply duration options.
- Coupon code application and validation.
- Payment method validation, including expired card scenarios.
- Multiple quantity selections and management.
- Shipping and billing information handling.
- Order confirmation validation.
- Google Tag Manager integration verification.
- Driver type verification.

### **Product Content Verification**

- Validate product information, FAQs, and dynamic content updates.
- FAQ content validation.
- Product badges and certifications verification.
- Subscription benefits validation.
- Trust badges verification.
- Dynamic price updates with quantity changes.
- Price normalization for consistent comparison.
- Structured validation for product content.

### **Price Management**

- Dynamic price updates with quantity changes.
- Verify correct price updates with quantity changes.
- Support for different quantity options.
- Subscription vs. one-time purchase pricing.
- Bundle pricing validation.
- Dedicated price comparison utilities.

### **Cross-Browser and Cross-Device Testing**

- Ensure UI functions correctly across various devices (mobile and desktop) via **BrowserStack**.
- BrowserStack integration with self-healing capabilities.
- Cross-device compatibility testing using **BrowserStack**.

### **Enhanced Data Management**

- Robust data validation in `TestDataContext`.
- Enhanced error handling.
- Data caching in `TestDataContext` for performance.
- Data normalization layer (product slug normalization, price format standardization).

### **State Management Architecture**

- Singleton pattern for shared state.
- Automatic state cleanup between scenarios.
- Cross-context data sharing.
- Cached test data loading.

### **Email Verification**: Verify order confirmation emails using Mailtrap API.

## **6.2 Nice-to-Have Features**

- **Visual Regression Testing**: Using **Percy** to catch UI inconsistencies across platforms and devices.
- **AI-Based Visual Validation**: Leveraging **OpenAI's API** for intelligent layout and visual validation in complex
  scenarios.

---

# **7. User Flow and Interaction**

- **Purchase Flows**:
    1. **One-Time Purchase**:
        - Select product and quantity.
        - Add to cart.
        - Optional coupon application.
        - Enter shipping/billing details.
        - Process payment.
        - Verify order confirmation.
        - Verify order confirmation email after purchase completion.

    2. **Subscription Purchase**:
        - Select subscription option.
        - Choose supply duration.
        - Set quantity.
        - Complete checkout process.
        - Verify subscription details.

    3. **Payment Validation**:
        - Verify successful payments with valid cards.
        - Validate error handling for expired cards.
        - Ensure proper error messaging.

- **Test User Flow**:
    - Begin on the homepage, navigate to product pages, add products to the cart, proceed to checkout, and complete
      purchases.
    - Validate customer interactions, such as adding products to the cart and completing orders.
    - Validate admin workflows, including order processing and product management.

- **Sylius Backend Workflow**:
    - Execute predefined Sylius commands for validating data consistency (e.g., checking orders, validating shipping
      info).

---

# **8. Constraints and Assumptions**

- **Constraints**:
    - Testing across multiple platforms may require optimization for execution time and stability.
    - Mobile-specific gestures (e.g., swipe, pinch) may need separate handling.

- **Assumptions**:
    - BrowserStack provides accurate device/browser emulation for reliable testing.
    - Sylius backend commands can be seamlessly integrated into the test framework.

---

# **9. Dependencies**

- **External Dependencies**:
    - **BrowserStack** for cross-browser and mobile testing.
    - **Sylius** for backend command execution within the testing framework.

- **Internal Dependencies**:
    - **Friends of Behat** extensions: MinkExtension, MinkContext, SymfonyExtension.
    - **PHP-based Sylius commands** executed using PHP's `exec()` or Symfony's `Process` component.

---

# **10. Milestones and Timeline**

- **Milestones**:
    1. **Initial Setup**: Establish the base test automation framework using Behat, Mink, and BrowserStack.
    2. **Integration of Sylius Commands**: Trigger backend commands for programmatic workflow validation.
    3. **Visual Testing Expansion**: Integrate visual testing using Percy and AI-based automation with the OpenAI API.

---

# **11. Risks and Mitigation Strategies**

- **Risks**:
    - **Cross-Browser Discrepancies**: UI behavior may differ across browsers.
    - **Sylius Backend Integration Issues**: Command execution issues may cause validation problems.

- **Mitigation Strategies**:
    - Use **BrowserStack** for thorough cross-browser testing to identify discrepancies.
    - Implement robust logging and error reporting for Sylius command execution.

---

# **12. Success Criteria and Acceptance Tests**

- **Acceptance Tests**:
    - Core workflows are tested and validated across all supported browsers and devices.
    - Backend workflows executed via Sylius commands function correctly and are reflected in the UI.

---

# **13. Future Considerations**

- **API Test Coverage**: Expand testing to include Sylius backend services APIs.
- **Visual Regression Testing**: Integrate **Percy** for visual consistency across platforms.
- **AI Integration**: Incorporate **OpenAI API** for intelligent layout comparison in complex UI scenarios.

---

# **14. Project Architecture Overview**

- **Multi-Brand Architecture**:
    - Each brand has its own configuration and test data.
    - Shared components for common functionality.
    - Environment-specific settings (staging/production).

- **Test Data Management**:
    - YAML fixtures organized by brand.
    - Separate configurations for different environments.
    - Centralized data access through `TestDataContext`.

- **State Management Architecture**:
    - `SharedDataContext` singleton pattern.
    - Automatic state cleanup between scenarios.
    - Cross-context data sharing.
    - Cached test data loading.
    - EmailContext manages email interactions and verification.

- **Content Verification Framework**:
    - Structured product content validation.
    - Dynamic price verification.
    - FAQ content management.
    - Product badges verification.
    - Includes email content verification for order confirmations.

- **Data Normalization Layer**:
    - Product slug normalization.
    - Price format standardization.
    - Content comparison utilities.

- **Page Object Model (POM)**:
    - Each page represented by a class encapsulating its elements and interactions.
    - `BasePage` provides common methods.
    - Specific page classes extend `BasePage` for page-specific functionalities.

- **CI/CD Integration**:
    - GitLab CI/CD pipeline with parallel execution.
    - Separate jobs for each brand.
    - Automatic test execution on merge requests.
    - Test report generation and artifact storage.

- **Verification Patterns**:
    - Content validation helpers.
    - Price verification utilities.
    - Dynamic content checks.
    - State management helpers.

---

# **15. Codebase Description**

## **Project Structure**

```
project_root/
├── .gitlab-ci.yml          # GitLab CI/CD configuration
├── behat.yml               # Base Behat configuration
├── composer.json           # Project dependencies
├── bin/
│   └── run-brand-tests.sh  # Test execution script
├── config/
│   ├── brands/             # Brand-specific configurations
│   │   ├── aeons/
│   │   ├── dss/
│   │   ├── odd/
│   │   ├── ypn/
│   │   └── apex/
│   └── common/             # Shared configurations
├── features/
│   ├── bootstrap/
│   │   └── Context/        # Behat contexts
│   ├── fixtures/           # Test data fixtures
│   │   ├── brands/         # Brand-specific fixtures
│   │   └── common/         # Shared fixtures
│   └── *.feature           # Feature files
└── src/
    └── Page/               # Page Objects
```

## **Core Classes and Structure**

- **BasePage.php**:  
  Provides common methods for interacting with web elements, such as `open()`, `findElement()`, `clickElement()`,
  `enterText()`, and `waitForElementVisible()`.

- **HomePage.php**:  
  Manages actions on the homepage, like navigating to the shop and verifying page load.

- **ProductPage.php**:  
  Handles interactions on the product detail page, including:
    - Selecting product variants.
    - Setting quantities.
    - Adding products to the cart.
    - Interacting with the FAQ accordion.
    - Enhanced price verification.
    - Methods:
        - `getProductName()`
        - `getProductSubtitle()`
        - `getProductDescription()`
        - `getProductBadges()`
        - `getFAQs()`
        - `getPricing()`
        - `expandFAQQuestion()`

- **CartPage.php**:  
  Manages shopping cart functionality, including:
    - Verifying cart contents.
    - Updating quantities.
    - Applying coupon codes.
    - Calculating totals.
    - Proceeding to checkout.
    - Validating error messages.

- **CheckoutPage.php**:  
  Handles the checkout process, including:
    - Filling in shipping/billing information.
    - Selecting shipping methods.
    - Entering payment details.
    - Completing the purchase and verifying confirmation.

- **FeatureContext.php**:  
  Implements global step definitions and coordinates interactions between page objects.

## **Behat Context Classes**

- **ProductPageContext.php**:  
  Manages step definitions for product page interactions.

- **CartPageContext.php**:  
  Contains step definitions for cart functionality.

- **CheckoutPageContext.php**:  
  Defines step definitions related to the checkout process.

- **ConfirmationPageContext.php**:  
  Manages order confirmation steps, including:
    - Waiting for page load.
    - Memorizing order numbers.
    - Verifying order details.

- **SharedDataContext.php**:
    - Manages data sharing between context classes.
    - Singleton pattern implementation.
    - Automatic state cleanup.
    - Methods: `set()`, `get()`, `setMultiple()`, `getAll()`.

- **TestDataContext.php**:
    - Manages brand-specific test data.
    - Loads fixtures based on brand/environment.
    - Provides data access methods.
    - Implements caching and validation.

- **EmailContext.php**:
    - Manages email interactions and verification using Mailtrap API.

## **Feature Files**

- **features/purchase.feature**:  
  Contains Gherkin scenarios outlining key user flows, including:
    - FAQ accordion functionality.
    - Successful purchase flows.
    - Coupon code application.
    - Payment validations.
    - Order Confirmation Email Verification: Scenario for verifying email content.

---

# **16. Test Coverage Mapping**

| PRD Feature                                   | Automated Test Scenario                                      | Implemented In         |
|-----------------------------------------------|--------------------------------------------------------------|------------------------|
| Shopping Cart Workflows                       | Scenario: Successful Purchase Flow                           | purchase.feature       |
|                                               | Scenario: Add Product to Cart with Subscription              | purchase.feature       |
|                                               | Steps in CartPageContext.php                                 |                        |
| Checkout Process                              | Scenario: Successful Purchase Flow                           | purchase.feature       |
|                                               | Steps in CheckoutPageContext.php                             |                        |
| Applying Coupon Codes                         | Scenario: Verify Cart Functionality with Coupon              | purchase.feature       |
|                                               | Steps in CartPageContext.php                                 |                        |
| UI Interactions (FAQ Accordion Functionality) | Scenario: Verify FAQ Accordion Functionality                 | purchase.feature       |
|                                               | Methods in ProductPage.php                                   |                        |
| Verifying Order Totals and Discounts          | Steps: "Then the order total should be calculated correctly" | CartPageContext.php    |
| Cross-Browser and Cross-Device Testing        | Configurations in behat.yml for BrowserStack integration     |                        |
| Backend Workflows via Sylius Commands         | To be implemented                                            | Future Enhancements    |
| Data Sharing Across Contexts                  | Implemented via `SharedDataContext`                          | Shared across contexts |
| Order Confirmation Verification               | Scenario: Successful One-Time Purchase with Normal Card      | purchase.feature       |
|                                               | Steps in `ConfirmationPageContext.php`                       |                        |
| Quantity Management                           | Scenario: Change product quantity                            | purchase.feature       |
| Price Updates                                 | Steps: Verify price updates with quantity                    | ProductPage.php        |
| FAQ Validation                                | Steps: Verify FAQ content                                    | ProductPage.php        |
| Product Content                               | Steps: Verify product content matches                        | FeatureContext.php     |
| Order Confirmation Email Verification         | Scenario: Verify order confirmation email                    | purchase.feature       |

---

# **17. Sylius Backend Integration Enhancements**

To align with the goal of integrating Sylius backend commands:

- **Implement Command Execution**:  
  Use PHP's `exec()` or Symfony's `Process` component to execute Sylius commands within tests.

- **Create Backend Validation Steps**:  
  Develop step definitions that trigger backend commands and verify outcomes.

- **Ensure Alignment**:  
  Verify that backend changes are reflected in the UI to ensure data consistency.

---

# **18. Instructions for AI Coder Agents**

## **Adding Support for a New Brand**

1. **Create Brand Configuration**:

    ```
    config/brands/new-brand/
    ├── prod.yml
    └── stage.yml
    ```

2. **Create Product Fixtures**:

    ```
    features/fixtures/brands/new-brand/
    ├── products.yml
    ├── test_data.yml
    └── discounts.yml
    ```

3. **Update CI/CD Configuration**:
    - Add a new job in `.gitlab-ci.yml`.
    - Configure brand-specific variables.
    - Configure Mailtrap inbox IDs in brand configuration files.

## **Adding a New Page**

1. **Create a New Page Class**:
    - In `src/Page/`, create `NewPage.php`.
    - Extend `BasePage`.
    - Implement page-specific methods.
    - Add docblocks and meaningful names.

2. **Add Step Definitions**:
    - In `FeatureContext.php`, add steps.
    - Inject `SharedDataContext` if needed.

3. **Update Feature Files**:
    - Add scenarios to existing or new feature files.
    - Ensure steps correspond to definitions.

4. **Use SharedDataContext**:
    - For data sharing across contexts.
    - Use `set()` and `get()` methods.

5. **Follow Coding Standards**:
    - Adhere to PSR-12.
    - Write clear comments and docblocks.

## **General Coding Guidelines**

- **Reusability**: Implement reusable methods.
- **Error Handling**: Include appropriate exceptions.
- **Documentation**: Provide comprehensive docblocks.

## **State Management Patterns**

- **SharedDataContext Usage**:

    ```php
    SharedDataContext::getInstance()->set('key', $value);
    ```

- **Cleanup Handling**:

    ```php
    /** @BeforeScenario */
    public function cleanup(): void {
        $this->data = [];
    }
    ```

## **Data Validation and Caching**

```php
class TestDataContext {
    private array $cache = [];

    public function getProductData(string $productSlug): array {
        if (isset($this->cache['products'][$productSlug])) {
            return $this->cache['products'][$productSlug];
        }
        // Load and cache data...
    }

    private function validateProductData(array $data): void {
        $requiredFields = ['name', 'slug', 'prices', 'options'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new RuntimeException("Missing required field: $field");
            }
        }
    }
}
```

## **Product Content Structure**

```yaml
product_name:
  name: "Product Name"
  slug: "product-slug"
  meta:
    category: "Category"
    subtitle: "Subtitle"
  content:
    description: "Description"
    badges: [ ]
    trust_badges: [ ]
    subscription_benefits: [ ]
    faq: [ ]
```

## **Error Handling Best Practices**

```php
throw new RuntimeException(sprintf(
    'Product "%s" not found in test data for brand %s',
    $productSlug,
    $this->brand
));

protected function loadEnvironmentConfig(): void {
    if (empty($this->config['brand']['url'])) {
        throw new RuntimeException(
            sprintf('Invalid environment configuration in %s', $configFile)
        );
    }
}
```

## **Page Object Pattern Updates**

```php
class ProductPage {
    public function getProductName(): string
    public function getProductSubtitle(): string
    public function getProductDescription(): string
    public function getProductBadges(): array
    public function getPricing(): array {
        return [
            'one_time' => ['options' => [], 'selected' => []],
            'subscription' => ['options' => [], 'selected' => []],
        ];
    }
}
```

## **Feature File Organization**

```gherkin
Feature: Product Purchase

  Background:
    Given I load brand configuration
    And I load product data
    And I am on the product page
    Then I verify Google Tag Manager is present

  @quantity
  Scenario: Change product quantity
    When I select "One-Time Purchase"
    And I set the quantity to "medium"
```

## **Key Lessons**

1. **State Management**:
    - Use singleton pattern for `SharedDataContext`.
    - Avoid duplicate cleanup mechanisms.

2. **Data Handling**:
    - Implement caching.
    - Validate data structures.
    - Use specific exceptions.

3. **Content Verification**:
    - Structure content hierarchically.
    - Implement comprehensive getters.

4. **Test Organization**:
    - Use `Background` for common setup.
    - Tag and group related scenarios.

5. **Error Prevention**:
    - Validate configurations early.
    - Use type hints and return types.

6. **Code Reusability**:
    - Extract common functionalities.
    - Share context appropriately.

---

# **19. Additional Instructions**

- **Brand-Specific Considerations**:
    - Test against the correct environment.
    - Use brand-specific test data.
    - Verify brand-specific UI elements.

- **CI/CD Pipeline**:
    - Run tests against staging first.
    - Use appropriate tags.
    - Monitor BrowserStack integration.

- **Test Data Management**:
    - Update fixtures with product changes.
    - Maintain separate test users.
    - Document brand-specific requirements.

- **Element Selectors Management**: Maintain consistent selectors.
- **Logging and Reporting**: Implement logging for debugging.
- **Performance Optimization**: Use proper synchronization methods.
- **BrowserStack Context**:
    - Ensure capabilities are set in `behat.yml`.
    - Use the `browser_stack` session in contexts.

---