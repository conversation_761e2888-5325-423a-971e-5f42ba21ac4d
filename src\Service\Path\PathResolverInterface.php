<?php

namespace App\Service\Path;

/**
 * Interface for path resolver service
 */
interface PathResolverInterface
{
    /**
     * Resolve a path with parameter placeholders
     *
     * @param string $path Path with placeholders
     * @return string Resolved path
     */
    public function resolvePath(string $path): string;

    /**
     * Get the project root directory
     *
     * @return string Project root directory
     */
    public function getProjectRoot(): string;

    /**
     * Get the config directory
     *
     * @return string Config directory
     */
    public function getConfigDir(): string;

    /**
     * Get the fixtures directory
     *
     * @return string Fixtures directory
     */
    public function getFixturesDir(): string;
}