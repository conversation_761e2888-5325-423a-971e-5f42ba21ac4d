<?php

namespace App\Service\State;

interface SharedStateServiceInterface
{
    /**
     * Set a value in the shared state
     *
     * @param string $key The key
     * @param mixed $value The value
     * @param string $scope The scope (scenario, feature, global)
     * @return void
     */
    public function set(string $key, $value, string $scope = 'scenario'): void;

    /**
     * Get a value from the shared state
     *
     * @param string $key The key
     * @param string $scope The scope (scenario, feature, global)
     * @return mixed The value or null if not found
     */
    public function get(string $key, string $scope = 'scenario');

    /**
     * Check if a key exists in the shared state
     *
     * @param string $key The key
     * @param string $scope The scope (scenario, feature, global)
     * @return bool True if the key exists, false otherwise
     */
    public function has(string $key, string $scope = 'scenario'): bool;

    /**
     * Get all values in a scope
     *
     * @param string $scope The scope (scenario, feature, global)
     * @return array All values in the scope
     */
    public function getAll(string $scope = 'scenario'): array;

    /**
     * Reset a scope
     *
     * @param string $scope The scope to reset (scenario, feature, global)
     * @return void
     */
    public function reset(string $scope = 'scenario'): void;
}
