# Set environment variables for Panther
$Env:TEST_BASE_URL = "https://aeonstest.info"
$Env:TEST_BRAND = "aeons"
$Env:TEST_ENV = "stage"
$Env:PANTHER_NO_SANDBOX = 1
$Env:PANTHER_CHROME_ARGUMENTS = "--disable-dev-shm-usage --disable-gpu --window-size=1920,1080 --ignore-certificate-errors"
$Env:PANTHER_ERROR_SCREENSHOT_DIR = "./screenshots"

# Run Behat tests with specified tags and features
.\vendor\bin\behat features/salesFunnel.feature --tags=@high-priority