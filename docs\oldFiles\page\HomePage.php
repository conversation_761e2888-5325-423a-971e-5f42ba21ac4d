<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\ElementNotFoundException;

/**
 * HomePage handles actions on the main landing page.
 */
class HomePage extends BasePage
{
    /**
     * The path of the home page.
     *
     * @var string
     */
    protected $path = '/';

    /**
     * Verifies that we're on the expected page.
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        $this->waitForElementVisible('a.btn[href="/range"]');
    }

    /**
     * Gets the URL of the page.
     *
     * @param array $urlParameters
     * @return string
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Clicks the "Shop Now" button on the main page.
     *
     * @throws ElementNotFoundException If the "Shop Now" button is not found.
     */
    public function clickShopNow(): void
    {
        $button = $this->getShopNowButton();
        $this->scrollToElement($button);
        $button->click();
    }

    /**
     * Finds and returns the "Shop Now" button element.
     *
     * @return NodeElement The "Shop Now" button element.
     * @throws ElementNotFoundException If the button is not found.
     */
    protected function getShopNowButton(): NodeElement
    {
        $selector = 'a.btn[href="/range"]';
        return $this->findElement($selector);
    }

    /**
     * Retrieves the page title.
     *
     * @return string The page title.
     * @throws ElementNotFoundException
     */
    public function getPageTitle(): string
    {
        $titleElement = $this->findElement('title');
        return $titleElement->getText();
    }

    /**
     * Selects a product by clicking on its link.
     *
     * @param string $productName The name of the product to select.
     * @throws ElementNotFoundException If the product link is not found.
     */
    public function selectProduct(string $productName): void
    {
        // Assuming the product links are anchor tags containing the product name.
        $productLink = $this->findElementLink($productName);
        $this->scrollToElement($productLink);
        $productLink->click();
    }

    /**
     * Finds a link element by its visible text.
     *
     * @param string $linkText The text of the link.
     * @return NodeElement The link element.
     * @throws ElementNotFoundException If the link is not found.
     */
    protected function findElementLink(string $linkText): NodeElement
    {
        $element = $this->getDocument()->findLink($linkText);
        if (!$element) {
            throw new ElementNotFoundException($this->getSession(), 'link', 'text', $linkText);
        }
        return $element;
    }
}
