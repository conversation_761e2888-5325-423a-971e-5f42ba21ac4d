<?php

namespace App\Page;

use App\Page\Base\BasePage;

/**
 * HomePage handles actions on the main landing page.
 */
class HomePage extends BasePage
{
    /**
     * The path of the home page.
     *
     * @var string
     */
    protected string $path = '/';

    /**
     * Navigate to the product range page
     *
     * @return void
     */
    public function goToProductRange(): void
    {
        $this->clickElement('a.btn[href="/range"]');
        $this->waitForPageToLoad();
    }

    /**
     * Check if the user is logged in
     *
     * @return bool
     */
    public function isUserLoggedIn(): bool
    {
        return $this->elementExists('.user-account-menu');
    }

    /**
     * Get the featured products
     *
     * @return array List of featured products
     */
    public function getFeaturedProducts(): array
    {
        $products = [];
        $productElements = $this->browserService->findElements('.featured-product');

        foreach ($productElements as $element) {
            $products[] = [
                'name' => $element->find('css', '.product-name')->getText(),
                'price' => $element->find('css', '.product-price')->getText(),
                'url' => $element->find('css', 'a')->getAttribute('href')
            ];
        }

        return $products;
    }

    /**
     * Search for a product
     *
     * @param string $searchTerm Search term
     * @return void
     */
    public function searchForProduct(string $searchTerm): void
    {
        $this->fillField('.search-input', $searchTerm);
        $this->clickElement('.search-button');
        $this->waitForPageToLoad();
        $this->waitForElementVisible('.search-results');
    }

    /**
     * Get the page title
     *
     * @return string
     */
    public function getPageTitle(): string
    {
        return $this->getTitle();
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible('a.btn[href="/range"]');
    }

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // Home page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // Home page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // Home page doesn't have subscription items, so return empty array
        return [];
    }

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array
    {
        // Home page doesn't have cart items, so return empty array
        return [];
    }
}
