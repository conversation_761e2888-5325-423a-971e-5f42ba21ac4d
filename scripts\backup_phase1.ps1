# Set error handling
$ErrorActionPreference = "Stop"

# Set variables
$BACKUP_ROOT = "backup"
$TIMESTAMP = Get-Date -Format "yyyyMMdd_HHmmss"
$BACKUP_DIR = Join-Path $BACKUP_ROOT $TIMESTAMP

# Create backup directories
Write-Host "Creating backup directories..."
New-Item -ItemType Directory -Force -Path (Join-<PERSON> $BACKUP_DIR "page_objects\original") | Out-Null
New-Item -ItemType Directory -Force -Path (Join-Path $BACKUP_DIR "page_objects\pre_migration") | Out-Null
New-Item -ItemType Directory -Force -Path (Join-<PERSON> $BACKUP_DIR "config") | Out-Null
New-Item -ItemType Directory -Force -Path (Join-Path $BACKUP_DIR "documentation") | Out-Null
New-Item -ItemType Directory -Force -Path (Join-Path $BACKUP_DIR "metadata") | Out-Null

# Backup page objects
Write-Host "Backing up page objects..."
if (Test-Path "features\bootstrap\Page") {
    Copy-Item "features\bootstrap\Page\*" -Destination (Join-<PERSON> $BACKUP_DIR "page_objects\original") -Recurse
    Copy-Item "features\bootstrap\Page\*" -Destination (Join-Path $BACKUP_DIR "page_objects\pre_migration") -Recurse
} else {
    Write-Warning "Page objects directory not found"
}

# Backup configuration
Write-Host "Backing up configuration files..."
if (Test-Path "behat.yml") {
    Copy-Item "behat.yml" -Destination (Join-Path $BACKUP_DIR "config")
} else {
    Write-Warning "behat.yml not found"
}

if (Test-Path "composer.json") {
    Copy-Item "composer.json" -Destination (Join-Path $BACKUP_DIR "config")
} else {
    Write-Warning "composer.json not found"
}

# Backup documentation
Write-Host "Backing up documentation..."
if (Test-Path "docs\page_object_rules.md") {
    Copy-Item "docs\page_object_rules.md" -Destination (Join-Path $BACKUP_DIR "documentation")
} else {
    Write-Warning "page_object_rules.md not found"
}

if (Test-Path "docs\page_object_extennsion_instructions.md") {
    Copy-Item "docs\page_object_extennsion_instructions.md" -Destination (Join-Path $BACKUP_DIR "documentation")
} else {
    Write-Warning "page_object_extennsion_instructions.md not found"
}

# Create file inventory
Write-Host "Creating file inventory..."
if (Test-Path "features\bootstrap\Page") {
    Get-ChildItem -Path "features\bootstrap\Page" -Filter "*.php" -Recurse | 
        Select-Object -ExpandProperty FullName | 
        Out-File -FilePath (Join-Path $BACKUP_DIR "metadata\file_inventory.json")
} else {
    "[]" | Out-File -FilePath (Join-Path $BACKUP_DIR "metadata\file_inventory.json")
    Write-Warning "No page objects found for inventory"
}

# Store composer dependencies
Write-Host "Storing dependency information..."
if (Get-Command "composer" -ErrorAction SilentlyContinue) {
    composer show | Out-File -FilePath (Join-Path $BACKUP_DIR "metadata\dependencies.json")
} else {
    Write-Warning "Composer not found"
}

# Create backup verification file
$backupInfo = @{
    timestamp = (Get-Date).ToUniversalTime().ToString("o")
    backup_path = $BACKUP_DIR
    files_backed_up = @{
        page_objects = (Get-ChildItem -Path (Join-Path $BACKUP_DIR "page_objects") -Recurse -File).Count
        config_files = (Get-ChildItem -Path (Join-Path $BACKUP_DIR "config") -Recurse -File).Count
        documentation = (Get-ChildItem -Path (Join-Path $BACKUP_DIR "documentation") -Recurse -File).Count
    }
}

$backupInfo | ConvertTo-Json | Out-File -FilePath (Join-Path $BACKUP_DIR "metadata\backup_info.json")

Write-Host "Backup completed successfully at $BACKUP_DIR"
Write-Host "Verify the backup contents and test restoration procedure" 