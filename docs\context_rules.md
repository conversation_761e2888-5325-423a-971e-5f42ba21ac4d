# Context Implementation Rules

## Service Configuration Rules

### Standard Context Service Definition
```yaml
Features\Bootstrap\Context\SomeContext:
    parent: FriendsOfBehat\PageObjectExtension\Context\PageObjectContext
    arguments:
        $somePage: '@Features\Bootstrap\Page\SomePage'
    tags: ['context.service']
```

### Page Object Dependencies
- Use constructor injection for page objects
- Remove setter injection methods
- Utilize PageObjectExtension factory
- No manual session management needed

Key points for context-page interaction:
- Inject page objects through constructor
- Keep page objects as private properties
- Use type hints for page object properties
- Access page methods through defined interfaces

Example:
```php
class ProductContext implements Context
{
    private ProductPage $productPage;

    public function __construct(ProductPage $productPage)
    {
        $this->productPage = $productPage;
    }

    /**
     * @When /^I add product to cart$/
     */
    public function iAddProductToCart(): void
    {
        $this->productPage->addToCart();
    }

    private function ensureDependencies(): void {
        if (!isset($this->productPage)) {
            throw new RuntimeException('ProductPage not initialized');
        }
    }
}
```

### Rules:
1. Service Definition:
   - Use named arguments in services.yml
   - Only inject required dependencies (pages/services)
   - Never inject SharedDataContext or ContextManager directly
   - Always include setContextManager call
   - Always tag with 'context.service'

2. Special Context Rules:
   - TestDataContext: Requires ConfigurationManager and TestDataRegistry
   - BrandContext: Requires ConfigurationManager and TestDataRegistry
   - FeatureContext: Requires Mink and ContextManager
   - SharedContext: Uses getInstance() factory

## Context Class Implementation

### Standard Context Class Template
```php
class SomeContext extends BaseContext
{
    private SomePage $somePage;
    
    public function __construct(SomePage $somePage)
    {
        parent::__construct();
        $this->somePage = $somePage;
    }
}
```

### Rules:
1. Property Rules:
   - Never redeclare $contextManager or $sharedData
   - Keep dependencies private
   - Use protected only for inherited properties
   - Declare all dependencies in constructor

2. Constructor Rules:
   - Always call parent::__construct()
   - Initialize only class-specific dependencies
   - Don't manually initialize ContextManager
   - Don't initialize SharedDataContext

3. Context Manager Usage:
   - Use setContextManager() for getting other contexts
   - Always call parent::setContextManager() first
   - Get other contexts via $this->contextManager->getContext()

4. Method Rules:
   - Make helper methods private unless needed by child classes
   - Use type hints for all parameters
   - Include return type declarations
   - Add PHPDoc for public methods

## Page Object Usage in Contexts

### 1. Initialization
```php
class ProductContext implements Context
{
    private ProductPage $productPage;
    private SharedDataContext $sharedData;

    public function __construct(ProductPage $productPage)
    {
        parent::__construct();
        $this->productPage = $productPage;
        $this->sharedData = SharedDataContext::getInstance();
    }
}
```

### 2. Error Handling
```php
/**
 * @When /^I add product to cart$/
 */
public function iAddProductToCart(): void
{
    try {
        $this->productPage->addToCart();
        $this->sharedData->set('cart_updated', true);
    } catch (ElementNotFoundException $e) {
        throw new RuntimeException(
            'Failed to add product to cart: ' . $e->getMessage()
        );
    }
}
```

### 3. State Management
- Store page interaction results in SharedDataContext
- Use page objects for verification steps
- Handle page transitions through NavigationContext
- See [Page Object Rules](page_object_rules.md) for detailed implementation guidelines

## Data Sharing and State Management

1. Shared Data:
   ```php
   // CORRECT
   $this->sharedData->set('key', $value);
   $data = $this->sharedData->get('key');
   
   // INCORRECT
   $this->someGlobalVariable = $value;
   ```

2. Context Communication:
   ```php
   // CORRECT
   $otherContext = $this->contextManager->getContext(OtherContext::class);
   
   // INCORRECT
   private OtherContext $otherContext;
   ```

## Error Handling

1. Exception Handling:
   ```php
   // CORRECT
   try {
       $this->somePage->doAction();
   } catch (ElementNotFoundException $e) {
       throw new RuntimeException('Action failed: ' . $e->getMessage());
   }
   ```

## Documentation Requirements

1. Class Documentation:
   ```php
   /**
    * Handles payment-related operations
    */
   class PaymentContext extends BaseContext
   ```

2. Method Documentation:
   ```php
   /**
    * @When /^I complete payment$/
    * @throws RuntimeException
    */
   public function completePayment(): void
   ```

## Testing Guidelines

1. Context Testing:
   - Test each context in isolation
   - Mock dependencies
   - Verify shared state management
   - Test error conditions

2. Integration Testing:
   - Test context interactions
   - Verify data sharing
   - Test complete workflows

## Singleton Pattern Implementation

### SharedDataContext Implementation
```php
class SharedDataContext implements Context
{
    private static ?self $instance = null;
    private array $data = [];

    public function __construct()
    {
        if (self::$instance === null) {
            self::$instance = $this;
        }
        return self::$instance;
    }

    private function __clone()
    {
        // Prevent cloning
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
```

### Rules for Singleton Contexts:
1. Constructor Rules:
   - Use public constructor for Behat compatibility
   - Initialize singleton instance in constructor
   - Prevent multiple instances via instance check
   - Keep clone method private

2. Instance Management:
   - Always provide getInstance() method
   - Maintain static instance property
   - Initialize instance in both constructor and getInstance()
   - Validate instance type after initialization

3. Service Configuration:
   ```yaml
   Features\Bootstrap\SharedDataContext:
       factory: ['Features\Bootstrap\SharedDataContext', 'getInstance']
       public: true
       tags: ['context.service']
   ```

4. Usage in Other Contexts:
   ```php
   protected function initializeSharedData(): void
   {
       $this->sharedData = SharedDataContext::getInstance();
       
       if (!$this->sharedData instanceof SharedDataContext) {
           throw new RuntimeException(
               'Failed to initialize SharedDataContext. getInstance() returned invalid instance.'
           );
       }
   }
   ```

### Dependency Management
- Use setter injection for page objects and context dependencies
- Always validate dependencies before use
- Document required dependencies in class PHPDoc
