<?php

namespace App\Service\Reporting;

interface ReportingServiceInterface
{
    /**
     * Initialize a test report
     *
     * @param string $name Report name
     * @param array $metadata Report metadata
     * @return void
     */
    public function initReport(string $name, array $metadata = []): void;

    /**
     * Record a test result
     *
     * @param string $name Test name
     * @param bool $success Whether the test succeeded
     * @param array $metadata Test metadata
     * @return void
     */
    public function recordResult(string $name, bool $success, array $metadata = []): void;

    /**
     * Record an error
     *
     * @param string $message Error message
     * @param \Throwable|null $exception Exception if available
     * @return void
     */
    public function recordError(string $message, \Throwable $exception = null): void;

    /**
     * Generate a report
     *
     * @param string $format Report format (html, json, xml)
     * @param string|null $outputPath Output path
     * @return string Report content
     */
    public function generateReport(string $format = 'html', string $outputPath = null): string;
}
