<?php

namespace App\Service\Browser;

use Behat\Mink\Driver\DriverInterface;
use Behat\Mink\Session;
use D<PERSON>ore\ChromeDriver\ChromeDriver;
use RuntimeException;

/**
 * Factory for creating Mink sessions with different drivers
 */
class SessionFactory
{
    private string $driverName;
    private array $driverOptions;

    /**
     * Constructor
     *
     * @param string $driverName Driver name (chrome)
     * @param array $driverOptions Driver options
     */
    public function __construct(string $driverName = 'chrome', array $driverOptions = [])
    {
        $this->driverName = $driverName;
        $this->driverOptions = $driverOptions;
    }

    /**
     * Create a Mink session with the specified driver
     *
     * @return Session
     */
    public function createSession(): Session
    {
        $driver = $this->createDriver();
        return new Session($driver);
    }

    /**
     * Create a driver instance
     *
     * @return DriverInterface
     */
    private function createDriver(): DriverInterface
    {
        switch ($this->driverName) {
            case 'chrome':
                return $this->createChromeDriver();
            default:
                throw new RuntimeException(sprintf('Unsupported driver: %s', $this->driverName));
        }
    }

    /**
     * Create a Chrome driver instance
     *
     * @return ChromeDriver
     */
    private function createChromeDriver(): ChromeDriver
    {
        $apiUrl = $this->driverOptions['api_url'] ?? 'http://localhost:9222';
        $downloadBehavior = $this->driverOptions['download_behavior'] ?? 'allow';
        $downloadPath = $this->driverOptions['download_path'] ?? sys_get_temp_dir();
        $socketTimeout = $this->driverOptions['socket_timeout'] ?? 60;

        return new ChromeDriver(
            $apiUrl,
            null,
            $downloadPath,
            $downloadBehavior,
            $socketTimeout
        );
    }
}
