<?php

namespace Features\Bootstrap;

use App\Service\Data\SimpleCriticalTestDataService;
use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Symfony\Component\Yaml\Yaml;

/**
 * Context for critical tests that need fast data access
 */
class CriticalTestContext implements Context
{
    /**
     * @BeforeScenario @high-priority
     */
    public function setupCriticalTestData(BeforeScenarioScope $scope)
    {
        echo "Setting up critical test data...\n";

        // Get all contexts
        $environment = $scope->getEnvironment();

        // Find the TestDataContext
        $testDataContext = $environment->getContext('Features\\Bootstrap\\TestDataContext');
        if (!$testDataContext) {
            echo "Warning: TestDataContext not found. Cannot register critical test data.\n";
            return;
        }

        // Get the critical test data service
        $criticalDataService = SimpleCriticalTestDataService::getInstance();
        echo "Using SimpleCriticalTestDataService singleton\n";

        try {
            // Load the critical data directly from the file
            $projectRoot = getenv('APP_PROJECT_ROOT') ?: '/app';
            $criticalDataFile = $projectRoot . '/features/fixtures/critical_sales_funnel_data.yml';

            if (!file_exists($criticalDataFile)) {
                // Try with realpath as fallback
                $fallbackPath = realpath(__DIR__ . '/../../features/fixtures/critical_sales_funnel_data.yml');
                if (!$fallbackPath || !file_exists($fallbackPath)) {
                    echo "Warning: Critical test data file not found at $criticalDataFile or $fallbackPath\n";
                    return;
                }
                $criticalDataFile = $fallbackPath;
            }

            echo "Loading critical test data from $criticalDataFile\n";
            $criticalData = Yaml::parseFile($criticalDataFile);

            // Register the critical data with the service
            $criticalDataService->registerData('_critical_data', $criticalData);
        } catch (\Exception $e) {
            echo "Error loading critical test data: " . $e->getMessage() . "\n";
            return;
        }

        // Register funnel data directly
        if (isset($criticalData['funnel_items'])) {
            foreach ($criticalData['funnel_items'] as $key => $item) {
                if (isset($item['entry']['url'])) {
                    $funnelKey = $item['entry']['url'];

                    // Register with the funnel URL as the key
                    $testDataContext->registerTestData('funnel', $funnelKey, $item);
                    echo "Registered funnel data with key: $funnelKey\n";

                    // Also register with the original key
                    $testDataContext->registerTestData('funnel_item', $key, $item);
                    echo "Registered funnel item with key: $key\n";

                    // Register with the service as well
                    $criticalDataService->registerData('funnel.' . $funnelKey, $item);
                    echo "Registered funnel data with service key: funnel.$funnelKey\n";

                    // Register directly with the URL as key
                    $criticalDataService->registerData($funnelKey, $item);
                    echo "Registered funnel data with service key: $funnelKey\n";
                }
            }
        }

        // Register product data
        if (isset($criticalData['products'])) {
            foreach ($criticalData['products'] as $key => $product) {
                $testDataContext->registerTestData('product', $key, $product);
                echo "Registered product data with key: $key\n";

                // Register with the service as well
                $criticalDataService->registerData('product.' . $key, $product);
            }
        }

        // Register user data
        if (isset($criticalData['test_users'])) {
            foreach ($criticalData['test_users'] as $key => $user) {
                $testDataContext->registerTestData('user', $key, $user);
                echo "Registered user data with key: $key\n";

                // Register with the service as well
                $criticalDataService->registerData('user.' . $key, $user);
            }
        }

        // Register payment methods
        if (isset($criticalData['payment_methods'])) {
            foreach ($criticalData['payment_methods'] as $key => $method) {
                $testDataContext->registerTestData('payment_method', $key, $method);
                echo "Registered payment method with key: $key\n";

                // Register with the service as well
                $criticalDataService->registerData('payment_method.' . $key, $method);
            }
        }

        // Register shipping methods
        if (isset($criticalData['shipping_methods'])) {
            foreach ($criticalData['shipping_methods'] as $key => $method) {
                $testDataContext->registerTestData('shipping_method', $key, $method);
                echo "Registered shipping method with key: $key\n";

                // Register with the service as well
                $criticalDataService->registerData('shipping_method.' . $key, $method);
            }
        }

        echo "Critical test data setup complete.\n";
    }
}
