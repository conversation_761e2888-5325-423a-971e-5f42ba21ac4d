<?php

namespace App\Service\Data;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Simple test data service that doesn't require validation
 */
class SimpleTestDataService extends AbstractService implements TestDataServiceInterface
{
    private string $fixturesDir;
    private array $testData = [];
    private array $cache = [];

    /**
     * Constructor
     *
     * @param string $fixturesDir Fixtures directory path
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(string $fixturesDir, ?LoggerInterface $logger = null)
    {
        parent::__construct($logger);
        $this->fixturesDir = $fixturesDir;
        $this->logInfo(sprintf("Initializing SimpleTestDataService with fixtures directory: %s", $fixturesDir));
    }

    /**
     * {@inheritdoc}
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        $cacheKey = sprintf('%s_%s_%s', $brand, $type, $key ?? 'all');
        $this->logInfo(sprintf("Loading test data - Brand: %s, Type: %s, Key: %s", $brand, $type, $key ?? 'all'));

        if (isset($this->cache[$cacheKey])) {
            $this->logInfo(sprintf("Returning cached data for key: %s", $cacheKey));
            return $this->cache[$cacheKey];
        }

        // Try multiple possible paths
        $paths = [
            sprintf('%s/brands/%s/%s.yml', $this->fixturesDir, $brand, $type),
            sprintf('%s/brands/%s/data/%s.yml', $this->fixturesDir, $brand, $type),
            sprintf('%s/brands/%s/fixtures/%s.yml', $this->fixturesDir, $brand, $type),
        ];

        $loadedPath = null;
        $data = null;

        foreach ($paths as $path) {
            $this->logInfo(sprintf("Trying path: %s", $path));
            if (file_exists($path)) {
                $this->logInfo(sprintf("Loading file: %s", $path));
                $loadedPath = $path;
                break;
            }
        }

        if (!$loadedPath) {
            $this->logError(sprintf("No valid fixture file found. Tried: %s", implode(', ', $paths)));
            throw new RuntimeException(
                sprintf('Test data file not found. Tried: %s', implode(', ', $paths))
            );
        }

        try {
            $fileData = Yaml::parseFile($loadedPath);

            // Simple extraction based on key
            if ($key !== null) {
                if (!isset($fileData[$key])) {
                    $availableKeys = array_keys($fileData);
                    $this->logError(sprintf("Key not found. Available keys: %s", implode(', ', $availableKeys)));
                    throw new RuntimeException(
                        sprintf('Test data key "%s" not found in %s. Available keys: %s',
                            $key,
                            $loadedPath,
                            implode(', ', $availableKeys)
                        )
                    );
                }
                $data = $fileData[$key];
            } else {
                $data = $fileData;
            }

            $this->cache[$cacheKey] = $data;
            $this->logInfo(sprintf("Data cached with key: %s", $cacheKey));

            return $data;
        } catch (\Exception $e) {
            $this->logError(sprintf("Error parsing test data file: %s", $loadedPath), $e);
            throw new RuntimeException(
                sprintf('Error parsing test data file: %s', $loadedPath),
                0,
                $e
            );
        }
    }

    /**
     * {@inheritdoc}
     */
    public function registerData(string $key, array $data): void
    {
        $this->logInfo(sprintf("Registering data with key: %s", $key));
        $this->testData[$key] = $data;
    }

    /**
     * {@inheritdoc}
     */
    public function getData(string $key)
    {
        if (!isset($this->testData[$key])) {
            $this->logError(sprintf("Test data not found for key: %s", $key));
            throw new RuntimeException(
                sprintf('Test data not found for key: %s', $key)
            );
        }

        return $this->testData[$key];
    }

    /**
     * {@inheritdoc}
     */
    public function hasData(string $key): bool
    {
        return isset($this->testData[$key]);
    }
}
