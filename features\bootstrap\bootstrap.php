<?php

require_once __DIR__ . '/../../vendor/autoload.php';

// Check if we're in a CI environment
$isCI = getenv('CI') === 'true';
echo "Environment: " . ($isCI ? "CI/CD" : "Local") . "\n";

// Load environment variables
// In CI, environment variables are already set by the CI platform
if (!$isCI && file_exists(__DIR__ . '/../../.env')) {
    if (class_exists('Symfony\Component\Dotenv\Dotenv')) {
        $dotenv = new \Symfony\Component\Dotenv\Dotenv();
        $dotenv->load(__DIR__ . '/../../.env');
        echo "Loaded environment variables from .env file\n";
    } else {
        // Manual parsing if Dotenv component is not available
        $envFile = file_get_contents(__DIR__ . '/../../.env');
        $lines = explode("\n", $envFile);
        foreach ($lines as $line) {
            if (empty(trim($line)) || strpos(trim($line), '#') === 0) {
                continue;
            }

            $pos = strpos($line, '=');
            if ($pos !== false) {
                $key = trim(substr($line, 0, $pos));
                $value = trim(substr($line, $pos + 1));

                // Handle special case for SSH_KEY with newlines
                if ($key === 'SSH_KEY') {
                    $value = str_replace(['\\n', '\n'], "\n", $value);
                }

                if (!empty($key) && !getenv($key)) {
                    putenv("$key=$value");
                    $_ENV[$key] = $value;
                    $_SERVER[$key] = $value;
                }
            }
        }
        echo "Manually loaded environment variables from .env file\n";
    }
}

// Set up project root and other key parameters
$projectRoot = dirname(dirname(__DIR__));
putenv("APP_PROJECT_ROOT={$projectRoot}");
$_ENV['APP_PROJECT_ROOT'] = $projectRoot;
$_SERVER['APP_PROJECT_ROOT'] = $projectRoot;

// Set up paths for Symfony DI container
putenv("KERNEL_PROJECT_DIR={$projectRoot}");
putenv("PATHS_BASE={$projectRoot}");
putenv("APP_CONFIG_DIR={$projectRoot}/config");
putenv("APP_FIXTURES_DIR={$projectRoot}/features/fixtures");
putenv("APP_CACHE_DIR={$projectRoot}/var/cache");
putenv("APP_LOGS_DIR={$projectRoot}/var/logs");
putenv("APP_DOWNLOADS_DIR={$projectRoot}/downloads");
putenv("APP_SCREENSHOTS_DIR={$projectRoot}/screenshots");

// Debug environment variables
echo "SSH_HOST: " . (getenv('SSH_HOST') ? "Set" : "Not set") . "\n";
echo "SSH_USER: " . (getenv('SSH_USER') ? "Set" : "Not set") . "\n";
echo "SSH_KEY: " . (getenv('SSH_KEY') ? "Set (length: " . strlen(getenv('SSH_KEY') ?: '') . ")" : "Not set") . "\n";
echo "BROWSERSTACK_USERNAME: " . (getenv('BROWSERSTACK_USERNAME') ? "Set" : "Not set") . "\n";
echo "BROWSERSTACK_ACCESS_KEY: " . (getenv('BROWSERSTACK_ACCESS_KEY') ? "Set (length: " . strlen(getenv('BROWSERSTACK_ACCESS_KEY') ?: '') . ")" : "Not set") . "\n";

// Create necessary directories if they don't exist
if (!is_dir($projectRoot . '/var/cache')) {
    mkdir($projectRoot . '/var/cache', 0777, true);
}
if (!is_dir($projectRoot . '/var/logs')) {
    mkdir($projectRoot . '/var/logs', 0777, true);
}
if (!is_dir($projectRoot . '/screenshots')) {
    mkdir($projectRoot . '/screenshots', 0777, true);
}

// Force real services
putenv("FORCE_REAL_SERVICES=true");

// Note: The container will be created by the Symfony Extension
// But we need to make sure it's available in the global scope for contexts
$GLOBALS['service_container'] = null;

// Register a function to set the container when it's created
if (!function_exists('setServiceContainer')) {
    function setServiceContainer($container)
    {
        $GLOBALS['service_container'] = $container;

        // Also make it available as a synthetic service
        if ($container->has('Symfony\Component\DependencyInjection\ContainerInterface')) {
            try {
                $container->set('Symfony\Component\DependencyInjection\ContainerInterface', $container);
            } catch (\Exception $e) {
                // Ignore if we can't set it
            }
        }

        // Also make it available as service_container
        if ($container->has('service_container')) {
            try {
                $container->set('service_container', $container);
            } catch (\Exception $e) {
                // Ignore if we can't set it
            }
        }
    }
}