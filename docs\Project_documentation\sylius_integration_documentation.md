# Sylius Integration Documentation

## Overview

The Malaberg test automation framework integrates with Sylius, a PHP-based e-commerce framework built on Symfony. This document covers the integration points between the test framework and Sylius, including how to interact with Sylius commands and entities.

## Sylius Architecture

Sylius follows a modular architecture based on Symfony components:

1. **Channels**: Represent different sales channels (e.g., web, mobile, API)
2. **Locales**: Support for multiple languages and regions
3. **Currencies**: Support for multiple currencies
4. **Products**: Product catalog with variants, options, and attributes
5. **Taxons**: Hierarchical categorization of products
6. **Orders**: Customer orders with items, adjustments, and payments
7. **Customers**: Customer accounts and profiles
8. **Promotions**: Discount rules and actions
9. **Shipping**: Shipping methods and calculators
10. **Taxation**: Tax categories and rates
11. **Payments**: Payment methods and gateways

## Sylius Commands

The test framework interacts with Sylius through commands to perform backend operations. These commands are executed through the `AdminCommandContext` class.

### Available Commands

1. **Product Commands**
   - `sylius:product:list`: List all products
   - `sylius:product:show`: Show product details
   - `sylius:product:create`: Create a new product
   - `sylius:product:update`: Update an existing product
   - `sylius:product:delete`: Delete a product

2. **Order Commands**
   - `sylius:order:list`: List all orders
   - `sylius:order:show`: Show order details
   - `sylius:order:cancel`: Cancel an order
   - `sylius:order:ship`: Mark an order as shipped
   - `sylius:order:complete`: Complete an order

3. **Customer Commands**
   - `sylius:customer:list`: List all customers
   - `sylius:customer:show`: Show customer details
   - `sylius:customer:create`: Create a new customer
   - `sylius:customer:update`: Update an existing customer
   - `sylius:customer:delete`: Delete a customer

4. **Promotion Commands**
   - `sylius:promotion:list`: List all promotions
   - `sylius:promotion:show`: Show promotion details
   - `sylius:promotion:create`: Create a new promotion
   - `sylius:promotion:update`: Update an existing promotion
   - `sylius:promotion:delete`: Delete a promotion

### Example Usage

```php
/**
 * @When I create a new product with name :name
 */
public function iCreateANewProductWithName(string $name): void
{
    $command = sprintf('sylius:product:create "%s"', $name);
    $this->executeAdminCommand($command);
}

/**
 * @When I cancel order :orderNumber
 */
public function iCancelOrder(string $orderNumber): void
{
    $command = sprintf('sylius:order:cancel %s', $orderNumber);
    $this->executeAdminCommand($command);
}
```

## Sylius Entities

The test framework interacts with Sylius entities through the Doctrine ORM. These entities are accessed through the `DatabaseContext` class.

### Key Entities

1. **Product**
   - `Sylius\Component\Core\Model\Product`: Represents a product
   - `Sylius\Component\Core\Model\ProductVariant`: Represents a product variant
   - `Sylius\Component\Core\Model\ProductOption`: Represents a product option
   - `Sylius\Component\Core\Model\ProductAttribute`: Represents a product attribute

2. **Order**
   - `Sylius\Component\Core\Model\Order`: Represents an order
   - `Sylius\Component\Core\Model\OrderItem`: Represents an order item
   - `Sylius\Component\Core\Model\OrderItemUnit`: Represents an order item unit
   - `Sylius\Component\Core\Model\Adjustment`: Represents an order adjustment

3. **Customer**
   - `Sylius\Component\Core\Model\Customer`: Represents a customer
   - `Sylius\Component\Core\Model\ShopUser`: Represents a shop user
   - `Sylius\Component\Core\Model\CustomerGroup`: Represents a customer group

4. **Address**
   - `Sylius\Component\Core\Model\Address`: Represents an address

### Example Usage

```php
/**
 * @Then the order :orderNumber should have status :status
 */
public function theOrderShouldHaveStatus(string $orderNumber, string $status): void
{
    $order = $this->getOrderByNumber($orderNumber);
    
    if ($order->getState() !== $status) {
        throw new \RuntimeException(
            sprintf('Order %s has status %s, expected %s', $orderNumber, $order->getState(), $status)
        );
    }
}

/**
 * @Then the product :productName should have :quantity items in stock
 */
public function theProductShouldHaveItemsInStock(string $productName, int $quantity): void
{
    $product = $this->getProductByName($productName);
    $variant = $product->getVariants()->first();
    
    if ($variant->getOnHand() !== $quantity) {
        throw new \RuntimeException(
            sprintf('Product %s has %d items in stock, expected %d', $productName, $variant->getOnHand(), $quantity)
        );
    }
}
```

## Sylius API

The test framework can also interact with Sylius through its API. This is done through the `ApiContext` class.

### Available Endpoints

1. **Product Endpoints**
   - `GET /api/v1/products`: List all products
   - `GET /api/v1/products/{id}`: Get product details
   - `POST /api/v1/products`: Create a new product
   - `PUT /api/v1/products/{id}`: Update an existing product
   - `DELETE /api/v1/products/{id}`: Delete a product

2. **Order Endpoints**
   - `GET /api/v1/orders`: List all orders
   - `GET /api/v1/orders/{id}`: Get order details
   - `POST /api/v1/orders`: Create a new order
   - `PUT /api/v1/orders/{id}`: Update an existing order
   - `DELETE /api/v1/orders/{id}`: Delete an order

3. **Customer Endpoints**
   - `GET /api/v1/customers`: List all customers
   - `GET /api/v1/customers/{id}`: Get customer details
   - `POST /api/v1/customers`: Create a new customer
   - `PUT /api/v1/customers/{id}`: Update an existing customer
   - `DELETE /api/v1/customers/{id}`: Delete a customer

### Example Usage

```php
/**
 * @When I create a new product via API with name :name
 */
public function iCreateANewProductViaApiWithName(string $name): void
{
    $data = [
        'name' => $name,
        'code' => strtolower(str_replace(' ', '_', $name)),
        'enabled' => true
    ];
    
    $response = $this->apiClient->post('/api/v1/products', $data);
    
    if ($response->getStatusCode() !== 201) {
        throw new \RuntimeException(
            sprintf('Failed to create product via API: %s', $response->getContent())
        );
    }
}

/**
 * @Then the API should return product :productName
 */
public function theApiShouldReturnProduct(string $productName): void
{
    $response = $this->apiClient->get('/api/v1/products', [
        'name' => $productName
    ]);
    
    $data = json_decode($response->getContent(), true);
    
    if (empty($data['_embedded']['items'])) {
        throw new \RuntimeException(
            sprintf('Product %s not found via API', $productName)
        );
    }
}
```

## Sylius Admin Panel

The test framework can interact with the Sylius admin panel through page objects. This is done through the `AdminContext` class and related page objects.

### Admin Page Objects

1. **AdminLoginPage**: Handles admin login
2. **AdminDashboardPage**: Handles the admin dashboard
3. **AdminProductsPage**: Handles product management
4. **AdminOrdersPage**: Handles order management
5. **AdminCustomersPage**: Handles customer management
6. **AdminPromotionsPage**: Handles promotion management

### Example Usage

```php
/**
 * @Given I am logged in as an administrator
 */
public function iAmLoggedInAsAnAdministrator(): void
{
    $adminLoginPage = $this->pageFactory->getPage('AdminLoginPage');
    $adminLoginPage->open();
    $adminLoginPage->login('admin', 'admin');
}

/**
 * @When I create a new product in the admin panel with name :name
 */
public function iCreateANewProductInTheAdminPanelWithName(string $name): void
{
    $adminProductsPage = $this->pageFactory->getPage('AdminProductsPage');
    $adminProductsPage->open();
    $adminProductsPage->clickCreateButton();
    $adminProductsPage->fillProductForm([
        'name' => $name,
        'code' => strtolower(str_replace(' ', '_', $name)),
        'enabled' => true
    ]);
    $adminProductsPage->saveProduct();
}
```

## Best Practices for Sylius Integration

1. **Use Commands for Backend Operations**: Use Sylius commands for backend operations that don't require UI interaction.
2. **Use API for Automated Testing**: Use the Sylius API for automated testing of backend functionality.
3. **Use Page Objects for UI Testing**: Use page objects for testing the Sylius admin panel and frontend.
4. **Verify Database State**: Verify the database state after operations to ensure data integrity.
5. **Handle Transactions**: Use database transactions to ensure test isolation.
6. **Mock External Services**: Mock external services to avoid dependencies on third-party systems.
7. **Use Fixtures**: Use fixtures to set up test data instead of creating it through the UI.
8. **Clean Up After Tests**: Clean up test data after tests to avoid interference between tests.
