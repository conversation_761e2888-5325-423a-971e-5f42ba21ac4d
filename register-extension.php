<?php

// Manually register the PageObjectExtension namespace
spl_autoload_register(function ($class) {
    // Only handle classes in the FriendsOfBehat\PageObjectExtension namespace
    if (strpos($class, 'FriendsOfBehat\\PageObjectExtension\\') !== 0) {
        return;
    }
    
    // Convert namespace to file path
    $relativeClass = substr($class, strlen('FriendsOfBehat\\PageObjectExtension\\'));
    $file = __DIR__ . '/temp-page-object/src/' . str_replace('\\', '/', $relativeClass) . '.php';
    
    // Check if file exists and load it
    if (file_exists($file)) {
        require $file;
        return true;
    }
    
    return false;
});

// If script is run directly, provide feedback
if (PHP_SAPI === 'cli' && basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    echo "PageObjectExtension autoloader registered successfully.\n";
    
    // Test loading the extension class
    if (class_exists('FriendsOfBehat\\PageObjectExtension\\PageObjectExtension')) {
        echo "Extension class loaded successfully.\n";
    } else {
        echo "Warning: Extension class could not be loaded.\n";
    }
}
