<?php

namespace App\Page;

use App\Page\Base\BasePage;
use App\Page\Base\ProductPageInterface;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use RuntimeException;

/**
 * ProductPage handles actions on the product detail page.
 */
class ProductPage extends BasePage implements ProductPageInterface
{
    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'QUANTITY' => '[data-value="1"]',
        'PURCHASE_TYPE' => '.ratio-title.product-variant-label-info',
        'PRODUCT_NAME' => '.title',
        'PRICE' => '#product-price',
        'RESTRICTIONS' => '.product-restrictions .warning',
        'FLAVOR' => '.flavor',
        'QUANTITY_OPTIONS' => '.quantity-options .quantity-option',
        'PURCHASE_OPTIONS' => '.purchase-options .purchase-option',
        'ONE_TIME_PURCHASE' => '.one-time-purchase',
        'SUBSCRIPTION_PURCHASE' => '.subscription-purchase',
        'SUBSCRIPTION_FREQUENCY' => '.subscription-frequency-select',
        'ADD_TO_CART_BUTTON' => '#sylius-product-adding-to-cart button[type="submit"]',
        'CART_SUMMARY' => '.cart-summary'
    ];
    /**
     * The path of the product page.
     *
     * @var string
     */
    protected string $path = '/product/{slug}';
    /**
     * Test data service
     *
     * @var TestDataServiceInterface
     */
    private TestDataServiceInterface $dataService;
    /**
     * Product name to URL slug mapping
     * @var array<string, string>
     */
    private array $productSlugs = [
        'Total Harmony' => 'aeons-total-harmony',
        'Ancient Roots' => 'aeons-ancient-roots',
        'Sunrise Spark' => 'aeons-sunrise-spark',
        'Sunset Soothe' => 'aeons-sunset-soothe',
        'Nature\'s Gift' => 'aeons-natures-gift',
        'Golden Harvest' => 'aeons-golden-harvest',
        // Add other products as needed
    ];

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     * @param TestDataServiceInterface $dataService Test data service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(
        BrowserServiceInterface  $browserService,
        TestDataServiceInterface $dataService,
        ?string                  $baseUrl = null
    )
    {
        parent::__construct($browserService, $baseUrl);
        $this->dataService = $dataService;
    }

    /**
     * Loads the product page for a specific product.
     *
     * @param string $productName The actual product name (e.g., "Total Harmony")
     * @param ?array $productData Optional product data
     * @throws RuntimeException If product name is not recognized
     */
    public function loadWithName(string $productName, ?array $productData = null): void
    {
        if ($productData) {
            // Verify product name matches
            if ($productData['name'] !== $productName) {
                throw new RuntimeException(
                    sprintf('Product name mismatch. Expected: %s, Got: %s',
                        $productName,
                        $productData['name']
                    )
                );
            }
            $fullUrl = $this->baseUrl . '/' . trim($productData['url_path'], '/');
        } else {
            // Fallback to using slugs if no data provided
            if (!isset($this->productSlugs[$productName])) {
                throw new RuntimeException("Unknown product: $productName");
            }
            $fullUrl = $this->baseUrl . '/product/' . $this->productSlugs[$productName];
        }

        $this->browserService->visit($fullUrl);
        $this->waitForPageToLoad();
    }

    /**
     * Select a flavor
     *
     * @param string $flavor Flavor name
     * @return void
     */
    public function selectFlavor(string $flavor): void
    {
        // Try using the specific selector first
        try {
            $this->clickElement(self::SELECTORS['FLAVOR'] . "[data-value='$flavor']");
        } catch (\Throwable $e) {
            // If that fails, try to find an element containing the flavor text
            $flavorSelector = '.flavor-options .flavor-option, .flavor';
            $flavorElements = $this->browserService->findElements($flavorSelector);

            $found = false;
            foreach ($flavorElements as $element) {
                if (trim($element->getText()) === $flavor) {
                    $element->click();
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                throw new RuntimeException("Flavor '$flavor' not found");
            }
        }

        $this->waitForAjaxToComplete();
    }

    /**
     * Add the product to cart
     *
     * @param int $quantity Quantity to add
     * @return void
     */
    public function addToCart(int $quantity = 1): void
    {
        // Set quantity if needed
        if ($quantity > 1) {
            $this->setQuantity($quantity);
        }

        // Click add to cart button
        $this->clickElement(self::SELECTORS['ADD_TO_CART_BUTTON']);
        $this->waitForAjaxToComplete();
        $this->waitForElementVisible(self::SELECTORS['CART_SUMMARY']);
    }

    /**
     * Set the product quantity
     *
     * @param int $quantity Quantity to set
     * @return void
     */
    public function setQuantity(int $quantity): void
    {
        $this->fillField('#sylius_add_to_cart_cartItem_quantity', (string)$quantity);
    }

    /**
     * Select quantity option by name
     *
     * @param string $optionName Option name (e.g., "minimum", "1 Jar")
     * @return void
     */
    public function selectQuantityOption(string $optionName): void
    {
        $optionSelector = self::SELECTORS['QUANTITY_OPTIONS'];
        $optionElements = $this->browserService->findElements($optionSelector);

        $found = false;
        foreach ($optionElements as $element) {
            if (trim($element->getText()) === $optionName ||
                stripos($element->getText(), $optionName) !== false) {
                $element->click();
                $found = true;
                break;
            }
        }

        if (!$found) {
            throw new RuntimeException("Quantity option '$optionName' not found");
        }

        $this->waitForAjaxToComplete();
    }

    /**
     * Select quantity
     *
     * @param mixed $quantity Quantity to select (int or string)
     * @return void
     */
    public function selectQuantity($quantity): void
    {
        if (is_numeric($quantity)) {
            $this->setQuantity((int)$quantity);
        } else {
            // Map string quantities to their UI representations
            $quantityMapping = [
                'minimum' => '1 Jar',
                'medium' => '3 Jars',
                'maximum' => '6 Jars'
            ];

            $uiQuantity = $quantityMapping[$quantity] ?? $quantity;
            $this->selectQuantityOption($uiQuantity);
        }

        $this->waitForAjaxToComplete();
    }

    /**
     * Select purchase type
     *
     * @param string $purchaseType Purchase type text
     * @return void
     */
    public function selectPurchaseType(string $purchaseType): void
    {
        // Map common purchase type labels to selectors
        $selectorMap = [
            'one-time' => self::SELECTORS['ONE_TIME_PURCHASE'],
            'one time' => self::SELECTORS['ONE_TIME_PURCHASE'],
            'One-Time Purchase' => self::SELECTORS['ONE_TIME_PURCHASE'],
            'subscribe' => self::SELECTORS['SUBSCRIPTION_PURCHASE'],
            'Subscribe & Save' => self::SELECTORS['SUBSCRIPTION_PURCHASE'],
            'subscription' => self::SELECTORS['SUBSCRIPTION_PURCHASE']
        ];

        $selector = $selectorMap[strtolower($purchaseType)] ?? null;

        if ($selector) {
            $this->clickElement($selector);
        } else {
            // Try to find element by text
            $this->clickElementContainingText(self::SELECTORS['PURCHASE_OPTIONS'], $purchaseType);
        }

        $this->waitForAjaxToComplete();
    }

    /**
     * Select supply duration
     *
     * @param string $frequency Frequency display text
     * @return int Days between deliveries
     */
    public function selectSupplyDuration(string $frequency): int
    {
        $this->browserService->selectOption(self::SELECTORS['SUBSCRIPTION_FREQUENCY'], $frequency);

        // Map frequency to days
        $daysMap = [
            '30 days' => 30,
            '60 days' => 60,
            '90 days' => 90,
            'Every month' => 30,
            'Every 2 months' => 60,
            'Every 3 months' => 90,
        ];

        $this->waitForAjaxToComplete();
        return $daysMap[$frequency] ?? 30;
    }

    /**
     * Get the product price
     *
     * @return string
     */
    public function getPrice(): string
    {
        return $this->getElementText(self::SELECTORS['PRICE']);
    }

    /**
     * Get the product name
     *
     * @return string
     */
    public function getProductName(): string
    {
        return $this->getElementText(self::SELECTORS['PRODUCT_NAME']);
    }

    /**
     * Check if page contains product name
     *
     * @param string $productName Product name
     * @return bool True if found
     */
    public function hasProductName(string $productName): bool
    {
        $actualName = $this->getProductName();
        return stripos($actualName, $productName) !== false;
    }

    /**
     * Check if page contains content text
     *
     * @param string $text Text to find
     * @return bool True if found
     */
    public function hasContentText(string $text): bool
    {
        return $this->browserService->getSession()->getPage()->hasContent($text);
    }

    /**
     * Get the selected purchase option
     *
     * @return string Selected purchase option text
     */
    public function getSelectedPurchaseOption(): string
    {
        $purchaseOptionsSelector = self::SELECTORS['PURCHASE_OPTIONS'];
        $purchaseElements = $this->browserService->findElements($purchaseOptionsSelector);

        foreach ($purchaseElements as $element) {
            if ($element->hasClass('selected') || $element->hasClass('active')) {
                return trim($element->getText());
            }
        }

        throw new RuntimeException("No selected purchase option found");
    }

    /**
     * Check if product has flavor options
     *
     * @return bool True if product has flavor options
     */
    public function hasFlavorOptions(): bool
    {
        $flavorSelector = '.flavor-options .flavor-option, .flavor';
        $flavorElements = $this->browserService->findElements($flavorSelector);

        return count($flavorElements) > 0;
    }

    /**
     * Get selected flavor
     *
     * @return string|null The selected flavor or null if none selected
     */
    public function getSelectedFlavor(): ?string
    {
        $flavorSelector = '.flavor-options .flavor-option.selected, .flavor.selected';
        $flavorElements = $this->browserService->findElements($flavorSelector);

        if (count($flavorElements) > 0) {
            return trim($flavorElements[0]->getText());
        }

        return null;
    }

    /**
     * Click element containing specified text
     *
     * @param string $selector Base selector
     * @param string $text Text to match
     * @return void
     */
    private function clickElementContainingText(string $selector, string $text): void
    {
        $elements = $this->browserService->findElements($selector);

        foreach ($elements as $element) {
            if (stripos($element->getText(), $text) !== false) {
                $element->click();
                return;
            }
        }

        throw new RuntimeException("No element found containing text: $text");
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible('#sylius-product-adding-to-cart');
    }

    /**
     * Check if the product name is visible
     *
     * @return bool True if product name is visible
     */
    public function isProductNameVisible(): bool
    {
        return $this->browserService->isElementVisible(self::SELECTORS['PRODUCT_NAME']);
    }

    /**
     * Check if the product price is visible
     *
     * @return bool True if product price is visible
     */
    public function isPriceVisible(): bool
    {
        return $this->browserService->isElementVisible(self::SELECTORS['PRICE']);
    }

    /**
     * Check if the product description is visible
     *
     * @return bool True if product description is visible
     */
    public function isDescriptionVisible(): bool
    {
        return $this->browserService->isElementVisible('.product-description');
    }

    /**
     * Check if quantity options are visible
     *
     * @return bool True if quantity options are visible
     */
    public function areQuantityOptionsVisible(): bool
    {
        return $this->browserService->isElementVisible(self::SELECTORS['QUANTITY_OPTIONS']);
    }

    /**
     * Check if purchase type options are visible
     *
     * @return bool True if purchase type options are visible
     */
    public function arePurchaseTypeOptionsVisible(): bool
    {
        return $this->browserService->isElementVisible(self::SELECTORS['PURCHASE_OPTIONS']);
    }

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // Product page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // Product page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // Product page doesn't have subscription items, so return empty array
        return [];
    }

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array
    {
        // Product page doesn't have cart items, so return empty array
        return [];
    }
}
