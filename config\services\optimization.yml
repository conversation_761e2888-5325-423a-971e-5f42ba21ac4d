# Performance optimization configuration
services:
  # Default configuration for all services
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Cache service configuration
  App\Service\Cache\CacheServiceInterface:
    class: App\Service\Cache\FilesystemCacheService
    arguments:
      $cacheDir: '%app.cache_dir%/app_cache'
      $defaultLifetime: 3600
    public: true

  # Browser service optimization
  browser.service.decorated:
    class: App\Service\Browser\BrowserService
    arguments:
      $session: '@mink.session'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: false

  App\Service\Browser\BrowserServiceInterface:
    class: App\Service\Browser\CachedBrowserService
    arguments:
      $innerService: '@browser.service.decorated'
      $cacheService: '@App\Service\Cache\CacheServiceInterface'
    public: true

  # Configuration service optimization
  configuration.service.decorated:
    class: App\Service\Configuration\ConfigurationService
    arguments:
      $configDir: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
    public: false

  App\Service\Configuration\ConfigurationServiceInterface:
    class: App\Service\Configuration\CachedConfigurationService
    arguments:
      $innerService: '@configuration.service.decorated'
      $cacheService: '@App\Service\Cache\CacheServiceInterface'
    public: true

  # Lazy loading for heavy services
  App\Service\Data\TestDataService:
    lazy: true
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
      $validator: '@App\Service\Validation\ValidationServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
      $logger: '@logger'
    public: true

  App\Service\Validation\ValidationService:
    lazy: true
    arguments:
      $schemasDir: '%app.project_root%/config/schemas'
      $logger: '@logger'
    public: true

  App\Service\Api\ApiServiceInterface:
    lazy: true

  # Performance-optimized page factory
  App\Service\Page\PageFactoryInterface:
    class: App\Factory\OptimizedPageFactory
    arguments:
      $container: '@service_container'
      $cacheService: '@App\Service\Cache\CacheServiceInterface'
      $logger: '@logger'
    public: true