# 06 Docker Usage

This guide shows how to build and use the Docker image for running tests.

## Build the Docker Image

From the project root:

```bash
docker build -f tests/Dockerfile -t project-behat-test:latest .
```

This will:

- Install PHP 8.1 CLI and necessary extensions.
- Install composer and project dependencies.
- Copy the entire codebase into `/app`.

## Run Tests Inside Docker

You can run Behat tests in a container without installing anything locally:

```bash
docker run --rm \
  -v $(pwd):/app \
  -e TEST_BASE_URL \
  -e TEST_ENV \
  -e BROWSERSTACK_USERNAME \
  -e BROWSERSTACK_ACCESS_KEY \
  project-behat-test:latest \
  --strict
```

To use the BrowserStack profile:

```bash
docker run --rm \
  -v $(pwd):/app \
  -e TEST_BASE_URL \
  -e TEST_ENV \
  -e BROWSERSTACK_USERNAME \
  -e BROWSERSTACK_ACCESS_KEY \
  project-behat-test:latest \
  --profile=browserstack --format=pretty,junit --out=report.xml
```

### CI with Docker

In GitLab CI, change the job to:

```yaml
test:browserstack:
  image: project-behat-test:latest
  stage: test
  script:
    - vendor/bin/behat --profile=browserstack --format=pretty,junit --out=report.xml
  artifacts:
    reports:
      junit: report.xml
    paths:
      - screenshots/
      - logs/
```

Benefits:

- No need to install PHP/composer per job.
- Deterministic environment matching local. 