<?php

namespace Features\Bootstrap\Tools;

use Features\Bootstrap\Helper\DatabaseHelper;
use PDO;
use RuntimeException;

class DatabaseConnectionVerifier
{
    private DatabaseHelper $dbHelper;

    public function __construct(array $config)
    {
        // Ensure config matches DatabaseHelper expectations
        $dbConfig = [
            'db' => [
                'host' => $config['db']['host'],
                'port' => $config['db']['port'],
                'database' => $config['db']['database'],
                'user' => $config['db']['user'],
                'password' => $config['db']['password']
            ]
        ];

        $this->dbHelper = new DatabaseHelper($dbConfig);
    }

    /**
     * Verifies database connection and performs basic checks
     *
     * @return array Connection status and diagnostic information
     * @throws RuntimeException if connection fails
     */
    public function verifyConnection(): array
    {
        try {
            $connection = $this->dbHelper->getConnection();
            $diagnostics = $this->runDiagnostics($connection);

            return [
                'status' => 'success',
                'message' => 'Database connection successful',
                'diagnostics' => $diagnostics
            ];
        } catch (\Exception $e) {
            throw new RuntimeException('Database connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Runs basic diagnostic tests on the database connection
     */
    private function runDiagnostics(PDO $connection): array
    {
        $diagnostics = [];

        // Test 1: Check if we can execute a simple query
        try {
            $stmt = $connection->query('SELECT 1');
            $diagnostics['basic_query'] = [
                'status' => 'success',
                'message' => 'Basic query execution successful'
            ];
        } catch (\Exception $e) {
            $diagnostics['basic_query'] = [
                'status' => 'error',
                'message' => 'Basic query failed: ' . $e->getMessage()
            ];
        }

        // Test 2: Check database version
        try {
            $version = $connection->getAttribute(PDO::ATTR_SERVER_VERSION);
            $diagnostics['server_version'] = [
                'status' => 'success',
                'version' => $version
            ];
        } catch (\Exception $e) {
            $diagnostics['server_version'] = [
                'status' => 'error',
                'message' => 'Failed to get server version: ' . $e->getMessage()
            ];
        }

        // Test 3: Check if we can access the sylius_order table
        try {
            $stmt = $connection->query('SELECT COUNT(*) FROM sylius_order');
            $count = $stmt->fetchColumn();
            $diagnostics['sylius_order_access'] = [
                'status' => 'success',
                'message' => "Successfully accessed sylius_order table. Total orders: $count"
            ];
        } catch (\Exception $e) {
            $diagnostics['sylius_order_access'] = [
                'status' => 'error',
                'message' => 'Failed to access sylius_order table: ' . $e->getMessage()
            ];
        }

        return $diagnostics;
    }
} 