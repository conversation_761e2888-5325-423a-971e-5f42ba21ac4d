# Create a function to parse .env file
function Load-EnvFile {
    param (
        [string]$envPath
    )
    
    $envVars = @{}
    
    if (Test-Path $envPath) {
        Write-Host "Loading environment variables from $envPath"
        Get-Content $envPath | ForEach-Object {
            if ($_ -match '^\s*([^#][^=]+)=(.*)$') {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim('"').Trim("'")
                $envVars[$name] = $value
            }
        }

        # Second pass to interpolate variables
        foreach ($key in $envVars.Keys) {
            $value = $envVars[$key]
            # Replace ${VAR} with actual values
            $value = [regex]::Replace($value, '\${([^}]+)}', {
                param($match)
                $varName = $match.Groups[1].Value
                $envVars[$varName]
            })
            [Environment]::SetEnvironmentVariable($key, $value, 'Process')
            Write-Host "Set $key"
        }
    }
}

# Load main .env file
Load-EnvFile ".\.env"

# Verify key variables
@(
    'BROWSERSTACK_USERNAME',
    'BROWSERSTACK_ACCESS_KEY',
    'BROWSERSTACK_URL',
    'TEST_BASE_URL'
) | ForEach-Object {
    $value = [Environment]::GetEnvironmentVariable($_)
    Write-Host "$($_): $value"
} 