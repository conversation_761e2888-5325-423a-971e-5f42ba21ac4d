<?php

require __DIR__ . '/vendor/autoload.php';

use App\Service\Browser\BrowserServiceInterface;
use App\Service\Browser\CachedBrowserService;
use App\Service\Browser\BrowserService;
use App\Service\Cache\FilesystemCacheService;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

// Initialize container
$container = new ContainerBuilder();

// Set base parameters
$projectRoot = __DIR__;
$container->setParameter('kernel.project_dir', $projectRoot);
$container->setParameter('paths.base', $projectRoot);
$container->setParameter('app.project_root', $projectRoot);
$container->setParameter('app.config_dir', $projectRoot . '/config');
$container->setParameter('app.fixtures_dir', $projectRoot . '/features/fixtures');
$container->setParameter('app.cache_dir', $projectRoot . '/var/cache');
$container->setParameter('app.logs_dir', $projectRoot . '/var/logs');
$container->setParameter('app.downloads_dir', $projectRoot . '/downloads');
$container->setParameter('kernel.debug', true);

// Load service configuration
$loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/config'));
$loader->load('services.yml');

// Compile container
$container->compile();

// Get the browser service
$browserService = $container->get(BrowserServiceInterface::class);

// Test that the browser service is an instance of CachedBrowserService
echo "Browser service class: " . get_class($browserService) . "\n";

// Test that all required methods are implemented
$methods = [
    'wait',
    'isSessionActive',
    'getDriverType',
    'hasContent',
    'navigateBack',
    'getPageTitle',
    'waitForUrlContains',
    'isBrowserStackSession',
    'findElement',
    'getCurrentUrl'
];

$missingMethods = [];
foreach ($methods as $method) {
    if (!method_exists($browserService, $method)) {
        $missingMethods[] = $method;
    }
}

if (empty($missingMethods)) {
    echo "All required methods are implemented.\n";
} else {
    echo "Missing methods: " . implode(', ', $missingMethods) . "\n";
}

echo "Test completed successfully.\n";
