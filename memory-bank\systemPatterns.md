# System Patterns

## Architecture Patterns

### Service-Oriented Architecture

The framework now follows a service-oriented architecture with the following key principles:

1. **Service-Oriented Design**: All functionality is encapsulated in services with well-defined interfaces.
2. **Dependency Injection**: Services are injected where needed rather than directly instantiated.
3. **Interface-Based Programming**: All services implement interfaces to allow for easy substitution and testing.
4. **Separation of Concerns**: Each component has a single responsibility.
5. **Testability**: The architecture is designed to be easily testable with unit and integration tests.

### Core Services

The architecture includes the following core services:

- **BrowserService**: Handles browser interactions
- **ConfigurationService**: Manages configuration settings
- **LoggingService**: Provides logging functionality
- **SharedStateService**: Manages shared state between contexts
- **TestDataService**: Provides access to test data
- **ValidationService**: Handles validation logic
- **CacheService**: Provides caching functionality

### Page Object Pattern

Page objects follow a consistent implementation pattern:

1. Extend `BasePage`
2. Define the page path
3. Implement page-specific methods
4. Register the page object in `config/services/pages.yml`

### Context Pattern

Contexts follow a consistent implementation pattern:

1. Extend `BaseContext`
2. Implement constructor with service dependencies
3. Implement step definitions that use services
4. Register the context in `config/services/contexts.yml`

## Performance Patterns

### Service Caching

Frequently used services are cached to avoid repeated instantiation:

```php
private array $serviceCache = [];

public function getService(string $id)
{
    if (!isset($this->serviceCache[$id])) {
        $this->serviceCache[$id] = $this->container->get($id);
    }
    return $this->serviceCache[$id];
}
```

### Lazy Loading

Heavy services are loaded only when needed:

```yaml
services:
  App\Service\HeavyService:
    lazy: true
    public: true
```

### Result Caching

Results of expensive operations are cached:

```php
private array $resultCache = [];

public function getExpensiveResult(string $key)
{
    if (!isset($this->resultCache[$key])) {
        $this->resultCache[$key] = $this->calculateExpensiveResult($key);
    }
    return $this->resultCache[$key];
}
```

## Error Handling Patterns

### Try-Catch-Log Pattern

```php
try {
    // Do something...
    $this->logInfo("Operation succeeded");
} catch (\Throwable $e) {
    $this->logError("Operation failed", $e);
    throw $e;
}
```

### Service Access Pattern

```php
protected function getConfigService(): ConfigurationServiceInterface
{
    return $this->getService(ConfigurationServiceInterface::class);
}
```

### Page Object Usage Pattern

```php
$productPage = $this->pageFactory->getPage('ProductPage');
$productPage->open(['slug' => $productSlug]);
$productPage->addToCart();
```

### State Management Pattern

```php
// Store data
$this->stateService->set('cart.items', $cartItems);

// Retrieve data with fallback
$cartItems = $this->stateService->get('cart.items') ?? [];
```

[2025-04-04 02:20:11] - Updated with information about the service-oriented architecture patterns.