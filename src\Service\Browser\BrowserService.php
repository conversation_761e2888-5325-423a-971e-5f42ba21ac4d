<?php

namespace App\Service\Browser;

use App\Service\AbstractService;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\DriverException;
use Behat\Mink\Session;
use D<PERSON>ore\ChromeDriver\ChromeDriver;
use Psr\Log\LoggerInterface;
use RuntimeException;

/**
 * Service for browser interactions
 */
class BrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    private int $defaultTimeout = 30;

    /**
     * Constructor
     *
     * @param Session $session Mink session
     * @param string $screenshotsDir Screenshots directory path
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        Session          $session,
        string           $screenshotsDir,
        ?LoggerInterface $logger = null
    )
    {
        parent::__construct($logger);

        $this->session = $session;
        $this->screenshotsDir = $screenshotsDir;

        // Create screenshots directory if it doesn't exist
        if (!is_dir($this->screenshotsDir) && !mkdir($this->screenshotsDir, 0777, true)) {
            $this->logError(sprintf("Failed to create screenshots directory: %s", $this->screenshotsDir));
        }

        $this->logInfo("Initializing BrowserService");
    }

    /**
     * {@inheritdoc}
     */
    public function getSession(): Session
    {
        return $this->session;
    }

    /**
     * {@inheritdoc}
     */
    public function visit(string $url): void
    {
        $this->logger->debug("Attempting to visit URL: {$url}");
        $startTime = microtime(true);
        $maxRetries = 3;

        for ($retry = 0; $retry < $maxRetries; $retry++) {
            try {
                // Check if session is active and start it if needed
                if (!$this->session->isStarted() || !$this->isSessionActive()) {
                    $this->logger->debug("Session not active, starting new session...");

                    // Stop existing session if needed
                    if ($this->session->isStarted()) {
                        try {
                            $this->session->stop();
                            $this->logger->debug("Stopped existing session");
                        } catch (\Throwable $e) {
                            $this->logger->warning("Could not stop existing session: " . $e->getMessage());
                        }
                    }

                    // Small delay before starting session
                    sleep(2);

                    $sessionStartTime = microtime(true);
                    $this->session->start();
                    $sessionElapsedTime = round((microtime(true) - $sessionStartTime) * 1000);
                    $this->logger->debug("Session started successfully in {$sessionElapsedTime}ms");
                }

                // Log driver info before navigation attempt
                $this->logger->debug("WebDriver info before navigation:", $this->getDriverInfo());

                // Log attempt
                $this->logger->info("Navigating to URL: {$url} (attempt " . ($retry + 1) . "/{$maxRetries})");

                // Perform the navigation with detailed logging
                $webDriverSessionId = 'N/A';
                try {
                    if ($this->session->isStarted() && method_exists($this->session->getDriver(), 'getWebDriverSessionId')) {
                        $webDriverSessionId = $this->session->getDriver()->getWebDriverSessionId() ?? 'N/A';
                    }
                    $this->logger->debug("Current WebDriver Session ID: {$webDriverSessionId}");
                } catch (\Throwable $e) {
                    // Ignore WebDriver ID retrieval errors
                }

                // Execute the navigation
                $this->logger->info(">>> EXECUTING session->visit('{$url}') NOW <<<");
                $this->session->visit($url);
                $this->logger->info(">>> FINISHED session->visit('{$url}') successfully <<<");

                // Wait for page to fully load
                $this->waitForPageToLoad(60); // Increased timeout to 60 seconds

                // Check status code after navigation
                $statusCode = $this->session->getStatusCode();
                $this->logger->debug("Page status code: {$statusCode}");

                if ($statusCode >= 400) {
                    $this->logger->warning("Received error status code {$statusCode} when visiting {$url}");
                    $this->captureExceptionScreenshot('error_status_code');
                }

                $totalElapsedTime = round((microtime(true) - $startTime) * 1000);
                $this->logger->debug("Total visit operation completed in {$totalElapsedTime}ms");

                // Navigation succeeded, exit retry loop
                return;

            } catch (DriverException $e) {
                $this->logger->error("DriverException while visiting {$url} (attempt " . ($retry + 1) . "/{$maxRetries}): " . $e->getMessage(), [
                    'driver_info' => $this->getDriverInfo(),
                    'trace' => $e->getTraceAsString()
                ]);
                $this->captureExceptionScreenshot('driver_exception');

                // Only retry if we haven't reached max retries
                if ($retry < $maxRetries - 1) {
                    // Exponential backoff: 5s, 10s, 15s
                    $delaySeconds = 5 * ($retry + 1);
                    $this->logger->debug("Waiting {$delaySeconds}s before retry");
                    sleep($delaySeconds);
                } else {
                    // Max retries reached, propagate the exception
                    throw new RuntimeException(
                        sprintf('Failed to visit URL after %d attempts: %s', $maxRetries, $url),
                        0,
                        $e
                    );
                }
            } catch (\Exception $e) {
                $this->logger->error("Exception while visiting {$url} (attempt " . ($retry + 1) . "/{$maxRetries}): " . $e->getMessage(), [
                    'exception_class' => get_class($e),
                    'trace' => $e->getTraceAsString(),
                    'driver_info' => $this->getDriverInfo()
                ]);
                $this->captureExceptionScreenshot('general_exception');

                // Only retry if we haven't reached max retries
                if ($retry < $maxRetries - 1) {
                    // Exponential backoff: 5s, 10s, 15s
                    $delaySeconds = 5 * ($retry + 1);
                    $this->logger->debug("Waiting {$delaySeconds}s before retry");
                    sleep($delaySeconds);
                } else {
                    // Max retries reached, propagate the exception
                    throw new RuntimeException(
                        sprintf('Failed to visit URL after %d attempts: %s', $maxRetries, $url),
                        0,
                        $e
                    );
                }
            }
        }
    }

    /**
     * Wait for page to load completely
     *
     * @param int $timeout Timeout in seconds
     * @throws RuntimeException When page load timeout occurs
     */
    public function waitForPageToLoad(int $timeout = 60): void
    {
        $this->logInfo(sprintf("Waiting for page to load (timeout: %d seconds)", $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;
        $initialUrl = $this->getCurrentUrlSafe();
        $lastReadyState = '';
        $urlChanges = 0;
        $lastUrl = $initialUrl;
        $urlStableSeconds = 0;
        $lastUrlChangeTime = $startTime;

        $this->logDebug("Starting URL: $initialUrl");

        do {
            try {
                $currentUrl = $this->getCurrentUrlSafe();

                // Detect URL changes (redirects)
                if ($currentUrl !== $lastUrl) {
                    $urlChanges++;
                    $this->logInfo(sprintf(
                        "URL changed during page load: %s -> %s (change #%d)",
                        $lastUrl,
                        $currentUrl,
                        $urlChanges
                    ));
                    $lastUrl = $currentUrl;
                    $lastUrlChangeTime = microtime(true);

                    // Short pause to let the new page start loading
                    usleep(500000); // 500ms
                    continue; // Continue checking for more redirects or page load
                }

                // Check URL stability - how long has it been since the last URL change
                $urlStableSeconds = microtime(true) - $lastUrlChangeTime;

                // Check document ready state
                $readyState = $this->session->evaluateScript('return document.readyState;');

                // Log ready state changes
                if ($readyState !== $lastReadyState) {
                    $this->logDebug(sprintf(
                        "Document ready state changed: %s -> %s (URL stable for %.1f seconds)",
                        $lastReadyState ?: 'none',
                        $readyState,
                        $urlStableSeconds
                    ));
                    $lastReadyState = $readyState;
                }

                if ($readyState === 'complete') {
                    // Also check for jQuery if it's present
                    $jQueryReady = $this->session->evaluateScript(
                        'return typeof jQuery === "undefined" || jQuery.active === 0;'
                    );

                    // Additional check for AJAX requests to be complete
                    $ajaxComplete = true;
                    try {
                        // Try to detect pending fetch or XHR requests
                        $pendingRequests = $this->session->evaluateScript(
                            'return window._pendingFetchRequests || 0 + window._pendingXHRRequests || 0;'
                        );

                        if ($pendingRequests > 0) {
                            $ajaxComplete = false;
                            $this->logDebug("Detected $pendingRequests pending network requests");
                        }
                    } catch (\Throwable $e) {
                        // Ignore failures in pending request detection
                    }

                    if ($jQueryReady && $ajaxComplete) {
                        // If URL has been stable for at least 1 second and page is loaded
                        if ($urlStableSeconds >= 1) {
                            // Try to locate common page elements for checkout or other key pages
                            $checkoutElements = [
                                '.checkout-container',
                                '#checkout-form',
                                '.checkout-page',
                                '.shipping-information',
                                '.product-container', // For product pages
                                '.cart-container',    // For cart pages
                                '.confirmation-page', // For confirmation pages
                                'form[name="checkout"]', // Alternative checkout form
                                '.order-summary'      // Common element on many pages
                            ];

                            foreach ($checkoutElements as $selector) {
                                if ($this->elementExists($selector) && $this->isElementVisible($selector)) {
                                    $this->logInfo(sprintf(
                                        "Page loaded successfully after %.1f seconds (found element: %s, URL changes: %d)",
                                        microtime(true) - $startTime,
                                        $selector,
                                        $urlChanges
                                    ));
                                    return;
                                }
                            }

                            // If no specific elements are found but page seems loaded and URL is stable
                            if ($urlStableSeconds >= 2) {
                                $this->logInfo(sprintf(
                                    "Page loaded successfully after %.1f seconds (general load checks passed, URL changes: %d)",
                                    microtime(true) - $startTime,
                                    $urlChanges
                                ));
                                return;
                            }
                        } else {
                            $this->logDebug(sprintf(
                                "Document ready but URL only stable for %.1f seconds, waiting for more stability",
                                $urlStableSeconds
                            ));
                        }
                    } else {
                        $this->logDebug("Document ready but jQuery/AJAX not complete");
                    }
                }
            } catch (\Throwable $e) {
                // Ignore exceptions during polling
                $this->logDebug("Exception while checking page load status: " . $e->getMessage());
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        // If we reach here, the page didn't load within timeout
        $finalUrl = $this->getCurrentUrlSafe();
        $totalTime = microtime(true) - $startTime;

        $this->logError(sprintf(
            "Timeout waiting for page to load after %.1f seconds. URL changes: %d, Initial: %s, Final: %s",
            $totalTime,
            $urlChanges,
            $initialUrl,
            $finalUrl
        ));

        // Take a screenshot to help with debugging
        $this->takeScreenshot('page_load_timeout');

        // Throw exception but only if URLs haven't changed recently
        // This avoids timeout errors during active redirects
        if ($urlStableSeconds > 5) {
            throw new RuntimeException(sprintf(
                "Timeout waiting for page to load after %.1f seconds. URL has been stable for %.1f seconds.",
                $totalTime,
                $urlStableSeconds
            ));
        } else {
            $this->logWarning(sprintf(
                "Page load timeout occurred but URL changed recently (%.1f seconds ago) - continuing execution",
                $urlStableSeconds
            ));
        }
    }

    /**
     * {@inheritdoc}
     */
    public function takeScreenshot(string $name = null): string
    {
        // Add retry logic for screenshot capture
        $maxRetries = 2;
        $attempt = 1;

        while ($attempt <= $maxRetries) {
            try {
                if (!$this->session->isStarted()) {
                    $errorMsg = "Cannot take screenshot: session not started (attempt $attempt/$maxRetries)";
                    $this->logError($errorMsg);

                    if ($attempt >= $maxRetries) {
                        throw new RuntimeException($errorMsg);
                    }

                    // Try to start session
                    try {
                        $this->logInfo("Attempting to start session for screenshot");
                        $this->session->start();
                    } catch (\Exception $e) {
                        $this->logError("Failed to start session for screenshot: " . $e->getMessage());
                    }

                    $attempt++;
                    sleep(2);
                    continue;
                }

                $driver = $this->session->getDriver();

                if (!$driver instanceof ChromeDriver) {
                    $errorMsg = sprintf(
                        "Current driver %s does not support screenshots",
                        get_class($driver)
                    );
                    $this->logError($errorMsg);
                    throw new RuntimeException($errorMsg);
                }

                $filename = $this->createScreenshotFilename($name);
                $filepath = $this->screenshotsDir . '/' . $filename;

                $this->logInfo(sprintf("Taking screenshot: %s", $filepath));

                $screenshot = $driver->getScreenshot();
                file_put_contents($filepath, $screenshot);
                $this->logInfo(sprintf("Screenshot saved: %s", $filepath));
                return $filepath;
            } catch (\Exception $e) {
                $this->logError(sprintf(
                    "Failed to take screenshot on attempt %d/%d: %s",
                    $attempt,
                    $maxRetries,
                    $e->getMessage()
                ));

                if ($attempt >= $maxRetries) {
                    throw new RuntimeException("Failed to take screenshot after $maxRetries attempts", 0, $e);
                }

                $attempt++;
                sleep(2);
            }
        }

        // This should never be reached, but added for safety
        throw new RuntimeException("Failed to take screenshot due to unknown error");
    }

    /**
     * Create a screenshot filename
     *
     * @param string|null $name Custom name
     * @return string Generated filename
     */
    private function createScreenshotFilename(?string $name = null): string
    {
        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid()), 0, 6);

        if ($name) {
            return sprintf('%s_%s_%s.png', $timestamp, $name, $random);
        }

        return sprintf('%s_screenshot_%s.png', $timestamp, $random);
    }

    /**
     * {@inheritdoc}
     */
    public function waitForElement(string $selector, int $timeout = 30): void
    {
        $this->logInfo(sprintf("Waiting for element: %s (timeout: %d seconds)", $selector, $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $element = $this->session->getPage()->find('css', $selector);
                if ($element && $element->isVisible()) {
                    $this->logInfo(sprintf("Element found: %s", $selector));
                    return;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logError(sprintf("Timeout waiting for element: %s", $selector));
        $this->takeScreenshot('element_timeout');
        throw new RuntimeException(sprintf("Timeout waiting for element: %s", $selector));
    }

    /**
     * {@inheritdoc}
     */
    public function executeScript(string $script)
    {
        $this->logInfo(sprintf("Executing JavaScript: %s", substr($script, 0, 50) . (strlen($script) > 50 ? '...' : '')));

        try {
            return $this->session->evaluateScript($script);
        } catch (\Exception $e) {
            $this->logError("Error executing JavaScript", $e);
            throw new RuntimeException("Error executing JavaScript", 0, $e);
        }
    }

    /**
     * Find an element with retry
     *
     * @param string $selector CSS selector
     * @param int $timeout Timeout in seconds
     * @return NodeElement|null Found element or null if not found
     */
    public function findElement(string $selector, int $timeout = 10): ?NodeElement
    {
        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $element = $this->session->getPage()->find('css', $selector);
                if ($element && $element->isVisible()) {
                    return $element;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function findElements(string $selector): array
    {
        $this->logInfo(sprintf("Finding elements: %s", $selector));

        try {
            return $this->session->getPage()->findAll('css', $selector);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error finding elements: %s", $selector), $e);
            return [];
        }
    }

    /**
     * {@inheritdoc}
     */
    public function waitForElementVisible(string $selector, int $timeout = 30): bool
    {
        $this->logInfo(sprintf("Waiting for element to be visible: %s (timeout: %d seconds)", $selector, $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $element = $this->session->getPage()->find('css', $selector);
                if ($element && $element->isVisible()) {
                    $this->logInfo(sprintf("Element visible: %s", $selector));
                    return true;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logInfo(sprintf("Element not visible within timeout: %s", $selector));
        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function scrollToElement(string $selector): void
    {
        $this->logInfo(sprintf("Scrolling to element: %s", $selector));

        try {
            $script = sprintf(
                "const element = document.querySelector('%s'); " .
                "if (element) { element.scrollIntoView({behavior: 'smooth', block: 'center'}); return true; } " .
                "return false;",
                addslashes($selector)
            );

            $result = $this->executeScript($script);

            if (!$result) {
                $this->logWarning(sprintf("Element not found for scrolling: %s", $selector));
            }
        } catch (\Exception $e) {
            $this->logError(sprintf("Error scrolling to element: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error scrolling to element: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function clickElement(string $selector): void
    {
        $this->logInfo(sprintf("Clicking element: %s", $selector));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Element not found for clicking: %s", $selector));
                throw new RuntimeException(sprintf("Element not found for clicking: %s", $selector));
            }

            $element->click();
        } catch (\Exception $e) {
            $this->logError(sprintf("Error clicking element: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error clicking element: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function fillField(string $selector, string $value): void
    {
        $this->logInfo(sprintf("Filling field: %s", $selector));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Field not found for filling: %s", $selector));
                throw new RuntimeException(sprintf("Field not found for filling: %s", $selector));
            }

            $element->setValue($value);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error filling field: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error filling field: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function selectOption(string $selector, string $value): void
    {
        $this->logInfo(sprintf("Selecting option: %s = %s", $selector, $value));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Select field not found: %s", $selector));
                throw new RuntimeException(sprintf("Select field not found: %s", $selector));
            }

            $element->selectOption($value);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error selecting option: %s = %s", $selector, $value), $e);
            throw new RuntimeException(sprintf("Error selecting option: %s = %s", $selector, $value), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getElementText(string $selector): string
    {
        $this->logInfo(sprintf("Getting text from element: %s", $selector));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Element not found for getting text: %s", $selector));
                throw new RuntimeException(sprintf("Element not found for getting text: %s", $selector));
            }

            return $element->getText();
        } catch (\Exception $e) {
            $this->logError(sprintf("Error getting text from element: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error getting text from element: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function isElementVisible(string $selector): bool
    {
        $this->logInfo(sprintf("Checking if element is visible: %s", $selector));

        try {
            $element = $this->session->getPage()->find('css', $selector);
            return $element && $element->isVisible();
        } catch (\Exception $e) {
            $this->logError(sprintf("Error checking element visibility: %s", $selector), $e);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function waitForDocumentReady(int $timeout = 30): void
    {
        $this->logInfo(sprintf("Waiting for document to be ready (timeout: %d seconds)", $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            $readyState = $this->session->evaluateScript('return document.readyState;');

            if ($readyState === 'complete') {
                $this->logInfo("Document is ready");
                return;
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logError("Timeout waiting for document to be ready");
        $this->takeScreenshot('document_ready_timeout');
        throw new RuntimeException("Timeout waiting for document to be ready");
    }

    /**
     * {@inheritdoc}
     */
    public function waitForAjaxToComplete(int $timeout = 30): void
    {
        $this->logInfo(sprintf("Waiting for AJAX requests to complete (timeout: %d seconds)", $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            // Check for jQuery AJAX requests
            $jQueryActive = $this->session->evaluateScript(
                'return (typeof jQuery !== "undefined") ? jQuery.active : 0;'
            );

            // Check for fetch requests (modern browsers)
            $fetchActive = $this->session->evaluateScript(
                'return (window._pendingFetchRequests || 0);'
            );

            // Check for XMLHttpRequest requests
            $xhrActive = $this->session->evaluateScript(
                'return (window._pendingXHRRequests || 0);'
            );

            if ($jQueryActive === 0 && $fetchActive === 0 && $xhrActive === 0) {
                $this->logInfo("All AJAX requests completed");
                return;
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logWarning("Timeout waiting for AJAX requests to complete");
    }

    /**
     * {@inheritdoc}
     */
    public function elementExists(string $selector): bool
    {
        $this->logInfo(sprintf("Checking if element exists: %s", $selector));

        try {
            $element = $this->session->getPage()->find('css', $selector);
            return $element !== null;
        } catch (\Exception $e) {
            $this->logError(sprintf("Error checking if element exists: %s", $selector), $e);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getDriverType(): string
    {
        $driver = $this->session->getDriver();
        $driverClass = get_class($driver);
        $driverName = basename(str_replace('\\', '/', $driverClass));

        $this->logInfo(sprintf("Driver type: %s", $driverName));
        return $driverName;
    }

    /**
     * {@inheritdoc}
     */
    public function navigateBack(): void
    {
        $this->logInfo("Navigating back in browser history");

        try {
            $this->session->back();
        } catch (\Exception $e) {
            $this->logError("Error navigating back in browser", $e);
            throw new RuntimeException("Error navigating back in browser", 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function isSessionActive(): bool
    {
        try {
            // First check if session is started
            if (!$this->session->isStarted()) {
                return false;
            }

            // Try to execute a simple JavaScript command to verify connectivity
            $jsResult = $this->executeScript('return true;');
            if ($jsResult !== true) {
                return false;
            }

            // Optionally check session status (for Selenium/WebDriver)
            try {
                if (method_exists($this->session->getDriver(), 'getWebDriverSessionId')) {
                    $sessionId = $this->session->getDriver()->getWebDriverSessionId();
                    return !empty($sessionId);
                }
            } catch (\Throwable $e) {
                $this->logWarning("Error checking WebDriver session status: " . $e->getMessage());
                // Don't return false yet - the JavaScript check already passed
            }

            return true;
        } catch (\Exception $e) {
            $this->logError("Session is not active: " . $e->getMessage(), $e);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function isBrowserStackSession(): bool
    {
        try {
            $capabilities = $this->session->getDriver()->getCapabilities();

            // Check if this is a BrowserStack session by looking for BrowserStack-specific capabilities
            if (isset($capabilities['browserstack.user']) ||
                isset($capabilities['bstack:options']) ||
                (isset($capabilities['browserName']) && strpos($capabilities['browserName'], 'BrowserStack') !== false)) {
                return true;
            }

            // Additional check through JavaScript
            $isBrowserStack = $this->executeScript(
                'return !!(window.navigator.userAgent.match(/BrowserStack/) || (window._bstack_flag === true));'
            );

            return (bool)$isBrowserStack;
        } catch (\Exception $e) {
            $this->logError("Error checking if session is BrowserStack", $e);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getPageTitle(): string
    {
        try {
            return $this->session->getPage()->find('css', 'title')?->getText() ??
                $this->executeScript('return document.title;') ?? '';
        } catch (\Exception $e) {
            $this->logError("Error getting page title", $e);
            return '';
        }
    }

    /**
     * {@inheritdoc}
     */
    public function wait(int $seconds): void
    {
        $this->logInfo(sprintf("Waiting for %d seconds", $seconds));
        sleep($seconds);
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentUrl(): string
    {
        try {
            return $this->session->getCurrentUrl();
        } catch (\Exception $e) {
            $this->logError("Error getting current URL", $e);
            throw new RuntimeException("Error getting current URL", 0, $e);
        }
    }

    /**
     * Helper method to get current URL safely
     *
     * @return string Current URL or error message
     */
    private function getCurrentUrlSafe(): string
    {
        try {
            if ($this->session->isStarted()) {
                return $this->session->getCurrentUrl();
            }
        } catch (\Throwable $e) {
            $this->logger->warning("Could not get current URL safely: " . $e->getMessage());
        }
        return 'N/A (Session not started or error)';
    }

    /**
     * {@inheritdoc}
     */
    public function waitForUrlContains(string $text, int $timeout = 30): bool
    {
        $this->logInfo(sprintf("Waiting for URL to contain: %s (timeout: %d seconds)", $text, $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $currentUrl = $this->getCurrentUrl();
                if (strpos($currentUrl, $text) !== false) {
                    $this->logInfo(sprintf("URL contains '%s': %s", $text, $currentUrl));
                    return true;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logInfo(sprintf("Timeout waiting for URL to contain: %s", $text));
        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function waitForRedirect(int $timeout = 60, int $maxRedirects = 5, int $stabilityDuration = 2): array
    {
        $this->logInfo(sprintf(
            "Waiting for redirect chain to complete (timeout: %d seconds, max redirects: %d, stability: %d seconds)",
            $timeout,
            $maxRedirects,
            $stabilityDuration
        ));

        $startTime = microtime(true);
        $overallEndTime = $startTime + $timeout;

        // Track the redirect chain
        $redirectChain = [];
        $visitedUrls = [];
        $redirectCount = 0;
        $stableUrlStartTime = null;
        $lastUrl = null;

        try {
            // Get initial URL
            $originalUrl = $this->getCurrentUrl();
            $redirectChain[] = [
                'url' => $originalUrl,
                'timestamp' => microtime(true),
                'index' => 0
            ];
            $visitedUrls[$originalUrl] = true;
            $currentUrl = $originalUrl;
            $this->logInfo(sprintf("Initial URL in redirect chain: %s", $originalUrl));

            // Monitor for URL changes
            while (microtime(true) < $overallEndTime) {
                try {
                    $newUrl = $this->getCurrentUrl();

                    // URL has changed
                    if ($newUrl !== $currentUrl) {
                        $redirectCount++;
                        $this->logInfo(sprintf("Redirect detected (%d/%d): %s -> %s",
                            $redirectCount, $maxRedirects, $currentUrl, $newUrl));

                        // Record the new URL in the chain
                        $redirectChain[] = [
                            'url' => $newUrl,
                            'timestamp' => microtime(true),
                            'index' => count($redirectChain)
                        ];

                        // Update tracking variables
                        $visitedUrls[$newUrl] = true;
                        $currentUrl = $newUrl;
                        $stableUrlStartTime = microtime(true);

                        // Basic cycle detection
                        if (count($visitedUrls) < count($redirectChain)) {
                            $this->logWarning("Detected a redirect cycle - same URLs revisited");
                        }

                        // Check if max redirects reached
                        if ($redirectCount >= $maxRedirects) {
                            $this->logWarning(sprintf(
                                "Maximum number of redirects reached (%d). Final URL: %s",
                                $maxRedirects,
                                $newUrl
                            ));
                            break;
                        }

                        // Wait for page to load after redirect
                        try {
                            $this->waitForDocumentReady(20);
                            // Short pause to ensure redirect is fully processed
                            usleep(100000); // 100ms
                        } catch (\Throwable $e) {
                            $this->logWarning("Error waiting for document ready after redirect: " . $e->getMessage());
                        }
                    } // URL is stable
                    else {
                        // If this is the first time we're seeing stability, set the start time
                        if ($stableUrlStartTime === null) {
                            $stableUrlStartTime = microtime(true);
                        }

                        // Check if URL has been stable for long enough
                        $stableDuration = microtime(true) - $stableUrlStartTime;
                        if ($stableDuration >= $stabilityDuration) {
                            $this->logInfo(sprintf(
                                "URL stable for %.1f seconds, considering redirect chain complete: %s",
                                $stableDuration,
                                $currentUrl
                            ));
                            break;
                        }
                    }

                    // Small pause between checks to reduce CPU usage
                    usleep(100000); // 100ms

                } catch (\Exception $e) {
                    // Ignore exceptions during URL polling
                    $this->logDebug("Error while polling URL during redirect: " . $e->getMessage());
                    // Continue monitoring for redirects
                }
            }

            $totalTime = microtime(true) - $startTime;
            $this->logInfo(sprintf(
                "Redirect chain complete. Followed %d redirects in %.1f seconds. Final URL: %s",
                $redirectCount,
                $totalTime,
                $currentUrl
            ));

            $finalResult = [
                'original_url' => $originalUrl,
                'final_url' => $currentUrl,
                'redirect_count' => $redirectCount,
                'chain' => $redirectChain,
                'total_time' => $totalTime,
                'timeout_reached' => (microtime(true) >= $overallEndTime)
            ];

            return $finalResult;

        } catch (\Throwable $e) {
            $this->logError("Error waiting for redirect chain: " . $e->getMessage(), $e);
            $finalResult = [
                'original_url' => $originalUrl ?? 'unknown',
                'final_url' => $currentUrl ?? 'unknown',
                'redirect_count' => $redirectCount,
                'chain' => $redirectChain,
                'error' => $e->getMessage(),
                'total_time' => microtime(true) - $startTime
            ];
            return $finalResult;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function hasContent(string $text): bool
    {
        $this->logInfo(sprintf("Checking if page contains text: %s", $text));

        try {
            return $this->session->getPage()->hasContent($text);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error checking if page contains text: %s", $text), $e);
            return false;
        }
    }

    /**
     * Gets diagnostic information about the current WebDriver session
     */
    private function getDriverInfo(): array
    {
        $info = [
            'driver_class' => get_class($this->session->getDriver()),
            'session_started' => $this->session->isStarted() ? 'yes' : 'no',
        ];

        // Try to get browser info if session is started
        if ($this->session->isStarted()) {
            try {
                $capabilities = $this->session->getDriver()->getCapabilities();
                if ($capabilities) {
                    $info['browser_name'] = $capabilities->getBrowserName() ?? 'unknown';
                    $info['browser_version'] = $capabilities->getVersion() ?? 'unknown';
                    $info['platform'] = $capabilities->getPlatform() ?? 'unknown';
                }

                $info['current_url'] = $this->session->getCurrentUrl();
            } catch (\Throwable $e) {
                $info['capabilities_error'] = $e->getMessage();
            }
        }

        return $info;
    }

    /**
     * Captures a screenshot when an exception occurs, if possible
     */
    private function captureExceptionScreenshot(string $errorType): void
    {
        try {
            if ($this->session->isStarted() && $this->session->getDriver()->getScreenshot()) {
                $screenshotDir = sys_get_temp_dir() . '/selenium_errors';
                if (!is_dir($screenshotDir)) {
                    mkdir($screenshotDir, 0777, true);
                }

                $timestamp = date('YmdHis');
                $filename = "{$screenshotDir}/{$errorType}_{$timestamp}.png";

                file_put_contents($filename, $this->session->getDriver()->getScreenshot());
                $this->logger->info("Error screenshot saved to: {$filename}");
            }
        } catch (\Throwable $e) {
            $this->logger->warning("Failed to capture error screenshot: " . $e->getMessage());
        }
    }
}
