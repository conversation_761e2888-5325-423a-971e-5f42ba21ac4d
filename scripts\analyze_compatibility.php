<?php

echo "PHP Package Compatibility Analyzer\n";

// --- Initial Checks ---
// Check if composer command exists
$composerCheck = `where composer`; // PowerShell/Windows command
if (empty(trim($composerCheck))) {
    echo "Error: 'composer' command not found in your PATH. Please ensure Composer is installed and accessible.\n";
    exit(1);
}

// Basic argument handling (expecting project path)
if ($argc < 2) {
    echo "Usage: php analyze_compatibility.php <path_to_project>\n";
    echo "  Example: php analyze_compatibility.php C:\\Users\\<USER>\\Projects\\MyPHPProject\n";
    exit(1);
}

$projectPath = realpath($argv[1]); // Use realpath for consistent path handling
if (!$projectPath) {
    echo "Error: Invalid project path provided: {$argv[1]}\n";
    exit(1);
}

$composerJsonPath = $projectPath . '/composer.json';
$composerLockPath = $projectPath . '/composer.lock';

echo "Project Path: {$projectPath}\n";

// --- 1. File Existence Check ---
if (!file_exists($composerJsonPath)) {
    echo "Error: composer.json not found at {$composerJsonPath}\n";
    exit(1);
}
if (!file_exists($composerLockPath)) {
    echo "Error: composer.lock not found at {$composerLockPath}\n";
    exit(1);
}

echo "Found composer.json and composer.lock.\n";

// --- 2. Parse Composer Files ---
echo "Parsing composer files...\n";
$composerJsonContent = file_get_contents($composerJsonPath);
$composerLockContent = file_get_contents($composerLockPath);

$composerJsonData = json_decode($composerJsonContent, true);
$composerLockData = json_decode($composerLockContent, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "Error parsing composer.json: " . json_last_error_msg() . "\n";
    exit(1);
}
if ($composerLockData === null && json_last_error() !== JSON_ERROR_NONE) {
    echo "Error parsing composer.lock: " . json_last_error_msg() . "\n";
    exit(1);
}

// Extract current versions from lock file for better reporting
$installedVersions = [];
if (isset($composerLockData['packages'])) {
    foreach ($composerLockData['packages'] as $package) {
        // Store version and short commit hash if available
        $versionString = $package['version'];
        if (isset($package['source']['reference'])) {
            $versionString .= ' (' . substr($package['source']['reference'], 0, 7) . ')';
        }
        $installedVersions[$package['name']] = $versionString;
    }
}
if (isset($composerLockData['packages-dev'])) { // Include dev dependencies
    foreach ($composerLockData['packages-dev'] as $package) {
        $versionString = $package['version'];
        if (isset($package['source']['reference'])) {
            $versionString .= ' (' . substr($package['source']['reference'], 0, 7) . ')';
        }
        $installedVersions[$package['name']] = $versionString;
    }
}

echo "Successfully parsed composer files and extracted installed versions.\n";

// --- Data Storage ---
$abandonedPackagesInfo = []; // Store abandoned package name => suggestion/replacement
// Report stores analysis results: package => [status, current_version, latest_version, dependents, suggestion, suggested_replacement_package, action_needed]
$report = [];

/**
 * Runs a Composer command in the specified project directory.
 *
 * @param string $command The Composer command to run (e.g., "show --abandoned").
 * @param string $projectPath The absolute path to the project directory.
 * @return array Contains 'output', 'errors', and 'return_code'.
 */
function run_composer_command($command, $projectPath)
{
    // Ensure we run composer commands within the project directory
    $escapedProjectPath = escapeshellarg($projectPath);
    // Prepend cd and use --no-ansi for cleaner output, redirect stderr to stdout (2>&1)
    $fullCommand = "cd $escapedProjectPath && composer $command --no-ansi 2>&1";

    $descriptorspec = [
        0 => ["pipe", "r"],  // stdin
        1 => ["pipe", "w"],  // stdout
        2 => ["pipe", "w"]   // stderr - might be redundant with 2>&1 but good practice
    ];
    $process = proc_open($fullCommand, $descriptorspec, $pipes, $projectPath);

    $output = '';
    $errors = ''; // If not using 2>&1, capture stderr here
    $return_value = -1;

    if (is_resource($process)) {
        $output = stream_get_contents($pipes[1]);
        fclose($pipes[1]);
        // $errors = stream_get_contents($pipes[2]); // Uncomment if not redirecting stderr
        // fclose($pipes[2]);
        $return_value = proc_close($process);
    } else {
        echo "Error: Could not initiate process for command: $fullCommand\n";
        return ['output' => '', 'errors' => 'Failed to initiate process', 'return_code' => -1];
    }

    // Don't treat all non-zero exits as critical errors for commands like `outdated` or `depends`
    // which might return non-zero if nothing is found, but log them as warnings.
    if ($return_value !== 0) {
        // Log non-critical warnings differently if needed
        // echo "Warning: Composer command finished with exit code $return_value for: $command\n";
        // echo "Output: $output\n";
    }

    return ['output' => $output, 'errors' => $errors, 'return_code' => $return_value];
}

// --- 3. Get Abandoned Packages (using composer command) ---
echo "\nChecking for abandoned packages via Composer...\n";
$result = run_composer_command("show --abandoned", $projectPath);

if ($result['return_code'] !== -1) { // Check if command ran, even if no abandoned found (exit code 0)
    $lines = explode("\n", trim($result['output']));
    foreach ($lines as $line) {
        // Regex: Matches lines starting with "abandoned", captures package name and the rest as suggestion.
        // Example: "abandoned php-http/message-factory Use a PSR-7 implementation instead."
        if (preg_match('/^abandoned\s+([\S]+)\s*(.*)$/', trim($line), $matches)) {
            $packageName = $matches[1];
            $suggestion = !empty(trim($matches[2])) ? trim($matches[2]) : 'No specific suggestion.';
            $abandonedPackagesInfo[$packageName] = $suggestion;
            echo "  Found abandoned: {$packageName} ({$suggestion})\n";
            // Initialize report entry
            $report[$packageName] = [
                'status' => 'abandoned',
                'current_version' => $installedVersions[$packageName] ?? 'N/A', // Get from lock file
                'latest_version' => 'N/A', // Abandoned means no 'latest' version in the usual sense
                'dependents' => [],
                'suggestion' => $suggestion,
                'suggested_replacement_package' => null,
                'action_needed' => 'Investigate replacement'
            ];
        }
    }
} else {
    // This indicates `proc_open` failed, which is more serious.
    echo "Error: Failed to execute 'composer show --abandoned'. Cannot check for abandoned packages.\n";
    // Decide if script should exit or continue with potentially incomplete data.
}

if (empty($abandonedPackagesInfo)) {
    echo "No abandoned packages detected by Composer.\n";
} else {
    // --- 4. Analyze Dependencies (Only if abandoned packages were found) ---
    echo "\nAnalyzing dependencies for abandoned packages...\n";
    foreach (array_keys($abandonedPackagesInfo) as $abandonedPackage) {
        echo "  Checking dependents for: {$abandonedPackage}\n";
        $escapedPackage = escapeshellarg($abandonedPackage);
        $result = run_composer_command("depends {$escapedPackage}", $projectPath);

        // `depends` might return non-zero if package not found or has no dependents, just check output
        if ($result['return_code'] !== -1) { // Check if command execution succeeded at process level
            $lines = explode("\n", trim($result['output']));
            $dependents = [];
            foreach ($lines as $line) {
                $trimmedLine = trim($line);
                // Improved check: Avoid header/self-reference/provides/replaces lines.
                // Looks for lines likely starting with a package name that isn't the abandoned one.
                if (!empty($trimmedLine) && strpos($trimmedLine, $abandonedPackage) === false && !preg_match('/requires|is required by|found in|virtual provides|replaces|cannot be found/i', $trimmedLine)) {
                    $depParts = preg_split('/\s+/', $trimmedLine, 2);
                    $dependentName = $depParts[0];
                    // Basic validation for package name format
                    if (!empty($dependentName) && preg_match('|^[a-zA-Z0-9\_\-\./]+$|', $dependentName)) {
                        $dependents[] = $dependentName;
                    }
                }
            }
            if (!empty($dependents)) {
                $uniqueDependents = array_unique($dependents); // Ensure uniqueness
                echo "    Dependents found: " . implode(', ', $uniqueDependents) . "\n";
                $report[$abandonedPackage]['dependents'] = $uniqueDependents;
                // Add dependents to action needed string
                $report[$abandonedPackage]['action_needed'] .= ". Update dependents: " . implode(', ', $uniqueDependents);
            } else {
                echo "    No direct dependents found (or error parsing output).\n";
            }

        } else {
            // proc_open failed
            echo "    Error: Failed to execute 'composer depends' for {$abandonedPackage}.\n";
            $report[$abandonedPackage]['dependents'] = ['Error checking dependents'];
            $report[$abandonedPackage]['action_needed'] .= ". Failed to check dependents";
        }
    }
}

// --- 5. Check for Updates/Replacements ---
echo "\nChecking for outdated direct dependencies and analyzing suggestions...\n";

// 5.1 Check outdated direct dependencies
$outdatedResult = run_composer_command("outdated --direct", $projectPath);
if ($outdatedResult['return_code'] !== -1) { // Check if command ran
    echo "Parsing outdated packages info...\n";
    $lines = explode("\n", trim($outdatedResult['output']));
    // Regex: Captures package name, current version, latest version from `composer outdated` output.
    // Example line: phpunit/phpunit 9.5.10 ! 9.6.17 PHPUnit is a programmer-oriented testing framework for PHP.
    $regex = '/^([\S]+)\s+([\S]+)\s+[!~=<>]*\s+([\S]+)\s+(.*)/i';

    foreach ($lines as $line) {
        $trimmedLine = trim($line);
        if (preg_match($regex, $trimmedLine, $matches)) {
            $packageName = $matches[1];
            $currentVersion = $matches[2];
            $latestVersion = $matches[3];
            echo "  Found outdated: {$packageName} (Current: {$currentVersion}, Latest: {$latestVersion})\n";

            // Update report or add new entry if not already present
            if (!isset($report[$packageName])) {
                $report[$packageName] = [
                    'status' => 'outdated',
                    'current_version' => $installedVersions[$packageName] ?? $currentVersion, // Prefer lock version if available
                    'latest_version' => $latestVersion,
                    'dependents' => [], // Dependents less critical for *direct* outdated, keeping structure
                    'suggestion' => 'N/A',
                    'suggested_replacement_package' => null,
                    'action_needed' => "Update to {$latestVersion}"
                ];
            } else {
                // If already in report (e.g., abandoned AND outdated), append status and add action
                if (strpos($report[$packageName]['status'], 'outdated') === false) {
                    $report[$packageName]['status'] .= ', outdated'; // Mark as outdated too
                }
                $report[$packageName]['current_version'] = $installedVersions[$packageName] ?? $currentVersion; // Ensure current version is accurate
                $report[$packageName]['latest_version'] = $latestVersion; // Update latest version info
                // Add update action if not already present
                if (strpos($report[$packageName]['action_needed'], "Update to {$latestVersion}") === false) {
                    $report[$packageName]['action_needed'] .= ". Update to {$latestVersion}";
                }
            }
        }
    }
} else {
    // proc_open failed
    echo "Error: Failed to execute 'composer outdated'. Cannot check for outdated packages.\n";
}

// 5.2 Analyze suggestions for abandoned packages
echo "Analyzing suggestions for abandoned packages...\n";
foreach ($report as $packageName => &$details) { // Use reference to modify report array directly
    // Check if package is marked as abandoned
    if (strpos($details['status'], 'abandoned') !== false) {
        // Regex: Looks for "use <package>" or "see <package>" in the suggestion string.
        if (preg_match('/(?:use|see)\s+([\S]+)/i', $details['suggestion'], $matches)) {
            $suggestedPackage = rtrim($matches[1], '.'); // Remove trailing dot if present
            // Basic check if it looks like a valid package name
            if (preg_match('|^[a-zA-Z0-9\_\-\./]+$|', $suggestedPackage)) {
                $details['suggested_replacement_package'] = $suggestedPackage;
                // Prepend replacement suggestion to action needed
                $details['action_needed'] = "Consider replacing with '{$suggestedPackage}'. " . ($details['action_needed'] ?? '');
                echo "  Suggestion for {$packageName}: Consider using {$suggestedPackage}\n";
            }
        } elseif ($details['suggestion'] === 'No specific suggestion.') {
            // Add note about manual search if not already present
            if (strpos($details['action_needed'], 'Manual search') === false) {
                $details['action_needed'] = "Manual search for replacement needed. " . ($details['action_needed'] ?? '');
            }
            echo "  No specific replacement suggested for {$packageName}. Manual investigation required.\n";
        }
    }
}
unset($details); // IMPORTANT: Unset reference after loop


// --- 6. Generate Report ---
// This script is useful for maintaining PHP projects, including test automation frameworks
// like PHPUnit, Codeception, Behat, etc., by identifying potential compatibility issues.
echo "\n=========================================\n";
echo "   PHP Package Compatibility Report    \n";
echo "=========================================\n";

if (empty($report)) {
    echo "\nNo issues (abandoned or outdated direct dependencies) found.\n";
} else {
    $updateCommands = [];
    $replaceCommands = [];
    $removeCommands = [];

    echo "\n--- Issues Found ---\n";

    // Iterate through the report and display details for each package
    foreach ($report as $package => $details) {
        echo "\n------------------------------------\n";
        echo "Package:          {$package}\n";
        echo "Status:           {$details['status']}\n";
        echo "Installed Version:{$details['current_version']}\n";

        // Only show latest if relevant (i.e., outdated)
        if (strpos($details['status'], 'outdated') !== false && $details['latest_version'] !== 'N/A') {
            echo "Latest Version:   {$details['latest_version']}\n";
        }

        if (!empty($details['dependents'])) {
            echo "Used By:          " . implode(', ', $details['dependents']) . "\n";
        }

        // Show Composer's original suggestion if available and not just generic
        if ($details['suggestion'] !== 'N/A' && $details['suggestion'] !== 'No specific suggestion.') {
            echo "Composer Suggestion: {$details['suggestion']}\n";
        }
        // Show the parsed potential replacement package
        if ($details['suggested_replacement_package']) {
            echo "Potential Replace: {$details['suggested_replacement_package']}\n";
        }

        echo "\nAction / Notes:   {$details['action_needed']}\n";

        // --- Collect Commands for Summary ---
        // Suggest update if outdated
        if (strpos($details['status'], 'outdated') !== false) {
            $updateCommands[$package] = "composer update {$package}"; // Use package as key to avoid duplicates
        }
        // Suggest replacement if a specific one was identified
        if ($details['suggested_replacement_package']) {
            $replaceCommands[$package] = "composer remove {$package} && composer require {$details['suggested_replacement_package']}";
        } elseif (strpos($details['status'], 'abandoned') !== false) {
            // Only suggest removal if abandoned AND no replacement was identified
            $removeCommands[$package] = "composer remove {$package}"; // Use package as key
        }
    }
    echo "\n------------------------------------\n";

    // --- Suggested Commands Summary ---
    echo "\n--- Suggested Commands ---\n";
    echo "Note: Review commands carefully before execution. Replacements often require significant code changes and testing.\n";

    if (!empty($updateCommands)) {
        echo "\n# To update outdated packages:";
        foreach ($updateCommands as $cmd) {
            echo "\n  {$cmd}";
        }
    }

    if (!empty($replaceCommands)) {
        echo "\n\n# To replace abandoned packages (REQUIRES CODE REVIEW & MIGRATION):";
        foreach ($replaceCommands as $pkg => $cmd) {
            echo "\n  # For package: {$pkg}";
            echo "\n  {$cmd}";
        }
    }

    if (!empty($removeCommands)) {
        echo "\n\n# To remove abandoned packages (REQUIRES FINDING REPLACEMENT & CODE CHANGES):";
        foreach ($removeCommands as $pkg => $cmd) {
            echo "\n  # For package: {$pkg}";
            echo "\n  {$cmd}";
        }
    }

    // Handle case where issues were found but no specific commands generated (e.g., complex cases)
    if (empty($updateCommands) && empty($replaceCommands) && empty($removeCommands)) {
        echo "\nNo specific commands generated based on analysis. Manual investigation of the issues above is required.";
    }

    echo "\n"; // Trailing newline for clarity
}

echo "\nAnalysis Complete.\n";

?>
