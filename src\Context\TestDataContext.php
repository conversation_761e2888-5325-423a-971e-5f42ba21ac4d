<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for test data management
 */
class TestDataContext extends BaseContext
{
    use TestDataContextExtension;
    private ConfigurationServiceInterface $configService;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;
    private ValidationServiceInterface $validationService;
    private array $currentTestData = [];

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param ConfigurationServiceInterface|null $configService Configuration service
     * @param TestDataServiceInterface|null $dataService Test data service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     * @param ValidationServiceInterface|null $validationService Validation service
     */
    public function __construct(
        ?ContainerInterface            $container = null,
        ?ConfigurationServiceInterface $configService = null,
        ?TestDataServiceInterface      $dataService = null,
        ?SharedStateServiceInterface   $stateService = null,
        ?ValidationServiceInterface    $validationService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->configService = $configService ?? $container->get(ConfigurationServiceInterface::class);
            $this->dataService = $dataService ?? $container->get(TestDataServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
            $this->validationService = $validationService ?? $container->get(ValidationServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->configService = $configService ?? $this->createMockConfigService();
            $this->dataService = $dataService ?? $this->createMockDataService();
            $this->stateService = $stateService ?? $this->createMockStateService();
            $this->validationService = $validationService ?? $this->createMockValidationService();
        }

        $this->logInfo("TestDataContext initialized");
    }

    /**
     * Create a mock configuration service for testing
     *
     * @return ConfigurationServiceInterface
     */
    private function createMockConfigService(): ConfigurationServiceInterface
    {
        return new class implements ConfigurationServiceInterface {
            private string $currentBrand = 'aeons';
            private string $currentEnvironment = 'stage';
            private array $config = [
                'base_url' => 'https://aeonstest.info'
            ];

            public function getBrandConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getEnvironmentConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getCurrentBrand(): string
            {
                return $this->currentBrand;
            }

            public function getCurrentEnvironment(): string
            {
                return $this->currentEnvironment;
            }

            public function setBrand(string $brand): void
            {
                $this->currentBrand = $brand;
            }

            public function setEnvironment(string $environment): void
            {
                $this->currentEnvironment = $environment;
            }

            public function getConfigValue(string $key)
            {
                return $this->config[$key] ?? null;
            }
        };
    }

    /**
     * Create a mock test data service for testing
     *
     * @return TestDataServiceInterface
     */
    private function createMockDataService(): TestDataServiceInterface
    {
        return new class implements TestDataServiceInterface {
            private array $testData = [];

            public function loadTestData(string $brand, string $type, ?string $key = null): array
            {
                return [];
            }

            public function getTestData(string $type, ?string $key = null): array
            {
                return [];
            }

            public function getRandomTestData(string $type): array
            {
                return [];
            }

            public function validateTestData(string $type, array $data): bool
            {
                return true;
            }

            public function registerData(string $key, array $data): void
            {
                $this->testData[$key] = $data;
            }

            public function getData(string $key)
            {
                return $this->testData[$key] ?? null;
            }

            public function hasData(string $key): bool
            {
                return isset($this->testData[$key]);
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * Create a mock validation service for testing
     *
     * @return ValidationServiceInterface
     */
    private function createMockValidationService(): ValidationServiceInterface
    {
        return new class implements ValidationServiceInterface {
            public function validateSchema(array $data, string $schema): void
            {
                // Do nothing in mock
            }

            public function validateProductData(array $data): void
            {
                // Do nothing in mock
            }

            public function validateUserData(array $data): void
            {
                // Do nothing in mock
            }

            public function validateShippingData(array $data): void
            {
                // Do nothing in mock
            }
        };
    }

    /**
     * @Given I load test data for product :productKey
     */
    public function iLoadTestDataForProduct(string $productKey): void
    {
        try {
            $data = $this->getProductData($productKey);
            $this->stateService->set('product.data', $data);
            $this->stateService->set('product.key', $productKey);
            $this->logInfo(sprintf('Loaded and shared test data for product: %s', $productKey));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to load test data for product: %s', $productKey), $e);
            throw $e;
        }
    }

    /**
     * Gets product data by key
     *
     * @param string $productKey Product key to load
     * @return array Product data
     * @throws \RuntimeException When product data cannot be loaded
     */
    public function getProductData(string $productKey): array
    {
        try {
            $this->logInfo(sprintf('Loading product data for key: %s', $productKey));

            $brand = $this->configService->getCurrentBrand();
            $this->logInfo(sprintf('Current brand: %s', $brand));

            $data = $this->dataService->loadTestData($brand, 'products', $productKey);
            $this->logInfo('Successfully loaded product data from registry');

            // Validate product data structure
            $this->validationService->validateProductData($data);
            $this->logInfo('Product data validation successful');

            $this->dataService->registerData('current_product', $data);
            $this->currentTestData['product'] = $data;

            return $data;
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to get product data for key: %s', $productKey), $e);
            throw new \RuntimeException(
                sprintf('Failed to get product data for key "%s": %s', $productKey, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given I load test data for user :userKey
     */
    public function iLoadTestDataForUser(string $userKey): void
    {
        try {
            $brand = $this->configService->getCurrentBrand();
            $data = $this->dataService->loadTestData($brand, 'users', $userKey);

            // Validate user data structure
            $this->validationService->validateUserData($data);

            $this->dataService->registerData('current_user', $data);
            $this->currentTestData['user'] = $data;

            // Store in shared state for other contexts
            $this->stateService->set('user.data', $data);
            $this->stateService->set('user.key', $userKey);

            $this->logInfo(sprintf('Loaded and shared test data for user: %s', $userKey));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to load test data for user: %s', $userKey), $e);
            throw $e;
        }
    }

    /**
     * @Given I load shipping data
     */
    public function iLoadShippingData(): void
    {
        try {
            $brand = $this->configService->getCurrentBrand();
            $data = $this->dataService->loadTestData($brand, 'shipping');

            // Validate shipping data structure
            $this->validationService->validateShippingData($data);

            $this->dataService->registerData('shipping', $data);
            $this->currentTestData['shipping'] = $data;

            // Store in shared state for other contexts
            $this->stateService->set('shipping.data', $data);

            $this->logInfo('Loaded and shared shipping data');
        } catch (\Throwable $e) {
            $this->logError('Failed to load shipping data', $e);
            throw $e;
        }
    }

    /**
     * @Given I load payment data for :paymentType
     */
    public function iLoadPaymentData(string $paymentType): void
    {
        try {
            $brand = $this->configService->getCurrentBrand();
            $data = $this->dataService->loadTestData($brand, 'payment_methods');

            if (!isset($data[$paymentType])) {
                throw new \RuntimeException(sprintf('Payment data for type "%s" not found', $paymentType));
            }

            $paymentData = $data[$paymentType];

            $this->dataService->registerData('current_payment', $paymentData);
            $this->currentTestData['payment'] = $paymentData;

            // Store in shared state for other contexts
            $this->stateService->set('payment.data', $paymentData);
            $this->stateService->set('payment.type', $paymentType);

            $this->logInfo(sprintf('Loaded and shared payment data for type: %s', $paymentType));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to load payment data for type: %s', $paymentType), $e);
            throw $e;
        }
    }

    /**
     * @Then the product data should be valid
     */
    public function theProductDataShouldBeValid(): void
    {
        $productData = $this->stateService->get('product.data');

        if (!$productData) {
            throw new \RuntimeException('No product data loaded');
        }

        try {
            $this->validationService->validateProductData($productData);
            $this->logInfo('Product data validation successful');
        } catch (\Throwable $e) {
            $this->logError('Product data validation failed', $e);
            throw $e;
        }
    }

    /**
     * @Given /^I load product data$/
     */
    public function iLoadProductData(): void
    {
        try {
            $productName = getenv('TEST_PRODUCT') ?? 'ancient_roots';

            try {
                $data = $this->getProductData($productName);
                $this->stateService->set('currentProduct', $data);
                $this->logInfo(sprintf('Loaded default product data for: %s', $productName));
            } catch (\Throwable $e) {
                $this->logError('Failed to load product data, using fallback data', $e);

                // Create fallback product data
                $fallbackData = [
                    'name' => 'Ancient Roots',
                    'slug' => 'ancient-roots',
                    'prices' => [
                        'one_time' => [
                            'minimum' => 29.95,
                            'medium' => 39.95,
                            'maximum' => 49.95
                        ],
                        'subscription' => [
                            'minimum' => 24.95,
                            'medium' => 34.95,
                            'maximum' => 44.95
                        ]
                    ],
                    'options' => [
                        'purchase_types' => [
                            'one_time' => 'One-Time Purchase',
                            'subscription' => 'Subscribe & Save'
                        ],
                        'quantities' => [
                            'minimum' => [
                                'fullName' => '1 Jar',
                                'numberOfItems' => 1
                            ],
                            'medium' => [
                                'fullName' => '3 Jars',
                                'numberOfItems' => 3
                            ],
                            'maximum' => [
                                'fullName' => '6 Jars',
                                'numberOfItems' => 6
                            ]
                        ]
                    ],
                    'content' => [
                        'description' => 'Ancient Roots is a powerful blend of traditional herbs and roots.',
                        'benefits' => ['Supports immune system', 'Promotes natural energy', 'Helps maintain healthy digestion']
                    ]
                ];

                $this->stateService->set('currentProduct', $fallbackData);
                $this->logInfo('Using fallback product data for browser interaction tests');
            }
        } catch (\Throwable $e) {
            $this->logError('Failed to load default product data', $e);
            throw new \RuntimeException('Failed to load default product data: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Given /^I load test data for "([^"]*)"$/
     */
    public function iLoadTestDataFor(string $dataPath): void
    {
        try {
            $brand = $this->configService->getCurrentBrand();
            $segments = explode('.', $dataPath);

            if (count($segments) < 2) {
                throw new \RuntimeException(sprintf('Invalid data path format: %s. Expected format: category.key', $dataPath));
            }

            $category = $segments[0];
            $key = $segments[1];

            $data = $this->dataService->loadTestData($brand, $category, $key);

            if (!is_array($data)) {
                throw new \RuntimeException(sprintf(
                    'Invalid test data format for key: %s. Expected array, got %s',
                    $dataPath,
                    gettype($data)
                ));
            }

            $this->stateService->set('testData', $data);
            $this->stateService->set('testData.' . $category, $data);
            $this->logInfo(sprintf('Loaded test data for: %s', $dataPath));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to load test data for: %s', $dataPath), $e);
            throw new \RuntimeException(
                sprintf('Failed to load test data for "%s": %s', $dataPath, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I load product data for "([^"]*)"$/
     */
    public function iLoadProductDataFor(string $productKey): void
    {
        try {
            $data = $this->getProductData($productKey);
            $this->stateService->set('currentProduct', $data);
            $this->logInfo(sprintf('Loaded product data for: %s', $productKey));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to load product data for: %s', $productKey), $e);

            // Create mock data as fallback (similar to legacy implementation)
            $this->logInfo('Using mock product data as fallback');
            $productData = [
                'name' => $productKey,
                'slug' => strtolower(str_replace(' ', '-', $productKey)),
                'prices' => [
                    'one_time' => [
                        'minimum' => 29.95,
                        'medium' => 39.95,
                        'maximum' => 49.95
                    ],
                    'subscription' => [
                        'minimum' => 24.95,
                        'medium' => 34.95,
                        'maximum' => 44.95
                    ]
                ],
                'options' => [
                    'purchase_types' => [
                        'one_time' => 'One-Time Purchase',
                        'subscription' => 'Subscribe & Save'
                    ],
                    'quantities' => [
                        'minimum' => [
                            'fullName' => '1 Jar',
                            'numberOfItems' => 1
                        ],
                        'medium' => [
                            'fullName' => '3 Jars',
                            'numberOfItems' => 3
                        ],
                        'maximum' => [
                            'fullName' => '6 Jars',
                            'numberOfItems' => 6
                        ]
                    ]
                ],
                'content' => [
                    'description' => 'Product description goes here',
                    'benefits' => ['Benefit 1', 'Benefit 2', 'Benefit 3']
                ]
            ];

            $this->stateService->set('currentProduct', $productData);
        }
    }
}
