# Technical Architecture Documentation

## Overview

This document provides a detailed technical overview of the service-oriented architecture implemented in the Malaberg test automation framework. It covers the key components, their interactions, and technical details that will help with debugging and understanding the code structure.

## Architecture Layers

The framework follows a layered architecture with clear separation of concerns:

1. **Service Layer**: Core services that encapsulate business logic and technical operations
2. **Context Layer**: Behat contexts that map Gherkin steps to service calls
3. **Page Object Layer**: Page objects that represent UI elements and interactions

### Service Layer

The service layer is the foundation of the architecture, providing core functionality to the other layers. Each service has a well-defined interface and implementation.

#### Service Interfaces and Implementations

| Service | Interface | Implementation | Purpose |
|---------|-----------|----------------|---------|
| Browser Service | `BrowserServiceInterface` | `BrowserService` | Handles browser interactions |
| Configuration Service | `ConfigurationServiceInterface` | `ConfigurationService` | Manages configuration settings |
| Shared State Service | `SharedStateServiceInterface` | `SharedStateService` | Manages shared state between contexts |
| Test Data Service | `TestDataServiceInterface` | `TestDataService` | Provides access to test data |
| Page Factory | `PageFactoryInterface` | `PageFactory` | Creates page objects |
| Validation Service | `ValidationServiceInterface` | `ValidationService` | Validates data |
| Cache Service | `CacheServiceInterface` | `FilesystemCacheService` | Provides caching functionality |

#### BrowserServiceInterface

The `BrowserServiceInterface` defines methods for interacting with the browser:

```php
interface BrowserServiceInterface
{
    public function getSession(): Session;
    public function visit(string $url): void;
    public function takeScreenshot(string $name = null): string;
    public function waitForElement(string $selector, int $timeout = 30): void;
    public function executeScript(string $script);
    public function findElements(string $selector): array;
    public function waitForElementVisible(string $selector, int $timeout = 30): bool;
    public function clickElement(string $selector): void;
    public function fillField(string $selector, string $value): void;
    public function selectOption(string $selector, string $value): void;
    public function getElementText(string $selector): string;
    public function isElementVisible(string $selector): bool;
    public function waitForPageToLoad(int $timeout = 30): void;
    public function waitForDocumentReady(int $timeout = 30): void;
    public function waitForAjaxToComplete(int $timeout = 30): void;
    public function elementExists(string $selector): bool;
    public function getDriverType(): string;
    public function navigateBack(): void;
    public function isSessionActive(): bool;
    public function isBrowserStackSession(): bool;
    public function getPageTitle(): string;
    public function wait(int $seconds): void;
    public function getCurrentUrl(): string;
    public function findElement(string $selector): ?NodeElement;
}
```

The `BrowserService` implementation uses the Mink session to interact with the browser:

```php
class BrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    private int $defaultTimeout = 30;

    public function __construct(
        Session $session,
        string $screenshotsDir,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->session = $session;
        $this->screenshotsDir = $screenshotsDir;
        // ...
    }

    // Implementation of interface methods...
}
```

#### SharedStateServiceInterface

The `SharedStateServiceInterface` defines methods for managing shared state:

```php
interface SharedStateServiceInterface
{
    public function set(string $key, $value, string $scope = 'scenario'): void;
    public function get(string $key, string $scope = 'scenario');
    public function has(string $key, string $scope = 'scenario'): bool;
    public function getAll(string $scope = 'scenario'): array;
    public function reset(string $scope = 'scenario'): void;
}
```

The `SharedStateService` implementation manages state in different scopes:

```php
class SharedStateService implements SharedStateServiceInterface
{
    private array $scenarioState = [];
    private array $featureState = [];
    private array $globalState = [];

    public function set(string $key, $value, string $scope = 'scenario'): void
    {
        // Implementation...
    }

    public function get(string $key, string $scope = 'scenario')
    {
        // Implementation with scope fallback...
    }

    // Implementation of other methods...
}
```

#### ConfigurationServiceInterface

The `ConfigurationServiceInterface` defines methods for accessing configuration:

```php
interface ConfigurationServiceInterface
{
    public function getBrandConfig(string $key);
    public function getEnvironmentConfig(string $key);
    public function getCurrentBrand(): string;
    public function getCurrentEnvironment(): string;
    public function setBrand(string $brand): void;
    public function setEnvironment(string $environment): void;
}
```

#### PageFactoryInterface

The `PageFactoryInterface` defines methods for creating page objects:

```php
interface PageFactoryInterface
{
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface;
    public function getPage(string $pageName, array $parameters = []): BasePageInterface;
    public function hasPage(string $pageName): bool;
}
```

The `PageFactory` implementation creates page objects using the service container:

```php
class PageFactory extends AbstractService implements PageFactoryInterface
{
    private ContainerInterface $container;
    private BrowserServiceInterface $browserService;
    private string $baseUrl;
    private array $pageNamespaces = [
        'App\\Page\\',
        'Features\\Bootstrap\\Page\\' // For backward compatibility
    ];

    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        ?string $baseUrl = null
    ) {
        $this->container = $container;
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    public function getPage(string $pageName, array $parameters = []): BasePageInterface
    {
        // Check if the page is registered as a service
        $serviceId = sprintf('app.page.%s', strtolower($pageName));
        if ($this->container->has($serviceId)) {
            return $this->container->get($serviceId);
        }

        // Try to find the page class
        $pageClass = $this->findPageClass($pageName);
        if (!$pageClass) {
            throw new RuntimeException(sprintf('Page "%s" not found', $pageName));
        }

        // Create the page object
        return $this->createPage($pageClass, $parameters);
    }

    // Implementation of other methods...
}
```

An optimized version of the page factory is also available:

```php
class OptimizedPageFactory extends AbstractService implements PageFactoryInterface
{
    private ContainerInterface $container;
    private CacheServiceInterface $cacheService;
    private array $pageInstances = [];

    // Implementation with caching...
}
```

### Context Layer

The context layer maps Gherkin steps to service calls. It consists of base context classes and specialized context classes for different domains.

#### Base Context Classes

##### ServiceAwareContext

The `ServiceAwareContext` provides access to the service container:

```php
abstract class ServiceAwareContext implements Context
{
    protected ContainerInterface $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    protected function getService(string $id): object
    {
        if (!$this->container->has($id)) {
            throw new \RuntimeException(sprintf('Service "%s" not found in container', $id));
        }

        try {
            return $this->container->get($id);
        } catch (\Exception $e) {
            throw new \RuntimeException(sprintf('Error getting service "%s": %s', $id, $e->getMessage()), 0, $e);
        }
    }

    // Logging methods...
}
```

##### BaseContext

The `BaseContext` extends `ServiceAwareContext` with helper methods for accessing specific services:

```php
abstract class BaseContext extends ServiceAwareContext implements Context
{
    protected function getConfigService(): object
    {
        return $this->getService(ConfigurationServiceInterface::class);
    }

    protected function getTestDataService(): object
    {
        return $this->getService(TestDataServiceInterface::class);
    }

    protected function getSharedStateService(): object
    {
        return $this->getService(SharedStateServiceInterface::class);
    }

    protected function getBrowserService(): object
    {
        return $this->getService(BrowserServiceInterface::class);
    }

    protected function getValidationService(): object
    {
        return $this->getService(ValidationServiceInterface::class);
    }

    protected function getPageFactory(): object
    {
        return $this->getService(PageFactoryInterface::class);
    }

    // Other helper methods...
}
```

#### Specialized Context Classes

Specialized context classes extend `BaseContext` and implement step definitions for specific domains:

```php
class ProductContext extends BaseContext
{
    private PageFactoryInterface $pageFactory;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;

    public function __construct(
        ?ContainerInterface $container = null,
        ?PageFactoryInterface $pageFactory = null,
        ?TestDataServiceInterface $dataService = null,
        ?SharedStateServiceInterface $stateService = null
    ) {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
            $this->dataService = $dataService ?? $container->get(TestDataServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
            $this->dataService = $dataService ?? $this->createMockDataService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }
    }

    // Step definitions...
}
```

### Page Object Layer

The page object layer represents UI elements and interactions. It consists of base page classes and specialized page classes for different pages.

#### Base Page Classes

##### BasePageInterface

The `BasePageInterface` defines methods that all page objects must implement:

```php
interface BasePageInterface
{
    public function open(array $urlParameters = []): void;
    public function getUrl(array $urlParameters = []): string;
    public function isOpen(): bool;
    public function waitForPageToLoad(int $timeout = 30): void;
    public function getTitle(): string;
    public function takeScreenshot(?string $name = null): string;
    public function getSubscriptionItemCount(): int;
    public function getOneTimePurchaseItemCount(): int;
    public function getSubscriptionItemFrequencies(): array;
    public function getCartItems(): array;
}
```

##### BasePage

The `BasePage` class implements `BasePageInterface` and provides common functionality:

```php
abstract class BasePage implements BasePageInterface
{
    protected BrowserServiceInterface $browserService;
    protected string $path = '/';
    protected string $baseUrl;

    public function __construct(BrowserServiceInterface $browserService, ?string $baseUrl = null)
    {
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    public function open(array $urlParameters = []): void
    {
        $url = $this->getUrl($urlParameters);
        $this->browserService->visit($url);
        $this->waitForPageToLoad();
    }

    public function getUrl(array $urlParameters = []): string
    {
        $path = $this->path;

        // Replace path parameters
        foreach ($urlParameters as $key => $value) {
            $path = str_replace(sprintf('{%s}', $key), $value, $path);
        }

        return $this->baseUrl . $path;
    }

    // Implementation of other methods...

    protected function verifyPage(): void
    {
        // To be implemented by child classes
    }

    // Helper methods for element interaction...
}
```

#### Specialized Page Classes

Specialized page classes extend `BasePage` and implement page-specific functionality:

```php
class ProductPage extends BasePage implements ProductPageInterface
{
    protected string $path = '/product/{slug}';
    private TestDataServiceInterface $dataService;
    private array $productSlugs = [
        'Total Harmony' => 'aeons-total-harmony',
        'Ancient Roots' => 'aeons-ancient-roots',
        // ...
    ];

    public function __construct(
        BrowserServiceInterface $browserService,
        TestDataServiceInterface $dataService,
        ?string $baseUrl = null
    ) {
        parent::__construct($browserService, $baseUrl);
        $this->dataService = $dataService;
    }

    public function loadWithName(string $productName, ?array $productData = null): void
    {
        // Implementation...
    }

    // Other page-specific methods...

    protected function verifyPage(): void
    {
        $this->waitForElementVisible('.product-details');
    }
}
```

## Service Container Configuration

The service container is configured using YAML files in the `config/services` directory:

### Core Services Configuration

```yaml
# config/services/core.yml
services:
  # Configuration Service
  App\Service\Configuration\ConfigurationServiceInterface:
    alias: App\Service\Configuration\ConfigurationService
    public: true

  App\Service\Configuration\ConfigurationService:
    arguments:
      $configDir: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
    public: true

  # Test Data Service
  App\Service\Data\TestDataServiceInterface:
    alias: App\Service\Data\TestDataService
    public: true

  App\Service\Data\TestDataService:
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
      $validator: '@App\Service\Validation\ValidationServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
      $logger: '@logger'
    public: true

  # Shared State Service
  App\Service\State\SharedStateServiceInterface:
    alias: App\Service\State\SharedStateService
    public: true

  App\Service\State\SharedStateService:
    public: true
    tags:
      - { name: kernel.event_subscriber }

  # Browser Service
  App\Service\Browser\BrowserServiceInterface:
    alias: App\Service\Browser\BrowserService
    public: true

  App\Service\Browser\BrowserService:
    arguments:
      $session: '@mink.session'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: true

  # Other services...
```

### Context Services Configuration

```yaml
# config/services/contexts.yml
services:
  # Base contexts
  App\Context\Base\ServiceAwareContext:
    abstract: true
    arguments:
      $container: '@service_container'

  App\Context\Base\BaseContext:
    abstract: true
    parent: App\Context\Base\ServiceAwareContext

  # Context service definitions with Behat-friendly service IDs
  behat.context.feature:
    class: App\Context\FeatureContext
    public: true
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
    tags: [ 'context.service' ]

  behat.context.brand:
    class: App\Context\BrandContext
    public: true
    arguments:
      $container: '@service_container'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
    tags: [ 'context.service' ]

  # Other context services...
```

### Page Services Configuration

```yaml
# config/services/pages.yml
services:
  # Page Factory
  App\Service\Page\PageFactoryInterface:
    alias: App\Service\Page\PageFactory
    public: true

  App\Service\Page\PageFactory:
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true

  # Page Objects
  App\Page\:
    resource: '../../src/Page/*'
    exclude: '../../src/Page/{Base,Element}/*'
    public: true
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    tags: [ 'page.service' ]

  # Page Elements
  App\Page\Element\:
    resource: '../../src/Page/Element/*'
    public: true
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
    tags: [ 'page.element' ]

  # Specific Page Objects with Additional Dependencies
  App\Page\UpsellPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]

  App\Page\ProductPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]

  # Other page objects...
```

## Optimization Configuration

```yaml
# config/services/optimization.yml
services:
  # Default configuration for all services
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Cache service configuration
  App\Service\Cache\CacheServiceInterface:
    class: App\Service\Cache\FilesystemCacheService
    arguments:
      $cacheDir: '%app.cache_dir%/app_cache'
      $defaultLifetime: 3600
    public: true

  # Browser service optimization
  browser.service.decorated:
    class: App\Service\Browser\BrowserService
    arguments:
      $session: '@mink.session'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: false

  # Lazy loading for heavy services
  App\Service\Data\TestDataService:
    lazy: true
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
      $validator: '@App\Service\Validation\ValidationServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
      $logger: '@logger'
    public: true

  App\Service\Validation\ValidationService:
    lazy: true
    arguments:
      $schemasDir: '%app.project_root%/config/schemas'
      $logger: '@logger'
    public: true

  # Performance-optimized page factory
  App\Service\Page\PageFactoryInterface:
    class: App\Factory\OptimizedPageFactory
    arguments:
      $container: '@service_container'
      $cacheService: '@App\Service\Cache\CacheServiceInterface'
      $logger: '@logger'
    public: true
```

## Debugging Tips

### Service Container Debugging

To debug the service container, you can use the following methods:

1. **List all services**:
   ```php
   $serviceIds = $this->container->getServiceIds();
   var_dump($serviceIds);
   ```

2. **Check if a service exists**:
   ```php
   $hasService = $this->container->has('App\Service\Browser\BrowserServiceInterface');
   var_dump($hasService);
   ```

3. **Get a service and inspect it**:
   ```php
   $browserService = $this->container->get('App\Service\Browser\BrowserServiceInterface');
   var_dump(get_class($browserService));
   ```

### Shared State Debugging

To debug the shared state, you can use the following methods:

1. **Dump all state in a scope**:
   ```php
   $state = $this->stateService->getAll('scenario');
   var_dump($state);
   ```

2. **Check if a key exists**:
   ```php
   $hasKey = $this->stateService->has('product.current', 'scenario');
   var_dump($hasKey);
   ```

3. **Get a specific value**:
   ```php
   $value = $this->stateService->get('product.current', 'scenario');
   var_dump($value);
   ```

### Browser Service Debugging

To debug the browser service, you can use the following methods:

1. **Take a screenshot**:
   ```php
   $screenshotPath = $this->browserService->takeScreenshot('debug');
   echo "Screenshot saved to: $screenshotPath";
   ```

2. **Get the current URL**:
   ```php
   $url = $this->browserService->getCurrentUrl();
   echo "Current URL: $url";
   ```

3. **Execute JavaScript**:
   ```php
   $result = $this->browserService->executeScript('return document.title;');
   echo "Page title: $result";
   ```

### Page Object Debugging

To debug page objects, you can use the following methods:

1. **Check if a page is open**:
   ```php
   $isOpen = $productPage->isOpen();
   var_dump($isOpen);
   ```

2. **Get the page URL**:
   ```php
   $url = $productPage->getUrl(['slug' => 'aeons-total-harmony']);
   echo "Page URL: $url";
   ```

3. **Take a screenshot of the page**:
   ```php
   $screenshotPath = $productPage->takeScreenshot('product-page');
   echo "Screenshot saved to: $screenshotPath";
   ```

## Common Error Patterns and Solutions

### Service Not Found

**Error**: `Service "App\Service\SomeService" not found in container`

**Solution**:
1. Check if the service is registered in the service container configuration
2. Check if the service interface and implementation are correctly defined
3. Check if the service is public (it needs to be public to be accessed directly)

### Page Not Found

**Error**: `Page "SomePage" not found`

**Solution**:
1. Check if the page class exists in the correct namespace
2. Check if the page class is registered in the service container
3. Check if the page factory is correctly configured

### Element Not Found

**Error**: `Element with selector ".some-selector" not found`

**Solution**:
1. Check if the selector is correct
2. Check if the element is present in the page
3. Try using `waitForElementVisible` to wait for the element to appear
4. Take a screenshot to see the current state of the page

### Shared State Key Not Found

**Error**: `Undefined index: some_key in SharedStateService.php`

**Solution**:
1. Check if the key is set before trying to get it
2. Use the `has` method to check if the key exists
3. Provide a default value when getting the key: `$this->stateService->get('some_key', 'scenario', 'default_value')`

### Browser Session Not Active

**Error**: `Session is not active`

**Solution**:
1. Check if the browser session is started
2. Check if the browser driver is running
3. Try restarting the browser session
4. Check for network issues or browser crashes

## Performance Optimization

The framework includes several performance optimizations:

1. **Lazy Loading**: Heavy services are loaded only when needed
2. **Caching**: The `CacheService` provides caching functionality for expensive operations
3. **Optimized Page Factory**: The `OptimizedPageFactory` caches page instances to avoid creating them multiple times
4. **Service Container**: The service container manages service lifecycle and dependencies

## Cache Service Architecture

### Overview

The framework implements a flexible caching system using Symfony's Cache component, providing:

- Multiple cache adapter support (Array, Filesystem, Redis)
- Configurable cache lifetimes per service type
- Logging and monitoring capabilities
- Integration with the service container

### Components

#### Core Cache Service

```php
namespace App\Service\Cache;

interface CacheServiceInterface {
    public function get(string $key): mixed;
    public function set(string $key, mixed $value, ?int $lifetime = null): void;
    public function delete(string $key): void;
    public function deletePattern(string $pattern): void;
    public function has(string $key): bool;
}
```

#### Implementation Classes

1. **SymfonyCacheService**
    - Primary implementation using Symfony Cache
    - Supports multiple cache adapters
    - Configurable through environment variables

2. **LoggingCacheDecorator**
    - Decorator pattern implementation
    - Adds logging capabilities
    - Tracks cache hits/misses
    - Monitors cache operations

### Configuration

Cache configuration is managed through:

- `/config/cache.yml` - Main configuration file
- Environment variables (see `/config/cache.env.example`)
- Service container tags and compiler passes

Example configuration:

```yaml
adapter:
  type: '%env(CACHE_ADAPTER)%'
  options:
    prefix: '%env(CACHE_PREFIX)%'
    namespace: 'behat_tests'

lifetimes:
  configuration: 3600  # 1 hour
  browser: 1800       # 30 minutes
  api: 300           # 5 minutes
  data: 600          # 10 minutes
```

### Integration Points

1. **Service Container**
    - Cache services are registered via compiler pass
    - Automatic decoration of tagged services
    - Environment-aware configuration

2. **Test Framework**
    - Caching of test configuration
    - Browser session state caching
    - API response caching
    - Test data caching

3. **Performance Optimization**
    - Configurable cache lifetimes per service
    - Pattern-based cache invalidation
    - Monitoring through logging decorator

## Conclusion

The service-oriented architecture of the Malaberg test automation framework provides a solid foundation for automated testing. By understanding the technical details of the architecture, you can effectively debug issues, extend the framework, and optimize performance.
