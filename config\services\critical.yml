parameters:
  critical_test_data_file: '/app/features/fixtures/critical_sales_funnel_data.yml'
  app.project_root: '/app'
  app.fixtures_dir: '/app/features/fixtures'

services:
  # Override the test data service for critical tests
  App\Service\Data\TestDataServiceInterface:
    class: App\Service\Data\CriticalTestDataService
    arguments:
      $criticalDataFile: '%critical_test_data_file%'
      $logger: '@logger'
    public: true

  # Make sure we have a logger service
  logger:
    class: Psr\Log\NullLogger
    public: true

  # Add the configuration service
  App\Service\Configuration\ConfigurationServiceInterface:
    class: App\Service\Configuration\ConfigurationService
    arguments:
      $configDir: '%app.project_root%/config'
      $brand: 'aeons'
      $environment: 'stage'
      $logger: '@logger'
    public: true

  # Add the validation service
  App\Service\Validation\ValidationServiceInterface:
    class: App\Service\Validation\ValidationService
    arguments:
      $logger: '@logger'
    public: true

  # Add the container service
  service_container:
    synthetic: true
    public: true