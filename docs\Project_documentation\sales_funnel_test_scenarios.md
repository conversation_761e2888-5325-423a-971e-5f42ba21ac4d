# Sales Funnel Test Scenarios

This document provides detailed information about the sales funnel test scenarios in the Malaberg test automation framework.

## Overview

The sales funnel tests verify the critical e-commerce flows from product selection through checkout, upsell, and order confirmation. These tests are essential for ensuring that customers can successfully complete purchases through the sales funnel.

## Feature Files

The sales funnel tests are defined in the following feature files:

- `features/salesFunnel.feature`: Main sales funnel scenarios
- `features/salesFunnel_variations.feature`: Variations of the sales funnel flow
- `features/salesFunnel_error_recovery.feature`: Error recovery scenarios for the sales funnel

## Key Scenarios

### Basic Funnel Flow with Successful Upsell

This is the high-priority scenario that tests the basic sales funnel flow with a successful upsell.

```gherkin
@funnel @sales-funnel-high-priority @high-priority @smoke
Scenario: Basic funnel flow with successful upsell
  Given I am on the sales funnel page "total-harmony-funnel"
  And I verify the funnel product details
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I use the same address for billing
  Then The shipping method "Domestic tracked" should be selected
  And I verify the shipping cost is "£2.95"
  When I enter "stripe_valid" payment details
  And I complete the purchase
  Then I should be redirected to the upsell page
  When I accept the upsell offer
  Then I wait for the order confirmation page to load
  And I verify the order details are correct
  And I verify the order confirmation email
  And I verify the welcome email contains account credentials
```

### Free Shipping Threshold with Different Funnel Combinations

This scenario tests the free shipping threshold with different funnel combinations.

```gherkin
@funnel @regression @shipping
Scenario Outline: Verify free shipping threshold with different funnel combinations
  Given I am on the sales funnel page "<funnel_id>"
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I use the same address for billing
  Then The shipping method "Domestic tracked" should be selected
  And I verify the shipping cost is "<initial_shipping>"
  When I enter "stripe_valid" payment details
  And I complete the purchase
  Then I should be redirected to the upsell page
  When I accept the upsell offer
  Then I wait for the order confirmation page to load
  And I verify the shipping cost is "<final_shipping>"
  And I validate both initial and upsell products are in the order

Examples:
  | funnel_id           | initial_shipping | final_shipping |
  | total-harmony-funnel | £2.95            | FREE           |
  | ancient-roots-small | £2.95            | £2.95          |
```

### Complete Funnel Purchase Using PayPal

This scenario tests the sales funnel flow with PayPal as the payment method.

```gherkin
@funnel @paypal @payment
Scenario: Complete funnel purchase using PayPal
  Given I am on the sales funnel page "total-harmony-funnel"
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I use the same address for billing
  Then The shipping method "Domestic tracked" should be selected
  When I select the "PayPal" payment method
  And I complete the purchase
  Then I should be redirected to the PayPal login page
  When I log in to PayPal with "valid" credentials
  And I confirm the PayPal payment
  Then I should be redirected to the upsell page
  When I accept the upsell offer
  Then I wait for the order confirmation page to load
  And I verify the order details are correct
  And I verify the order confirmation email
```

### Handle Expired Card in Funnel Checkout

This scenario tests the error handling for an expired card during checkout.

```gherkin
@funnel @negative @payment
Scenario: Handle expired card in funnel checkout
  Given I am on the sales funnel page "total-harmony-funnel"
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I use the same address for billing
  Then The shipping method "Domestic tracked" should be selected
  When I enter "stripe_expired" payment details
  And I complete the purchase
  Then I should see an error message indicating the card has expired
  And I should remain on the checkout page
```

### Handle Browser Back Button During Funnel Flow

This scenario tests the handling of the browser back button during the funnel flow.

```gherkin
@funnel @back-button @regression
Scenario: Handle browser back button during funnel flow
  Given I am on the sales funnel page "total-harmony-funnel"
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I complete the purchase
  Then I should be redirected to the upsell page
  When I navigate back in browser
  Then I should be redirected to the upsell page
  When I accept the upsell offer
  Then I wait for the order confirmation page to load
  And I verify the order details are correct
```

### Verify Product Combination Restrictions

This scenario tests the product combination restrictions in the sales funnel.

```gherkin
@funnel @compatibility
Scenario: Verify product combination restrictions
  Given I am on the sales funnel page "natures-gift-basic"
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I complete the purchase
  Then I should be redirected to the upsell page
  And I validate all dietary restriction warnings are displayed
  When I accept the upsell offer
  Then I wait for the order confirmation page to load
  And I verify the product instructions contain all warnings
```

### Prevent Duplicate Upsell Submissions

This scenario tests the prevention of duplicate upsell submissions.

```gherkin
@funnel @duplicate-prevention @regression
Scenario: Prevent duplicate upsell submissions
  Given I am on the sales funnel page "total-harmony-funnel"
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I complete the purchase
  Then I should be redirected to the upsell page
  When I click the accept button multiple times
  Then I wait for the order confirmation page to load
  And I verify only one upsell product is in the order
```

### Multi-Step Funnel with Multiple Upsell Pages

This scenario tests a multi-step funnel with multiple upsell pages.

```gherkin
@funnel @multi-step
Scenario: Multi-step funnel with multiple upsell pages
  Given I am on the multi-step funnel page "premium-journey"
  When I proceed to checkout
  And I fill in the shipping information with "default" user data
  And I use the same address for billing
  And I enter "stripe_valid" payment details
  And I complete the purchase
  Then I should be redirected to the first upsell page
  When I accept the first upsell offer
  Then I should be redirected to the second upsell page
  When I accept the second upsell offer
  Then I wait for the order confirmation page to load
  And I verify the order contains all three products
  And I verify the order confirmation email contains all products
```

### Force Complete a Sales Funnel Order After Frontend Upsell Page Failure

This scenario tests the recovery process when the upsell page fails to load.

```gherkin
@funnel @error-recovery @high-priority
Scenario: Force complete a sales funnel order after frontend upsell page failure
  Given I am logged into the admin panel
  And a sales funnel item with code "demo-dsv-1" exists as an initial product
  And I open the sales funnel in a new browser window
  When I fill in the checkout form with email "<EMAIL>"
  And I select the "Domestic tracked" shipping method
  And I enter "stripe_valid" payment details
  And I complete the purchase
  And the upsell page fails to load
  Then I run the sales funnel completion command
  And a new order should be created with status "Paid"
  And the order should contain only the initial product
  And I verify the order confirmation email was sent
```

## Step Definitions

The step definitions for the sales funnel scenarios are implemented in the following context classes:

### SalesFunnelContext

The `SalesFunnelContext` class implements the sales funnel-specific steps:

```php
/**
 * @Given I am on the sales funnel page :funnelId
 */
public function iAmOnTheSalesFunnelPage(string $funnelId): void
{
    try {
        // Load funnel data
        $this->loadFunnelData($funnelId);

        // Construct the funnel URL
        $baseUrl = $this->configService->getEnvironmentConfig('base_url');
        $funnelUrl = sprintf('%s/f/%s', $baseUrl, $funnelId);

        // Navigate to the funnel page
        $this->browserService->visit($funnelUrl);
        $this->browserService->waitForPageLoad();

        // Store current funnel info in state
        $funnelData = $this->stateService->get('currentFunnel');
        if (!$funnelData) {
            throw new \RuntimeException(sprintf('Funnel data not found for ID: %s', $funnelId));
        }
        
        $this->logInfo(sprintf('Navigated to sales funnel page: %s', $funnelId));
    } catch (\Throwable $e) {
        $this->logError(sprintf('Failed to navigate to sales funnel page: %s', $funnelId), $e);
        throw new \RuntimeException(
            sprintf('Failed to navigate to sales funnel page: %s', $e->getMessage()),
            0,
            $e
        );
    }
}
```

### UpsellContext

The `UpsellContext` class implements the upsell-specific steps:

```php
/**
 * @When I accept the upsell offer
 */
public function iAcceptTheUpsellOffer(): void
{
    try {
        $upsellPage = $this->pageFactory->getPage('UpsellPage');
        $upsellPage->acceptOffer();

        $this->stateService->set('upsell.accepted', true);
        $this->logInfo("Accepted upsell offer");
    } catch (\Throwable $e) {
        $this->logError("Failed to accept upsell offer", $e);
        throw new \RuntimeException(
            sprintf('Failed to accept upsell offer: %s', $e->getMessage()),
            0,
            $e
        );
    }
}
```

## Page Objects

The sales funnel tests use the following page objects:

### UpsellPage

The `UpsellPage` class represents the upsell page in the sales funnel:

```php
class UpsellPage extends BasePage
{
    protected string $path = '/upsell';

    public function acceptOffer(): void
    {
        $this->browserService->clickElement('.upsell-accept');
        $this->browserService->waitForPageToLoad();
    }

    public function declineOffer(): void
    {
        $this->browserService->clickElement('.upsell-decline');
        $this->browserService->waitForPageToLoad();
    }

    public function getUpsellMessage(): string
    {
        return $this->browserService->getElementText('.upsell-message');
    }

    protected function verifyPage(): void
    {
        $this->browserService->waitForElementVisible('.upsell-container');
    }
}
```

## Test Data

The sales funnel tests use test data defined in YAML files:

```yaml
# features/fixtures/brands/aeons/funnels.yml
total-harmony-funnel:
  entry:
    product: total_harmony
    quantity: medium
    purchase_type: one_time
  upsell:
    product: ancient_roots
    quantity: minimum
    purchase_type: one_time
```

## Conclusion

The sales funnel test scenarios provide comprehensive coverage of the sales funnel functionality, including:

- Basic funnel flow with successful upsell
- Free shipping threshold with different funnel combinations
- PayPal payment integration
- Error handling for expired cards
- Browser back button handling
- Product combination restrictions
- Duplicate submission prevention
- Multi-step funnels with multiple upsell pages
- Error recovery when the upsell page fails to load

These scenarios ensure that the sales funnel functionality works correctly and provides a smooth customer experience.
