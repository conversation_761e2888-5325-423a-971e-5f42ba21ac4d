<?php
require __DIR__ . '/vendor/autoload.php';
use Features\Bootstrap\Core\ConfigurationManager;
use Features\Bootstrap\Core\TestDataRegistry;
use Features\Bootstrap\Core\DataValidator;

// Test Configuration Manager
try {
    $confManager = new ConfigurationManager(__DIR__ . '/config', 'aeons', 'stage');
    echo "Brand: " . $confManager->getCurrentBrand() . PHP_EOL;
    echo "Environment: " . $confManager->getCurrentEnvironment() . PHP_EOL;
    echo "Base URL: " . $confManager->getEnvironmentConfig('base_url') . PHP_EOL;
    
    // Test TestDataRegistry with correct parameters
    $validator = new DataValidator();
    $dataRegistry = new TestDataRegistry($validator, __DIR__ . '/features/fixtures', $confManager);
    
    try {
        $productData = $dataRegistry->loadTestData('aeons', 'products', 'total_harmony');
        if ($productData) {
            echo "Product loaded successfully: " . $productData['name'] . PHP_EOL;
        } else {
            echo "Failed to load product data" . PHP_EOL;
        }
    } catch (Exception $e) {
        echo "Product data error: " . $e->getMessage() . PHP_EOL;
    }
} catch (Exception $e) {
    echo "Configuration error: " . $e->getMessage() . PHP_EOL;
} 