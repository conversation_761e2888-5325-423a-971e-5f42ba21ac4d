<?php

namespace App\Context\Base;

use Behat\Behat\Context\Context;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Base class for contexts that need access to services
 */
abstract class ServiceAwareContext implements Context
{
    protected ContainerInterface $container;

    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     */
    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    // The buildContainer method has been removed as we now require the container to be injected

    /**
     * Get a service from the container
     *
     * @param string $id Service ID
     * @return object Service instance
     * @throws \RuntimeException When service is not found
     */
    protected function getService(string $id): object
    {
        if (!$this->container->has($id)) {
            throw new \RuntimeException(sprintf('Service "%s" not found in container', $id));
        }

        try {
            return $this->container->get($id);
        } catch (\Exception $e) {
            throw new \RuntimeException(sprintf('Error getting service "%s": %s', $id, $e->getMessage()), 0, $e);
        }
    }

    /**
     * Log an informational message
     *
     * @param string $message Message to log
     * @param array $context Context data
     * @return void
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if ($this->container !== null && $this->container->has('logger')) {
            $this->container->get('logger')->info($message, $context);
        } else {
            error_log("[INFO] $message");
        }
    }

    /**
     * Log an error message
     *
     * @param string $message Message to log
     * @param \Throwable|null $exception Exception that occurred
     * @param array $context Context data
     * @return void
     */
    protected function logError(string $message, ?\Throwable $exception = null, array $context = []): void
    {
        if ($exception) {
            $context['exception'] = $exception;
            $context['trace'] = $exception->getTraceAsString();
        }

        if ($this->container !== null && $this->container->has('logger')) {
            $this->container->get('logger')->error($message, $context);
        } else {
            $logMessage = "[ERROR] $message";
            if ($exception) {
                $logMessage .= " - " . $exception->getMessage();
            }
            error_log($logMessage);
        }
    }
}
