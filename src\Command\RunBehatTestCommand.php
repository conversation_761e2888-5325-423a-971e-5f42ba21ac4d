<?php

namespace App\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Process\Process;

/**
 * Command to run a single Behat test with enhanced logging
 */
class RunBehatTestCommand extends Command
{
    protected static $defaultName = 'app:run-behat-test';

    protected function configure()
    {
        $this
            ->setDescription('Runs a single Behat test with enhanced debugging')
            ->setHelp('This command helps diagnose Behat test issues with detailed logging and retry mechanisms')
            ->addOption('feature', null, InputOption::VALUE_REQUIRED, 'Feature file to run')
            ->addOption('name', null, InputOption::VALUE_REQUIRED, 'Scenario name to run')
            ->addOption('retry', null, InputOption::VALUE_REQUIRED, 'Number of retries', 1)
            ->addOption('delay', null, InputOption::VALUE_REQUIRED, 'Delay between retries in seconds', 5)
            ->addOption('selenium-check', null, InputOption::VALUE_NONE, 'Run Selenium connectivity check before test');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Behat Test Runner with Enhanced Debugging');

        $feature = $input->getOption('feature');
        $scenarioName = $input->getOption('name');
        $retries = (int)$input->getOption('retry');
        $delay = (int)$input->getOption('delay');
        $seleniumCheck = $input->getOption('selenium-check');

        if (empty($feature)) {
            $io->error('You must specify a feature file with --feature option');
            return Command::FAILURE;
        }

        // Check if Selenium connectivity check is requested
        if ($seleniumCheck) {
            $io->section('Running Selenium connectivity check');

            $seleniumCommand = ['php', 'bin/console', 'app:test-selenium-connection'];
            $process = new Process($seleniumCommand);
            $process->setTimeout(30);

            $io->writeln('Executing: ' . implode(' ', $seleniumCommand));
            $process->run(function ($type, $buffer) use ($io) {
                if (Process::ERR === $type) {
                    $io->error($buffer);
                } else {
                    $io->writeln($buffer);
                }
            });

            if (!$process->isSuccessful()) {
                $io->error('Selenium connectivity check failed. Fix connectivity issues before running tests.');
                return Command::FAILURE;
            }

            $io->success('Selenium connectivity check passed');

            // Add a small delay after Selenium check
            $io->writeln("Waiting 3 seconds before starting tests...");
            sleep(3);
        }

        // Build the Behat command
        $behatArgs = [
            './vendor/bin/behat',
            $feature,
            '--verbose',
        ];

        if (!empty($scenarioName)) {
            $behatArgs[] = '--name=' . escapeshellarg($scenarioName);
        }

        // Always format output for better debugging
        $behatArgs[] = '--format=pretty';

        // Set environment variables to increase debug output
        $env = [
            'BEHAT_DEBUG' => '1',
            'MINK_DEBUG' => '1',
            'SCREENSHOTS_ON_FAILURE' => '1',
            'PATH' => getenv('PATH'),
        ];

        $io->section('Running Behat test with enhanced debugging');
        $io->writeln('Command: ' . implode(' ', $behatArgs));

        $attempt = 1;
        $success = false;

        while ($attempt <= $retries && !$success) {
            $io->writeln(sprintf('Attempt %d of %d', $attempt, $retries));

            // Include timestamp in output
            $io->writeln('Start time: ' . date('Y-m-d H:i:s'));

            $process = new Process($behatArgs);
            $process->setTimeout(300); // 5 minutes
            $process->setEnv($env);

            $process->run(function ($type, $buffer) use ($io) {
                if (Process::ERR === $type) {
                    $io->error($buffer);
                } else {
                    $io->writeln($buffer);
                }
            });

            // Add timestamp after completion
            $io->writeln('End time: ' . date('Y-m-d H:i:s'));

            if ($process->isSuccessful()) {
                $io->success('Behat test completed successfully');
                $success = true;
            } else {
                $io->error('Behat test failed');

                // If we have more attempts, wait before retrying
                if ($attempt < $retries) {
                    $io->writeln(sprintf('Waiting %d seconds before retry...', $delay));
                    sleep($delay);
                }
            }

            $attempt++;
        }

        return $success ? Command::SUCCESS : Command::FAILURE;
    }
} 