<?php

namespace App\Page;

use App\Page\Base\BasePage;
use App\Page\Base\PaymentPageInterface;

/**
 * Stripe3DSPage handles actions on the Stripe 3D Secure authentication page.
 */
class Stripe3DSPage extends BasePage implements PaymentPageInterface
{
    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'COMPLETE_BUTTON' => 'button[data-testid="hosted-3ds2-complete"]',
        'FAIL_BUTTON' => 'button[data-testid="hosted-3ds2-fail"]',
        '3DS_IFRAME' => 'iframe[name*="privateStripeFrame"]'
    ];
    /**
     * The path of the Stripe 3DS page.
     *
     * @var string
     */
    protected string $path = '/stripe/3ds';

    /**
     * Complete the 3DS authentication
     *
     * @return void
     */
    public function complete3DSAuthentication(): void
    {
        $this->switchToStripeFrame();
        $this->waitForElementVisible(self::SELECTORS['COMPLETE_BUTTON']);
        $this->clickElement(self::SELECTORS['COMPLETE_BUTTON']);
        $this->switchToDefaultContent();
        $this->waitForPageToLoad();
    }

    /**
     * Switch to the Stripe iframe
     *
     * @return void
     */
    private function switchToStripeFrame(): void
    {
        $iframe = $this->browserService->findElement(self::SELECTORS['3DS_IFRAME']);
        $this->browserService->getSession()->switchToIFrame($iframe->getAttribute('name'));
    }

    /**
     * Switch back to the default content
     *
     * @return void
     */
    private function switchToDefaultContent(): void
    {
        $this->browserService->getSession()->switchToIFrame(null);
    }

    /**
     * Fail the 3DS authentication
     *
     * @return void
     */
    public function fail3DSAuthentication(): void
    {
        $this->switchToStripeFrame();
        $this->waitForElementVisible(self::SELECTORS['FAIL_BUTTON']);
        $this->clickElement(self::SELECTORS['FAIL_BUTTON']);
        $this->switchToDefaultContent();
        $this->waitForPageToLoad();
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible(self::SELECTORS['3DS_IFRAME']);
    }

    /**
     * Handle 3D Secure authentication
     *
     * @return void
     */
    public function handle3DSecureAuthentication()
    {
        $this->complete3DSAuthentication();
    }

    /**
     * Complete the PayPal checkout process
     * This method is required by the PaymentPageInterface but not applicable for this page
     *
     * @return void
     */
    public function completePayPalCheckout()
    {
        throw new \RuntimeException('PayPal checkout is not applicable for Stripe 3DS page');
    }

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // Stripe 3DS page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // Stripe 3DS page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // Stripe 3DS page doesn't have subscription items, so return empty array
        return [];
    }

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array
    {
        // Stripe 3DS page doesn't have cart items, so return empty array
        return [];
    }
}
