# Comprehensive Notion QA Management System Guide for AI Agents

## Table of Contents
1. [System Overview](#system-overview)
2. [Database Structure and Reference IDs](#database-structure-and-reference-ids)
3. [Test Case Management Workflows](#test-case-management-workflows)
4. [Automation Framework Integration](#automation-framework-integration)
5. [Multi-Brand Testing Considerations](#multi-brand-testing-considerations)
6. [Dashboard and Reporting](#dashboard-and-reporting)
7. [API Integration and Functions](#api-integration-and-functions)
8. [Best Practices for AI Agents](#best-practices-for-ai-agents)
9. [Recent Updates and Current Status](#recent-updates-and-current-status)
10. [Appendix: Example API Calls](#appendix-example-api-calls)

## System Overview

The Notion QA Management System is a comprehensive solution for managing QA activities across multiple brands and automation frameworks for a Sylius-based e-commerce platform. The system provides centralized test case management, test coverage tracking, and automation status reporting.

### Core Components

1. **Test Case Database**: Central repository for all test cases
2. **Test Coverage Database**: Tracks test coverage by feature
3. **Test Plan Database**: Used for planning and tracking test activities
4. **Dashboards**: Provide visibility into test automation status
5. **Documentation Pages**: Contain detailed information about testing approaches

### Brands Covered

The system supports five distinct brands, each with unique configurations:

| Brand Name | Short Code | Category | Unique Features |
|------------|------------|----------|-----------------|
| Aeons Labs | aeons | Premium Health Supplements | Subscription Plans, Unique Packaging |
| Dr Sister Skincare | dss | Luxury Skincare Products | Personalized Recommendations |
| Origins Diet | odd | Holistic Nutrition Products | Customizable Meal Plans |
| Your Pet Nutrition | ypn | Pet Health & Wellness | Multi-Pet Subscriptions |
| Apex Laboratories | apex | Advanced Supplement Research | Wholesale & Retail Pricing |

### Critical Workflows

The system focuses on five critical workflows:

1. **Purchase Flow**: Standard one-time purchase journey
2. **Checkout Flow**: Payment processing and order completion
3. **Subscription Flow**: Subscription setup and recurring orders
4. **Abandoned Cart Flow**: Recovery of abandoned shopping carts
5. **Sales Funnel Flow**: Guided purchase path with upsells

## Database Structure and Reference IDs

### Test Case Database

**Database ID**: `1e5a8ba3-b0b5-81a4-bfa2-e3b89d6b21d6`

#### Properties

| Property Name | Property Type | Description |
|---------------|--------------|-------------|
| ID | Title | Unique identifier for the test case (format: TC-XXX Name) |
| Description | Rich Text | Brief description of what the test verifies |
| Preconditions | Rich Text | Required setup before test execution |
| Test Steps | Rich Text | Step-by-step instructions for test execution |
| Expected Results | Rich Text | Expected outcome of the test |
| Priority | Select | Critical, High, Medium, Low |
| Status | Select | Draft, Ready, In Progress, Passed, Failed, Blocked |
| Brand Applicability | Multi-select | Aeons, DSS, ODD, YPN, APEX |
| Workflow | Select | Purchase, Abandoned Cart, Sales Funnel, Checkout, Subscription |
| Automation Status | Select | Not Automated, Behat Automated, Playwright Automated, Both |
| Automation Link | URL | Link to the automation code in repository |
| Tags | Multi-select | Various tags like Regression, Smoke, etc. |
| Last Modified | Date | Date of last modification |
| Last Executed | Date | Date when the test was last executed |
| Created By | Person | Person who created the test case |

### Test Coverage Database

**Database ID**: `1e5a8ba3-b0b5-8142-b3f6-c99490db369d`

#### Properties

| Property Name | Property Type | Description |
|---------------|--------------|-------------|
| Feature/Module | Title | Name of the feature or module |
| Description | Rich Text | Brief description of the feature |
| Critical Workflow | Select | Purchase, Abandoned Cart, Sales Funnel, Checkout, Subscription |
| Risk Level | Select | Critical, High, Medium, Low |
| Business Impact | Select | High, Medium, Low |
| Brand Applicability | Multi-select | Aeons, DSS, ODD, YPN, APEX |
| Notes | Rich Text | Additional information about coverage |
| Related Test Cases | Relation | Link to test cases in Test Case Database |
| Next Automation Priority | Select | High, Medium, Low |

### Test Plan Database

**Database ID**: `1e5a8ba3-b0b5-8159-9c20-fc5d1a9a1284`

#### Properties

| Property Name | Property Type | Description |
|---------------|--------------|-------------|
| Sprint/Release | Title | Name of the sprint or release |
| Scope | Rich Text | Features/functionality to be tested |
| Out of Scope | Rich Text | Features explicitly excluded |
| Test Strategy | Rich Text | Approach to testing |
| Resource Requirements | Rich Text | People, environments, tools needed |
| Schedule | Date | Timeline for test activities |
| Risk Assessment | Rich Text | Potential risks and mitigations |
| Entry Criteria | Rich Text | Conditions required to start testing |
| Exit Criteria | Rich Text | Conditions required to complete testing |
| Test Cases | Relation | Link to test cases included |
| Test Results | Rich Text | Summary of test results |
| Approvals | Person | Required sign-offs |
| Status | Select | Planning, In Progress, Completed, Archived |

### Dashboard Pages

| Dashboard | Page ID | Purpose |
|-----------|---------|---------|
| Automation Coverage Dashboard | 1e6a8ba3-b0b5-8191-bee7-cc2fc78f5ec6 | Provides overview of automation coverage |
| Detailed Coverage Analysis | 1e6a8ba3-b0b5-8122-9b32-f10979535c5f | Detailed coverage analysis by feature |
| Main QA Management Page | 1e5a8ba3-b0b5-80e4-b7e2-cb6cce63c8d0 | Landing page for the QA system |

## Test Case Management Workflows

### Creating New Test Cases

When creating a new test case, follow these steps:

1. **Check for duplicates**: Query the Test Case Database to check if a similar test case exists
2. **Use proper ID format**: TC-XXX [Short descriptive title]
3. **Provide comprehensive details**:
   - Clear description of what the test verifies
   - Step-by-step test steps with expected results
   - Preconditions for the test
   - Set appropriate metadata (workflow, brand applicability, priority, tags)
4. **Set automation status**: Not Automated, Behat Automated, Playwright Automated, or Both
5. **Add automation link**: If automated, provide the link to the automation code

### Updating Existing Test Cases

When updating a test case, follow these steps:

1. **Query the test case**: Find the test case in the database using its ID
2. **Update relevant fields**: Only update the fields that need to be changed
3. **Preserve existing information**: Be careful not to overwrite important information
4. **Update metadata**: Update dates, automation status, and links as needed
5. **Update relations**: If needed, update relations to test coverage or test plans

### Adding Automation Status

When updating automation status, follow these steps:

1. **Query the test case**: Find the test case in the database
2. **Update automation status**: Set to Behat Automated, Playwright Automated, or Both
3. **Add automation link**: Provide the link to the automation code
   - For "Both" status, include both Behat and Playwright links separated by a comma
4. **Update dates**: Set Last Modified and Last Executed dates
5. **Update test coverage**: Update coverage metrics in the Test Coverage Database if needed

## Automation Framework Integration

The QA Management System integrates with two automation frameworks:

1. **Behat Framework**: PHP-based BDD framework for functional testing
   - Location: `C:\development\Malaberg\Projects\MalabergTest\features`
   - Feature files have `.feature` extension
   - Used for E2E functional testing

2. **Playwright Framework**: JavaScript-based framework for browser automation
   - Location: `C:\development\Malaberg\Projects\browserstack-playwright\tests`
   - Test files have `.spec.js` extension
   - Used for cross-browser testing with BrowserStack integration

### Behat Automation Structure

The Behat automation project contains the following key feature files:

| Feature File | Description | Related Test Cases |
|--------------|-------------|-------------------|
| purchase.feature | Tests for product purchase flow | TC-001 to TC-011 |
| salesFunnel.feature | Tests for sales funnel flow | TC-012 to TC-019 |
| abandoned_cart.feature | Tests for abandoned cart flow | TC-025 to TC-029 |
| subscription_reorder.feature | Tests for subscription reordering | TC-034, TC-035 |
| mixedCart.feature | Tests for mixed cart purchasing | TC-036 |

### Playwright Automation Structure

The Playwright automation project is organized by brand and test type:

```
C:\development\Malaberg\Projects\browserstack-playwright\tests\
├── regression\
│   ├── aeons\
│   │   ├── smoke\
│   │   │   └── main-purchase.spec.js
│   │   └── ...
│   ├── dss\
│   │   ├── abandoned-cart-email.spec.js
│   │   ├── one-time-purchase-paypal.spec.js
│   │   ├── sales-funnel-upsell.spec.js
│   │   ├── subscription-purchase-creditcard.spec.js
│   │   ├── subscription-renewal.spec.js
│   │   └── ...
│   └── ...
└── ...
```

## Multi-Brand Testing Considerations

When working with the QA Management System, remember that it supports multiple brands with different configurations:

### Brand-Specific Configurations

Each brand has unique settings, URLs, and payment options. Test cases need to be executed separately for each applicable brand.

### Brand Applicability

Not all test cases apply to all brands. Use the Brand Applicability field to indicate which brands a test applies to.

### Brand-Specific Test Data

Different brands have different product catalogs, pricing models, and checkout options. Test cases should reference brand-specific test data where applicable.

## Dashboard and Reporting

### Automation Coverage Dashboard

The Automation Coverage Dashboard provides metrics on automation coverage across the system:

- **Overall Automation Statistics**
  - Total test cases: 45
  - Automated test cases: 35 (Behat: 25, Playwright: 6, Both: 4)
  - Not automated test cases: 10
  - Overall automation coverage: 78%

- **Automation Coverage by Workflow**
  - Purchase: 92%
  - Sales Funnel: 90%
  - Checkout: 38%
  - Subscription: 71%
  - Abandoned Cart: 86%

- **Automation Coverage by Brand**
  - Coverage metrics for each brand (Aeons, DSS, ODD, YPN, APEX)

- **Automation Gaps & Priorities**
  - High priority gaps
  - Medium priority gaps

### Detailed Coverage Analysis

The Detailed Coverage Analysis page provides in-depth analysis of test coverage for each feature:

- **Coverage by Feature**
  - Fully automated areas
  - Partially automated areas
  - Not automated areas
  - Test file references
  - Feature stability

## API Integration and Functions

The following Notion API functions are available for interacting with the QA Management System:

### User Management Functions

- `API-get-user`, `API-get-users`, `API-get-self`: User management functions

### Database Query Functions

- `API-post-database-query`: Query databases for test cases and coverage
- `API-post-search`: Search for test cases or features

### Content Management Functions

- `API-get-block-children`, `API-patch-block-children`: Modify dashboard content
- `API-retrieve-a-page`, `API-patch-page`: Update test cases
- `API-post-page`: Create new test cases or coverage entries
- `API-create-a-database`, `API-update-a-database`: Modify database structure
- `API-retrieve-a-database`: Get database properties

## Best Practices for AI Agents

When working with the Notion QA Management System, AI agents should follow these best practices:

1. **Query before creating**: Always check for existing test cases before creating new ones
2. **Maintain consistent formatting**: Follow established formatting for test steps and expected results
3. **Use proper relations**: Properly link test cases to coverage entries
4. **Update dashboards**: Update dashboard metrics when making significant changes
5. **Follow naming conventions**: Use the standardized test case ID format
6. **Provide complete information**: Ensure all required fields are filled for new test cases
7. **Handle automation status correctly**: Use the correct automation status and provide proper links
8. **Document API calls**: Document any API calls made to the system for traceability

## Recent Updates and Current Status

### Recent Updates

The system has recently been updated with the following changes:

1. **Updated TC-013 (Complete Funnel Purchase Using PayPal)**: 
   - Changed automation status from "Behat Automated" to "Both"
   - Added Playwright test file link while preserving Behat link

2. **Created new test cases**:
   - TC-037: Subscription Purchase with Credit Card (Playwright Automated)
   - TC-038: Abandoned Cart Email Notification (Playwright Automated)
   - TC-039: One-Time Purchase with Standard Payment (Playwright Automated)

3. **Updated dashboard metrics**:
   - Total test cases: 45
   - Total automated: 35 (Behat: 25, Playwright: 6, Both: 4)
   - Not automated: 10
   - Overall automation coverage: 78%

### Current Automation Focus Areas

Based on the current automation coverage, the following areas need attention:

1. **Checkout Workflow** (38% coverage):
   - Payment method validation
   - Address validation
   - Shipping method selection

2. **Subscription Workflow** (71% coverage):
   - Subscription cancellation
   - Subscription modification

## Appendix: Example API Calls

### Querying Test Cases by Workflow

```json
{
  "filter": {
    "property": "Workflow", 
    "select": {"equals": "Purchase"}
  },
  "database_id": "1e5a8ba3-b0b5-81a4-bfa2-e3b89d6b21d6"
}
```

### Querying Test Cases by Automation Status

```json
{
  "filter": {
    "property": "Automation Status", 
    "select": {"equals": "Not Automated"}
  },
  "database_id": "1e5a8ba3-b0b5-81a4-bfa2-e3b89d6b21d6"
}
```

### Creating a New Test Case

```json
{
  "parent": {"database_id": "1e5a8ba3-b0b5-81a4-bfa2-e3b89d6b21d6"},
  "properties": {
    "ID": {"title": [{"text": {"content": "TC-040 Example Test Case"}}]},
    "Description": {"rich_text": [{"text": {"content": "Example test case description"}}]},
    "Test Steps": {"rich_text": [{"text": {"content": "1. Step one\n2. Step two\n3. Step three"}}]},
    "Expected Results": {"rich_text": [{"text": {"content": "- Expected result one\n- Expected result two"}}]},
    "Priority": {"select": {"name": "High"}},
    "Status": {"select": {"name": "Ready"}},
    "Workflow": {"select": {"name": "Purchase"}},
    "Brand Applicability": {"multi_select": [{"name": "Aeons"}, {"name": "DSS"}]},
    "Automation Status": {"select": {"name": "Not Automated"}},
    "Tags": {"multi_select": [{"name": "End-to-End"}, {"name": "Purchase"}]}
  }
}
```

### Updating Automation Status

```json
{
  "page_id": "page_id_here",
  "properties": {
    "Automation Status": {"select": {"name": "Playwright Automated"}},
    "Automation Link": {"url": "C:\\path\\to\\test\\file.spec.js"},
    "Last Modified": {"date": {"start": "2025-05-01"}},
    "Last Executed": {"date": {"start": "2025-05-01"}}
  }
}
```
