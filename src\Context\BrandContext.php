<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for brand-specific functionality
 */
class BrandContext extends BaseContext
{
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param ConfigurationServiceInterface|null $configService Configuration service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface            $container = null,
        ?ConfigurationServiceInterface $configService = null,
        ?SharedStateServiceInterface   $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->configService = $configService ?? $container->get(ConfigurationServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->configService = $configService ?? $this->createMockConfigService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $this->logInfo("BrandContext initialized");
    }

    /**
     * Create a mock configuration service for testing
     *
     * @return ConfigurationServiceInterface
     */
    private function createMockConfigService(): ConfigurationServiceInterface
    {
        return new class implements ConfigurationServiceInterface {
            private string $currentBrand = 'aeons';
            private string $currentEnvironment = 'stage';
            private array $config = [
                'name' => 'Aeons',
                'code' => 'aeons',
                'url' => 'https://aeonstest.info',
                'base_url' => 'https://aeonstest.info'
            ];

            public function getBrandConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getEnvironmentConfig(string $key)
            {
                // Special handling for base_url to match the actual service implementation
                if ($key === 'base_url') {
                    return $this->config['url'] ?? null;
                }
                return $this->config[$key] ?? null;
            }

            public function getCurrentBrand(): string
            {
                return $this->currentBrand;
            }

            public function getCurrentEnvironment(): string
            {
                return $this->currentEnvironment;
            }

            public function setBrand(string $brand): void
            {
                $this->currentBrand = $brand;
            }

            public function setEnvironment(string $environment): void
            {
                $this->currentEnvironment = $environment;
            }

            // getConfigValue method removed as it's not in the interface
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @Given I am using the :brand brand
     */
    public function iAmUsingTheBrand(string $brand): void
    {
        try {
            $this->configService->setBrand($brand);
            $this->stateService->set('brand.current', $brand);
            $this->logInfo("Set current brand to: $brand");
        } catch (\Throwable $e) {
            $this->logError("Failed to set brand: $brand", $e);
            throw $e;
        }
    }

    /**
     * @Given I am using the :environment environment
     */
    public function iAmUsingTheEnvironment(string $environment): void
    {
        try {
            $this->configService->setEnvironment($environment);
            $this->stateService->set('environment.current', $environment);
            $this->logInfo("Set current environment to: $environment");
        } catch (\Throwable $e) {
            $this->logError("Failed to set environment: $environment", $e);
            throw $e;
        }
    }

    /**
     * @Then the brand should be :brand
     */
    public function theBrandShouldBe(string $brand): void
    {
        $currentBrand = $this->configService->getCurrentBrand();

        if ($currentBrand !== $brand) {
            throw new \RuntimeException(
                sprintf('Expected brand to be "%s", but got "%s"', $brand, $currentBrand)
            );
        }

        $this->logInfo("Verified current brand is: $brand");
    }

    /**
     * @Then the environment should be :environment
     */
    public function theEnvironmentShouldBe(string $environment): void
    {
        $currentEnvironment = $this->configService->getCurrentEnvironment();

        if ($currentEnvironment !== $environment) {
            throw new \RuntimeException(
                sprintf('Expected environment to be "%s", but got "%s"', $environment, $currentEnvironment)
            );
        }

        $this->logInfo("Verified current environment is: $environment");
    }

    /**
     * @Given I am using the default brand and environment
     */
    public function iAmUsingTheDefaultBrandAndEnvironment(): void
    {
        try {
            $defaultBrand = getenv('TEST_BRAND') ?: 'aeons';
            $defaultEnvironment = getenv('TEST_ENV') ?: 'stage';

            $this->configService->setBrand($defaultBrand);
            $this->configService->setEnvironment($defaultEnvironment);

            $this->stateService->set('brand.current', $defaultBrand);
            $this->stateService->set('environment.current', $defaultEnvironment);

            $this->logInfo("Set default brand to: $defaultBrand and environment to: $defaultEnvironment");
        } catch (\Throwable $e) {
            $this->logError("Failed to set default brand and environment", $e);
            throw $e;
        }
    }

    /**
     * @Given /^I load brand configuration$/
     */
    public function iLoadBrandConfiguration(): void
    {
        try {
            $brand = getenv('TEST_BRAND') ?? 'aeons';
            $environment = getenv('TEST_ENV') ?? 'stage';

            $this->configService->setBrand($brand);
            $this->configService->setEnvironment($environment);

            $this->stateService->set('brand.current', $brand);
            $this->stateService->set('environment.current', $environment);

            // Set base URL in shared state for other contexts to use
            $baseUrl = $this->configService->getEnvironmentConfig('base_url');
            $this->stateService->set('base_url', $baseUrl);

            $this->logInfo("Loaded brand configuration: brand=$brand, environment=$environment, baseUrl=$baseUrl");
        } catch (\Throwable $e) {
            $this->logError("Failed to load brand configuration", $e);
            throw new \RuntimeException('Failed to load brand configuration: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Returns the base URL for the current brand and environment
     *
     * @return string The base URL
     * @throws \RuntimeException If base URL cannot be determined
     */
    public function getBaseUrl(): string
    {
        try {
            return $this->configService->getEnvironmentConfig('base_url');
        } catch (\Throwable $e) {
            $this->logError("Failed to get base URL", $e);
            throw new \RuntimeException('Failed to get base URL: ' . $e->getMessage(), 0, $e);
        }
    }
}
