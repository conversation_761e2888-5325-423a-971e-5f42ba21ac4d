# Dependency Injection Guide

This document provides guidelines for using dependency injection in the Malaberg Test Framework.

## Principles

1. **Depend on interfaces, not implementations**: Always type-hint against interfaces rather than concrete classes
2. **Constructor injection**: Use constructor injection for required dependencies
3. **Explicit dependencies**: Make all dependencies explicit in the constructor
4. **No service locator**: Avoid retrieving services from the container directly
5. **No global state**: Avoid using global variables or static methods

## Service Definition

Services should be defined in the appropriate YAML file under `config/services/`:

```yaml
services:
  # Interface alias
  App\Service\MyFeature\MyServiceInterface:
    alias: App\Service\MyFeature\MyService
    public: true

  # Concrete implementation
  App\Service\MyFeature\MyService:
    arguments:
      $dependency1: '@App\Service\OtherFeature\OtherServiceInterface'
      $dependency2: '@App\Service\AnotherFeature\AnotherServiceInterface'
      $parameter: '%app.some_parameter%'
    public: true
```

## Service Implementation

Services should follow these principles:

1. Implement an interface
2. Accept dependencies through constructor injection
3. Be stateless when possible
4. Have a single responsibility

Example:

```php
<?php

namespace App\Service\MyFeature;

use App\Service\OtherFeature\OtherServiceInterface;
use App\Service\AnotherFeature\AnotherServiceInterface;
use Psr\Log\LoggerInterface;

class MyService implements MyServiceInterface
{
    private OtherServiceInterface $otherService;
    private AnotherServiceInterface $anotherService;
    private string $parameter;
    private ?LoggerInterface $logger;

    public function __construct(
        OtherServiceInterface $otherService,
        AnotherServiceInterface $anotherService,
        string $parameter,
        ?LoggerInterface $logger = null
    ) {
        $this->otherService = $otherService;
        $this->anotherService = $anotherService;
        $this->parameter = $parameter;
        $this->logger = $logger;
    }

    // Implementation methods...
}
```

## Context Dependency Injection

Behat contexts should follow the same principles as services:

1. Accept dependencies through constructor injection
2. Type-hint against interfaces
3. Make all dependencies explicit

Example:

```php
<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class MyContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private ConfigurationServiceInterface $configService;

    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        ConfigurationServiceInterface $configService
    ) {
        parent::__construct($container);

        $this->browserService = $browserService;
        $this->configService = $configService;
    }

    // Step definitions and other methods...
}
```

## Service Tags

Services can be tagged to be used by collectors or for other purposes:

```yaml
services:
  App\Service\MyFeature\MyService:
    arguments:
      # ...
    tags:
      - { name: 'app.my_feature.processor' }
```

Common tags in the framework:

- `context.service`: Behat contexts
- `app.page.factory`: Page object factories
- `app.browser.driver`: Browser drivers
- `app.validator`: Validators

## Parameters

Configuration parameters should be defined in `config/parameters.yml` or in the appropriate service configuration file:

```yaml
parameters:
  app.my_feature.enabled: true
  app.my_feature.timeout: 30
  app.my_feature.base_url: 'https://example.com'
```

Parameters can be injected into services:

```yaml
services:
  App\Service\MyFeature\MyService:
    arguments:
      $enabled: '%app.my_feature.enabled%'
      $timeout: '%app.my_feature.timeout%'
      $baseUrl: '%app.my_feature.base_url%'
```

## Best Practices

1. **Keep services focused**: Each service should have a single responsibility
2. **Use interfaces**: Define interfaces for all services
3. **Minimize dependencies**: Keep the number of dependencies to a minimum
4. **Document dependencies**: Use PHPDoc to document dependencies
5. **Use nullable dependencies**: Make optional dependencies nullable
6. **Use typed properties**: Use PHP 7.4+ typed properties for dependencies
7. **Use return type hints**: Use return type hints for all methods

## Troubleshooting

If a service cannot be instantiated, check:

1. That all dependencies are properly defined in the service container
2. That the constructor parameter names match the service definition
3. That there are no circular dependencies
4. That all required parameters are defined

Use `bin/verify-container.php` to verify that services can be instantiated.
