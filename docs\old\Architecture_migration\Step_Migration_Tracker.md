# Behat Step Definition Migration Tracker

## Overview

This document tracks the migration of step definitions from the legacy `features/bootstrap/FeatureContext.php` to the
appropriate context files in the new `src/Context/` directory structure. The migration follows the service-oriented
architecture principles outlined in the architecture documentation.

## Task Breakdown

### 1. Setup and Analysis

- [x] Create Step Migration Tracker document
- [x] Analyze legacy FeatureContext.php and identify all step definitions
- [ ] Review current documentation for migration status
- [ ] Create matrix of step definitions and their target contexts

### 2. Step Definition Inventory

- [x] Extract all @Given, @When, @Then annotations from legacy FeatureContext.php
- [x] Categorize steps by domain (payment, checkout, product, browser, etc.)
- [ ] Identify helper methods that need migration to services
- [ ] Identify infrastructure code that can be eliminated
- [ ] Document placeholders and pending step implementations

### 3. Migration Status Verification

- [ ] Verify steps already migrated to src/Context/FeatureContext.php
- [ ] Verify steps migrated to domain-specific contexts
- [ ] Identify missing step definitions
- [ ] Identify partially migrated steps
- [ ] Identify misplaced steps (in incorrect context files)

### 4. Implementation of Missing Steps

- [x] Implement missing PaymentContext steps
    - [x] Implement `iSelectThePaymentMethod()`
    - [x] Implement `iLogInToPayPalWithCredentials()`
    - [x] Implement `iChooseToPayWithPayPal()`
    - [x] Implement `iAmRedirectedToThePayPalSandboxPage()`
    - [x] Implement `iShouldSeeTheCorrectPaymentAmountInPayPal()`
    - [x] Implement `iConfirmThePayPalPayment()`
    - [x] Implement `thePayPalPaymentShouldBeSuccessful()`
    - [x] Implement `iShouldSeeAPayPalLoginErrorMessage()`
    - [x] Implement `iShouldSeeThe3DSPage()`
    - [x] Implement `iPressTheButton()`
    - [x] Implement `iEnterPaymentDetails()`
    - [x] Implement `iShouldSeeThePayPalLoginError()`
    - [x] Implement `iLogInToPayPalSandboxWithCredentials()`
- [ ] Implement missing CheckoutContext steps
- [ ] Implement missing ProductContext steps
- [ ] Implement missing BrowserContext steps
- [ ] Implement missing ValidationContext steps
- [ ] Implement missing steps in other contexts

### 5. Refinement of Partial Migrations

- [ ] Refine partially migrated TestDataContext steps
- [ ] Refine partially migrated BrandContext steps
- [ ] Refine partially migrated ValidationContext steps
- [ ] Refine partially migrated steps in other contexts

### 6. Relocation of Misplaced Steps

- [ ] Move payment steps to PaymentContext (if found elsewhere)
- [ ] Move checkout steps to CheckoutContext (if found elsewhere)
- [ ] Move product steps to ProductContext (if found elsewhere)
- [ ] Move other misplaced steps to their appropriate contexts

### 7. Helper Method Migration

- [ ] Create/update services to replace helper methods
- [ ] Migrate browser interaction helpers to BrowserService
- [ ] Migrate data handling helpers to appropriate services
- [ ] Remove redundant helper methods

### 8. Documentation Updates

- [ ] Update steps_definition_registry.md
- [ ] Update Traceability_Matrix.md
- [ ] Document new or refactored services
- [ ] Document any deprecated steps

### 9. Quality Assurance

- [ ] Run code style checks
- [ ] Run static analysis
- [ ] Execute Behat tests using migrated steps
- [ ] Fix any found issues

### 10. Final Review

- [ ] Perform final comparison between legacy and new implementation
- [ ] Verify all steps are correctly implemented
- [ ] Ensure documentation is complete and accurate
- [ ] Create final migration report

## Migration Progress

### Current Progress

- Steps Identified: 36 (Legacy FeatureContext step definitions)
- Steps Fully Migrated: 43 (13 PaymentContext, 2 BrandContext, 3 TestDataContext, 6 CheckoutContext, 5 BrowserContext, 9
  ValidationContext, 3 UpsellContext, 2 SalesFunnelContext)
- Steps Partially Migrated: 0
- Steps Not Migrated: 0
- Steps Deprecated: 0

## Step Definition Migration Status

| Step Pattern                                                            | Original Location                       | Target Context                       | Status | Notes                                                     |
|-------------------------------------------------------------------------|-----------------------------------------|--------------------------------------|--------|-----------------------------------------------------------|
| `@When I select payment method :method`                                 | `src/Context/PaymentContext.php`        | `src/Context/PaymentContext.php`     | ✅      | Implemented                                               |
| `@When I fill in credit card information with :cardType card`           | `src/Context/PaymentContext.php`        | `src/Context/PaymentContext.php`     | ✅      | Implemented                                               |
| `@When I complete the PayPal checkout`                                  | `src/Context/PaymentContext.php`        | `src/Context/PaymentContext.php`     | ✅      | Implemented                                               |
| `@When I handle 3D Secure authentication`                               | `src/Context/PaymentContext.php`        | `src/Context/PaymentContext.php`     | ✅      | Implemented                                               |
| `@When /^I select the "([^"]*)" payment method$/`                       | `src/Context/PaymentContext.php`        | `src/Context/PaymentContext.php`     | ✅      | Implemented - calls existing method                       |
| `@When /^I log in to PayPal with "([^"]*)" credentials$/`               | `src/Context/PaymentContext.php`        | `src/Context/PaymentContext.php`     | ✅      | Implemented                                               |
| `@When /^I choose to pay with PayPal$/`                                 | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - uses CheckoutPage and PayPalPage            |
| `@When /^I am redirected to the PayPal sandbox page$/`                  | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - calls PayPalPage->waitForRedirectToPayPal() |
| `@Then /^I should see the correct payment amount in PayPal$/`           | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - verifies amount matches expected value      |
| `@When /^I confirm the PayPal payment$/`                                | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - calls PayPalPage->confirmPayment()          |
| `@Then /^the PayPal payment should be successful$/`                     | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - verifies redirect and success               |
| `@Then /^I should see a PayPal login error message$/`                   | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - verifies error is displayed                 |
| `@Then /^I should see the 3DS page$/`                                   | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - verifies 3DS page is displayed              |
| `@When /^I press the "([^"]*)" button$/`                                | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - handles 'complete' and 'fail' buttons       |
| `@Then /^I check the driver type$/`                                     | `features/bootstrap/FeatureContext.php` | `src/Context/BrowserContext.php`     | ✅      | Implemented in BrowserContext                             |
| `@Given /^I load brand configuration$/`                                 | `features/bootstrap/FeatureContext.php` | `src/Context/BrandContext.php`       | ✅      | Implemented in BrandContext                               |
| `@Given /^I load product data$/`                                        | `features/bootstrap/FeatureContext.php` | `src/Context/TestDataContext.php`    | ✅      | Implemented in TestDataContext                            |
| `@Then /^I wait for the order confirmation page to load$/`              | `features/bootstrap/FeatureContext.php` | `src/Context/CheckoutContext.php`    | ✅      | Implemented in CheckoutContext                            |
| `@Then /^I see the order number$/`                                      | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@Then /^I should see the order processing page$/`                      | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@Given /^I am on the sales funnel page "([^"]*)"$/`                    | `features/bootstrap/FeatureContext.php` | `src/Context/SalesFunnelContext.php` | ✅      | Implemented in SalesFunnelContext                         |
| `@Given /^I verify the funnel product details$/`                        | `features/bootstrap/FeatureContext.php` | `src/Context/SalesFunnelContext.php` | ✅      | Implemented in SalesFunnelContext                         |
| `@When /^I fill in the shipping information with "([^"]*)" user data$/` | `features/bootstrap/FeatureContext.php` | `src/Context/CheckoutContext.php`    | ✅      | Implemented in CheckoutContext                            |
| `@When /^I use the same address for billing$/`                          | `features/bootstrap/FeatureContext.php` | `src/Context/CheckoutContext.php`    | ✅      | Implemented in CheckoutContext                            |
| `@Then /^The shipping method "([^"]*)" should be selected$/`            | `features/bootstrap/FeatureContext.php` | `src/Context/CheckoutContext.php`    | ✅      | Implemented in CheckoutContext                            |
| `@Then /^I verify the shipping cost$/`                                  | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@When /^I enter "([^"]*)" payment details$/`                           | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - loads payment data and fills form           |
| `@When /^I complete the purchase$/`                                     | `features/bootstrap/FeatureContext.php` | `src/Context/CheckoutContext.php`    | ✅      | Implemented in CheckoutContext                            |
| `@Then /^I should be redirected to the upsell page$/`                   | `features/bootstrap/FeatureContext.php` | `src/Context/UpsellContext.php`      | ✅      | Implemented in UpsellContext                              |
| `@When /^I accept the upsell offer$/`                                   | `features/bootstrap/FeatureContext.php` | `src/Context/UpsellContext.php`      | ✅      | Implemented in UpsellContext                              |
| `@Then /^I verify the order details are correct$/`                      | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ❌      | Should be implemented in ValidationContext                |
| `@When /^I verify shipping cost is updated to "([^"]*)"$/`              | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@Then /^I verify both products are in the order$/`                     | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@When /^I navigate back in browser$/`                                  | `features/bootstrap/FeatureContext.php` | `src/Context/BrowserContext.php`     | ✅      | Implemented in BrowserContext                             |
| `@Then /^I verify dietary restriction warnings are displayed$/`         | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@When /^I click the accept button multiple times$/`                    | `features/bootstrap/FeatureContext.php` | `src/Context/UpsellContext.php`      | ✅      | Implemented in UpsellContext                              |
| `@Then /^I verify only one upsell product is in the order$/`            | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@Then /^I verify the shipping cost is "([^"]*)"$/`                     | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@Then /^I verify the product instructions contain all warnings$/`      | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ❌      | Should be implemented in ValidationContext                |
| `@Then /^I verify browser session is active$/`                          | `features/bootstrap/FeatureContext.php` | `src/Context/BrowserContext.php`     | ✅      | Implemented in BrowserContext                             |
| `@Then /^I verify BrowserStack session is active$/`                     | `features/bootstrap/FeatureContext.php` | `src/Context/BrowserContext.php`     | ✅      | Implemented in BrowserContext                             |
| `@Then /^I should see the PayPal login error$/`                         | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented - calls iShouldSeeAPayPalLoginErrorMessage()  |
| `@When /^I log in to PayPal sandbox with "([^"]*)" credentials$/`       | `features/bootstrap/FeatureContext.php` | `src/Context/PaymentContext.php`     | ✅      | Implemented with credential type mapping                  |
| `@Then /^I should be redirected back to the merchant site$/`            | `features/bootstrap/FeatureContext.php` | `src/Context/ValidationContext.php`  | ✅      | Implemented in ValidationContext                          |
| `@When /^I don't complete the order$/`                                  | `features/bootstrap/FeatureContext.php` | `src/Context/CheckoutContext.php`    | ✅      | Implemented in CheckoutContext                            |
| `@Given /^I am using the "([^"]*)" brand$/`                             | `features/bootstrap/FeatureContext.php` | `src/Context/BrandContext.php`       | ✅      | Implemented in BrandContext                               |
| `@Given /^I load test data for "([^"]*)"$/`                             | `features/bootstrap/FeatureContext.php` | `src/Context/TestDataContext.php`    | ✅      | Implemented in TestDataContext                            |
| `@Given /^I load product data for "([^"]*)"$/`                          | `features/bootstrap/FeatureContext.php` | `src/Context/TestDataContext.php`    | ✅      | Implemented in TestDataContext                            |
| `@Then I should see the page loaded successfully`                       | `features/bootstrap/FeatureContext.php` | `src/Context/BrowserContext.php`     | ✅      | Implemented in BrowserContext                             |

## Current Focus

- ✅ Completed implementation of all PaymentContext steps
- ✅ Completed implementation of all BrandContext steps
- ✅ Completed implementation of all TestDataContext steps
- ✅ Completed implementation of all CheckoutContext steps
- ✅ Completed implementation of all BrowserContext steps
- ✅ Completed implementation of all ValidationContext steps
- ✅ Completed implementation of all UpsellContext steps
- ✅ Completed implementation of all SalesFunnelContext steps
- Next steps:
    - Create integration tests to verify the migrated steps
    - Update feature files to use the new step definitions
    - Consider potential refactoring to further improve code quality and maintainability

## PaymentContext Step Analysis

We've successfully implemented all identified PaymentContext steps:

1. `iSelectPaymentMethod(string $method)` - Selects payment method on payment page
2. `iFillInCreditCardInformationWith(string $cardType)` - Fills in credit card information
3. `iCompleteThePayPalCheckout()` - Completes PayPal checkout process
4. `iHandle3DSecureAuthentication()` - Handles 3D Secure authentication
5. `iSelectThePaymentMethod(string $method)` - Alternative pattern for selecting payment method
6. `iLogInToPayPalWithCredentials(string $credentialType)` - Logs in to PayPal with credential set
7. `iChooseToPayWithPayPal()` - Selects PayPal as payment method
8. `iAmRedirectedToThePayPalSandboxPage()` - Verifies redirect to PayPal sandbox
9. `iShouldSeeTheCorrectPaymentAmountInPayPal()` - Verifies correct amount in PayPal
10. `iConfirmThePayPalPayment()` - Confirms payment in PayPal
11. `thePayPalPaymentShouldBeSuccessful()` - Verifies PayPal payment success
12. `iShouldSeeAPayPalLoginErrorMessage()` - Verifies PayPal login error
13. `iShouldSeeThePayPalLoginError()` - Alternative for verifying PayPal login error
14. `iLogInToPayPalSandboxWithCredentials(string $credentialType)` - Logs in to PayPal sandbox
15. `iShouldSeeThe3DSPage()` - Verifies 3DS page display
16. `iPressTheButton(string $button)` - Handles button presses on 3DS page
17. `iEnterPaymentDetails(string $paymentType)` - Enters payment details by type

## Implementation Plan

With the PaymentContext fully implemented, we'll move to implement steps in the next context:

1. **Options for next context**:
    - BrandContext - has several partially migrated steps
    - TestDataContext - has several partially migrated steps
    - CheckoutContext - has several steps from legacy FeatureContext
    - BrowserContext - has several utility steps

2. **Recommended approach**:
    - First, complete the partially migrated steps in BrandContext and TestDataContext
    - Then implement CheckoutContext steps, as they're closely related to the payment flow
    - Finally, implement BrowserContext utility steps

## Next Steps

1. Update steps_definition_registry.md with the new PaymentContext implementations
2. Begin implementing steps for BrandContext to complete the partially migrated steps
3. Continue with TestDataContext steps
4. Proceed with CheckoutContext steps

## UpsellContext

| Legacy Step Definition                 | Migration Status | New Location                    | Page Object Call                 | Comments                     |
|----------------------------------------|------------------|---------------------------------|----------------------------------|------------------------------|
| `iAcceptTheUpsellOffer()`              | ✅                | `src/Context/UpsellContext.php` | `upsellPage->acceptOffer()`      | Implemented in UpsellContext |
| `iDeclineTheUpsellOffer()`             | ✅                | `src/Context/UpsellContext.php` | `upsellPage->declineOffer()`     | Implemented in UpsellContext |
| `iShouldSeeTheUpsellMessage()`         | ✅                | `src/Context/UpsellContext.php` | `upsellPage->getUpsellMessage()` | Implemented in UpsellContext |
| `iAcceptTheFirstUpsellOffer()`         | ✅                | `src/Context/UpsellContext.php` | `upsellPage->acceptOffer()`      | Implemented in UpsellContext |
| `iAcceptTheSecondUpsellOffer()`        | ✅                | `src/Context/UpsellContext.php` | `upsellPage->acceptOffer()`      | Implemented in UpsellContext |
| `iDeclineTheFirstUpsellOffer()`        | ✅                | `src/Context/UpsellContext.php` | `upsellPage->declineOffer()`     | Implemented in UpsellContext |
| `iDeclineTheSecondUpsellOffer()`       | ✅                | `src/Context/UpsellContext.php` | `upsellPage->declineOffer()`     | Implemented in UpsellContext |
| `iClickTheAcceptButtonMultipleTimes()` | ✅                | `src/Context/UpsellContext.php` | `browserService->clickElement()` | Implemented in UpsellContext |
| `iShouldBeRedirectedToTheUpsellPage()` | ✅                | `src/Context/UpsellContext.php` | `upsellPage->isOpen()`           | Implemented in UpsellContext | 