<?php

namespace App\Service\Environment;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;

/**
 * Service for managing environment variables
 */
class EnvironmentService extends AbstractService implements EnvironmentServiceInterface
{
    private array $variables = [];

    /**
     * Constructor
     *
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?LoggerInterface $logger = null)
    {
        parent::__construct($logger);

        // Initialize with current environment variables
        $this->variables = getenv();
        $this->logInfo("EnvironmentService initialized with " . count($this->variables) . " variables");
    }

    /**
     * {@inheritdoc}
     */
    public function getVariable(string $name, string $default = null): ?string
    {
        if (isset($this->variables[$name])) {
            return $this->variables[$name];
        }

        // Check if it's available in the environment but not in our cache
        $value = getenv($name);
        if ($value !== false) {
            $this->variables[$name] = $value;
            return $value;
        }

        return $default;
    }

    /**
     * {@inheritdoc}
     */
    public function hasVariable(string $name): bool
    {
        if (isset($this->variables[$name])) {
            return true;
        }

        // Check if it's available in the environment but not in our cache
        $value = getenv($name);
        if ($value !== false) {
            $this->variables[$name] = $value;
            return true;
        }

        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function loadFromFile(string $filePath): void
    {
        if (!file_exists($filePath)) {
            $this->logError("Environment file not found: $filePath");
            throw new RuntimeException("Environment file not found: $filePath");
        }

        $this->logInfo("Loading environment variables from file: $filePath");
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip comments and empty lines
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }

            // Parse variable assignment
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $name = trim($name);
                $value = trim($value);

                // Remove quotes if present
                if (preg_match('/^(["\'])(.*)\1$/', $value, $matches)) {
                    $value = $matches[2];
                }

                $this->setVariable($name, $value);
            }
        }

        $this->logInfo("Loaded environment variables from file");
    }

    /**
     * {@inheritdoc}
     */
    public function setVariable(string $name, string $value): void
    {
        $this->logInfo("Setting environment variable: $name");
        $this->variables[$name] = $value;
        putenv("$name=$value");
    }

    /**
     * {@inheritdoc}
     */
    public function getAllVariables(): array
    {
        return $this->variables;
    }
}
