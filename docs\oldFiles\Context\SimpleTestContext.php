<?php

use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;

/**
 * Simple test context for verifying Behat functionality
 */
class SimpleTestContext implements Context
{
    private $testValue = false;

    /**
     * @Given I have a simple test
     */
    public function iHaveASimpleTest()
    {
        // Just a simple step that always passes
    }

    /**
     * @When I run the test
     */
    public function iRunTheTest()
    {
        $this->testValue = true;
    }

    /**
     * @Then the test should pass
     */
    public function theTestShouldPass()
    {
        if (!$this->testValue) {
            throw new \Exception('Test failed: testValue is false');
        }
    }
}
