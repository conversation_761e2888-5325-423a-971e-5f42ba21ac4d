<?xml version="1.0"?>
<psalm
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        errorLevel="3"
        resolveFromConfigFile="true"
        xmlns="https://getpsalm.org/schema/config"
        xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
        findUnusedBaselineEntry="true"
        findUnusedCode="true"
>
    <projectFiles>
        <directory name="."/>
        <ignoreFiles>
            <directory name="vendor"/>
        </ignoreFiles>
    </projectFiles>
</psalm>
