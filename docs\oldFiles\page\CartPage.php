<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Exception\ElementNotFoundException;

/**
 * CartPage handles actions on the shopping cart page.
 */
class CartPage extends BasePage
{
    /**
     * The path of the cart page.
     *
     * @var string
     */
    protected $path = '/cart';

    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'CART_ITEM' => '.cart-table tbody tr',
        'PRODUCT_NAME' => '.product-description h3',
        'UNIT_PRICE' => 'td.numbers span',
        'QUANTITY' => '#sylius_cart_items_0_quantity',
        'TOTAL' => 'td.numbers:nth-child(4)',
        'SHIPPING_TOTAL' => '.summary-section .ch-shipping-value span',
        'ORDER_TOTAL' => '.summary-section .ch-total-value span',
        'SUCCESS_MESSAGE' => '.alert.alert-success',
        'ERROR_MESSAGE' => '.alert.alert-danger',
        'SUBSCRIPTION_BADGE' => '.subscription-badge',
        'SUBSCRIPTION_FREQUENCY' => '.subscription-frequency',
        'CART_ITEMS' => '.cart-table tbody tr'
    ];
    public $brandContext;

    /**
     * Verifies that we're on the expected page.
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        $this->waitForElementVisible(self::SELECTORS['CART_ITEM']);
    }

    /**
     * Gets the URL for the cart page.
     *
     * @return string The complete URL
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Navigates to the cart page URL.
     */
    public function load(): void
    {
        $this->open();
    }

    /**
     * Waits for the cart page to load.
     *
     * @param int $timeout The maximum time to wait in milliseconds.
     * @throws ElementNotFoundException
     */
    public function waitForPageToLoad(int $timeout = 10000): void
    {
        parent::waitForPageToLoad($timeout);
        $this->waitForElementVisible('.cart-title', $timeout);
    }

    /**
     * Checks if the current URL matches the cart page URL.
     *
     * @return bool True if URLs match, false otherwise.
     */
    public function isUrlMatches(): bool
    {
        return $this->getCurrentUrl() === $this->getUrl();
    }

    /**
     * Gets the title text of the cart page.
     *
     * @return string The cart title.
     * @throws ElementNotFoundException
     */
    public function getCartTitle(): string
    {
        $element = $this->findElement('.cart-title h1');
        return trim($element->getText());
    }

    /**
     * Checks if the specified product is displayed in the cart.
     *
     * @param string $productName The name of the product.
     * @return bool True if the product is displayed, false otherwise.
     */
    public function isProductDisplayed(string $productName): bool
    {
        try {
            $element = $this->findElement('.product-description h3');
            return trim($element->getText()) === $productName;
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Checks if the product image is displayed.
     *
     * @return bool True if the product image is displayed, false otherwise.
     */
    public function isProductImageDisplayed(): bool
    {
        try {
            $element = $this->findElement('.product-image-and-description img');
            return $element->isVisible();
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Updates the quantity of the product in the cart.
     *
     * @param int $quantity The desired quantity.
     * @throws ElementNotFoundException
     */
    public function updateQuantity(int $quantity): void
    {
        $selector = '#sylius_cart_items_0_quantity';
        $element = $this->findElement($selector);
        $element->setValue('');
        $element->setValue((string)$quantity);
    }

    /**
     * Gets the current quantity value from the quantity input.
     *
     * @return int The quantity.
     * @throws ElementNotFoundException
     */
    public function getQuantity(): int
    {
        $selector = '#sylius_cart_items_0_quantity';
        $element = $this->findElement($selector);
        return (int)$element->getValue();
    }

    /**
     * Gets the unit price of the product.
     *
     * @return float The unit price.
     * @throws ElementNotFoundException
     */
    public function getUnitPrice(): float
    {
        $element = $this->findElement(self::SELECTORS['UNIT_PRICE']);
        return $this->parsePrice($element->getText());
    }

    /**
     * Parses a price string into a float value.
     *
     * @param string $priceText The price text to parse
     * @return float The parsed price value
     */
    private function parsePrice(string $priceText): float
    {
        // Remove currency symbols, commas, and whitespace
        $cleanPrice = preg_replace('/[^0-9.]/', '', $priceText);
        return (float) $cleanPrice;
    }

    /**
     * Gets the total price from the cart.
     *
     * @return float The total price.
     * @throws ElementNotFoundException
     */
    public function getTotalPrice(): float
    {
        $element = $this->findElement(self::SELECTORS['TOTAL']);
        return $this->parsePrice($element->getText());
    }

    /**
     * Gets the shipping cost.
     *
     * @return float The shipping cost.
     * @throws ElementNotFoundException
     */
    public function getShippingCost(): float
    {
        $element = $this->findElement(self::SELECTORS['SHIPPING_TOTAL']);
        return $this->parsePrice($element->getText());
    }

    /**
     * Gets the total before upsell items.
     *
     * @return float The pre-upsell total.
     * @throws ElementNotFoundException
     */
    public function getPreUpsellTotal(): float
    {
        try {
            $element = $this->findElement('.pre-upsell-total .amount');
            return $this->parsePrice($element->getText());
        } catch (ElementNotFoundException $e) {
            // If pre-upsell total not found, return regular total
            return $this->getTotalPrice();
        }
    }

    /**
     * Determines if the order qualifies for free shipping.
     *
     * @return bool True if order qualifies for free shipping.
     */
    public function qualifiesForFreeShipping(): bool
    {
        try {
            $orderTotal = $this->getTotalPrice();
            $threshold = $this->brandContext->getShippingThreshold();
            return $orderTotal >= $threshold;
        } catch (\Exception $e) {
            error_log("[CartPage] Error checking free shipping qualification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Verifies if the upsell price was correctly added to the total.
     *
     * @param float $upsellPrice The price of the upsell item
     * @return bool True if the price addition is correct
     */
    public function verifyUpsellPriceAddition(float $upsellPrice): bool
    {
        try {
            $preUpsellTotal = $this->getPreUpsellTotal();
            $finalTotal = $this->getTotalPrice();
            return abs(($preUpsellTotal + $upsellPrice) - $finalTotal) < 0.01;
        } catch (\Exception $e) {
            error_log("[CartPage] Error verifying upsell price: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Removes the item from the cart.
     */
    public function removeItem(): void
    {
        $selector = '.remove-item-button';
        $this->clickElement($selector);
    }

    /**
     * Checks if the remove item button is displayed.
     *
     * @return bool True if displayed, false otherwise.
     */
    public function isRemoveButtonDisplayed(): bool
    {
        try {
            $element = $this->findElement('.remove-item-button');
            return $element->isVisible();
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Applies a coupon code to the cart
     *
     * @param string $couponCode The coupon code to apply
     * @return bool True if coupon was applied successfully
     */
    public function applyCoupon(string $couponCode): bool
    {
        try {
            // Find coupon input
            $couponInput = $this->findElement("#sylius_cart_promotionCoupon");
            if (!$couponInput) {
                error_log("[CartPage] Coupon input field not found");
                return false;
            }

            // Scroll and enter coupon code
            $this->scrollToElementWithRetry($couponInput);
            $couponInput->click();
            $couponInput->setValue(''); // Clear first
            $couponInput->setValue($couponCode);
            error_log("[CartPage] Entered coupon code: " . $couponCode);

            // Find and click apply button
            $applyButton = $this->findElement(".coupon-section button[type='submit']");
            if (!$applyButton) {
                error_log("[CartPage] Apply button not found");
                return false;
            }

            $this->scrollToElementWithRetry($applyButton);
            $applyButton->click();
            error_log("[CartPage] Clicked apply button");

            // Wait for AJAX response
            $this->getSession()->wait(3000);

            // Check for success message first
            try {
                $successMessage = $this->findElement('.alert.alert-success');
                if ($successMessage && $successMessage->isVisible()) {
                    error_log("[CartPage] Success message found: " . $successMessage->getText());
                    return true;
                }
            } catch (\Exception $e) {
                error_log("[CartPage] No success message found");
            }

            // Check for error message
            try {
                $errorMessage = $this->findElement('.alert.alert-danger');
                if ($errorMessage && $errorMessage->isVisible()) {
                    error_log("[CartPage] Error message found: " . $errorMessage->getText());
                    return false;
                }
            } catch (\Exception $e) {
                error_log("[CartPage] No error message found");
            }

            // Verify discount was actually applied
            $isDiscountApplied = $this->verifyDiscountApplied();
            error_log("[CartPage] Discount applied status: " . ($isDiscountApplied ? 'true' : 'false'));

            return $isDiscountApplied;

        } catch (\Exception $e) {
            error_log("[CartPage] Exception in applyCoupon: " . $e->getMessage());
            error_log("[CartPage] Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Verifies if a discount has been applied to the cart
     *
     * @return bool True if discount is present and matches expected amount
     */
    private function verifyDiscountApplied(): bool
    {
        try {
            // Wait for potential AJAX updates
            $this->getSession()->wait(3000);

            // Using XPath for more reliable element location
            $discountElement = $this->findElement("//div[contains(@class, 'summary-section')]//p[contains(text(), 'Promotion discount:')]//span[@class='numbers']");

            if (!$discountElement) {
                error_log("[CartPage] Discount element not found");
                return false;
            }

            $discountAmount = $this->getNumericValue($discountElement->getText());
            error_log("[CartPage] Found discount amount: " . $discountAmount);

            return $discountAmount > 0;
        } catch (\Exception $e) {
            error_log("[CartPage] Exception in verifyDiscountApplied: " . $e->getMessage());
            return false;
        }
    }

    private function getNumericValue(string $text): float
    {
        return (float)preg_replace('/[^0-9.]/', '', $text);
    }

    /**
     * Checks if an error message is displayed.
     *
     * @return bool True if error message is displayed, false otherwise.
     */
    public function isErrorMessageDisplayed(): bool
    {
        try {
            $element = $this->findElement('.alert-danger');
            return $element->isVisible();
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Clicks the update cart button to update the cart details.
     */
    public function updateCart(): void
    {
        $selector = '.update-cart-button';
        $this->clickElement($selector);
    }

    /**
     * Waits for the cart to update by checking for the appearance of the success message or any changes in the cart.
     *
     * @param int $timeout The maximum time to wait in milliseconds.
     */
    public function waitForCartToUpdate(int $timeout = 10000): void
    {
        $this->session->wait(
            $timeout,
            "document.querySelector('.alert.alert-success') !== null"
        );
    }

    /**
     * Checks if the coupon was applied successfully.
     *
     * @return bool True if coupon seems to be applied, false otherwise.
     */
    public function isCouponAppliedSuccessfully(): bool
    {
        try {
            $element = $this->findElement('.alert.alert-success');
            return stripos($element->getText(), 'coupon applied') !== false;
        } catch (ElementNotFoundException $e) {
            return $this->verifyOrderTotal();
        }
    }

    /**
     * Verifies that the order total is correct based on items total, promotion discount, and shipping cost.
     *
     * @return bool True if order total is correct, false otherwise.
     */
    public function verifyOrderTotal(): bool
    {
        try {
            // Using XPath to find elements containing specific text
            $itemsTotal = $this->getNumericValue(
                $this->findElement("//div[contains(@class, 'summary-section')]//p[contains(text(), 'Items total:')]//span[@class='numbers']")->getText()
            );

            $promotionDiscount = $this->getNumericValue(
                $this->findElement("//div[contains(@class, 'summary-section')]//p[contains(text(), 'Promotion discount:')]//span[@class='numbers']")->getText()
            );

            $shippingCost = $this->getNumericValue(
                $this->findElement("//div[contains(@class, 'summary-section')]//p[contains(text(), 'Shipping:')]//span[@class='numbers']")->getText()
            );

            $displayedOrderTotal = $this->getNumericValue(
                $this->findElement("//div[contains(@class, 'summary-section')]//p[contains(text(), 'Order total:')]//span[@class='numbers']")->getText()
            );

            $calculatedOrderTotal = $itemsTotal - $promotionDiscount + $shippingCost;

            return abs($calculatedOrderTotal - $displayedOrderTotal) < 0.01;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Checks if the cart is empty.
     *
     * @return bool True if cart is empty and displays the correct message, false otherwise.
     */
    public function isCartEmpty(): bool
    {
        try {
            $element = $this->findElement('.cart-title .alert');
            return trim($element->getText()) === 'Your cart is empty';
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Clicks the checkout button to proceed to the checkout page.
     * @throws ElementNotFoundException
     */
    public function proceedToCheckout(): void
    {
        $element = $this->findElement('button.checkout-btn');
        $element->click();
    }

    /**
     * Checks if the success message for adding an item to the cart is displayed.
     *
     * @return bool True if the success message is displayed and contains the expected text, false otherwise.
     */
    public function isSuccessMessageDisplayed(): bool
    {
        try {
            $element = $this->findElement('.alert-success');
            return $element->isVisible() &&
                stripos($element->getText(), 'Item has been added to cart') !== false;
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Gets the purchase type text from the cart page.
     *
     * @return string The purchase type.
     * @throws ElementNotFoundException
     */
    public function getPurchaseType(): string
    {
        $selector = "//td[contains(text(), 'Purchase type:')]//following-sibling::td";
        $element = $this->findElement($selector);
        return trim($element->getText());
    }

    /**
     * Checks if the user is prevented from proceeding to checkout due to an empty cart.
     *
     * @return bool True if prevented with an error message, false otherwise.
     */
    public function isPreventedFromCheckout(): bool
    {
        try {
            $element = $this->findElement('.checkout-error-message');
            return $element->isVisible();
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Checks if the discount is applied correctly.
     *
     * @return bool True if discount is applied correctly, false otherwise.
     */
    public function isDiscountApplied(): bool
    {
        try {
            // Using XPath instead of jQuery-style selectors
            $itemsTotal = $this->getNumericValue(
                $this->findElement("//div[contains(@class, 'summary-section')]//p[contains(text(), 'Items total:')]//span[@class='numbers']")->getText()
            );

            $discountAmount = $this->getNumericValue(
                $this->findElement("//div[contains(@class, 'summary-section')]//p[contains(text(), 'Promotion discount:')]//span[@class='numbers']")->getText()
            );

            // Calculate expected discount (15% of items total)
            $expectedDiscount = round($itemsTotal * 0.15, 2);

            return abs($discountAmount - $expectedDiscount) < 0.01;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Retrieves the product name from the cart.
     *
     * @return string The product name.
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getProductName(): string
    {
        $selector = '.product-description h3';
        $element = $this->findElement($selector);
        return trim($element->getText());
    }

    /**
     * Retrieves the subscription frequency from the cart.
     *
     * @return string The subscription frequency (e.g., "every 30 days").
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getSubscriptionFrequency(): string
    {
        $frequencySpan = $this->getSession()->getPage()->find(
            'xpath',
            '//div[@class="product-description"]//span[contains(text(), "Frequency:")]/span'
        );

        if (!$frequencySpan) {
            throw new ElementNotFoundException($this->session, 'span', 'Frequency');
        }

        return trim($frequencySpan->getText());
    }

    /**
     * Helper method to wait for element visibility
     */
    private function waitForElement(string $selector, int $timeout = 10): void
    {
        $this->getSession()->wait($timeout * 1000, "document.querySelector('$selector') !== null");
    }

    /**
     * Gets the total number of items in cart
     *
     * @return int Number of items
     */
    public function getItemCount(): int
    {
        return count($this->findElements(self::SELECTORS['CART_ITEMS']));
    }

    /**
     * Gets the number of subscription items in cart
     *
     * @return int Number of subscription items
     */
    public function getSubscriptionItemCount(): int
    {
        $items = $this->findElements(self::SELECTORS['CART_ITEMS']);
        return count(array_filter($items, function ($item) {
            return $item->find('css', self::SELECTORS['SUBSCRIPTION_BADGE']) !== null;
        }));
    }

    /**
     * Gets the number of one-time purchase items in cart
     *
     * @return int Number of one-time items
     */
    public function getOneTimePurchaseItemCount(): int
    {
        return $this->getItemCount() - $this->getSubscriptionItemCount();
    }

    /**
     * Verifies subscription frequencies in cart
     *
     * @return array Array of subscription frequencies
     */
    public function getSubscriptionFrequencies(): array
    {
        $frequencies = [];
        $items = $this->findElements(self::SELECTORS['CART_ITEMS']);

        foreach ($items as $item) {
            $frequencyElement = $item->find('css', self::SELECTORS['SUBSCRIPTION_FREQUENCY']);
            if ($frequencyElement) {
                $frequencies[] = trim($frequencyElement->getText());
            }
        }

        return $frequencies;
    }

    /**
     * Checks if cart limit message is displayed
     */
    public function isCartLimitMessageDisplayed(string $message): bool
    {
        try {
            $element = $this->findElement('.cart-limit-message');
            return strpos($element->getText(), $message) !== false;
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Checks if checkout is enforced
     */
    public function isCheckoutEnforced(): bool
    {
        try {
            return $this->findElement('.enforce-checkout-message')->isVisible();
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Checks if the cart is empty
     *
     * @return bool True if the cart is empty, false otherwise
     */
    public function isEmpty(): bool
    {
        try {
            return $this->getItemCount() === 0;
        } catch (\Exception $e) {
            // If there's an error finding items, the cart is likely empty
            return true;
        }
    }

}
