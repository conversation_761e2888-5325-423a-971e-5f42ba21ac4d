# Service Catalog

This document provides an overview of all services available in the new architecture, their purpose, and usage examples.

## Core Services

### SharedStateService

**Purpose**: Manages shared state between contexts and steps during test execution.

**Interface**: `App\Service\State\SharedStateServiceInterface`

**Implementation**: `App\Service\State\SharedStateService`

**Dependencies**:
- None

**Usage Example**:
```php
// Setting a value
$this->stateService->set('product.current', $productSlug);

// Getting a value with fallback
$productSlug = $this->stateService->get('product.current') ?? 'default_product';

// Checking if a value exists
if ($this->stateService->has('product.current')) {
    // Do something
}

// Setting feature-scoped state
$this->stateService->set('brand.current', 'aeons', 'feature');

// Getting feature-scoped state
$brand = $this->stateService->get('brand.current', 'feature');
```

### ConfigurationService

**Purpose**: Manages configuration for different brands and environments.

**Interface**: `App\Service\Configuration\ConfigurationServiceInterface`

**Implementation**: `App\Service\Configuration\ConfigurationService`

**Dependencies**:
- `App\Service\Path\PathServiceInterface`

**Usage Example**:
```php
// Get current brand
$brand = $this->configService->getCurrentBrand();

// Get current environment
$environment = $this->configService->getCurrentEnvironment();

// Get brand-specific configuration
$brandName = $this->configService->getBrandConfig('name');
$products = $this->configService->getBrandConfig('products');

// Get environment-specific configuration
$baseUrl = $this->configService->getEnvironmentConfig('base_url');
$apiUrl = $this->configService->getEnvironmentConfig('api_url');
```

### TestDataService

**Purpose**: Manages test data for different brands and products.

**Interface**: `App\Service\Data\TestDataServiceInterface`

**Implementation**: `App\Service\Data\TestDataService`

**Dependencies**:
- `App\Service\Path\PathServiceInterface`
- `App\Service\Configuration\ConfigurationServiceInterface`

**Usage Example**:
```php
// Load test data for a product
$productData = $this->dataService->loadTestData('aeons', 'products', 'product1');

// Get test data for a specific scenario
$checkoutData = $this->dataService->getTestData('checkout', 'standard');

// Get random test data from a category
$randomCustomer = $this->dataService->getRandomTestData('customers');
```

### BrowserService

**Purpose**: Provides browser interaction capabilities.

**Interface**: `App\Service\Browser\BrowserServiceInterface`

**Implementation**: `App\Service\Browser\BrowserService`

**Dependencies**:
- Mink Session

**Usage Example**:
```php
// Wait for an element to be visible
$this->browserService->waitForElementVisible('#product-title');

// Click an element
$this->browserService->clickElement('#add-to-cart-button');

// Fill a form field
$this->browserService->fillField('#email', '<EMAIL>');

// Select an option from a dropdown
$this->browserService->selectOption('#quantity', '2');

// Get text from an element
$productTitle = $this->browserService->getElementText('#product-title');

// Check if an element is visible
$isVisible = $this->browserService->isElementVisible('#error-message');

// Take a screenshot
$this->browserService->takeScreenshot('error-state.png');
```

### PathService

**Purpose**: Manages path resolution for different types of files.

**Interface**: `App\Service\Path\PathServiceInterface`

**Implementation**: `App\Service\Path\PathService`

**Dependencies**:
- None

**Usage Example**:
```php
// Get project root directory
$projectRoot = $this->pathService->getProjectRoot();

// Get configuration directory
$configDir = $this->pathService->getConfigDir();

// Get fixtures directory
$fixturesDir = $this->pathService->getFixturesDir();

// Get brand configuration file path
$brandConfigPath = $this->pathService->getBrandConfigPath('aeons');

// Get environment configuration file path
$envConfigPath = $this->pathService->getEnvironmentConfigPath('stage');

// Get brand fixtures file path
$brandFixturesPath = $this->pathService->getBrandFixturesPath('aeons', 'products');
```

## Utility Services

### ValidationService

**Purpose**: Provides validation utilities for form fields and data.

**Interface**: `App\Service\Validation\ValidationServiceInterface`

**Implementation**: `App\Service\Validation\ValidationService`

**Dependencies**:
- `App\Service\Browser\BrowserServiceInterface`

**Usage Example**:
```php
// Validate a form field
$isValid = $this->validationService->validateField('#email', 'email');

// Validate a form
$errors = $this->validationService->validateForm('#checkout-form');

// Check for validation error messages
$hasErrors = $this->validationService->hasValidationErrors();

// Get all validation error messages
$errorMessages = $this->validationService->getValidationErrorMessages();
```

### LoggingService

**Purpose**: Provides logging capabilities.

**Interface**: `App\Service\Logging\LoggingServiceInterface`

**Implementation**: `App\Service\Logging\LoggingService`

**Dependencies**:
- `Psr\Log\LoggerInterface`

**Usage Example**:
```php
// Log an info message
$this->loggingService->info('Starting checkout process');

// Log an error message
$this->loggingService->error('Checkout failed', ['error' => $exception->getMessage()]);

// Log a step
$this->loggingService->logStep('I am on the product page');

// Log with context
$this->loggingService->debug('Product added to cart', [
    'product' => $productName,
    'quantity' => $quantity,
    'price' => $price
]);
```

### ElementInteractionService

**Purpose**: Provides utilities for interacting with page elements.

**Interface**: `App\Service\Element\ElementInteractionServiceInterface`

**Implementation**: `App\Service\Element\ElementInteractionService`

**Dependencies**:
- `App\Service\Browser\BrowserServiceInterface`

**Usage Example**:
```php
// Check if an element contains text
$containsText = $this->elementService->elementContainsText('#product-description', 'Premium quality');

// Get all elements matching a selector
$elements = $this->elementService->findAllElements('.product-item');

// Count elements matching a selector
$count = $this->elementService->countElements('.cart-item');

// Get element attribute
$href = $this->elementService->getElementAttribute('#product-link', 'href');

// Check if element has class
$hasClass = $this->elementService->elementHasClass('#message', 'success');
```

## Test Runner Services

### TestRunnerService

**Purpose**: Provides capabilities for running Behat tests.

**Interface**: `App\Service\TestRunner\TestRunnerServiceInterface`

**Implementation**: `App\Service\TestRunner\TestRunnerService`

**Dependencies**:
- `App\Service\Configuration\ConfigurationServiceInterface`
- `App\Service\Path\PathServiceInterface`

**Usage Example**:
```php
// Run all tests
$exitCode = $this->testRunnerService->runAll();

// Run tests for a specific product
$exitCode = $this->testRunnerService->runSingleProduct('product1');

// Run tests with specific tags
$exitCode = $this->testRunnerService->runWithTags('@checkout');

// Run a specific feature
$exitCode = $this->testRunnerService->runFeature('features/checkout.feature');

// Run tests in dry-run mode
$exitCode = $this->testRunnerService->runWithTags('@checkout', true);
```

## Service Registration

All services are registered in the service container configuration files:

- `config/services.yml`: Main service configuration
- `config/services/core.yml`: Core service definitions
- `config/services/contexts.yml`: Context service definitions
- `config/services/pages.yml`: Page object service definitions

Example service registration:

```yaml
# config/services/core.yml
services:
    App\Service\State\SharedStateServiceInterface:
        alias: App\Service\State\SharedStateService
    
    App\Service\State\SharedStateService:
        public: true
    
    App\Service\Configuration\ConfigurationServiceInterface:
        alias: App\Service\Configuration\ConfigurationService
    
    App\Service\Configuration\ConfigurationService:
        arguments:
            $pathService: '@App\Service\Path\PathServiceInterface'
        public: true
```

## Service Injection

Services are injected into contexts and page objects through constructor injection:

```php
// Example context with service injection
class ProductContext extends BaseContext
{
    private ProductPage $productPage;
    private SharedStateServiceInterface $stateService;
    
    public function __construct(
        ContainerInterface $container,
        ProductPage $productPage,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->productPage = $productPage;
        $this->stateService = $stateService;
    }
    
    // Step definitions...
}
```

## Service Factory Methods

For services with circular dependencies, factory methods are used:

```php
// Service factory
class ServiceFactory
{
    private ConfigurationServiceInterface $configService;
    
    public function __construct(ConfigurationServiceInterface $configService)
    {
        $this->configService = $configService;
    }
    
    public function createBrowserService(): BrowserServiceInterface
    {
        $baseUrl = $this->configService->getEnvironmentConfig('base_url');
        return new BrowserService($baseUrl);
    }
}

// Service registration with factory
services:
    App\Service\ServiceFactory:
        arguments:
            $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
        public: true
    
    App\Service\Browser\BrowserServiceInterface:
        factory: ['@App\Service\ServiceFactory', 'createBrowserService']
        public: true
```
