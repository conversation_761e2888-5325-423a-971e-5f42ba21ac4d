# Step Definitions Documentation

## Overview

Step definitions are the glue between Gherkin feature files and the actual test implementation. They translate the human-readable steps in feature files into executable code. This documentation covers the step definition classes (contexts) used in the Malaberg test automation framework.

## Base Context Structure

### BaseContext

The `BaseContext` class is the base implementation for all context classes. It provides common functionality and access to services.

```php
abstract class BaseContext implements Context
{
    /**
     * Service container
     *
     * @var ContainerInterface|null
     */
    protected ?ContainerInterface $container;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     */
    public function __construct(?ContainerInterface $container = null)
    {
        $this->container = $container;
    }

    // Common methods and service accessors...
}
```

### Service-Aware Context

The `ServiceAwareContext` interface marks a context as requiring access to the service container.

```php
interface ServiceAwareContext extends Context
{
    /**
     * Set the service container
     *
     * @param ContainerInterface $container Service container
     * @return void
     */
    public function setContainer(ContainerInterface $container): void;
}
```

## Context Classes

### FeatureContext

The `FeatureContext` class is the main context class that integrates with other contexts.

#### Properties

- `browserService`: Service for interacting with the browser
- `stateService`: Service for managing shared state

#### Methods

- `gatherContexts(BeforeScenarioScope $scope)`: Gathers other contexts from the environment
- `iAmOnTheHomepage()`: Navigates to the homepage
- `iTakeAScreenshotNamed(string $name)`: Takes a screenshot

### ProductContext

The `ProductContext` class handles product-related steps.

#### Properties

- `pageFactory`: Factory for creating page objects
- `dataService`: Service for loading test data
- `stateService`: Service for managing shared state

#### Methods

- `iAmViewingTheProduct(string $productIdentifier = null)`: Navigates to a product page
- `iSelectFlavor(string $flavor)`: Selects a product flavor
- `iSelectQuantity($quantity)`: Selects a product quantity
- `iSelectPurchaseType(string $purchaseType)`: Selects a purchase type
- `iAddTheProductToCart()`: Adds the product to the cart
- `iShouldSeeTheProductDetailsFor(string $productSlug)`: Verifies product details
- `theProductPriceShouldBeCorrect()`: Verifies the product price

### CartContext

The `CartContext` class handles cart-related steps.

#### Properties

- `pageFactory`: Factory for creating page objects
- `stateService`: Service for managing shared state

#### Methods

- `iAmOnTheCartPage()`: Navigates to the cart page
- `iShouldSeeItemsInMyCart(int $count)`: Verifies the number of items in the cart
- `iShouldSeeTheCartTotal(string $total)`: Verifies the cart total
- `iProceedToCheckout()`: Proceeds to checkout
- `iRemoveTheFirstItemFromTheCart()`: Removes the first item from the cart
- `theCartShouldBeEmpty()`: Verifies that the cart is empty

### CheckoutContext

The `CheckoutContext` class handles checkout-related steps.

#### Properties

- `pageFactory`: Factory for creating page objects
- `dataService`: Service for loading test data
- `stateService`: Service for managing shared state
- `validationService`: Service for validating data
- `browserService`: Service for interacting with the browser

#### Methods

- `iFillInTheShippingInformationWith(string $userType = 'default')`: Fills in shipping information
- `iChooseToUseTheSameAddressForBilling()`: Uses the same address for billing
- `iFillInTheBillingInformationWith(string $userType)`: Fills in billing information
- `iSelectShippingMethod(string $method)`: Selects a shipping method
- `iSelectPaymentMethod(string $method)`: Selects a payment method
- `iCompleteTheOrder()`: Completes the order
- `iShouldBeOnTheOrderConfirmationPage()`: Verifies that we're on the order confirmation page

### AbandonedCartContext

The `AbandonedCartContext` class handles abandoned cart scenarios.

#### Properties

- `pageFactory`: Factory for creating page objects
- `dataService`: Service for loading test data
- `stateService`: Service for managing shared state
- `cartContext`: Reference to the cart context
- `checkoutContext`: Reference to the checkout context
- `productContext`: Reference to the product context
- `emailContext`: Reference to the email context

#### Methods

- `gatherContexts(BeforeScenarioScope $scope)`: Gathers other contexts from the environment
- `iAbandonMyCart()`: Abandons the cart
- `iAbandonMyCartWithProduct(string $productSlug)`: Abandons the cart with a specific product
- `iShouldReceiveAnAbandonedCartEmail()`: Verifies that an abandoned cart email was received

### UpsellContext

The `UpsellContext` class handles upsell-related steps.

#### Properties

- `pageFactory`: Factory for creating page objects
- `stateService`: Service for managing shared state
- `emailContext`: Reference to the email context

#### Methods

- `gatherContexts(BeforeScenarioScope $scope)`: Gathers other contexts from the environment
- `iAmOnTheUpsellPage()`: Navigates to the upsell page
- `iAcceptTheUpsellOffer()`: Accepts the upsell offer
- `iDeclineTheUpsellOffer()`: Declines the upsell offer
- `iShouldSeeTheUpsellConfirmation()`: Verifies that the upsell confirmation is displayed

### ValidationContext

The `ValidationContext` class handles validation-related steps.

#### Properties

- `pageFactory`: Factory for creating page objects
- `dataService`: Service for loading test data
- `stateService`: Service for managing shared state
- `validationService`: Service for validating data

#### Methods

- `iShouldSeeValidationErrorFor(string $field)`: Verifies that a validation error is displayed for a field
- `allFieldsShouldBeValid()`: Verifies that all fields are valid
- `iShouldSeeTheErrorMessage(string $message)`: Verifies that an error message is displayed

## Common Context Methods

### Service Access Methods

- `getConfigService()`: Gets the configuration service
- `getDataService()`: Gets the data service
- `getPageFactory()`: Gets the page factory service
- `getStateService()`: Gets the shared state service
- `getValidationService()`: Gets the validation service
- `getBrowserService()`: Gets the browser service

### Utility Methods

- `logInfo(string $message)`: Logs an informational message
- `logWarning(string $message)`: Logs a warning message
- `logError(string $message, \Throwable $exception = null)`: Logs an error message
- `createMockPageFactory()`: Creates a mock page factory service
- `createMockDataService()`: Creates a mock data service
- `createMockStateService()`: Creates a mock shared state service
- `createMockValidationService()`: Creates a mock validation service
- `createMockBrowserService()`: Creates a mock browser service

## Best Practices for Step Definitions

1. **Single Responsibility**: Each context class should handle a specific area of functionality.
2. **Descriptive Step Names**: Step names should be descriptive and follow a consistent pattern.
3. **Error Handling**: Step definitions should handle errors gracefully and provide meaningful error messages.
4. **State Management**: Use the shared state service to manage state between steps.
5. **Service Injection**: Use dependency injection to access services.
6. **Context Gathering**: Use the `gatherContexts()` method to gather other contexts from the environment.
7. **Documentation**: Step definitions should be well-documented with clear method descriptions.

## Creating New Step Definitions

To create a new step definition:

1. Create a new class in the `src/Context` directory
2. Extend the `BaseContext` class
3. Implement the necessary step methods
4. Register the context in the `behat.yml` file

Example:

```php
<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for my-feature-related functionality
 */
class MyFeatureContext extends BaseContext
{
    private PageFactoryInterface $pageFactory;
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param PageFactoryInterface|null $pageFactory Page factory service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface $container = null,
        ?PageFactoryInterface $pageFactory = null,
        ?SharedStateServiceInterface $stateService = null
    ) {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }
    }

    /**
     * @Given I am doing something
     */
    public function iAmDoingSomething(): void
    {
        try {
            $myPage = $this->pageFactory->getPage('MyPage');
            $myPage->doSomething();
            $this->stateService->set('my_feature.something_done', true);
            $this->logInfo('Did something');
        } catch (\Throwable $e) {
            $this->logError('Failed to do something', $e);
            throw new \RuntimeException('Failed to do something: ' . $e->getMessage(), 0, $e);
        }
    }
}
```
