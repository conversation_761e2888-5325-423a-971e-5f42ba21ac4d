#!/bin/bash

# Set error handling
set -e
set -o pipefail

# Set variables
BACKUP_ROOT="backup"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="${BAC<PERSON>UP_ROOT}/${TIMESTAMP}"

# Create backup directories
echo "Creating backup directories..."
mkdir -p "${BACKUP_DIR}/page_objects/"{original,pre_migration}
mkdir -p "${BACKUP_DIR}/config"
mkdir -p "${BACKUP_DIR}/documentation"
mkdir -p "${BACKUP_DIR}/metadata"

# Backup page objects
echo "Backing up page objects..."
if [ -d "features/bootstrap/Page" ]; then
    cp -r features/bootstrap/Page/* "${BACKUP_DIR}/page_objects/original/"
    cp -r features/bootstrap/Page/* "${BACKUP_DIR}/page_objects/pre_migration/"
else
    echo "Warning: Page objects directory not found"
fi

# Backup configuration
echo "Backing up configuration files..."
if [ -f "behat.yml" ]; then
    cp behat.yml "${BACKUP_DIR}/config/"
else
    echo "Warning: behat.yml not found"
fi

if [ -f "composer.json" ]; then
    cp composer.json "${BACKUP_DIR}/config/"
else
    echo "Warning: composer.json not found"
fi

# Backup documentation
echo "Backing up documentation..."
if [ -f "docs/page_object_rules.md" ]; then
    cp docs/page_object_rules.md "${BACKUP_DIR}/documentation/"
else
    echo "Warning: page_object_rules.md not found"
fi

if [ -f "docs/page_object_extennsion_instructions.md" ]; then
    cp docs/page_object_extennsion_instructions.md "${BACKUP_DIR}/documentation/"
else
    echo "Warning: page_object_extennsion_instructions.md not found"
fi

# Create file inventory
echo "Creating file inventory..."
if [ -d "features/bootstrap/Page" ]; then
    find features/bootstrap/Page -type f -name "*.php" > "${BACKUP_DIR}/metadata/file_inventory.json"
else
    echo "[]" > "${BACKUP_DIR}/metadata/file_inventory.json"
    echo "Warning: No page objects found for inventory"
fi

# Store composer dependencies
echo "Storing dependency information..."
if command -v composer &> /dev/null; then
    composer show > "${BACKUP_DIR}/metadata/dependencies.json"
else
    echo "Warning: Composer not found"
fi

# Create backup verification file
cat << EOF > "${BACKUP_DIR}/metadata/backup_info.json"
{
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
    "backup_path": "${BACKUP_DIR}",
    "files_backed_up": {
        "page_objects": $(find "${BACKUP_DIR}/page_objects" -type f | wc -l),
        "config_files": $(find "${BACKUP_DIR}/config" -type f | wc -l),
        "documentation": $(find "${BACKUP_DIR}/documentation" -type f | wc -l)
    }
}
EOF

echo "Backup completed successfully at ${BACKUP_DIR}"
echo "Verify the backup contents and test restoration procedure" 