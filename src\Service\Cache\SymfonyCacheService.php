<?php

namespace App\Service\Cache;

use Symfony\Component\Cache\Adapter\AdapterInterface;
use Symfony\Component\Cache\CacheItem;

/**
 * Symfony cache implementation of CacheServiceInterface
 */
class SymfonyCacheService implements CacheServiceInterface
{
    public function __construct(
        private readonly AdapterInterface $cache
    )
    {
    }

    public function get(string $key): mixed
    {
        $item = $this->cache->getItem($key);
        return $item->isHit() ? $item->get() : null;
    }

    public function set(string $key, mixed $value, ?int $lifetime = null): void
    {
        $item = $this->cache->getItem($key);
        $item->set($value);

        if ($lifetime !== null) {
            $item->expiresAfter($lifetime);
        }

        $this->cache->save($item);
    }

    public function delete(string $key): void
    {
        $this->cache->deleteItem($key);
    }

    public function deletePattern(string $pattern): void
    {
        // Note: This requires a cache adapter that supports deleteItems
        // For Redis this would use KEYS pattern matching
        // For filesystem this would use glob
        $this->cache->clear($pattern);
    }

    public function has(string $key): bool
    {
        return $this->cache->hasItem($key);
    }
} 