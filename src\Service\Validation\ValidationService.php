<?php

namespace App\Service\Validation;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;

/**
 * Service for validating test data structure and content
 */
class ValidationService extends AbstractService implements ValidationServiceInterface
{
    private array $schemas = [];

    /**
     * Constructor
     *
     * @param string|null $schemasDir Schemas directory path
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?string $schemasDir = null, ?LoggerInterface $logger = null)
    {
        parent::__construct($logger);

        if ($schemasDir && is_dir($schemasDir)) {
            $this->loadSchemas($schemasDir);
        }
    }

    /**
     * Load schema definitions from directory
     *
     * @param string $schemasDir Schemas directory path
     */
    private function loadSchemas(string $schemasDir): void
    {
        $this->logInfo(sprintf("Loading schemas from directory: %s", $schemasDir));

        $schemaFiles = glob($schemasDir . '/*.json');

        foreach ($schemaFiles as $file) {
            $schemaName = basename($file, '.json');
            $schemaContent = file_get_contents($file);

            if ($schemaContent === false) {
                $this->logError(sprintf("Failed to read schema file: %s", $file));
                continue;
            }

            $schema = json_decode($schemaContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logError(sprintf(
                    "Failed to parse schema file %s: %s",
                    $file,
                    json_last_error_msg()
                ));
                continue;
            }

            $this->schemas[$schemaName] = $schema;
            $this->logInfo(sprintf("Loaded schema: %s", $schemaName));
        }
    }

    /**
     * {@inheritdoc}
     */
    public function validateProductData(array $data): void
    {
        $this->logInfo("Validating product data");

        $this->validateRequiredFields($data, [
            'name',
            'slug',
            'prices',
            'options',
            'content'
        ], 'product');

        $this->validatePricing($data['prices']);
        $this->validateOptions($data['options']);
        $this->validateContent($data['content']);

        $this->logInfo("Product data validation successful");
    }

    /**
     * Validate required fields in data
     *
     * @param array $data Data to validate
     * @param array $requiredFields Required field names
     * @param string $context Context for error messages
     * @throws RuntimeException When validation fails
     */
    private function validateRequiredFields(array $data, array $requiredFields, string $context): void
    {
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $message = sprintf("Missing required field '%s' in %s data", $field, $context);
                $this->logError($message);
                throw new RuntimeException($message);
            }
        }
    }

    /**
     * Validate product pricing structure
     *
     * @param array $pricing Pricing data to validate
     * @throws RuntimeException When validation fails
     */
    private function validatePricing(array $pricing): void
    {
        $this->logInfo("Validating product pricing");

        if (!isset($pricing['one_time']) && !isset($pricing['subscription'])) {
            throw new RuntimeException(
                'Product must have at least one price type (one_time or subscription)'
            );
        }

        if (isset($pricing['one_time'])) {
            $this->validatePriceData($pricing['one_time'], 'one_time');
        }

        if (isset($pricing['subscription'])) {
            $this->validatePriceData($pricing['subscription'], 'subscription');
        }

        $this->logInfo("Pricing validation successful");
    }

    /**
     * Validate price data structure
     *
     * @param array $priceData Price data to validate
     * @param string $type Price type (one_time or subscription)
     * @throws RuntimeException When validation fails
     */
    private function validatePriceData(array $priceData, string $type): void
    {
        $this->logInfo(sprintf("Validating %s price data", $type));

        $requiredFields = ['amount', 'currency'];

        if ($type === 'subscription') {
            $requiredFields[] = 'interval';
        }

        $this->validateRequiredFields($priceData, $requiredFields, $type . ' price');

        if ($type === 'subscription' && !in_array($priceData['interval'], ['day', 'week', 'month', 'year'])) {
            throw new RuntimeException(
                sprintf("Invalid subscription interval: %s", $priceData['interval'])
            );
        }
    }

    /**
     * Validate product options structure
     *
     * @param array $options Options data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateOptions(array $options): void
    {
        $this->logInfo("Validating product options");

        // Validate quantities
        if (empty($options['quantities'])) {
            throw new RuntimeException('Product must have at least one quantity option');
        }

        foreach ($options['quantities'] as $quantity) {
            $this->validateRequiredFields($quantity, [
                'id',
                'name',
                'value'
            ], 'quantity option');
        }

        // Validate purchase types
        if (empty($options['purchase_types'])) {
            throw new RuntimeException('Product must have at least one purchase type');
        }

        $requiredPurchaseTypes = ['one_time', 'subscription'];
        foreach ($requiredPurchaseTypes as $type) {
            if (!isset($options['purchase_types'][$type])) {
                throw new RuntimeException(
                    sprintf('Missing required purchase type: %s', $type)
                );
            }
        }

        $this->logInfo("Options validation successful");
    }

    /**
     * Validate product content structure
     *
     * @param array $content Content data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateContent(array $content): void
    {
        $this->logInfo("Validating product content");

        // Required base fields
        $this->validateRequiredFields($content, [
            'description'
        ], 'product content');

        // Validate badges if present
        if (isset($content['badges'])) {
            foreach ($content['badges'] as $badge) {
                $this->validateRequiredFields($badge, [
                    'name',
                    'icon'
                ], 'product badge');
            }
        }

        $this->logInfo("Content validation successful");
    }

    /**
     * {@inheritdoc}
     */
    public function validateUserData(array $data): void
    {
        $this->logInfo("Validating user data");

        $this->validateRequiredFields($data, [
            'email',
            'password',
            'address'
        ], 'user');

        $this->validateAddress($data['address']);

        $this->logInfo("User data validation successful");
    }

    /**
     * Validate address structure
     *
     * @param array $address Address data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateAddress(array $address): void
    {
        $this->logInfo("Validating address data");

        $this->validateRequiredFields($address, [
            'first_name',
            'last_name',
            'address1',
            'city',
            'country',
            'postcode'
        ], 'address');

        $this->logInfo("Address validation successful");
    }

    /**
     * {@inheritdoc}
     */
    public function validateShippingData(array $data): void
    {
        $this->logInfo("Validating shipping data");

        $this->validateRequiredFields($data, [
            'method',
            'cost',
            'zones'
        ], 'shipping');

        foreach ($data['zones'] as $zone) {
            $this->validateRequiredFields($zone, [
                'code',
                'name',
                'countries'
            ], 'shipping zone');
        }

        $this->logInfo("Shipping data validation successful");
    }

    /**
     * {@inheritdoc}
     */
    public function validateSchema(array $data, string $schema): void
    {
        $this->logInfo(sprintf("Validating data against schema '%s'", $schema));

        if (!isset($this->schemas[$schema])) {
            throw new RuntimeException(sprintf("Schema '%s' not found", $schema));
        }

        $schemaData = $this->schemas[$schema];

        if (isset($schemaData['required'])) {
            $this->validateRequiredFields($data, $schemaData['required'], $schema);
        }

        if (isset($schemaData['properties'])) {
            foreach ($schemaData['properties'] as $property => $rules) {
                if (isset($data[$property])) {
                    $this->validateProperty($data[$property], $property, $rules, $schema);
                }
            }
        }

        $this->logInfo(sprintf("Schema validation successful for '%s'", $schema));
    }

    /**
     * Validate a property against schema rules
     *
     * @param mixed $value Property value
     * @param string $property Property name
     * @param array $rules Validation rules
     * @param string $schema Schema name
     * @throws RuntimeException When validation fails
     */
    private function validateProperty($value, string $property, array $rules, string $schema): void
    {
        if (isset($rules['type'])) {
            $this->validateType($value, $property, $rules['type'], $schema);
        }

        if (isset($rules['enum']) && !in_array($value, $rules['enum'])) {
            throw new RuntimeException(sprintf(
                "Invalid value for property '%s' in schema '%s'. Expected one of: %s",
                $property,
                $schema,
                implode(', ', $rules['enum'])
            ));
        }

        if (isset($rules['minLength']) && is_string($value) && strlen($value) < $rules['minLength']) {
            throw new RuntimeException(sprintf(
                "Property '%s' in schema '%s' must be at least %d characters long",
                $property,
                $schema,
                $rules['minLength']
            ));
        }

        if (isset($rules['maxLength']) && is_string($value) && strlen($value) > $rules['maxLength']) {
            throw new RuntimeException(sprintf(
                "Property '%s' in schema '%s' must be at most %d characters long",
                $property,
                $schema,
                $rules['maxLength']
            ));
        }

        if (isset($rules['minimum']) && is_numeric($value) && $value < $rules['minimum']) {
            throw new RuntimeException(sprintf(
                "Property '%s' in schema '%s' must be at least %s",
                $property,
                $schema,
                $rules['minimum']
            ));
        }

        if (isset($rules['maximum']) && is_numeric($value) && $value > $rules['maximum']) {
            throw new RuntimeException(sprintf(
                "Property '%s' in schema '%s' must be at most %s",
                $property,
                $schema,
                $rules['maximum']
            ));
        }
    }

    /**
     * Validate value type
     *
     * @param mixed $value Value to validate
     * @param string $property Property name
     * @param string $expectedType Expected type
     * @param string $schema Schema name
     * @throws RuntimeException When validation fails
     */
    private function validateType($value, string $property, string $expectedType, string $schema): void
    {
        $actualType = gettype($value);

        // Map PHP types to JSON Schema types
        $typeMap = [
            'boolean' => 'boolean',
            'integer' => 'integer',
            'double' => 'number',
            'string' => 'string',
            'array' => 'array',
            'object' => 'object',
            'NULL' => 'null'
        ];

        $actualJsonType = $typeMap[$actualType] ?? $actualType;

        if ($actualJsonType !== $expectedType) {
            throw new RuntimeException(sprintf(
                "Invalid type for property '%s' in schema '%s'. Expected '%s', got '%s'",
                $property,
                $schema,
                $expectedType,
                $actualJsonType
            ));
        }
    }
}
