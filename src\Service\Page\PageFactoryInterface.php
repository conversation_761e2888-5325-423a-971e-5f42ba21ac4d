<?php

namespace App\Service\Page;

use App\Page\Base\BasePageInterface;

/**
 * Interface for the page factory service.
 */
interface PageFactoryInterface
{
    /**
     * Create a page object
     *
     * @param string $pageClass Page class name
     * @param array $parameters Constructor parameters
     * @return BasePageInterface
     */
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface;

    /**
     * Get a page object by name
     *
     * @param string $pageName Page name (e.g., 'HomePage')
     * @param array $parameters Constructor parameters
     * @return BasePageInterface
     */
    public function getPage(string $pageName, array $parameters = []): BasePageInterface;

    /**
     * Check if a page exists
     *
     * @param string $pageName Page name
     * @return bool
     */
    public function hasPage(string $pageName): bool;
}
