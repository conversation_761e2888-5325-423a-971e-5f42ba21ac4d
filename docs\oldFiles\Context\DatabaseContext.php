<?php

namespace Features\Bootstrap\Context;

use Features\Bootstrap\Core\ConfigurationManager;
use Features\Bootstrap\Helper\DatabaseHelper;
use RuntimeException;
use Throwable;

/**
 * Handles database interactions and verifications
 */
class DatabaseContext extends BaseContext
{
    private DatabaseHelper $dbHelper;
    private TestDataContext $testDataContext;
    private ConfigurationManager $configManager;

    /**
     * @param DatabaseHelper $dbHelper Helper for database operations
     * @param TestDataContext $testDataContext Context for test data access
     * @param ConfigurationManager $configManager Configuration manager
     */
    public function __construct(
        DatabaseHelper       $dbHelper,
        TestDataContext      $testDataContext,
        ConfigurationManager $configManager
    )
    {
        parent::__construct();
        $this->dbHelper = $dbHelper;
        $this->testDataContext = $testDataContext;
        $this->configManager = $configManager;

        $this->logInfo(sprintf(
            'Initialized DatabaseContext for brand "%s" in environment "%s"',
            $this->configManager->getCurrentBrand(),
            $this->configManager->getCurrentEnvironment()
        ));
    }

    /**
     * @Then /^I should see that the order status is "([^"]*)" in the database$/
     * @throws RuntimeException
     */
    public function iShouldSeeThatTheOrderStatusIsInTheDatabase(string $expectedStatus): void
    {
        try {
            $this->theOrderStatusShouldBeInTheDatabase($expectedStatus);
            $this->logInfo(sprintf('Verified order status is "%s" in database', $expectedStatus));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify order status "%s"', $expectedStatus), $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see that order status is not "([^"]*)" in the database$/
     * @throws RuntimeException
     */
    public function iShouldSeeThatOrderStatusIsNotInTheDatabase(string $unexpectedStatus): void
    {
        try {
            $orderId = $this->stateService->get('order.number');
            if (!$orderId) {
                throw new RuntimeException('No order number found in shared context');
            }

            $actualStatus = $this->dbHelper->getOrderStatus($orderId);
            if ($actualStatus === $unexpectedStatus) {
                throw new RuntimeException(
                    sprintf('Order status should not be "%s", but it is', $unexpectedStatus)
                );
            }

            $this->logInfo(sprintf('Verified order status is not "%s" (actual: "%s")', $unexpectedStatus, $actualStatus));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify order status is not "%s"', $unexpectedStatus), $e);
            throw $e;
        }
    }

    /**
     * @Then /^I should see that cart was cancelled (\d+) times with different order ids$/
     * @throws RuntimeException
     */
    public function iShouldSeeThatCartWasCancelledTimesWithDifferentOrderIds(int $expectedCount): void
    {
        try {
            // Get test user email from test data
            $testUser = $this->testDataContext->getTestUser('default');
            $userEmail = $testUser['email'];

            if (!$userEmail) {
                throw new RuntimeException('No user email found in test data');
            }

            $cancelledCount = $this->dbHelper->getCancelledOrderCount($userEmail);
            if ($cancelledCount !== $expectedCount) {
                throw new RuntimeException(
                    sprintf('Expected %d cancelled orders, but found %d', $expectedCount, $cancelledCount)
                );
            }

            // Verify orders have different IDs
            $orders = $this->dbHelper->getOrdersByEmail($userEmail, 'cancelled');
            $orderIds = array_column($orders, 'number');
            $uniqueOrderIds = array_unique($orderIds);

            if (count($uniqueOrderIds) !== $expectedCount) {
                throw new RuntimeException(
                    sprintf('Expected %d unique order IDs, but found %d', $expectedCount, count($uniqueOrderIds))
                );
            }

            $this->logInfo(sprintf('Verified %d cancelled orders with unique IDs', $expectedCount));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify %d cancelled orders', $expectedCount), $e);
            throw $e;
        }
    }

    /**
     * Finds a user by email in the database
     *
     * @param string $email User email to find
     * @return array|null User data if found, null otherwise
     * @throws RuntimeException When database query fails
     */
    public function findUserByEmail(string $email): ?array
    {
        try {
            $sql = "SELECT * FROM sylius_shop_user WHERE email = ?";
            $result = $this->dbHelper->executeQuery($sql, [$email]);
            $user = $result->fetch() ?: null;

            if ($user) {
                $this->logInfo(sprintf('Found user with email "%s"', $email));
            } else {
                $this->logInfo(sprintf('User with email "%s" not found', $email));
            }

            return $user;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to find user by email "%s"', $email), $e);
            throw new RuntimeException(
                sprintf('Failed to find user by email "%s": %s', $email, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets the current order ID from the database
     *
     * @return string Order ID
     * @throws RuntimeException When order not found or query fails
     * @sets order.number
     */
    public function getCurrentOrderId(): string
    {
        try {
            $userEmail = $this->stateService->get('user.email');
            if (!$userEmail) {
                throw new RuntimeException('No user email found in shared context');
            }

            $sql = "SELECT number FROM sylius_order 
                    WHERE customer_id = (SELECT id FROM sylius_customer WHERE email = ?)
                    ORDER BY created_at DESC LIMIT 1";

            $result = $this->dbHelper->executeQuery($sql, [$userEmail]);
            $orderId = $result->fetchColumn();

            if (!$orderId) {
                throw new RuntimeException('No order found for current user');
            }

            // Store in shared data for other contexts to use
            $this->stateService->set('order.number', $orderId);
            $this->logInfo(sprintf('Retrieved current order ID: %s', $orderId));

            return $orderId;
        } catch (Throwable $e) {
            $this->logError('Failed to get current order ID', $e);
            throw new RuntimeException(
                sprintf('Failed to get current order ID: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets the order status from the database
     *
     * @param string $orderId Order ID to check
     * @return string Order status
     * @throws RuntimeException When order not found or query fails
     */
    public function getOrderStatus(string $orderId): string
    {
        try {
            $sql = "SELECT state FROM sylius_order WHERE number = ?";
            $result = $this->dbHelper->executeQuery($sql, [$orderId]);
            $status = $result->fetchColumn();

            if ($status === false) {
                throw new RuntimeException(sprintf('Order "%s" not found', $orderId));
            }

            $this->logInfo(sprintf('Retrieved status "%s" for order "%s"', $status, $orderId));
            return $status;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get status for order "%s"', $orderId), $e);
            throw new RuntimeException(
                sprintf('Failed to get status for order "%s": %s', $orderId, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets the count of cancelled orders for a user
     *
     * @param string $userEmail User email to check
     * @return int Count of cancelled orders
     * @throws RuntimeException When query fails
     */
    public function getCancelledOrderCount(string $userEmail): int
    {
        try {
            $sql = "SELECT COUNT(*) FROM sylius_order 
                    WHERE customer_id = (SELECT id FROM sylius_customer WHERE email = ?)
                    AND state = 'cancelled'";

            $result = $this->dbHelper->executeQuery($sql, [$userEmail]);
            $count = (int)$result->fetchColumn();

            $this->logInfo(sprintf('Found %d cancelled orders for user "%s"', $count, $userEmail));
            return $count;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get cancelled order count for user "%s"', $userEmail), $e);
            throw new RuntimeException(
                sprintf('Failed to get cancelled order count for user "%s": %s', $userEmail, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets orders by email and status
     *
     * @param string $email User email
     * @param string $status Order status to filter by
     * @return array Orders matching the criteria
     * @throws RuntimeException When query fails
     */
    public function getOrdersByEmail(string $email, string $status): array
    {
        try {
            $sql = "SELECT number, state, created_at 
                    FROM sylius_order 
                    WHERE customer_id = (SELECT id FROM sylius_customer WHERE email = ?)
                    AND state = ?
                    ORDER BY created_at DESC";

            $result = $this->dbHelper->executeQuery($sql, [$email, $status]);
            $orders = $result->fetchAll();

            $this->logInfo(sprintf('Found %d orders with status "%s" for user "%s"', count($orders), $status, $email));
            return $orders;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get orders for user "%s" with status "%s"', $email, $status), $e);
            throw new RuntimeException(
                sprintf('Failed to get orders for user "%s" with status "%s": %s', $email, $status, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Updates the order timestamp for testing purposes
     *
     * @param string $orderId Order ID to update
     * @param int $hoursDiff Hours to adjust the timestamp by (positive or negative)
     * @throws RuntimeException When update fails
     */
    public function updateOrderTimestamp(string $orderId, int $hoursDiff): void
    {
        try {
            $sql = "UPDATE sylius_order 
                    SET created_at = DATE_ADD(created_at, INTERVAL ? HOUR) 
                    WHERE number = ?";

            $this->dbHelper->executeQuery($sql, [$hoursDiff, $orderId]);
            $this->logInfo(sprintf('Updated timestamp for order "%s" by %d hours', $orderId, $hoursDiff));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to update timestamp for order "%s"', $orderId), $e);
            throw new RuntimeException(
                sprintf('Failed to update timestamp for order "%s": %s', $orderId, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Runs the abandoned cart cleanup job
     *
     * @throws RuntimeException When job fails
     */
    public function runCleanupJob(): void
    {
        try {
            $sql = "UPDATE sylius_order 
                    SET state = 'expired' 
                    WHERE state = 'abandoned' 
                    AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)";

            $result = $this->dbHelper->executeQuery($sql);
            $affectedRows = $result->rowCount();

            $this->logInfo(sprintf('Ran cleanup job, affecting %d abandoned orders', $affectedRows));
        } catch (Throwable $e) {
            $this->logError('Failed to run cleanup job', $e);
            throw new RuntimeException(
                sprintf('Failed to run cleanup job: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^the order status should be "([^"]*)" in the database$/
     * @throws RuntimeException
     */
    public function theOrderStatusShouldBeInTheDatabase(string $expectedStatus): void
    {
        try {
            $orderId = $this->stateService->get('order.number');
            if (!$orderId) {
                throw new RuntimeException('No order number found in shared context');
            }

            $actualStatus = $this->getOrderStatus($orderId);
            if ($actualStatus !== $expectedStatus) {
                throw new RuntimeException(
                    sprintf('Expected order status to be "%s", but got "%s"', $expectedStatus, $actualStatus)
                );
            }

            $this->logInfo(sprintf('Verified order "%s" has status "%s"', $orderId, $expectedStatus));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify order status is "%s"', $expectedStatus), $e);
            throw $e;
        }
    }

    /**
     * @Then /^the order status should not be "([^"]*)" in the database$/
     * @throws RuntimeException
     */
    public function theOrderStatusShouldNotBeInTheDatabase(string $unexpectedStatus): void
    {
        try {
            $orderId = $this->stateService->get('order.number');
            if (!$orderId) {
                throw new RuntimeException('No order number found in shared context');
            }

            $actualStatus = $this->getOrderStatus($orderId);
            if ($actualStatus === $unexpectedStatus) {
                throw new RuntimeException(
                    sprintf('Order status should not be "%s", but it is', $unexpectedStatus)
                );
            }

            $this->logInfo(sprintf('Verified order "%s" does not have status "%s" (actual: "%s")',
                $orderId, $unexpectedStatus, $actualStatus));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify order status is not "%s"', $unexpectedStatus), $e);
            throw $e;
        }
    }
} 