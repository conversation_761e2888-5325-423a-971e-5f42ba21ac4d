# Critical Sales Funnel Test Data
# This file contains all the data needed for high-priority sales funnel tests

brands:
  aeons:
    name: "Aeons"
    base_url: "https://aeonstest.info"
    admin_url: "https://aeonstest.info/admin"

products:
  total_harmony:
    name: "Total Harmony"
    slug: "aeons-total-harmony"
    prices:
      one_time:
        minimum: 49.95
        medium: 134.85
        maximum: 199.8
      subscription:
        minimum: 44.95
        medium: 121.23
        maximum: 179.25
    options:
      purchase_types:
        one_time: "One-Time Purchase"
        subscription: "Subscribe & Save"
      quantities:
        minimum:
          fullName: "1 Jar"
          numberOfItems: 1
        medium:
          fullName: "3 Jars"
          numberOfItems: 3
        maximum:
          fullName: "6 Jars"
          numberOfItems: 6

  ancient_roots:
    name: "Ancient Roots"
    slug: "aeons-ancient-roots-olive-oil"
    prices:
      one_time:
        minimum: 34.95
        medium: 89.95
        maximum: 167.7
      subscription:
        minimum: 31.85
        medium: 80.85
        maximum: 150.9
    options:
      purchase_types:
        one_time: "One-Time Purchase"
        subscription: "Subscribe & Save"
      quantities:
        minimum:
          fullName: "1 Bottle"
          numberOfItems: 1
        medium:
          fullName: "3 Bottles"
          numberOfItems: 3
        maximum:
          fullName: "6 Bottles"
          numberOfItems: 6

# Original test data structure from features/bootstrap/fixtures/brands/aeons/test_data.yml
test_users:
  default:
    email: "<EMAIL>"
    first_name: "Alice"
    last_name: "Johnson"
    phone: "1234567890"
    address: "789 Oak St"
    city: "Manchester"
    postcode: "M1 1AA"
    country: "GB"

product_options:
  ancient_roots:
    flavors:
      classic:
        name: "Classic"
        description: "Our best-seller"
      lemon:
        name: "Lemon"
        description: "Zesty and fresh"
      truffle:
        name: "Truffle"
        description: "Earthy and rich"

payment_methods:
  stripe_valid:
    card_number: "****************"
    expiry: "12/26"
    cvc: "123"
  stripe_valid_3dsecure:
    card_number: "****************"
    expiry: "12/26"
    cvc: "123"
  stripe_expired:
    card_number: "****************"
    expiry: "12/20"
    cvc: "123"
  stripe_invalid:
    card_number: "****************"
    expiry: "12/26"
    cvc: "123"
  paypal_sandbox:
    username: "<EMAIL>"
    password: "Ll<9/%n:"
  paypal_invalid:
    username: "invalid_username"
    password: "invalid_password"

shipping_methods:
  UK:
    method: "Domestic tracked"
    cost: "2.95"
  US:
    method: "International tracked"
    cost: "9.99"
  EU:
    method: "European tracked"
    cost: "5.99"

funnel_configurations:
  default:
    shipping_threshold: 100.00
    free_shipping_regions: [ "GB", "US" ]

  variants:
    one_time:
      medium:
        variant_id: 72
    subscription:
      medium:
        variant_id: 73

funnel_items:
  total_harmony_basic:
    entry:
      url: "total-harmony-funnel"
      product: "total_harmony"
      quantity: "medium"
      purchase_type: "one_time"
    upsell:
      url: "total-harmony-upsell"
      product: "ancient_roots"
      quantity: "minimum"
      purchase_type: "one_time"
  ancient_roots_small:
    entry:
      url: "ancient-roots-small"
      product: "ancient_roots"
      quantity: "minimum"
      purchase_type: "one_time"
    upsell:
      url: "ancient-roots-golden-harvest"
      product: "golden_harvest"
      quantity: "minimum"
      purchase_type: "one_time"

  natures_gift_basic:
    entry:
      url: "natures-gift-basic"
      product: "natures_gift_bone_broth"
      quantity: "minimum"
      purchase_type: "one_time"
    upsell:
      url: "natures-gift-golden-harvest"
      product: "golden_harvest"
      quantity: "minimum"
      purchase_type: "one_time"
