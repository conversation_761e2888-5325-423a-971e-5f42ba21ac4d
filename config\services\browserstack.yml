# BrowserStack service configuration
services:
  # Default configuration for all services
  _defaults:
    autowire: true
    autoconfigure: true
    public: true

  # BrowserStack Browser Service
  App\Service\Browser\BrowserServiceInterface:
    class: App\Service\Browser\BrowserStackBrowserService
    arguments:
      $screenshotsDir: '%app.screenshots_dir%'
      $logger: '@logger'

  # BrowserStack capabilities builder
  App\Service\Browser\BrowserStack\BrowserStackCapabilitiesBuilder:
    arguments:
      $logger: '@logger'

  # BrowserStack session factory
  App\Service\Browser\BrowserStack\BrowserStackSessionFactory:
    arguments:
      $logger: '@logger'

  # BrowserStack status reporter
  App\Service\Browser\BrowserStack\BrowserStackStatusReporter:
    arguments:
      $logger: '@logger'

  # Legacy service alias for backward compatibility
  browser.service:
    alias: App\Service\Browser\BrowserServiceInterface
