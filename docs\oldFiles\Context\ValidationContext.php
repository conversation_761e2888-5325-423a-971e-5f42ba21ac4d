<?php

namespace Features\Bootstrap\Context;

use ElementNotFoundException;
use Exception;
use Features\Bootstrap\ContextManager;
use Features\Bootstrap\Page\CartPage;
use Features\Bootstrap\Page\CheckoutPage;
use Features\Bootstrap\Page\ConfirmationPage;
use Features\Bootstrap\Page\ProductPage;
use App\Service\State\SharedStateServiceInterface;
use RuntimeException;

class ValidationContext extends BaseContext
{
    private ProductPage $productPage;
    private CartPage $cartPage;
    private CheckoutPage $checkoutPage;
    private ConfirmationPage $confirmationPage;

    public function __construct(
        ProductPage $productPage,
        CartPage $cartPage,
        CheckoutPage $checkoutPage,
        ConfirmationPage $confirmationPage
    ) {
        parent::__construct();
        $this->productPage = $productPage;
        $this->cartPage = $cartPage;
        $this->checkoutPage = $checkoutPage;
        $this->confirmationPage = $confirmationPage;
    }

    public function setContextManager(ContextManager|\Features\Bootstrap\Core\ContextManager $contextManager): void
    {
        $this->contextManager = $contextManager;
    }

    /**
     * Gets the current product data or throws an exception
     *
     * @throws RuntimeException
     */
    private function getCurrentProductData(): array
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('No product data loaded');
        }
        return $productData;
    }


    /**
     * @Then /^I verify product restrictions are displayed$/
     * @throws RuntimeException
     */
    public function iVerifyProductRestrictionsDisplayed(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            if (!isset($productData['restrictions'])) {
                throw new RuntimeException('Product restrictions data not found');
            }

            foreach ($productData['restrictions'] as $restriction) {
                if (!$this->productPage->isRestrictionDisplayed($restriction)) {
                    throw new RuntimeException(
                        sprintf('Restriction not displayed: %s', $restriction)
                    );
                }
            }
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify product restrictions: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify dietary restriction warnings are displayed$/
     * @throws RuntimeException
     */
    public function iVerifyDietaryRestrictionWarningsAreDisplayed(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            if (!isset($productData['dietary_warnings'])) {
                throw new RuntimeException('Dietary warnings data not found');
            }

            foreach ($productData['dietary_warnings'] as $warning) {
                if (!$this->productPage->isDietaryWarningDisplayed($warning)) {
                    throw new RuntimeException(
                        sprintf('Dietary warning not displayed: %s', $warning)
                    );
                }
            }
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify dietary warnings: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify product instructions contain all warnings$/
     * @throws RuntimeException
     */
    public function iVerifyProductInstructionsContainAllWarnings(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            if (!isset($productData['warnings'])) {
                throw new RuntimeException('Product warnings data not found');
            }

            foreach ($productData['warnings'] as $warning) {
                if (!$this->productPage->isWarningInInstructions($warning)) {
                    throw new RuntimeException(
                        sprintf('Warning not found in instructions: %s', $warning)
                    );
                }
            }
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify product warnings: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify the cart contains the correct product details$/
     * @throws RuntimeException
     */
    public function iVerifyTheCartContainsTheCorrectProductDetails(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            $selectedQuantity = $this->stateService->get('selectedQuantity');
            if (!$selectedQuantity) {
                throw new RuntimeException('Selected quantity data not found');
            }

            $selectedPurchaseOption = $this->stateService->get('selectedPurchaseOption');
            if (!$selectedPurchaseOption) {
                throw new RuntimeException('Selected purchase option not found');
            }

            if (!$this->cartPage->verifyProductDetails($productData, $selectedQuantity, $selectedPurchaseOption)) {
                throw new RuntimeException('Product details in cart do not match selected options');
            }
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify cart product details: ' . $e->getMessage());
        }
    }


    /**
     * @Then /^I verify the order total is correct$/
     * @throws RuntimeException
     */
    public function iVerifyTheOrderTotalIsCorrect(): void
    {
        try {
            $expectedTotal = $this->stateService->get('expectedTotal');
            if (!$expectedTotal) {
                throw new RuntimeException('Expected total not found in shared data');
            }

            $actualTotal = $this->cartPage->getOrderTotal();
            if ($expectedTotal !== $actualTotal) {
                throw new RuntimeException(
                    sprintf('Expected order total %s but got %s', $expectedTotal, $actualTotal)
                );
            }
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify order total: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify product content matches "([^"]*)"$/
     */
    public function iVerifyProductContentMatches(string $contentKey): void
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('No product data loaded');
        }

        if (!isset($productData['content'][$contentKey])) {
            throw new RuntimeException(sprintf('Content key "%s" not found in product data', $contentKey));
        }

        $expectedContent = $productData['content'][$contentKey];
        $actualContent = $this->getPage()->find('css', '.product-' . $contentKey)->getText();

        Assert::assertEquals($expectedContent, $actualContent, sprintf('Product %s does not match', $contentKey));
    }

    /**
     * @Then /^I verify product badges are present:$/
     */
    public function iVerifyProductBadgesArePresent(array $badges): void
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('No product data loaded');
        }

        $expectedBadges = $productData['content']['badges'] ?? [];
        Assert::assertEquals($badges, $expectedBadges, 'Product badges do not match');
    }

    /**
     * @Then /^I verify product FAQs are present:$/
     */
    public function iVerifyProductFAQsArePresent(array $faqs): void
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('No product data loaded');
        }

        $expectedFAQs = $productData['content']['faqs'] ?? [];
        Assert::assertEquals($faqs, $expectedFAQs, 'Product FAQs do not match');
    }

    /**
     * @Then /^I verify product pricing is correct:$/
     */
    public function iVerifyProductPricingIsCorrect(array $pricing): void
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('No product data loaded');
        }

        $expectedPricing = $productData['prices'] ?? [];
        Assert::assertEquals($pricing, $expectedPricing, 'Product pricing does not match');
    }

    /**
     * @Then /^I verify product images are loaded$/
     */
    public function iVerifyProductImages(): void
    {
        $page = $this->getSession()->getPage();
        $images = $page->findAll('css', '.product-image');

        foreach ($images as $image) {
            $src = $image->getAttribute('src');
            $response = $this->getHttpClient()->head($src);

            if ($response->getStatusCode() === 404) {
                $this->addWarning(sprintf('Product image not found: %s', $src));
                continue;
            }

            Assert::assertEquals(200, $response->getStatusCode());
        }
    }

    /**
     * @Then /^I verify the URL is "([^"]*)"$/
     */
    public function iVerifyTheUrlIs($expectedUrl): void
    {
        $actualUrl = $this->getSession()->getCurrentUrl();
        Assert::assertEquals($expectedUrl, $actualUrl, "Expected URL '$expectedUrl', but found '$actualUrl'");
    }

    /**
     * @Then /^the purchase option "([^"]*)" should be selected$/
     */
    public function verifySelectedPurchaseOption(string $expectedOption): void
    {
        $selectedOption = $this->productPage->getSelectedPurchaseOption();
        Assert::assertEquals(
            $expectedOption,
            $selectedOption,
            sprintf('Expected purchase option "%s" but got "%s"', $expectedOption, $selectedOption)
        );
    }

    /**
     * @Then /^I verify Google Tag Manager is present/
     * @throws \Exception
     */
    public function iVerifyGoogleTagManagerIsPresent(): void
    {
        // Get current page object based on URL
        $currentUrl = $this->getSession()->getCurrentUrl();

        $currentPage = match (true) {
            str_contains($currentUrl, '/products/') => $this->productPage,
            str_contains($currentUrl, '/cart') => $this->cartPage,
            str_contains($currentUrl, '/checkout') => $this->checkoutPage,
            str_contains($currentUrl, '/order/thank-you') => $this->confirmationPage,
            default => throw new \Exception('Unable to determine current page for GTM verification')
        };

        try {
            $currentPage->verifyGoogleTagManager();
        } catch (\Exception $e) {
            throw new \Exception('GTM Verification failed: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify the sum of products is calculated correctly$/
     */
    public function iVerifySumOfProducts(): void
    {
        $displayedTotal = $this->productPage->getDisplayedTotal();
        $selectedSizeOption = $this->productPage->getSelectedSizeOption();
        $quantity = $this->productPage->getProductQuantity();
        $pricingMode = $this->productPage->getPricingMode();

        // Calculate expected total
        $expectedTotal = $this->calculateExpectedTotal($selectedSizeOption, $quantity, $pricingMode);

        // Assert that the displayed total matches the expected total
        Assert::assertEquals(
            $expectedTotal,
            $displayedTotal,
            "Displayed total does not match expected total."
        );
    }

    /**
     * Calculates the expected total based on size option, quantity, and pricing mode.
     *
     * @param string $sizeOption The size option selected.
     * @param int $quantity The product quantity.
     * @param string $mode The pricing mode.
     *
     * @return string The expected total formatted with currency symbol.
     * @throws \Exception If the size option is unknown.
     */
    private function calculateExpectedTotal(string $sizeOption, int $quantity, string $mode): string
    {
        // Define the unit price based on product size and mode
        $unitPrice = match ($sizeOption) {
            '1 Jar' => $mode === 'One-Time Purchase' ? 69.95 : 49.95,
            '3 Jars' => $mode === 'One-Time Purchase' ? 59.95 : 39.95,
            '6 Jars' => $mode === 'One-Time Purchase' ? 49.95 : 29.95,
            default => throw new \Exception('Unknown product size: ' . $sizeOption),
        };

        // Calculate the expected total
        $expectedTotal = number_format($unitPrice * $quantity, 2);

        // Return the expected total with currency symbol
        return '£' . $expectedTotal;
    }


    /**
     * @Then /^I verify the order details are correct$/
     */
    public function iVerifyTheOrderDetailsAreCorrect(): void
    {
        $sharedData = this->stateService->getAll();
        $shippingInfo = $sharedData['shippingInfo'] ?? [];

        if (empty($shippingInfo)) {
            throw new \Exception('Shipping information not found in shared data');
        }

        // Get displayed address from confirmation page
        $displayedAddress = $this->confirmationPage->getShippingAddress();

        // Create expected address in the same format as displayed
        $expectedAddress = implode("\n", [
            "{$shippingInfo['first_name']} {$shippingInfo['last_name']}", // Name
            $shippingInfo['phone'],                                        // Phone
            $shippingInfo['address'],                                      // Street
            "{$shippingInfo['city']}, {$shippingInfo['postcode']}",       // City and Postcode
            strtoupper($this->mapCountryCodeToName($shippingInfo['country']))  // Country
        ]);

        // Clean up both addresses for comparison
        $displayedAddress = $this->cleanupAddressText($displayedAddress);
        $expectedAddress = $this->cleanupAddressText($expectedAddress);

        // Compare full addresses
        Assert::assertEquals(
            $expectedAddress,
            $displayedAddress,
            "Shipping address mismatch.\nExpected:\n$expectedAddress\nActual:\n$displayedAddress"
        );
    }

    /**
     * Cleans up address text for comparison
     *
     * @param string $address
     * @return string
     */
    private function cleanupAddressText(string $address): string
    {
        // Remove any HTML
        $address = strip_tags($address);

        // Normalize line endings
        $address = str_replace(["\r\n", "\r"], "\n", $address);

        // Remove multiple spaces
        $address = preg_replace('/\s+/', ' ', $address);

        // Clean up each line
        $lines = array_map(function ($line) {
            return trim($line);
        }, explode("\n", $address));

        // Remove empty lines
        $lines = array_filter($lines);

        // Rejoin with consistent line endings
        return implode("\n", $lines);
    }


    /**
     * @Given /^I memorize the page title$/
     * @Then /^I verify the page title is "([^"]*)"$/
     */
    public function handlePageTitle(?string $expectedTitle = null): void
    {
        $actualTitle = $this->homePage->getPageTitle();

        if ($expectedTitle !== null) {
            Assert::assertEquals(
                $expectedTitle,
                $actualTitle,
                "Expected page title '$expectedTitle', but found '$actualTitle'"
            );
        } else {
            this->stateService->set('pageTitle', $actualTitle);
        }
    }

    /**
     * @When /^I memorize the product details$/
     * @Then /^I verify product details match "([^"]*)"$/
     */
    public function handleProductDetails(?string $expectedProduct = null): void
    {
        $details = [
            'productName' => $this->productPage->getProductName(),
            'purchaseType' => $this->productPage->getSelectedPurchaseOption(),
            'quantity' => $this->productPage->getQuantity(),
            'price' => $this->productPage->getCurrentPrice(),
            'subscriptionFrequency' => $this->productPage->getSubscriptionFrequency()
        ];

        if ($expectedProduct !== null) {
            $expectedDetails = this->stateService->get($expectedProduct);
            foreach ($details as $key => $value) {
                if (isset($expectedDetails[$key])) {
                    Assert::assertEquals(
                        $expectedDetails[$key],
                        $value,
                        sprintf("Product %s mismatch. Expected: %s, Got: %s",
                            $key,
                            $expectedDetails[$key],
                            $value
                        )
                    );
                }
            }
        } else {
            this->stateService->setMultiple($details);
        }
    }

    //Page Acceptance test steps

    /**
     * @Then /^I verify that product content matches configuration$/
     */
    public function iVerifyProductContentMatchesConfiguration(): void
    {
        $productData = this->stateService->get('currentProduct');
        $errors = [];

        // Collect all verification errors
        $errors = array_merge(
            $errors,
            $this->collectProductBasicInfoErrors($productData),
            $this->collectPricingOptionsErrors($productData),
            $this->collectProductBadgesErrors($productData)
        );

        // Check subscription benefits if available
        if (isset($productData['content']['subscription_benefits'])) {
            $errors = array_merge($errors, $this->collectSubscriptionBenefitsErrors($productData));
        }

        // If we collected any errors, throw them all at once
        if (!empty($errors)) {
            throw new Exception("Found content verification errors:\n" . implode("\n", $errors));
        }
    }

    private function collectProductBasicInfoErrors(array $productData): array
    {
        $errors = [];

        // Check product name
        if ($productData['name'] !== $this->productPage->getProductName()) {
            $errors[] = sprintf(
                "Product name mismatch:\nExpected: %s\nActual: %s",
                $productData['name'],
                $this->productPage->getProductName()
            );
        }

        // Check category
        if (isset($productData['meta']['category'])
            && $productData['meta']['category'] !== $this->productPage->getProductSubtitle()
        ) {
            $errors[] = sprintf(
                "Product category mismatch:\nExpected: %s\nActual: %s",
                $productData['meta']['category'],
                $this->productPage->getProductSubtitle()
            );
        }

        // Check description
        if (isset($productData['content']['description'])
            && $productData['content']['description'] !== $this->productPage->getProductDescription()
        ) {
            $errors[] = sprintf(
                "Product description mismatch:\nExpected: %s\nActual: %s",
                $productData['content']['description'],
                $this->productPage->getProductDescription()
            );
        }

        return $errors;
    }

    private function collectPricingOptionsErrors(array $productData): array
    {
        $errors = [];
        $actualPricing = $this->productPage->getPricing();

        // Check one-time prices
        foreach (['minimum', 'medium', 'maximum'] as $pricePoint) {
            if (isset($productData['prices']['one_time'][$pricePoint]) &&
                isset($actualPricing['one_time'][$pricePoint])) {

                if ($productData['prices']['one_time'][$pricePoint] !== $actualPricing['one_time'][$pricePoint]) {
                    $errors[] = sprintf(
                        "One-time %s price mismatch:\nExpected: %.2f\nActual: %.2f",
                        $pricePoint,
                        $productData['prices']['one_time'][$pricePoint],
                        $actualPricing['one_time'][$pricePoint]
                    );
                }
            }
        }

        // Check subscription prices
        foreach (['minimum', 'medium', 'maximum'] as $pricePoint) {
            if (isset($productData['prices']['subscription'][$pricePoint]) &&
                isset($actualPricing['subscription'][$pricePoint])) {

                if ($productData['prices']['subscription'][$pricePoint] !== $actualPricing['subscription'][$pricePoint]) {
                    $errors[] = sprintf(
                        "Subscription %s price mismatch:\nExpected: %.2f\nActual: %.2f",
                        $pricePoint,
                        $productData['prices']['subscription'][$pricePoint],
                        $actualPricing['subscription'][$pricePoint]
                    );
                }
            }
        }

        return $errors;
    }

    private function collectProductBadgesErrors(array $productData): array
    {
        $errors = [];
        $expectedBadges = array_column($productData['content']['badges'], 'name');
        $actualBadges = $this->productPage->getProductBadges();

        sort($expectedBadges);
        sort($actualBadges);

        if ($expectedBadges !== $actualBadges) {
            $errors[] = sprintf(
                "Product badges mismatch:\nExpected: %s\nActual: %s",
                implode(', ', $expectedBadges),
                implode(', ', $actualBadges)
            );
        }

        return $errors;
    }

    private function collectSubscriptionBenefitsErrors(array $productData): array
    {
        $errors = [];
        $expectedBenefits = $productData['content']['subscription_benefits'];
        $actualBenefits = $this->productPage->getSubscriptionBenefits();

        sort($expectedBenefits);
        sort($actualBenefits);

        if ($expectedBenefits !== $actualBenefits) {
            $errors[] = sprintf(
                "Subscription benefits mismatch:\nExpected: %s\nActual: %s",
                implode(', ', $expectedBenefits),
                implode(', ', $actualBenefits)
            );
        }

        return $errors;
    }

    /**
     * @Then /^I verify that FAQ section is correct$/
     */
    public function iVerifyFAQSectionIsCorrect(): void
    {
        $productData = this->stateService->get('currentProduct');
        $expectedFAQs = $productData['content']['faq'];
        $actualFAQs = $this->productPage->getFAQs();

        foreach ($expectedFAQs as $index => $expectedFAQ) {
            Assert::assertEquals(
                $expectedFAQ['question'],
                $actualFAQs[$index]['question'],
                sprintf("FAQ question %d mismatch", $index + 1)
            );

            // Expand FAQ to check answer
            $this->productPage->expandFAQQuestion($index);

            Assert::assertEquals(
                $expectedFAQ['answer'],
                $actualFAQs[$index]['answer'],
                sprintf("FAQ answer %d mismatch", $index + 1)
            );
        }
    }

    /**
     * @Then /^I verify the funnel product details$/
     */
    public function iVerifyFunnelProductDetails(): void
    {
        $funnel = this->stateService->getCurrentFunnel();
        if (!$funnel) {
            throw new RuntimeException('No funnel data found in context');
        }

        $productData = $this->testDataContext->getProductData($funnel['entry']['product']);

        // Verify pre-selected quantity
        $actualQuantity = $this->productPage->getQuantity();
        Assert::assertEquals(
            $funnel['entry']['quantity'],
            $actualQuantity,
            'Pre-selected quantity does not match funnel configuration'
        );

        // Verify purchase type
        $actualPurchaseType = $this->productPage->getSelectedPurchaseOption();
        Assert::assertEquals(
            $funnel['entry']['purchase_type'],
            $actualPurchaseType,
            'Purchase type does not match funnel configuration'
        );
    }

    // /**
    //  * @Then /^I verify product restrictions are displayed$/
    //  */
    // public function iVerifyProductRestrictionsDisplayed(): void
    // {
    //     $funnel = this->stateService->getCurrentFunnel();
    //     $productData = $this->testDataContext->getProductData($funnel['entry']['product']);

    //     if (isset($productData['funnel']['restrictions'])) {
    //         foreach ($productData['funnel']['restrictions'] as $type => $restrictions) {
    //             if (is_array($restrictions)) {
    //                 foreach ($restrictions as $restriction) {
    //                     Assert::assertTrue(
    //                         $this->productPage->hasRestrictionWarning($restriction),
    //                         sprintf('Restriction warning "%s" not found', $restriction)
    //                     );
    //                 }
    //             }
    //         }
    //     }
    // }

    /**
     * @Then /^I verify the mixed cart order details are correct$/
     */
    public function iVerifyTheMixedCartOrderDetailsAreCorrect(): void
    {
        $orderDetails = $this->confirmationPage->getMixedCartOrderDetails();
        $sharedData = this->stateService;

        // Verify subscription items
        $subscriptionItems = array_filter($orderDetails, fn($item) => $item['type'] === 'subscription');
        Assert::assertEquals(2, count($subscriptionItems), 'Expected 2 subscription items');

        // Verify one-time items
        $oneTimeItems = array_filter($orderDetails, fn($item) => $item['type'] === 'one-time');
        Assert::assertEquals(1, count($oneTimeItems), 'Expected 1 one-time purchase item');

        // Verify frequencies match what was selected
        $frequencies = array_map(
            fn($item) => $item['frequency'],
            array_filter($orderDetails, fn($item) => isset($item['frequency']))
        );
        Assert::assertEquals(
            $sharedData->get('subscriptionFrequencies'),
            $frequencies,
            'Subscription frequencies do not match'
        );
    }


}
