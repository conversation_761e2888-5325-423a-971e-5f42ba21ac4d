Feature: Full Context Migration Test
  As a developer
  I want to verify that all migrated contexts work correctly together
  So that I can be confident in the architecture migration

  @context_migration @comprehensive @validation
  Scenario: Verify validation context
    Given I am using the "aeons" brand
    And I load test data for product "golden_harvest"
    And I am viewing the product "golden_harvest"
    Then I verify product content matches "description"
    And I verify product images are loaded
    And I verify Google Tag Manager is present
    When I select quantity 2
    Then I verify the sum of products is calculated correctly

  @context_migration @comprehensive @abandoned_cart
  Scenario: Verify abandoned cart context
    Given I am using the "aeons" brand
    And I load test data for product "golden_harvest"
    And I load test data for user "default"
    And I have an abandoned cart
    Then I should receive an abandoned cart email within "1" hour
    When I click the recovery link in the abandoned cart email
    Then I should see my cart with the abandoned items

  @context_migration @comprehensive @database
  Scenario: Verify database context
    Given I am using the "aeons" brand
    And I load test data for user "default"
    When I execute database query "SELECT COUNT(*) FROM sylius_customer WHERE email = '<EMAIL>'"
    Then the database query should return 1 rows
    And the database query result should contain "1"

  @context_migration @comprehensive @ssh
  Scenario: Verify SSH context
    Given I am using the "aeons" brand
    When I execute SSH command "echo 'SSH test successful'"
    Then the SSH command output should contain "SSH test successful"

  @context_migration @comprehensive @all_contexts
  Scenario: Complete purchase flow with all contexts
    Given I am using the "aeons" brand
    And I load test data for product "golden_harvest"
    And I load test data for user "default"
    And I am viewing the product "golden_harvest"
    When I select flavor "chocolate"
    And I select quantity 2
    And I add the product to cart
    Then I verify the cart contains the correct product details
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I select shipping method "standard"
    And I select payment method "credit_card"
    And I fill in credit card information with "visa" card
    And I confirm the order
    Then I should be on the order confirmation page
    And I verify the order details are correct
    And I should receive an order confirmation email
    And I execute database query "SELECT state FROM sylius_order ORDER BY created_at DESC LIMIT 1"
    And the database query result should contain "completed"
