# BrowserStack-specific service configuration
imports:
  - { resource: 'services.yml' }
  - { resource: 'parameters.browserstack.yml' }

parameters:
  # Screenshots directory
  app.screenshots_dir: '%app.project_root%/screenshots'

services:
  # Override browser service with BrowserStack implementation
  App\Service\Browser\BrowserServiceInterface:
    class: App\Service\Browser\BrowserStackBrowserService
    arguments:
      $screenshotsDir: '%app.screenshots_dir%'
      $logger: '@logger'
    public: true

  # BrowserStack capabilities builder
  App\Service\Browser\BrowserStack\BrowserStackCapabilitiesBuilder:
    arguments:
      $logger: '@logger'
    public: true

  # BrowserStack session factory
  App\Service\Browser\BrowserStack\BrowserStackSessionFactory:
    arguments:
      $logger: '@logger'
    public: true

  # BrowserStack status reporter
  App\Service\Browser\BrowserStack\BrowserStackStatusReporter:
    arguments:
      $logger: '@logger'
    public: true
