@migration @smoke @page_objects
Feature: Page Object Extension Migration - Smoke Tests
  As a test framework maintainer
  I want to verify basic page object functionality
  So that I can ensure the migration doesn't break core features

  Background:
    Given I am using a desktop device
    And I am on the homepage

  @page_instantiation
  Scenario: Verify page object instantiation
    When I create a new "HomePage" instance
    Then the page object should be properly initialized
    And the page URL should be correctly set
    And I should be able to access page elements

  @element_interaction
  Scenario: Verify basic element interaction
    When I locate the main navigation menu
    Then I should be able to click menu items
    And I should be able to get element text
    And I should be able to verify element visibility

  @session_handling
  Scenario: Verify session management
    When I navigate to the "ProductPage"
    Then the session should persist
    And I should be able to get the current URL
    And I should be able to refresh the page
    
  @ajax_handling
  Scenario: Verify AJAX interaction handling
    Given I am on the "ProductPage"
    When I trigger an AJAX request
    Then I should be able to wait for the response
    And the page should update accordingly
    
  @error_handling
  Scenario: Verify error handling
    When I attempt to access a non-existent element
    Then an appropriate error should be thrown
    And the error message should be descriptive
    And the test should fail gracefully 