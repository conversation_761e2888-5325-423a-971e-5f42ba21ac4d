<?php

namespace Features\Bootstrap\Context;

use Features\Bootstrap\Core\ConfigurationManager;
use Features\Bootstrap\Core\TestDataRegistry;
use RuntimeException;
use Throwable;

/**
 * Manages brand-specific behavior and configuration
 */
class BrandContext extends BaseContext
{
    private ConfigurationManager $configManager;
    private TestDataRegistry $testDataRegistry;
    private array $brandFeatures = [];

    /**
     * @param ConfigurationManager $configManager Configuration manager for brand settings
     * @param TestDataRegistry $testDataRegistry Registry for test data
     */
    public function __construct(ConfigurationManager $configManager, TestDataRegistry $testDataRegistry)
    {
        parent::__construct();
        $this->configManager = $configManager;
        $this->testDataRegistry = $testDataRegistry;
        
        // Load default brand features if brand is already set
        try {
            $this->loadBrandFeatures();
            $this->logInfo(sprintf('Loaded features for brand "%s"', $this->configManager->getCurrentBrand()));
        } catch (Throwable $e) {
            // Ignore errors since brand might not be set yet
            $this->logInfo('Brand features not loaded during initialization - brand might not be set yet');
        }
    }

    /**
     * Returns the current base URL
     *
     * @return string The base URL for the current environment
     * @throws RuntimeException When base URL cannot be retrieved
     */
    public function getBaseUrl(): string
    {
        try {
            $url = $this->configManager->getEnvironmentConfig('base_url');
            $this->logInfo(sprintf('Retrieved base URL: %s', $url));
            return $url;
        } catch (Throwable $e) {
            $this->logError('Failed to get base URL', $e);
            throw new RuntimeException('Failed to get base URL: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Switches the brand
     *
     * @param string $brand Brand identifier
     * @throws RuntimeException When brand switch fails
     */
    public function switchBrand(string $brand): void
    {
        try {
            $this->configManager->setBrand($brand);
            $this->loadBrandFeatures();
            $this->stateService->set('brand.current', $brand);
            $this->stateService->set('brand.features', $this->brandFeatures);
            $this->logInfo(sprintf('Switched to brand: %s', $brand));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to switch to brand "%s"', $brand), $e);
            throw new RuntimeException(
                sprintf('Failed to switch to brand "%s": %s', $brand, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given I am using the :brand brand
     * @sets brand.current, brand.features
     */
    public function iAmUsingTheBrand(string $brand): void
    {
        try {
            $this->configManager->setBrand($brand);
            $this->loadBrandFeatures();
            $this->stateService->set('brand.current', $brand);
            $this->stateService->set('brand.features', $this->brandFeatures);
            $this->logInfo(sprintf('Set current brand to: %s', $brand));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to set brand to "%s"', $brand), $e);
            throw new RuntimeException(
                sprintf('Failed to set brand to "%s": %s', $brand, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given I am using the :environment environment
     * @sets brand.environment
     */
    public function iAmUsingTheEnvironment(string $environment): void
    {
        try {
            $this->configManager->setEnvironment($environment);
            $this->stateService->set('brand.environment', $environment);
            $this->logInfo(sprintf('Set environment to: %s', $environment));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to set environment to "%s"', $environment), $e);
            throw new RuntimeException(
                sprintf('Failed to set environment to "%s": %s', $environment, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given the brand has the :feature feature enabled
     */
    public function theBrandHasFeatureEnabled(string $feature): void
    {
        try {
            if (!$this->hasFeature($feature)) {
                throw new RuntimeException(
                    sprintf('Feature "%s" is not enabled for brand "%s"',
                        $feature,
                        $this->configManager->getCurrentBrand()
                    )
                );
            }
            $this->logInfo(sprintf('Verified feature "%s" is enabled', $feature));
        } catch (Throwable $e) {
            $this->logError(sprintf('Feature verification failed for "%s"', $feature), $e);
            throw $e;
        }
    }

    /**
     * @Given the brand has the :feature feature disabled
     */
    public function theBrandHasFeatureDisabled(string $feature): void
    {
        try {
            if ($this->hasFeature($feature)) {
                throw new RuntimeException(
                    sprintf('Feature "%s" is enabled for brand "%s"',
                        $feature,
                        $this->configManager->getCurrentBrand()
                    )
                );
            }
            $this->logInfo(sprintf('Verified feature "%s" is disabled', $feature));
        } catch (Throwable $e) {
            $this->logError(sprintf('Feature verification failed for "%s"', $feature), $e);
            throw $e;
        }
    }

    /**
     * @Then I should be on the brand's homepage
     */
    public function iShouldBeOnBrandHomepage(): void
    {
        try {
            $expectedUrl = $this->configManager->getEnvironmentConfig('base_url');
            // Actual URL verification would be implemented here
            // This is just a placeholder for the example
            $this->logInfo(sprintf('Verified user is on brand homepage: %s', $expectedUrl));
        } catch (Throwable $e) {
            $this->logError('Failed to verify brand homepage', $e);
            throw new RuntimeException('Failed to verify brand homepage: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Gets brand configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     * @throws RuntimeException When configuration cannot be retrieved
     */
    public function getBrandConfig(string $key)
    {
        try {
            $value = $this->configManager->getBrandConfig($key);
            $this->logInfo(sprintf('Retrieved brand config for key "%s"', $key));
            return $value;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get brand config for key "%s"', $key), $e);
            throw new RuntimeException(
                sprintf('Failed to get brand config for key "%s": %s', $key, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets environment configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     * @throws RuntimeException When configuration cannot be retrieved
     */
    public function getEnvironmentConfig(string $key)
    {
        try {
            $value = $this->configManager->getEnvironmentConfig($key);
            $this->logInfo(sprintf('Retrieved environment config for key "%s"', $key));
            return $value;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get environment config for key "%s"', $key), $e);
            throw new RuntimeException(
                sprintf('Failed to get environment config for key "%s": %s', $key, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Checks if brand has a specific feature
     *
     * @param string $feature Feature name
     * @return bool Whether the feature is enabled
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->brandFeatures);
    }

    /**
     * Gets all enabled brand features
     *
     * @return array List of enabled features
     */
    public function getEnabledFeatures(): array
    {
        return $this->brandFeatures;
    }

    /**
     * Loads brand features from configuration
     *
     * @throws RuntimeException When features cannot be loaded
     */
    private function loadBrandFeatures(): void
    {
        try {
            $this->brandFeatures = $this->configManager->getBrandConfig('features');
        } catch (Throwable $e) {
            $this->logError('Failed to load brand features', $e);
            throw new RuntimeException('Failed to load brand features: ' . $e->getMessage(), 0, $e);
        }
    }
} 