# Step Definition Migration Registry

This document tracks the migration status of Behat step definitions from the legacy structure (`features/bootstrap/Context/` and `features/bootstrap/FeatureContext.php`) to the new architecture (`src/Context/`).

## Migration Status Legend

- ✅ **Migrated**: Step definition has been successfully migrated to the new architecture
- ⚠️ **Partially Migrated**: Step definition exists in new architecture but may need adjustments
- ❌ **Not Migrated**: Step definition has not been migrated yet
- 🔄 **Duplicate**: Step definition exists in both old and new architecture with similar functionality
- 🛑 **Deprecated**: Step definition that should not be migrated (obsolete)

## CartContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iProceedToCheckout()` | ✅ | `src/Context/CartContext.php` | `cartPage->proceedToCheckout()` | Implementation is similar, but new version uses PageFactory instead of direct page object injection |
| `addToCart()` | ❌ | N/A | N/A | This is a helper method, not a step definition. Should be migrated to ProductContext as it uses ProductPage |
| `iViewMyCart()` | ⚠️ | `src/Context/CartContext.php::iAmOnTheCartPage()` | `cartPage->open()` | Similar functionality but different step text. Consider standardizing |
| `iRemoveItemFromCart()` | ⚠️ | `src/Context/CartContext.php::iRemoveItemFromTheCart()` | `cartPage->removeItem()` | New version takes an index parameter, old version removes first item |
| `theCartShouldBeEmpty()` | ✅ | `src/Context/CartContext.php` | `cartPage->isEmpty()` | Implementation is similar |
| `theCartShouldContainItems()` | ✅ | `src/Context/CartContext.php::iShouldSeeItemsInTheCart()` | `cartPage->getItemCount()` | Similar functionality but different step text |

## ProductContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iSelectFlavor()` | ✅ | `src/Context/ProductContext.php` | `productPage->selectFlavor()` | Implementation is similar |
| `iAmOnTheSpecificProductPage()` | ⚠️ | `src/Context/ProductContext.php::iAmViewingTheProduct()` | `productPage->open()` | Different parameter (productSlug vs productName) and implementation |
| `iSelectTheQuantityOption()` | ✅ | `src/Context/ProductContext.php::iSelectQuantity()` | `productPage->selectQuantity()` | Implementation is similar but parameter type differs (string vs int) |
| `iSetTheSupplyTo()` | ❌ | N/A | N/A | Not migrated yet. Handles subscription frequency |
| `userSubscribesToProduct()` | ❌ | N/A | N/A | Not migrated yet. Handles subscription selection |
| `userSelectsPurchaseType()` | ⚠️ | `src/Context/ProductContext.php::iSelectPurchaseType()` | `productPage->selectPurchaseType()` | Similar functionality but different implementation |
| `expectedSumOfProductsShouldBeCalculatedCorrectly()` | ❌ | N/A | N/A | Not migrated yet. Verifies product sum calculation |
| `iVerifyProductContentMatchesConfiguration()` | ❌ | N/A | N/A | Not migrated yet. Complex validation logic |
| `iSelectThePurchaseType()` | 🔄 | `src/Context/ProductContext.php::iSelectPurchaseType()` | Different implementations | Duplicate functionality with different implementations |
| `iSelectQuantity()` | 🔄 | `src/Context/ProductContext.php::iSelectQuantity()` | `productPage->selectQuantity()` | Duplicate with different parameter handling |
| `iAmOnTheProductPage()` | ⚠️ | `src/Context/ProductContext.php::iAmViewingTheProduct()` | Different implementations | Similar functionality but different implementation |

## BrandContext

| Legacy Step Definition        | Migration Status | New Location                   | Page Object Call | Comments                                  |
|-------------------------------|------------------|--------------------------------|------------------|-------------------------------------------|
| `iAmUsingTheBrand()`          | ✅                | `src/Context/BrandContext.php` | N/A              | Fully implemented in BrandContext         |
| `iLoadBrandConfiguration()`   | ✅                | `src/Context/BrandContext.php` | N/A              | Fully implemented in BrandContext         |
| `theBrandHasFeatureEnabled()` | ❌                | N/A                            | N/A              | Not migrated yet. Verifies brand features |
| `iAmUsingTheEnvironment()`    | ✅                | `src/Context/BrandContext.php` | N/A              | Fully implemented in BrandContext         |

## TestDataContext

| Legacy Step Definition      | Migration Status | New Location                      | Page Object Call | Comments                                    |
|-----------------------------|------------------|-----------------------------------|------------------|---------------------------------------------|
| `iLoadTestDataForProduct()` | ✅                | `src/Context/TestDataContext.php` | N/A              | Implementation is similar                   |
| `iLoadPaymentMethods()`     | ❌                | N/A                               | N/A              | Not migrated yet. Loads payment method data |
| `iLoadProductData()`        | ✅                | `src/Context/TestDataContext.php` | N/A              | Fully implemented in TestDataContext        |
| `iLoadTestDataFor()`        | ✅                | `src/Context/TestDataContext.php` | N/A              | Fully implemented in TestDataContext        |
| `iLoadProductDataFor()`     | ✅                | `src/Context/TestDataContext.php` | N/A              | Fully implemented in TestDataContext        |

## EmailContext

| Legacy Step Definition                                        | Migration Status | New Location                   | Page Object Call          | Comments                                                                              |
|---------------------------------------------------------------|------------------|--------------------------------|---------------------------|---------------------------------------------------------------------------------------|
| `iShouldReceiveAbandonedCartEmailWithinHour()`                | ✅                | `src/Context/EmailContext.php` | N/A                       | Implementation is similar, uses HTTP client directly instead of EmailVerificationTool |
| `iShouldNotReceiveAnyMoreRecoveryEmails()`                    | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with robust error handling                                          |
| `iClickTheRecoveryLinkInTheEmail()`                           | ✅                | `src/Context/EmailContext.php` | `browserService->visit()` | Fully implemented with link extraction                                                |
| `iFollowTheLinkInTheEmail()`                                  | ✅                | `src/Context/EmailContext.php` | `browserService->visit()` | Fully implemented with link extraction                                                |
| `iVerifyTheWelcomeEmailContainsAccountCredentials()`          | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with content verification                                           |
| `iVerifyTheSubscriptionConfirmationEmail()`                   | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with content verification                                           |
| `iShouldReceiveAnOrderConfirmationEmail()`                    | ✅                | `src/Context/EmailContext.php` | N/A                       | Implementation is similar, uses HTTP client directly instead of EmailVerificationTool |
| `iShouldReceiveAnAbandonedCartEmailWithSubject()`             | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with robust error handling                                          |
| `iShouldReceiveAnAbandonedCartEmailWithACouponCode()`         | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with robust error handling                                          |
| `iShouldNotSeeAnAbandonedCartEmail()`                         | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with robust error handling                                          |
| `iShouldSeeThatEmailsWasSentForEachTime()`                    | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with robust error handling                                          |
| `iVerifyTheOrderConfirmationEmail()`                          | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with content verification                                           |
| `iVerifyTheOrderConfirmationEmailForInitialProductOnly()`     | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with content verification                                           |
| `iVerifyTheOrderConfirmationEmailForTheRenewalOrder()`        | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with content verification                                           |
| `iVerifyTheOrderConfirmationEmailContainsAllPurchasedItems()` | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with content verification                                           |
| `theAbandonedCartEmailContainsTheCorrectProductInformation()` | ✅                | `src/Context/EmailContext.php` | N/A                       | Fully implemented with content verification                                           |

## CheckoutContext

| Legacy Step Definition                                  | Migration Status | New Location                      | Page Object Call                          | Comments                                     |
|---------------------------------------------------------|------------------|-----------------------------------|-------------------------------------------|----------------------------------------------|
| `iFillInTheShippingInformationWith()`                   | ✅                | `src/Context/CheckoutContext.php` | `checkoutPage->fillShippingInformation()` | Implementation is similar                    |
| `iChooseToUseTheSameAddressForBilling()`                | ✅                | `src/Context/CheckoutContext.php` | `checkoutPage->fillBillingInformation()`  | Fully implemented with additional validation |
| `iEnterThePaymentDetails()`                             | ✅                | `src/Context/CheckoutContext.php` | `checkoutPage->fillPaymentInformation()`  | Fully implemented with validation            |
| `iCompleteThePurchase()`                                | ✅                | `src/Context/CheckoutContext.php` | `checkoutPage->placeOrder()`              | Fully implemented with validation            |
| `iAmRedirectedToThePayPalSandboxPage()`                 | ✅                | `src/Context/CheckoutContext.php` | `browserService->waitForUrlContains()`    | Fully implemented                            |
| `iShouldSeeTheCorrectPaymentAmountInPayPal()`           | ✅                | `src/Context/CheckoutContext.php` | `browserService->findElement()`           | Fully implemented with validation            |
| `iVerifyTheProductDetailsAndPricingAreCorrect()`        | ⚠️               | `src/Context/CheckoutContext.php` | N/A                                       | Implementation preserved for future use      |
| `iShouldSeeAnErrorMessageIndicatingTheCardHasExpired()` | ⚠️               | `src/Context/CheckoutContext.php` | N/A                                       | Implementation preserved for future use      |
| `iVerifyTheOrderTotalIsCorrect()`                       | ⚠️               | `src/Context/CheckoutContext.php` | N/A                                       | Implementation preserved for future use      |
| `iShouldRemainOnCheckoutPage()`                         | ⚠️               | `src/Context/CheckoutContext.php` | N/A                                       | Implementation preserved for future use      |
| `iLeaveTheCheckoutPage()`                               | ⚠️               | `src/Context/CheckoutContext.php` | N/A                                       | Implementation preserved for future use      |
| `iShouldBeOnTheCheckoutPage()`                          | ✅                | `src/Context/CheckoutContext.php` | `checkoutPage->open()`                    | Fully implemented                            |
| `iShouldSeeThatTheCouponIsApplied()`                    | ⚠️               | `src/Context/CheckoutContext.php` | N/A                                       | Implementation preserved for future use      |
| `iWaitForTheOrderConfirmationPageToLoad()`              | ✅                | `src/Context/CheckoutContext.php` | `confirmationPage->waitForPageToLoad()`   | Fully implemented                            |
| `iDontCompleteTheOrder()`                               | ✅                | `src/Context/CheckoutContext.php` | `homePage->open()`                        | Fully implemented                            |

## UpsellContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `iAcceptTheUpsellOffer()` | ❌ | N/A | N/A | Not migrated yet. In src/Page/UpsellPage.php there is an `acceptOffer()` method that could be used |
| `iDeclineTheUpsellOffer()` | ❌ | N/A | N/A | Not migrated yet |
| `iShouldSeeTheUpsellMessage()` | ❌ | N/A | N/A | Not migrated yet |
| `iAcceptTheFirstUpsellOffer()` | ❌ | N/A | N/A | Not migrated yet. Handles multi-step funnels |
| `iAcceptTheSecondUpsellOffer()` | ❌ | N/A | N/A | Not migrated yet. Handles multi-step funnels |
| `iDeclineTheFirstUpsellOffer()` | ❌ | N/A | N/A | Not migrated yet. Handles multi-step funnels |
| `iDeclineTheSecondUpsellOffer()` | ❌ | N/A | N/A | Not migrated yet. Handles multi-step funnels |
| `iClickTheAcceptButtonMultipleTimes()` | ❌ | N/A | N/A | Not migrated yet. Tests multiple clicks |
| `iVerifyDietaryRestrictionWarningsAreDisplayed()` | ❌ | N/A | N/A | Not migrated yet. Verifies dietary restrictions |
| `iShouldBeRedirectedToTheUpsellPage()` | ❌ | N/A | N/A | Not migrated yet. Verifies redirect to upsell page |
| `iVerifyOnlyOneUpsellProductIsInTheOrder()` | ❌ | N/A | N/A | Not migrated yet. Placeholder for future implementation in SalesFunnelContext |

## SalesFunnelContext

| Legacy Step Definition                       | Migration Status | New Location                         | Page Object Call                                                   | Comments                                                                  |
|----------------------------------------------|------------------|--------------------------------------|--------------------------------------------------------------------|---------------------------------------------------------------------------|
| `iAmOnTheMultiStepFunnelPage()`              | ✅                | `src/Context/SalesFunnelContext.php` | `browserService->visit()`                                          | Implementation is similar but uses browserService instead of getSession() |
| `iAmOnTheSubscriptionFunnelPage()`           | ✅                | `src/Context/SalesFunnelContext.php` | `browserService->visit()`                                          | Implementation is similar but uses browserService instead of getSession() |
| `iAmOnTheHighValueFunnelPage()`              | ✅                | `src/Context/SalesFunnelContext.php` | `browserService->visit()`                                          | Implementation is similar but uses browserService instead of getSession() |
| `iVerifyTheFunnelProductDetails()`           | ✅                | `src/Context/SalesFunnelContext.php` | `productPage->getProductName()`, `productPage->isPriceDisplayed()` | Fully implemented with proper page object usage                           |
| `theUpsellPageFailsToLoad()`                 | ✅                | `src/Context/SalesFunnelContext.php` | `browserService->executeScript()`                                  | Fully implemented with detailed state tracking                            |
| `iCloseTheBrowserWithoutCompletingPayment()` | ✅                | `src/Context/SalesFunnelContext.php` | `browserService->clearCookies()`, `browserService->visit()`        | Fully implemented with proper abandonment simulation                      |
| `iCompleteThePurchaseFlowForWith()`          | ✅                | `src/Context/SalesFunnelContext.php` | Multiple                                                           | New implementation that combines multiple steps into one flow             |
| `iAmOnTheSalesFunnelPage()`                  | ✅                | `src/Context/SalesFunnelContext.php` | `browserService->visit()`, `browserService->waitForPageLoad()`     | General-purpose funnel navigation method                                  |

## FeatureContext

| Legacy Step Definition                     | Migration Status | New Location                      | Page Object Call                                                             | Comments                                                                  |
|--------------------------------------------|------------------|-----------------------------------|------------------------------------------------------------------------------|---------------------------------------------------------------------------|
| `iCheckTheDriverType()`                    | ✅                | `src/Context/BrowserContext.php`  | `browserService->getDriverType()`                                            | Migrated to BrowserContext                                                |
| `iLoadBrandConfiguration()`                | ✅                | `src/Context/BrandContext.php`    | N/A                                                                          | Migrated to BrandContext                                                  |
| `iLoadProductData()`                       | ✅                | `src/Context/TestDataContext.php` | N/A                                                                          | Migrated to TestDataContext                                               |
| `iWaitForTheOrderConfirmationPageToLoad()` | ✅                | `src/Context/CheckoutContext.php` | `confirmationPage->waitForPageToLoad()`                                      | Migrated to CheckoutContext                                               |
| `iShouldBeRedirectedToTheUpsellPage()`     | ❌                | N/A                               | N/A                                                                          | Not migrated yet. Verifies redirect to upsell page                        |
| `iAcceptTheUpsellOffer()`                  | ❌                | N/A                               | N/A                                                                          | Not migrated yet. Placeholder in FeatureContext                           |
| `iVerifyTheOrderDetailsAreCorrect()`       | ❌                | N/A                               | N/A                                                                          | Not migrated yet. Placeholder in FeatureContext                           |
| `verifyBrowserSession()`                   | ✅                | `src/Context/BrowserContext.php`  | `browserService->isSessionActive()`                                          | Migrated to BrowserContext                                                |
| `verifyBrowserStackSession()`              | ✅                | `src/Context/BrowserContext.php`  | `browserService->isBrowserStackSession()`                                    | Migrated to BrowserContext                                                |
| `iPressTheButton()`                        | ✅                | `src/Context/PaymentContext.php`  | `stripe3dsPage->clickCompleteButton()` or `stripe3dsPage->clickFailButton()` | Migrated to PaymentContext                                                |
| `iDontCompleteTheOrder()`                  | ✅                | `src/Context/CheckoutContext.php` | `homePage->open()`                                                           | Migrated to CheckoutContext                                               |
| `iAmUsingTheBrand()`                       | ✅                | `src/Context/BrandContext.php`    | N/A                                                                          | Migrated to BrandContext                                                  |
| `iLoadTestDataFor()`                       | ✅                | `src/Context/TestDataContext.php` | N/A                                                                          | Migrated to TestDataContext                                               |
| `iLoadProductDataFor()`                    | ✅                | `src/Context/TestDataContext.php` | N/A                                                                          | Migrated to TestDataContext                                               |
| `iAmOnTheHomepage()`                       | ✅                | `src/Context/FeatureContext.php`  | `browserService->visit()`                                                    | Implementation is similar but uses browserService instead of getSession() |
| `iTakeAScreenshotNamed()`                  | ✅                | `src/Context/FeatureContext.php`  | `browserService->takeScreenshot()`                                           | Implementation is similar but uses browserService instead of getSession() |
| `iShouldSee()`                             | ✅                | `src/Context/FeatureContext.php`  | `page->hasContent()`                                                         | Implementation is similar but uses browserService instead of getSession() |
| `iClickOn()`                               | ✅                | `src/Context/FeatureContext.php`  | `element->click()`                                                           | Implementation is similar but uses browserService instead of getSession() |
| `iWaitForSeconds()`                        | ✅                | `src/Context/BrowserContext.php`  | `browserService->wait()`                                                     | Migrated to BrowserContext                                                |
| `iWaitForElement()`                        | ✅                | `src/Context/BrowserContext.php`  | `browserService->waitForElement()`                                           | Migrated to BrowserContext                                                |
| `iShouldSeeThePageLoadedSuccessfully()`    | ✅                | `src/Context/BrowserContext.php`  | Multiple                                                                     | Migrated to BrowserContext                                                |
| `iNavigateBackInBrowser()`                 | ✅                | `src/Context/BrowserContext.php`  | `browserService->navigateBack()`                                             | Migrated to BrowserContext                                                |

## ValidationContext

| Legacy Step Definition | Migration Status | New Location | Page Object Call | Comments |
|------------------------|------------------|--------------|------------------|----------|
| `handlePageTitle()` | ❌ | N/A | N/A | Not migrated yet. Verifies page title |
| `iVerifyTheCartContainsTheCorrectProductDetails()` | ✅ | `src/Context/ValidationContext.php::iVerifyCartContainsCorrectProductDetails()` | `cartPage->verifyProductName()`, `cartPage->verifyProductQuantity()`, `cartPage->verifyPurchaseType()` | Implementation is similar but with more detailed verification |
| `iVerifyTheOrderTotalIsCorrect()` | ✅ | `src/Context/ValidationContext.php` | `checkoutPage->getOrderTotal()` | Implementation is similar |
| `iVerifyProductContentMatches()` | ✅ | `src/Context/ValidationContext.php` | `productPage->getContentText()` | Implementation is similar but with more robust error handling |
| `calculateExpectedTotal()` | ⚠️ | `src/Context/ValidationContext.php::calculateExpectedTotal()` | N/A | Similar functionality but different implementation. New version uses product data from test data service |
| `iVerifyTheOrderDetailsAreCorrect()` | ✅ | `src/Context/ValidationContext.php` | `confirmationPage->getShippingAddress()` | Implementation is similar but with more robust error handling |
| `handleProductDetails()` | ❌ | N/A | N/A | Not migrated yet. Verifies product details |
| `iVerifyProductContentMatchesConfiguration()` | ❌ | N/A | N/A | Not migrated yet. Complex validation of product content against configuration |
| `iVerifyTheMixedCartOrderDetailsAreCorrect()` | ❌ | N/A | N/A | Not migrated yet. Verifies mixed cart order details |
| `iVerifyTheUrlIs()` | ✅ | `src/Context/ValidationContext.php` | `basePage->getCurrentUrl()` | New implementation in ValidationContext |

## General Observations and Recommendations

1. **Inconsistent Step Text**: There are inconsistencies in step text between legacy and new implementations. For example, `iViewMyCart()` vs `iAmOnTheCartPage()`. Consider standardizing step text across the codebase.

2. **Page Object Usage**:
   - Legacy contexts directly inject page objects
   - New contexts use PageFactory to get page objects
   - Ensure all page object method calls are consistent between old and new implementations

3. **Error Handling**:
   - Both implementations use try/catch blocks
   - New implementation has more standardized error handling
   - Ensure all exceptions are properly caught and logged

4. **State Management**:
   - Legacy uses `stateService->set()`
   - New also uses `stateService->set()`
   - Ensure state keys are consistent between implementations

5. **Migration Priority**:
   - Focus on migrating core step definitions first (cart, product, checkout)
   - Then migrate supporting step definitions (brand, test data)
   - Finally migrate utility step definitions (validation, browser verification)

6. **Duplicate Functionality**:
   - Several step definitions have duplicate functionality with different implementations
   - Standardize on one implementation and remove duplicates

7. **Missing Step Definitions**:
   - Several step definitions from legacy contexts are not yet migrated
   - Create tickets to track migration of these step definitions

## Next Steps

1. Complete the analysis of remaining context files
2. Update this registry with findings
3. Create tickets for missing step definitions
4. Standardize step text and implementation across the codebase
5. Remove duplicate step definitions
6. Update feature files to use standardized step text

## BrowserContext

| Legacy Step Definition                  | Migration Status | New Location                     | Page Object Call                                                    | Comments                      |
|-----------------------------------------|------------------|----------------------------------|---------------------------------------------------------------------|-------------------------------|
| `iCheckTheDriverType()`                 | ✅                | `src/Context/BrowserContext.php` | `browserService->getDriverType()`                                   | Implemented in BrowserContext |
| `iNavigateBackInBrowser()`              | ✅                | `src/Context/BrowserContext.php` | `browserService->navigateBack()`                                    | Implemented in BrowserContext |
| `verifyBrowserSession()`                | ✅                | `src/Context/BrowserContext.php` | `browserService->isSessionActive()`                                 | Implemented in BrowserContext |
| `verifyBrowserStackSession()`           | ✅                | `src/Context/BrowserContext.php` | `browserService->isBrowserStackSession()`                           | Implemented in BrowserContext |
| `iShouldSeeThePageLoadedSuccessfully()` | ✅                | `src/Context/BrowserContext.php` | `browserService->elementExists()`, `browserService->getPageTitle()` | Implemented in BrowserContext |
| `iWaitForSeconds()`                     | ✅                | `src/Context/BrowserContext.php` | `browserService->wait()`                                            | Implemented in BrowserContext |
| `iWaitForElementToBeVisible()`          | ✅                | `src/Context/BrowserContext.php` | `browserService->waitForElementVisible()`                           | Implemented in BrowserContext |
| `iWaitForPageToLoad()`                  | ✅                | `src/Context/BrowserContext.php` | `browserService->waitForPageLoad()`                                 | Implemented in BrowserContext |
| `iTakeAScreenshot()`                    | ✅                | `src/Context/BrowserContext.php` | `browserService->takeScreenshot()`                                  | Implemented in BrowserContext |
