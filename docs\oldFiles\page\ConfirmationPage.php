<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\ElementNotFoundException;

/**
 * ConfirmationPage handles actions on the order confirmation page.
 */
class ConfirmationPage extends BasePage
{
    /**
     * The path of the confirmation page.
     *
     * @var string
     */
    protected $path = '/confirmation';

    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'ORDER_ITEMS' => '.ui.celled.table tbody tr',
        'PRODUCT_NAME' => '.product-description h3',
        'QUANTITY' => '.text-center',
        'PRICE' => '.text-center:last-child',
        'SHIPPING_INFO' => '.card-header:contains("Shipping address")',
        'ORDER_SUMMARY' => '.order-summary-component',
        'SUBTOTAL' => '.ch-subtotal-value span',
        'SHIPPING_COST' => '.ch-shipping-value span',
        'TOTAL' => '.ch-total-value span',
        'SHIPPING_METHOD' => '.shipping-method',
        'ADDRESS' => '.card-body address',
        'PURCHASE_TYPE' => 'span[data-sylius-option-name="Purchase type"]',
        'FLAVOR' => 'span[data-sylius-option-name="Flavour"]',
        'ORDER_NUMBER' => '.order-number',
        'SUBSCRIPTION_BADGE' => '.ui.label.label-success',
        'SUBSCRIPTION_FREQUENCY' => '.ui.label.label-success',
        'THANK_YOU_SECTION' => '.section-thank-you h1'
    ];

    private array $orderDetails = [];
    private array $warnings = [];

    /**
     * Verifies that we're on the expected page.
     *
     * @throws ElementNotFoundException If required elements are not found
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        try {
            $this->waitForElementVisible(self::SELECTORS['ORDER_NUMBER']);
        } catch (ElementNotFoundException $e) {
            error_log("[ConfirmationPage] Failed to verify page: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Gets the URL for the confirmation page.
     *
     * @return string The complete URL
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Waits for the confirmation page to fully load
     *
     * @param int $timeout Timeout in milliseconds
     * @throws ElementNotFoundException If required elements are not found
     */
    public function waitForPageToLoad(int $timeout = 10000): void
    {
        try {
            error_log("[ConfirmationPage] Waiting for page to load");

            // Wait for page load with proper JavaScript syntax
            $this->getSession()->wait($timeout, "document.readyState === 'complete'");

            // Wait for customer details with proper JavaScript syntax
            $this->getSession()->wait($timeout,
                "typeof window.customerDetails !== 'undefined' && window.customerDetails !== null"
            );

            // Wait for transaction data with proper JavaScript syntax
            $this->getSession()->wait($timeout,
                "typeof window.dataLayer !== 'undefined' && " .
                "window.dataLayer !== null && " .
                "window.dataLayer.length > 0 && " .
                "typeof window.dataLayer[0].ecommerce !== 'undefined'"
            );

            // Wait for thank you section to be visible
            $this->waitForElementVisible(self::SELECTORS['THANK_YOU_SECTION'], $timeout);
            
            error_log("[ConfirmationPage] Page loaded successfully");
        } catch (ElementNotFoundException $e) {
            $error = sprintf('Failed to load confirmation page: %s', $e->getMessage());
            error_log("[ConfirmationPage] " . $error);
            throw new ElementNotFoundException(
                $this->getSession(),
                'thank you section',
                'css',
                self::SELECTORS['THANK_YOU_SECTION']
            );
        } catch (\Exception $e) {
            $error = sprintf('Error loading confirmation page: %s', $e->getMessage());
            error_log("[ConfirmationPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Helper method to wait for element to be visible
     *
     * @param string $selector CSS selector
     * @param int $timeout Timeout in milliseconds
     */
    protected function waitForElementVisible(string $selector, int $timeout = 5000): void
    {
        $this->getSession()->wait($timeout,
            "document.querySelector('" . $selector . "') !== null && " .
            "document.querySelector('" . $selector . "').offsetParent !== null"
        );
    }

    /**
     * Gets the order number from the confirmation page.
     *
     * @return string The order number
     * @throws \RuntimeException If order number cannot be retrieved
     */
    public function getOrderNumber(): string
    {
        try {
            error_log("[ConfirmationPage] Attempting to get order number");
            $script = "return window.dataLayer[0].ecommerce.transaction_id";
            $orderNumber = $this->getSession()->evaluateScript($script);
            error_log("[ConfirmationPage] Successfully retrieved order number: " . $orderNumber);
            return $orderNumber;
        } catch (\Exception $e) {
            $error = sprintf('Failed to get order number: %s', $e->getMessage());
            error_log("[ConfirmationPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Gets the customer email from the confirmation page.
     *
     * @return string The customer email
     * @throws \RuntimeException If customer email cannot be retrieved
     */
    public function getCustomerEmail(): string
    {
        try {
            error_log("[ConfirmationPage] Attempting to get customer email");
            $emailScript = "return window.customerDetails.email";
            $email = $this->getSession()->evaluateScript($emailScript);
            error_log("[ConfirmationPage] Successfully retrieved customer email");
            return $email;
        } catch (\Exception $e) {
            $error = sprintf('Failed to get customer email: %s', $e->getMessage());
            error_log("[ConfirmationPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Gets the shipping address from the confirmation page.
     *
     * @return string The formatted shipping address
     * @throws ElementNotFoundException If address element is not found
     */
    public function getShippingAddress(): string
    {
        try {
            error_log("[ConfirmationPage] Attempting to get shipping address");
            $selector = "//div[contains(@class, 'card-header') and contains(text(), 'Shipping address')]/../div[@class='card-body']/address";
            $addressElement = $this->findElement($selector);
            $address = $addressElement->getText();
            error_log("[ConfirmationPage] Successfully retrieved shipping address");
            return $address;
        } catch (ElementNotFoundException $e) {
            error_log("[ConfirmationPage] Failed to get shipping address: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Retrieves the product name from the confirmation page.
     *
     * @return string The product name.
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getProductName(): string
    {
        $selector = '.product-description h3';
        $element = $this->findElement($selector);
        return trim($element->getText());
    }

    /**
     * Retrieves the product quantity from the confirmation page.
     *
     * @return int The product quantity.
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getProductQuantity(): int
    {
        $selector = 'td.text-center:nth-child(3)';
        $element = $this->findElement($selector);
        return (int)trim($element->getText());
    }

    /**
     * Retrieves the subtotal sum from the confirmation page.
     *
     * @return int The subtotal sum.
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getSubtotalSum(): int
    {
        $selector = 'td.text-center:nth-child(4)';
        $element = $this->findElement($selector);
        return (int)trim($element->getText());
    }

    /**
     * Helper method to normalize address text for comparison
     *
     * @param string $address
     * @return string
     */
    private function normalizeAddressText(string $address): string
    {
        // Remove HTML tags while preserving line breaks
        $address = strip_tags($address, '<br>');

        // Replace <br> tags and newlines with spaces
        $address = str_replace(['<br>', '<br/>', '<br />', "\n"], ' ', $address);

        // Normalize multiple spaces to single ones
        $address = preg_replace('/\s+/', ' ', $address);

        // Trim whitespace
        $address = trim($address);

        // Split into components
        $parts = explode(' ', $address);

        // Reconstruct address in expected format
        $name = array_shift($parts) . ' ConfirmationPage.php' . array_shift($parts);  // First and Last name
        $phone = array_shift($parts);  // Phone
        $street = array_shift($parts) . ' ConfirmationPage.php' . array_shift($parts) . ' ' . array_shift($parts);  // Street address
        $city = array_shift($parts);
        $postcode = array_shift($parts);
        $country = array_pop($parts);  // Last element is country

        // Build final string with n/a for county/state
        return sprintf(
            '%s %s %s %s, %s n/a %s',
            $name,
            $phone,
            $street,
            $city,
            $postcode,
            $country
        );
    }

    /**
     * Gets all ordered products with their details
     *
     * @return array Array of products with name, quantity, and price
     * @throws ElementNotFoundException If product elements are not found
     */
    public function getOrderedProducts(): array
    {
        try {
            error_log("[ConfirmationPage] Attempting to get ordered products");
            $products = [];
            $rows = $this->findElements(self::SELECTORS['ORDER_ITEMS']);
            
            foreach ($rows as $row) {
                $nameElement = $row->find('css', self::SELECTORS['PRODUCT_NAME']);
                $quantityElement = $row->find('css', self::SELECTORS['QUANTITY']);
                $priceElement = $row->find('css', self::SELECTORS['PRICE']);
                
                if ($nameElement && $quantityElement && $priceElement) {
                    $products[] = [
                        'name' => trim($nameElement->getText()),
                        'quantity' => (int)trim($quantityElement->getText()),
                        'price' => $this->parsePrice($priceElement->getText()),
                        'subscription' => $this->isSubscriptionProduct($nameElement)
                    ];
                }
            }
            
            error_log("[ConfirmationPage] Successfully retrieved " . count($products) . " ordered products");
            return $products;
        } catch (ElementNotFoundException $e) {
            $error = sprintf('Failed to get ordered products: %s', $e->getMessage());
            error_log("[ConfirmationPage] " . $error);
            throw new ElementNotFoundException(
                $this->getSession(),
                'ordered products',
                'css',
                self::SELECTORS['ORDER_ITEMS']
            );
        }
    }

    /**
     * Gets complete shipping information
     *
     * @return array Shipping details including method, cost, and address
     * @throws ElementNotFoundException If shipping elements are not found
     */
    public function getShippingInfo(): array
    {
        try {
            error_log("[ConfirmationPage] Attempting to get shipping information");
            $info = [
                'method' => $this->getElementText(self::SELECTORS['SHIPPING_METHOD']),
                'cost' => $this->getElementText(self::SELECTORS['SHIPPING_COST']),
                'address' => $this->getFormattedAddress()
            ];
            error_log("[ConfirmationPage] Successfully retrieved shipping information");
            return $info;
        } catch (ElementNotFoundException $e) {
            $error = sprintf('Failed to get shipping information: %s', $e->getMessage());
            error_log("[ConfirmationPage] " . $error);
            throw new ElementNotFoundException(
                $this->getSession(),
                'shipping information',
                'css',
                self::SELECTORS['SHIPPING_INFO']
            );
        }
    }

    /**
     * Checks if free shipping was applied
     *
     * @return bool
     */
    public function hasFreeShipping(): bool
    {
        try {
            $shippingCost = $this->getElementText(self::SELECTORS['SHIPPING_COST']);
            return strtoupper(trim($shippingCost)) === 'FREE';
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Gets all order totals
     *
     * @return array Array containing subtotal, shipping, and total amounts
     * @throws ElementNotFoundException If total elements are not found
     */
    public function getOrderTotals(): array
    {
        try {
            error_log("[ConfirmationPage] Attempting to get order totals");
            $totals = [
                'subtotal' => $this->parsePrice(
                    $this->getElementText(self::SELECTORS['SUBTOTAL'])
                ),
                'shipping' => $this->parsePrice(
                    $this->getElementText(self::SELECTORS['SHIPPING_COST'])
                ),
                'total' => $this->parsePrice(
                    $this->getElementText(self::SELECTORS['TOTAL'])
                )
            ];
            error_log("[ConfirmationPage] Successfully retrieved order totals");
            return $totals;
        } catch (ElementNotFoundException $e) {
            $error = sprintf('Failed to get order totals: %s', $e->getMessage());
            error_log("[ConfirmationPage] " . $error);
            throw new ElementNotFoundException(
                $this->getSession(),
                'order totals',
                'css',
                self::SELECTORS['ORDER_SUMMARY']
            );
        }
    }

    /**
     * Checks if a product is a subscription
     *
     * @param NodeElement $element
     * @return bool
     */
    private function isSubscriptionProduct(NodeElement $element): bool
    {
        try {
            return str_contains(strtolower($element->getText()), 'subscription');
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Gets formatted address from the page
     *
     * @return string
     * @throws ElementNotFoundException
     */
    private function getFormattedAddress(): string
    {
        $addressElement = $this->findElement(self::SELECTORS['ADDRESS']);
        $address = $addressElement->getText();
        return $this->normalizeAddressText($address);
    }

    private function parsePrice(string $priceText): float
    {
        return (float)preg_replace('/[^0-9.]/', '', $priceText);
    }

    public function hasWarningInInstructions(string $warning): bool
    {
        try {
            $instructions = $this->findElements('.order-instructions .warning');
        foreach ($instructions as $instruction) {
            if (strpos($instruction->getText(), $warning) !== false) {
                return true;
            }
        }
        return false;
    } catch (ElementNotFoundException $e) {
        return false;
        }
    }

    /**
     * Gets the selected flavor from the confirmation page.
     *
     * @return string|null The selected flavor or null if flavor option is not present
     * @throws ElementNotFoundException If the product description section is not found
     */
    public function getSelectedFlavor(): ?string
    {
        try {
            $flavorElement = $this->findElement(self::SELECTORS['FLAVOR']);
            return trim($flavorElement->getText());
        } catch (ElementNotFoundException $e) {
            return null; // Return null if flavor is not present (for products without flavors)
        }
    }

    /**
     * Verifies if the selected flavor matches the expected one.
     *
     * @param string $expectedFlavor The expected flavor
     * @return bool True if flavor matches or if flavor selection is not applicable
     * @throws RuntimeException|ElementNotFoundException If flavor doesn't match when it should be present
     */
    public function verifySelectedFlavor(string $expectedFlavor): bool
    {
        $actualFlavor = $this->getSelectedFlavor();

        // If no flavor is present, it's valid for products without flavors
        if ($actualFlavor === null) {
            return true;
        }

        if ($actualFlavor !== $expectedFlavor) {
            throw new \RuntimeException(
                sprintf('Expected flavor "%s", but got "%s" on confirmation page',
                    $expectedFlavor,
                    $actualFlavor
                )
            );
        }

        return true;
    }

    /**
     * Verifies mixed cart order details
     *
     * @return array Order details including subscriptions and one-time items
     * @throws ElementNotFoundException
     */
    public function getMixedCartOrderDetails(): array
    {
        $orderDetails = [];
        $items = $this->findElements(self::SELECTORS['ORDER_ITEMS']);

        foreach ($items as $item) {
            $itemDetails = [
                'name' => $item->find('css', self::SELECTORS['PRODUCT_NAME'])->getText(),
                'quantity' => (int)$item->find('css', self::SELECTORS['QUANTITY'])->getText(),
                'price' => $this->parsePrice($item->find('css', self::SELECTORS['PRICE'])->getText()),
                'type' => $item->find('css', self::SELECTORS['SUBSCRIPTION_BADGE']) ? 'subscription' : 'one-time'
            ];

            if ($itemDetails['type'] === 'subscription') {
                $itemDetails['frequency'] = $item->find('css', self::SELECTORS['SUBSCRIPTION_FREQUENCY'])->getText();
            }

            $orderDetails[] = $itemDetails;
        }

        return $orderDetails;
    }
}
