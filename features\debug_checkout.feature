Feature: Checkout Page Navigation Debug
  As a developer
  I want to test isolated checkout page navigation
  So that I can debug issues with BrowserStack tests

  Background:
    Given I load brand configuration

  @debug @checkout @isolated
  Scenario: Direct navigation to checkout page
    When I am on the checkout page
    Then I should see the shipping form
    And I take a screenshot named "debug_checkout_direct"

  @debug @checkout @cart-to-checkout
  Scenario: Navigate from cart to checkout
    Given I have a product in my cart
    When I proceed to checkout
    Then I should be on the checkout page
    And I take a screenshot named "debug_checkout_from_cart"

  @debug @checkout @funnel-to-checkout
  Scenario: Navigate from funnel to checkout
    Given I am on the sales funnel page "total-harmony-funnel"
    When I proceed to checkout
    Then I should be on the checkout page
    And I take a screenshot named "debug_checkout_from_funnel" 