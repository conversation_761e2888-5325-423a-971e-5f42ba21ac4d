<?php

namespace App\Page;

use App\Page\Base\BasePage;
use App\Page\Base\PaymentPageInterface;

/**
 * PayPalPage handles actions on the PayPal payment page.
 */
class PayPalPage extends BasePage implements PaymentPageInterface
{
    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'EMAIL_FIELD' => '#email',
        'PASSWORD_FIELD' => '#password',
        'NEXT_BUTTON' => 'button[data-testid="next-button"]',
        'LOGIN_BUTTON' => 'button[data-testid="login-button"]',
        'AMOUNT' => '.test_transaction-amount',
        'CONFIRM_BUTTON' => '#confirmButtonTop',
        'PAYPAL_PAYMENT_OPTION' => '#paypal-payment-option',
        'PAYPAL_BUTTON' => '#paypal-button',
        'ERROR_MESSAGE' => '.notification-critical',
        'SUCCESS_MESSAGE' => '.payment-success-message'
    ];
    /**
     * The path of the PayPal page.
     *
     * @var string
     */
    protected string $path = '/paypal';

    /**
     * Login to PayPal
     *
     * @param string $email PayPal email
     * @param string $password PayPal password
     * @return void
     */
    public function login(string $email, string $password): void
    {
        // Enter email
        $this->fillField(self::SELECTORS['EMAIL_FIELD'], $email);
        $this->clickElement(self::SELECTORS['NEXT_BUTTON']);
        $this->waitForElementVisible(self::SELECTORS['PASSWORD_FIELD']);

        // Enter password
        $this->fillField(self::SELECTORS['PASSWORD_FIELD'], $password);
        $this->clickElement(self::SELECTORS['LOGIN_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Confirm the PayPal payment
     *
     * @return void
     */
    public function confirmPayment(): void
    {
        $this->waitForElementVisible(self::SELECTORS['CONFIRM_BUTTON']);
        $this->clickElement(self::SELECTORS['CONFIRM_BUTTON']);
        $this->waitForPageToLoad();
    }

    /**
     * Select PayPal as the payment method
     *
     * @return void
     */
    public function selectPayPalPayment(): void
    {
        $this->waitForElementVisible(self::SELECTORS['PAYPAL_PAYMENT_OPTION']);
        $this->clickElement(self::SELECTORS['PAYPAL_PAYMENT_OPTION']);
        $this->waitForElementVisible(self::SELECTORS['PAYPAL_BUTTON']);
        $this->clickElement(self::SELECTORS['PAYPAL_BUTTON']);
        $this->waitForPageToLoad();
    }

    /**
     * Get the payment amount
     *
     * @return string
     */
    public function getPaymentAmount(): string
    {
        return $this->getElementText(self::SELECTORS['AMOUNT']);
    }

    /**
     * Check if there is an error message
     *
     * @return bool
     */
    public function hasErrorMessage(): bool
    {
        return $this->elementExists(self::SELECTORS['ERROR_MESSAGE']);
    }

    /**
     * Get the error message
     *
     * @return string
     */
    public function getErrorMessage(): string
    {
        return $this->getElementText(self::SELECTORS['ERROR_MESSAGE']);
    }

    /**
     * Check if the payment was successful
     *
     * @return bool
     */
    public function isPaymentSuccessful(): bool
    {
        return $this->elementExists(self::SELECTORS['SUCCESS_MESSAGE']);
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible(self::SELECTORS['EMAIL_FIELD']);
    }

    /**
     * Handle 3D Secure authentication
     * This method is required by the PaymentPageInterface but not applicable for PayPal
     *
     * @return void
     */
    public function handle3DSecureAuthentication()
    {
        throw new \RuntimeException('3D Secure authentication is not applicable for PayPal');
    }

    /**
     * Complete the PayPal checkout process
     *
     * @return void
     */
    public function completePayPalCheckout()
    {
        // Login if needed (email field is visible)
        if ($this->elementExists(self::SELECTORS['EMAIL_FIELD'])) {
            // Use test account credentials
            $this->login('<EMAIL>', 'testpassword');
        }

        // Confirm the payment
        $this->confirmPayment();
    }

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // PayPal page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // PayPal page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // PayPal page doesn't have subscription items, so return empty array
        return [];
    }

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array
    {
        // PayPal page doesn't have cart items, so return empty array
        return [];
    }
}
