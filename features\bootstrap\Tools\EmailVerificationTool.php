<?php

namespace Features\Bootstrap\Tools;

use Mailtrap\Config;
use Mailtrap\Helper\ResponseHelper;
use <PERSON>trap\MailtrapSandboxClient;
use RuntimeException;

/**
 * EmailVerificationTool - A tool for verifying emails in Mailtrap for test automation
 */
class EmailVerificationTool
{
    private MailtrapSandboxClient $sandboxClient;
    private string $inboxId;
    private string $accountId;
    private array $cachedEmails = [];
    private ?int $lastEmailCheckTime = null;
    private int $maxRetries = 3;
    private int $retryDelay = 2;

    /**
     * Constructor
     *
     * @throws RuntimeException if required environment variables are not set
     */
    public function __construct()
    {
        $this->validateEnvironment();
        $this->initializeClient();
    }

    /**
     * Validates that all required environment variables are set
     *
     * @throws RuntimeException if any required variable is missing
     */
    private function validateEnvironment(): void
    {
        $requiredVars = ['MAILTRAP_TOKEN', 'MAILTRAP_AEONS_INBOX_ID', 'MAILTRAP_ACCOUNT_ID'];
        $missing = [];

        foreach ($requiredVars as $var) {
            if (!getenv($var)) {
                $missing[] = $var;
            }
        }

        if (!empty($missing)) {
            throw new RuntimeException(
                sprintf('Missing required environment variables: %s', implode(', ', $missing))
            );
        }
    }

    /**
     * Initializes the Mailtrap client
     */
    private function initializeClient(): void
    {
        $this->inboxId = getenv('MAILTRAP_AEONS_INBOX_ID');
        $this->accountId = getenv('MAILTRAP_ACCOUNT_ID');

        // Initialize Mailtrap sandbox client
        $config = new Config(getenv('MAILTRAP_TOKEN'));
        $this->sandboxClient = new MailtrapSandboxClient($config);
    }

    /**
     * Verifies if an email matching criteria exists within timeframe
     *
     * @param array $criteria Search criteria (subject, to_email, from_email)
     * @param int $timeframeSeconds How recent the email should be
     * @param int $maxAttempts Maximum number of attempts to find email
     * @param int $delayBetweenAttempts Seconds to wait between attempts
     * @return array|null Found email or null
     */
    public function verifyEmailExists(
        array $criteria,
        int   $timeframeSeconds = 300,
        int   $maxAttempts = 3,
        int   $delayBetweenAttempts = 10
    ): ?array
    {
        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            $emails = $this->findEmails($criteria, $timeframeSeconds);

            if (!empty($emails)) {
                return reset($emails); // Return first matching email
            }

            if ($attempt < $maxAttempts) {
                sleep($delayBetweenAttempts);
            }
        }

        return null;
    }

    /**
     * Searches for emails matching specific criteria
     *
     * @param array $criteria Array of criteria to match (subject, to_email, from_email)
     * @param int $timeframeSeconds How far back to search in seconds
     * @return array Matching emails
     */
    public function findEmails(array $criteria, int $timeframeSeconds = 3600): array
    {
        $emails = $this->getLatestEmails(50, false);
        $matchingEmails = [];
        $cutoffTime = time() - $timeframeSeconds;

        foreach ($emails as $email) {
            if (strtotime($email['created_at']) < $cutoffTime) {
                continue;
            }

            $matches = true;
            foreach ($criteria as $field => $value) {
                if (!isset($email[$field]) || stripos($email[$field], $value) === false) {
                    $matches = false;
                    break;
                }
            }

            if ($matches) {
                $matchingEmails[] = $email;
            }
        }

        return $matchingEmails;
    }

    /**
     * Gets the latest emails with retry mechanism
     *
     * @param int $limit Maximum number of emails to retrieve
     * @param bool $useCache Whether to use cached emails if available
     * @return array List of emails
     * @throws RuntimeException if the API request fails after retries
     */
    public function getLatestEmails(int $limit = 10, bool $useCache = true): array
    {
        if ($useCache && !empty($this->cachedEmails) &&
            (time() - ($this->lastEmailCheckTime ?? 0) < 300)) { // 5 minute cache
            return array_slice($this->cachedEmails, 0, $limit);
        }

        return $this->withRetry(function () use ($limit) {
            $messages = $this->sandboxClient->messages($this->accountId, $this->inboxId);
            $response = $messages->getList($this->accountId, $this->inboxId, 1);
            $this->cachedEmails = ResponseHelper::toArray($response);
            $this->lastEmailCheckTime = time();
            return array_slice($this->cachedEmails, 0, $limit);
        });
    }

    /**
     * Executes a function with retry mechanism
     *
     * @param callable $callback Function to execute
     * @return mixed Result of the callback
     * @throws RuntimeException if all retries fail
     */
    private function withRetry(callable $callback)
    {
        $lastException = null;

        for ($attempt = 1; $attempt <= $this->maxRetries; $attempt++) {
            try {
                return $callback();
            } catch (\Exception $e) {
                $lastException = $e;
                if ($attempt < $this->maxRetries) {
                    sleep($this->retryDelay * $attempt); // Exponential backoff
                }
            }
        }

        throw new RuntimeException(
            sprintf('Operation failed after %d attempts. Last error: %s',
                $this->maxRetries,
                $lastException->getMessage()
            )
        );
    }

    /**
     * Formats email content for display
     *
     * @param array $email Email data
     * @return array Formatted email content
     */
    public function formatEmailContent(array $email): array
    {
        return [
            'id' => $email['id'] ?? 'N/A',
            'subject' => $email['subject'] ?? 'N/A',
            'from' => $email['from_email'] ?? 'N/A',
            'to' => $email['to_email'] ?? 'N/A',
            'date' => $email['created_at'] ?? 'N/A',
            'has_html' => isset($email['html_body']) && !empty($email['html_body']),
            'has_text' => isset($email['text_body']) && !empty($email['text_body']),
            'links' => $this->extractEmailLinks($email['id'])
        ];
    }

    /**
     * Extracts links from email HTML content
     *
     * @param string $emailId ID of the email to extract links from
     * @param string|null $linkPattern Optional regex pattern to match specific links
     * @return array Array of extracted links
     */
    public function extractEmailLinks(string $emailId, ?string $linkPattern = null): array
    {
        $html = $this->getEmailHtml($emailId);
        $links = [];

        if (empty($html)) {
            return $links;
        }

        preg_match_all('/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/i', $html, $matches);

        if (empty($matches[1])) {
            return $links;
        }

        foreach ($matches[1] as $index => $link) {
            if ($linkPattern && !preg_match($linkPattern, $link)) {
                continue;
            }

            $links[] = [
                'url' => $link,
                'text' => strip_tags($matches[2][$index])
            ];
        }

        return $links;
    }

    /**
     * Gets the HTML content of an email
     *
     * @param string $emailId The ID of the email to retrieve
     * @return string HTML content of the email
     * @throws RuntimeException if the email cannot be found or retrieved
     */
    public function getEmailHtml(string $emailId): string
    {
        try {
            $messages = $this->sandboxClient->messages($this->accountId, $this->inboxId);
            $response = $messages->getHtml($this->accountId, $this->inboxId, $emailId);
            return ResponseHelper::toString($response);
        } catch (\Exception $e) {
            throw new RuntimeException(
                sprintf('Failed to retrieve HTML content for email ID %s: %s', $emailId, $e->getMessage())
            );
        }
    }

    /**
     * Gets the text content of an email
     *
     * @param string $emailId The ID of the email to retrieve
     * @return string Text content of the email
     * @throws RuntimeException if the email cannot be found or retrieved
     */
    public function getEmailText(string $emailId): string
    {
        try {
            $messages = $this->sandboxClient->messages($this->accountId, $this->inboxId);
            $response = $messages->getText($this->accountId, $this->inboxId, $emailId);
            return ResponseHelper::toString($response);
        } catch (\Exception $e) {
            throw new RuntimeException(
                sprintf('Failed to retrieve text content for email ID %s: %s', $emailId, $e->getMessage())
            );
        }
    }

    /**
     * Gets email content by subject pattern
     *
     * @param string $subjectPattern Pattern to match in email subject
     * @param int $timeframeSeconds How far back to search in seconds
     * @return array|null Email content or null if not found
     */
    public function getEmailBySubject(string $subjectPattern, int $timeframeSeconds = 3600): ?array
    {
        $emails = $this->findEmails(['subject' => $subjectPattern], $timeframeSeconds);

        if (empty($emails)) {
            return null;
        }

        // Get the most recent matching email
        $mostRecent = reset($emails);
        return $this->getEmailContent($mostRecent['id']);
    }

    /**
     * Gets email content with retry mechanism
     */
    public function getEmailContent(string $emailId): array
    {
        return $this->withRetry(function () use ($emailId) {
            $messages = $this->sandboxClient->messages($this->accountId, $this->inboxId);
            $response = $messages->getById($this->accountId, $this->inboxId, $emailId);
            return ResponseHelper::toArray($response);
        });
    }

    /**
     * Clears the email cache
     */
    public function clearCache(): void
    {
        $this->cachedEmails = [];
        $this->lastEmailCheckTime = null;
    }

    /**
     * Gets the timestamp of the last email check
     *
     * @return int|null Timestamp of last check or null if no check performed
     */
    public function getLastCheckTime(): ?int
    {
        return $this->lastEmailCheckTime;
    }
} 