Feature: Validate Service Dependencies
  As a developer
  I want to verify that all service dependencies work correctly
  So that I can ensure test stability

  Background:
    Given I load brand configuration

  @debug @service-validation @shared-state
  Scenario: Shared state service should persist data between steps
    When I set "test_value" as "123" in the shared state
    Then I should be able to retrieve "test_value" from the shared state
    And the value of "test_value" should be "123"

  @debug @service-validation @page-factory
  Scenario: Page factory should create page objects
    When I request the "HomePage" from the page factory
    Then I should receive a valid page object
    And I should be able to navigate to the homepage

  @debug @service-validation @browser-service
  Scenario: Browser service should perform basic operations
    When I navigate to the homepage
    Then I should be able to take a screenshot
    And I should be able to check for elements
    And I should be able to execute JavaScript 