Feature: Context Migration Test
  As a developer
  I want to verify that the migrated contexts work correctly
  So that I can be confident in the architecture migration

  @context_migration
  Scenario: Verify brand context
    Given I am using the "aeons" brand
    Then the brand should be "aeons"

  @context_migration
  Scenario: Verify feature context
    Given I am on the homepage
    When I take a screenshot named "homepage"
    Then I should see "Welcome"

  @context_migration
  Scenario: Verify product context
    Given I am using the "aeons" brand
    And I am viewing the product "golden_harvest"
    Then I should see the product details for "golden_harvest"

  @context_migration
  Scenario: Verify cart context
    Given I am on the cart page
    Then the cart should be empty

  @context_migration
  Scenario: Verify checkout context
    Given I am on the checkout page
    When I fill in the shipping information with "default" user data
    And I select shipping method "standard"
    Then I should be on the order confirmation page

  @context_migration
  Scenario: Verify payment context
    Given I am on the checkout page
    When I select payment method "credit_card"
    And I fill in credit card information with "visa" card
    Then I should be on the order confirmation page
