<?php

namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\State\SharedStateServiceInterface;

/**
 * UpsellPage handles actions on the upsell page.
 */
class UpsellPage extends BasePage
{
    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'ACCEPT_BUTTON' => '.upsell-accept',
        'DECLINE_BUTTON' => '.upsell-decline',
        'UPSELL_MESSAGE' => '.upsell-message',
        'PRODUCT_TITLE' => '.product-title',
        'PRODUCT_PRICE' => '.product-price .amount',
        'PRODUCT_RESTRICTIONS' => '.product-restrictions .warning'
    ];
    /**
     * The path of the upsell page.
     *
     * @var string
     */
    protected string $path = '/upsell';
    /**
     * Shared state service
     *
     * @var SharedStateServiceInterface
     */
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     * @param SharedStateServiceInterface $stateService Shared state service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(
        BrowserServiceInterface     $browserService,
        SharedStateServiceInterface $stateService,
        ?string                     $baseUrl = null
    )
    {
        parent::__construct($browserService, $baseUrl);
        $this->stateService = $stateService;
    }

    /**
     * Accept the upsell offer
     *
     * @return void
     */
    public function acceptOffer(): void
    {
        // Store the upsell product info in shared state
        $this->storeUpsellProductInfo();

        // Click the accept button
        $this->clickElement(self::SELECTORS['ACCEPT_BUTTON']);
        $this->waitForPageToLoad();
    }

    /**
     * Store the upsell product information in shared state
     *
     * @return void
     */
    private function storeUpsellProductInfo(): void
    {
        $this->stateService->set('upsell.product_title', $this->getProductTitle());
        $this->stateService->set('upsell.product_price', $this->getProductPrice());
    }

    /**
     * Get the product title
     *
     * @return string
     */
    public function getProductTitle(): string
    {
        return $this->getElementText(self::SELECTORS['PRODUCT_TITLE']);
    }

    /**
     * Get the product price
     *
     * @return string
     */
    public function getProductPrice(): string
    {
        return $this->getElementText(self::SELECTORS['PRODUCT_PRICE']);
    }

    /**
     * Decline the upsell offer
     *
     * @return void
     */
    public function declineOffer(): void
    {
        $this->clickElement(self::SELECTORS['DECLINE_BUTTON']);
        $this->waitForPageToLoad();
    }

    /**
     * Get the upsell message
     *
     * @return string
     */
    public function getUpsellMessage(): string
    {
        return $this->getElementText(self::SELECTORS['UPSELL_MESSAGE']);
    }

    /**
     * Get the product restrictions
     *
     * @return array
     */
    public function getProductRestrictions(): array
    {
        $restrictions = [];
        $elements = $this->browserService->findElements(self::SELECTORS['PRODUCT_RESTRICTIONS']);

        foreach ($elements as $element) {
            $restrictions[] = $element->getText();
        }

        return $restrictions;
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible(self::SELECTORS['UPSELL_MESSAGE']);
    }

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // Upsell page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // Upsell page doesn't have cart items, so return 0
        return 0;
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // Upsell page doesn't have subscription items, so return empty array
        return [];
    }

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array
    {
        // Upsell page doesn't have cart items, so return empty array
        return [];
    }
}
