<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for sales funnel functionality
 */
class SalesFunnelContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;

    // References to other contexts
    private ?CartContext $cartContext = null;
    private ?CheckoutContext $checkoutContext = null;
    private ?PaymentContext $paymentContext = null;
    private ?ProductContext $productContext = null;
    private ?EmailContext $emailContext = null;

    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     * @param BrowserServiceInterface $browserService Browser service
     * @param ConfigurationServiceInterface $configService Configuration service
     * @param SharedStateServiceInterface $stateService Shared state service
     */
    public function __construct(
        ContainerInterface          $container,
        BrowserServiceInterface     $browserService,
        ConfigurationServiceInterface $configService,
        SharedStateServiceInterface $stateService
    )
    {
        parent::__construct($container);

        $this->browserService = $browserService;
        $this->configService = $configService;
        $this->stateService = $stateService;

        $this->logInfo("SalesFunnelContext initialized");
    }

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        $environment = $scope->getEnvironment();

        // Get related contexts if available
        if ($environment->hasContextClass(CartContext::class)) {
            $this->cartContext = $environment->getContext(CartContext::class);
        }

        if ($environment->hasContextClass(CheckoutContext::class)) {
            $this->checkoutContext = $environment->getContext(CheckoutContext::class);
        }

        if ($environment->hasContextClass(PaymentContext::class)) {
            $this->paymentContext = $environment->getContext(PaymentContext::class);
        }

        if ($environment->hasContextClass(ProductContext::class)) {
            $this->productContext = $environment->getContext(ProductContext::class);
        }

        if ($environment->hasContextClass(EmailContext::class)) {
            $this->emailContext = $environment->getContext(EmailContext::class);
        }

        $this->logInfo("Gathered contexts in BeforeScenario hook");
    }

    /**
     * @Given I complete the purchase flow for :productSlug with :paymentMethod
     */
    public function iCompleteThePurchaseFlowForWith(string $productSlug, string $paymentMethod): void
    {
        try {
            // Ensure we have the required contexts
            $this->ensureRequiredContexts();

            // Step 1: View the product
            $this->productContext->iAmViewingTheProduct($productSlug);

            // Step 2: Add to cart
            $this->productContext->iAddTheProductToCart();

            // Step 3: Proceed to checkout
            $this->cartContext->iProceedToCheckout();

            // Step 4: Fill in shipping information
            $this->checkoutContext->iFillInTheShippingInformationWith('default');

            // Step 5: Select shipping method
            $this->checkoutContext->iSelectShippingMethod('standard');

            // Step 6: Select payment method
            $this->paymentContext->iSelectPaymentMethod($paymentMethod);

            // Step 7: Fill in payment details
            if ($paymentMethod === 'credit_card') {
                $this->paymentContext->iFillInCreditCardInformationWith('visa');
            } elseif ($paymentMethod === 'paypal') {
                $this->paymentContext->iCompleteThePayPalCheckout();
            }

            // Step 8: Confirm the order
            $this->checkoutContext->iConfirmTheOrder();

            // Store completion status in shared state
            $this->stateService->set('funnel.completed', true);
            $this->stateService->set('funnel.product', $productSlug);
            $this->stateService->set('funnel.payment_method', $paymentMethod);

            $this->logInfo(sprintf(
                'Completed purchase flow for product "%s" with payment method "%s"',
                $productSlug,
                $paymentMethod
            ));
        } catch (\Throwable $e) {
            $this->logError(sprintf(
                'Failed to complete purchase flow for product "%s" with payment method "%s"',
                $productSlug,
                $paymentMethod
            ), $e);
            throw $e;
        }
    }

    /**
     * Ensure required contexts are available
     *
     * @param array $contextTypes Specific context types to check
     * @throws \RuntimeException When required contexts are not available
     */
    private function ensureRequiredContexts(array $contextTypes = []): void
    {
        $missingContexts = [];

        if (empty($contextTypes) || in_array('cart', $contextTypes)) {
            if (!$this->cartContext) {
                /** @var TYPE_NAME $missingContexts */
                $missingContexts[] = 'CartContext';
            }
        }

        if (empty($contextTypes) || in_array('checkout', $contextTypes)) {
            if (!$this->checkoutContext) {
                $missingContexts[] = 'CheckoutContext';
            }
        }

        if (empty($contextTypes) || in_array('payment', $contextTypes)) {
            if (!$this->paymentContext) {
                $missingContexts[] = 'PaymentContext';
            }
        }

        if (empty($contextTypes) || in_array('product', $contextTypes)) {
            if (!$this->productContext) {
                $missingContexts[] = 'ProductContext';
            }
        }

        if (empty($contextTypes) || in_array('email', $contextTypes)) {
            if (!$this->emailContext) {
                $missingContexts[] = 'EmailContext';
            }
        }

        if (!empty($missingContexts)) {
            throw new \RuntimeException(
                sprintf('Missing required contexts: %s', implode(', ', $missingContexts))
            );
        }
    }

    /**
     * @Given I abandon my cart at the shipping step
     */
    public function iAbandonMyCartAtTheShippingStep(): void
    {
        try {
            // Ensure we have the required contexts
            $this->ensureRequiredContexts(['cart', 'checkout']);

            // Step 1: Proceed to checkout
            $this->cartContext->iProceedToCheckout();

            // Step 2: Start filling shipping information but don't complete
            // We'll just navigate away to simulate abandonment
            $baseUrl = $this->configService->getEnvironmentConfig('base_url');
            $this->browserService->visit($baseUrl);

            // Store abandonment status in shared state
            $this->stateService->set('funnel.abandoned', true);
            $this->stateService->set('funnel.abandoned_at', 'shipping');

            $this->logInfo('Abandoned cart at the shipping step');
        } catch (\Throwable $e) {
            $this->logError('Failed to abandon cart at the shipping step', $e);
            throw $e;
        }
    }

    /**
     * @Given I abandon my cart at the payment step
     */
    public function iAbandonMyCartAtThePaymentStep(): void
    {
        try {
            // Ensure we have the required contexts
            $this->ensureRequiredContexts(['cart', 'checkout']);

            // Step 1: Proceed to checkout
            $this->cartContext->iProceedToCheckout();

            // Step 2: Fill in shipping information
            $this->checkoutContext->iFillInTheShippingInformationWith('default');

            // Step 3: Select shipping method
            $this->checkoutContext->iSelectShippingMethod('standard');

            // Step 4: Abandon at payment step
            $baseUrl = $this->configService->getEnvironmentConfig('base_url');
            $this->browserService->visit($baseUrl);

            // Store abandonment status in shared state
            $this->stateService->set('funnel.abandoned', true);
            $this->stateService->set('funnel.abandoned_at', 'payment');

            $this->logInfo('Abandoned cart at the payment step');
        } catch (\Throwable $e) {
            $this->logError('Failed to abandon cart at the payment step', $e);
            throw $e;
        }
    }

    /**
     * @Then I should complete the purchase successfully
     */
    public function iShouldCompleteThePurchaseSuccessfully(): void
    {
        try {
            // Ensure we have the required contexts
            $this->ensureRequiredContexts(['checkout']);

            // Verify we're on the confirmation page
            $this->checkoutContext->iShouldBeOnTheOrderConfirmationPage();

            // Store success status in shared state
            $this->stateService->set('funnel.success', true);

            $this->logInfo('Purchase completed successfully');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify purchase completion', $e);
            throw $e;
        }
    }

    /**
     * @Then I should receive a confirmation email
     */
    public function iShouldReceiveAConfirmationEmail(): void
    {
        try {
            // Ensure we have the required contexts
            $this->ensureRequiredContexts(['email']);

            // Verify confirmation email
            $this->emailContext->iShouldReceiveAnOrderConfirmationEmail();

            $this->logInfo('Received confirmation email');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify confirmation email', $e);
            throw $e;
        }
    }

    /**
     * @Given /^I am on the multi-step funnel page "([^"]*)"$/
     */
    public function iAmOnTheMultiStepFunnelPage(string $funnelId): void
    {
        try {
            $this->loadFunnelData($funnelId);
            $funnelData = $this->stateService->get('currentFunnel');

            if (!isset($funnelData['multi_step']) || $funnelData['multi_step'] !== true) {
                throw new \RuntimeException(
                    sprintf('Funnel "%s" is not configured as a multi-step funnel', $funnelId)
                );
            }

            // Construct the funnel URL
            $baseUrl = $this->configService->getEnvironmentConfig('base_url');
            $funnelUrl = sprintf('%s/f/%s', $baseUrl, $funnelId);

            try {
                $this->browserService->visit($funnelUrl);
                $this->browserService->waitForPageToLoad();
                $this->logInfo("Visited multi-step funnel page: $funnelUrl");
            } catch (\Throwable $e) {
                throw new \RuntimeException(
                    sprintf('Failed to navigate to multi-step funnel page "%s": %s', $funnelId, $e->getMessage())
                );
            }
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to navigate to multi-step funnel page "%s"', $funnelId), $e);
            throw $e;
        }
    }

    /**
     * @Given /^I am on the subscription funnel page "([^"]*)"$/
     */
    public function iAmOnTheSubscriptionFunnelPage(string $funnelId): void
    {
        try {
            $this->loadFunnelData($funnelId);
            $funnelData = $this->stateService->get('currentFunnel');

            if (!isset($funnelData['subscription_enabled']) || $funnelData['subscription_enabled'] !== true) {
                throw new \RuntimeException(
                    sprintf('Funnel "%s" is not configured for subscriptions', $funnelId)
                );
            }

            // Construct the funnel URL
            $baseUrl = $this->configService->getEnvironmentConfig('base_url');
            $funnelUrl = sprintf('%s/specials/start/%s', $baseUrl, $funnelId);

            try {
                $this->browserService->visit($funnelUrl);
                $this->browserService->waitForPageToLoad();
                $this->logInfo("Visited subscription funnel page: $funnelUrl");
            } catch (\Throwable $e) {
                throw new \RuntimeException(
                    sprintf('Failed to navigate to subscription funnel page "%s": %s', $funnelId, $e->getMessage())
                );
            }
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to navigate to subscription funnel page "%s"', $funnelId), $e);
            throw $e;
        }
    }

    /**
     * @Given /^I am on the high-value funnel page "([^"]*)"$/
     */
    public function iAmOnTheHighValueFunnelPage(string $funnelId): void
    {
        try {
            $this->loadFunnelData($funnelId);
            $funnelData = $this->stateService->get('currentFunnel');

            if (!isset($funnelData['high_value']) || $funnelData['high_value'] !== true) {
                throw new \RuntimeException(
                    sprintf('Funnel "%s" is not configured as a high-value funnel', $funnelId)
                );
            }

            // Construct the funnel URL
            $baseUrl = $this->configService->getEnvironmentConfig('base_url');
            $funnelUrl = sprintf('%s/f/%s', $baseUrl, $funnelId);

            try {
                $this->browserService->visit($funnelUrl);
                $this->browserService->waitForPageToLoad();
                $this->logInfo("Visited high-value funnel page: $funnelUrl");
            } catch (\Throwable $e) {
                throw new \RuntimeException(
                    sprintf('Failed to navigate to high-value funnel page "%s": %s', $funnelId, $e->getMessage())
                );
            }
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to navigate to high-value funnel page "%s"', $funnelId), $e);
            throw $e;
        }
    }

    /**
     * @Given /^I verify the funnel product details$/
     */
    public function iVerifyTheFunnelProductDetails(): void
    {
        try {
            $funnelData = $this->stateService->get('funnel.current_funnel');
            if (!$funnelData) {
                throw new \RuntimeException('No funnel data available in shared context');
            }

            // Get expected product data from funnel configuration
            $expectedProduct = $funnelData['entry']['product'];

            // Get product details from the page
            $pageFactory = $this->getPageFactory();
            $productPage = $pageFactory->getPage('ProductPage');

            // Verify product name
            $actualProductName = $productPage->getProductName();
            if (strpos($actualProductName, $expectedProduct) === false) {
                throw new \RuntimeException(
                    sprintf('Expected product name "%s" not found in "%s"', $expectedProduct, $actualProductName)
                );
            }

            // Verify product price is displayed
            if (!$productPage->isPriceDisplayed()) {
                throw new \RuntimeException('Product price is not displayed on funnel page');
            }

            // Verify product description is displayed
            if (!$productPage->isDescriptionDisplayed()) {
                throw new \RuntimeException('Product description is not displayed on funnel page');
            }

            $this->logInfo(sprintf('Verified funnel product details for "%s"', $expectedProduct));
        } catch (\Throwable $e) {
            $this->logError('Failed to verify funnel product details', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify funnel product details: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^the upsell page fails to load$/
     */
    public function theUpsellPageFailsToLoad(): void
    {
        try {
            // Simulate a page load interruption using JavaScript
            $this->browserService->executeScript("window.stop();");

            // Store upsell failure state
            $this->stateService->set('funnel.upsell_failed', true);
            $this->stateService->set('funnel.upsell_failure_time', time());

            // Store current URL for debug purposes
            $currentUrl = $this->browserService->getCurrentUrl();
            $this->stateService->set('funnel.interrupted_url', $currentUrl);

            $this->logInfo(sprintf('Simulated upsell page failure at URL: %s', $currentUrl));
        } catch (\Throwable $e) {
            $this->logError('Failed to simulate upsell page failure', $e);
            throw new \RuntimeException(
                sprintf('Failed to simulate upsell page failure: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I close the browser without completing payment$/
     */
    public function iCloseTheBrowserWithoutCompletingPayment(): void
    {
        try {
            // Store current URL for debugging
            $currentUrl = $this->browserService->getCurrentUrl();
            $this->stateService->set('funnel.abandoned_url', $currentUrl);

            // Simulate closing the browser by clearing cookies and session data
            $this->browserService->clearCookies();

            // Navigate to a neutral page to simulate browser closure
            $this->browserService->visit('about:blank');

            // Set abandon flags in state
            $this->stateService->set('funnel.payment_abandoned', true);
            $this->stateService->set('funnel.abandon_time', time());

            $this->logInfo(sprintf('Simulated browser closing during payment at URL: %s', $currentUrl));
        } catch (\Throwable $e) {
            $this->logError('Failed to simulate browser closing', $e);
            throw new \RuntimeException(
                sprintf('Failed to simulate browser closing: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify only one upsell product is in the order$/
     */
    public function iVerifyOnlyOneUpsellProductIsInTheOrder(): void
    {
        try {
            $funnelData = $this->stateService->get('currentFunnel');
            $upsellProduct = $funnelData['upsell_product']['name'];

            // Get the confirmation page
            $confirmationPage = $this->getConfigService()->getPage('ConfirmationPage');

            // Get all products in the order
            $products = $confirmationPage->getOrderedProducts();

            // Count occurrences of the upsell product
            $upsellCount = 0;
            foreach ($products as $product) {
                if (strpos($product['name'], $upsellProduct) !== false) {
                    $upsellCount++;
                }
            }

            if ($upsellCount !== 1) {
                throw new \RuntimeException(
                    sprintf('Expected exactly 1 upsell product, found %d', $upsellCount)
                );
            }

            $this->logInfo("Verified only one upsell product is in the order");
        } catch (\Throwable $e) {
            $this->logError('Failed to verify upsell product count', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify upsell product count: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify both products are in the order$/
     */
    public function iVerifyBothProductsAreInTheOrder(): void
    {
        try {
            $funnelData = $this->stateService->get('currentFunnel');
            $initialProduct = $funnelData['initial_product']['name'];
            $upsellProduct = $funnelData['upsell_product']['name'];

            // Get the confirmation page
            $confirmationPage = $this->getConfigService()->getPage('ConfirmationPage');

            // Get all products in the order
            $products = $confirmationPage->getOrderedProducts();

            // Check both products are present
            $initialFound = false;
            $upsellFound = false;

            foreach ($products as $product) {
                if (strpos($product['name'], $initialProduct) !== false) {
                    $initialFound = true;
                }
                if (strpos($product['name'], $upsellProduct) !== false) {
                    $upsellFound = true;
                }
            }

            if (!$initialFound) {
                throw new \RuntimeException(
                    sprintf('Initial product "%s" not found in order', $initialProduct)
                );
            }

            if (!$upsellFound) {
                throw new \RuntimeException(
                    sprintf('Upsell product "%s" not found in order', $upsellProduct)
                );
            }

            $this->logInfo("Verified both products are in the order");
        } catch (\Throwable $e) {
            $this->logError('Failed to verify both products in order', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify both products in order: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the order contains all three products$/
     */
    public function iVerifyTheOrderContainsAllThreeProducts(): void
    {
        try {
            $funnelData = $this->stateService->get('currentFunnel');
            $initialProduct = $funnelData['initial_product']['name'];
            $firstUpsell = $funnelData['upsells'][0]['name'];
            $secondUpsell = $funnelData['upsells'][1]['name'];

            // Get the confirmation page
            $confirmationPage = $this->getConfigService()->getPage('ConfirmationPage');

            // Get all products in the order
            $products = $confirmationPage->getOrderedProducts();

            // Check all three products are present
            $initialFound = false;
            $firstUpsellFound = false;
            $secondUpsellFound = false;

            foreach ($products as $product) {
                if (strpos($product['name'], $initialProduct) !== false) {
                    $initialFound = true;
                }
                if (strpos($product['name'], $firstUpsell) !== false) {
                    $firstUpsellFound = true;
                }
                if (strpos($product['name'], $secondUpsell) !== false) {
                    $secondUpsellFound = true;
                }
            }

            if (!$initialFound || !$firstUpsellFound || !$secondUpsellFound) {
                throw new \RuntimeException(
                    sprintf('Not all expected products found in order')
                );
            }

            $this->logInfo("Verified all three products are in the order");
        } catch (\Throwable $e) {
            $this->logError('Failed to verify all three products in order', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify all three products in order: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the product instructions contain all warnings$/
     */
    public function iVerifyTheProductInstructionsContainAllWarnings(): void
    {
        try {
            $funnelData = $this->stateService->get('currentFunnel');

            if (!isset($funnelData['restrictions'])) {
                throw new \RuntimeException('No restrictions defined for this funnel');
            }

            foreach ($funnelData['restrictions'] as $category => $warnings) {
                if (is_array($warnings)) {
                    foreach ($warnings as $warning) {
                        // Check if product instructions section contains the warning
                        $instructionsText = $this->browserService->getElementText('.product-instructions');
                        if (strpos($instructionsText, $warning) === false) {
                            throw new \RuntimeException(
                                sprintf('Warning "%s" not found in product instructions', $warning)
                            );
                        }
                    }
                }
            }

            $this->logInfo("Verified product instructions contain all warnings");
        } catch (\Throwable $e) {
            $this->logError('Failed to verify warnings in product instructions', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify warnings in product instructions: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I select the subscription frequency "([^"]*)"$/
     */
    public function iSelectTheSubscriptionFrequency(string $frequency): void
    {
        try {
            // Find and select the frequency option
            $frequencySelector = '.subscription-frequency-options';
            $this->browserService->waitForElementVisible($frequencySelector);

            // Select the option by text
            $this->browserService->selectOptionByText($frequencySelector, $frequency);

            $this->stateService->set('selectedFrequency', $frequency);
            $this->logInfo("Selected subscription frequency: $frequency");
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to select subscription frequency: %s', $frequency), $e);
            throw new \RuntimeException(
                sprintf('Failed to select subscription frequency: %s - %s', $frequency, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the subscription frequency is "([^"]*)"$/
     */
    public function iVerifyTheSubscriptionFrequencyIs(string $expectedFrequency): void
    {
        try {
            $selectedFrequency = $this->stateService->get('selectedFrequency');
            if ($selectedFrequency !== $expectedFrequency) {
                throw new \RuntimeException(
                    sprintf('Expected subscription frequency "%s", got "%s"', $expectedFrequency, $selectedFrequency)
                );
            }
            $this->logInfo("Verified subscription frequency is: $expectedFrequency");
        } catch (\Throwable $e) {
            $this->logError('Failed to verify subscription frequency', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify subscription frequency: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I have a valid discount code "([^"]*)"$/
     */
    public function iHaveAValidDiscountCode(string $code): void
    {
        $this->stateService->set('discountCode', $code);
        $this->logInfo("Stored discount code: $code");
    }

    /**
     * @When /^I enter the discount code$/
     */
    public function iEnterTheDiscountCode(): void
    {
        try {
            $code = $this->stateService->get('discountCode');
            if (!$code) {
                throw new \RuntimeException('No discount code found in shared context');
            }

            // Find and interact with discount code field and button
            $this->browserService->fillField('#discount-code', $code);
            $this->browserService->clickElement('#apply-discount');
            $this->browserService->waitForAjaxToComplete();

            $this->logInfo("Entered discount code: $code");
        } catch (\Throwable $e) {
            $this->logError('Failed to enter discount code', $e);
            throw new \RuntimeException(
                sprintf('Failed to enter discount code: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^the discount should be applied to the order$/
     */
    public function theDiscountShouldBeAppliedToTheOrder(): void
    {
        try {
            // Check for discount in the cart summary
            $discountElement = $this->browserService->findElement('.discount-amount');
            if (!$discountElement) {
                throw new \RuntimeException('Discount not found in order summary');
            }

            // Check discount is non-zero
            $discountText = $discountElement->getText();
            if (strpos($discountText, '0.00') !== false) {
                throw new \RuntimeException('Discount amount is zero');
            }

            $this->logInfo("Verified discount applied to order: $discountText");
        } catch (\Throwable $e) {
            $this->logError('Failed to verify discount applied to order', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify discount applied to order: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I am on the sales funnel page "([^"]*)"$/
     */
    public function iAmOnTheSalesFunnelPage(string $funnelId): void
    {
        try {
            // Load funnel data
            $this->loadFunnelData($funnelId);
            $this->logInfo(sprintf("Starting navigation to sales funnel page: %s", $funnelId));

            // Construct the funnel URL
            $baseUrl = $this->configService->getEnvironmentConfig('base_url');
            $funnelUrl = sprintf('%s/specials/start/%s', $baseUrl, $funnelId);
            $this->logInfo(sprintf("Funnel entry URL: %s", $funnelUrl));

            // Store original URL for tracking
            $this->stateService->set('funnel.entry_url', $funnelUrl);

            // Navigate to the funnel page with extended timeout
            try {
                // First visit the URL
                $this->browserService->visit($funnelUrl);

                // Track the redirect chain
                $this->logInfo("Tracking redirect chain for funnel page");
                $redirectResults = $this->browserService->waitForRedirect(90, 10, 3);

                // Store redirect info in state for debugging
                $this->stateService->set('funnel.redirect_chain', $redirectResults);

                // Log redirect details
                $this->logInfo(sprintf(
                    "Redirect chain complete: %d redirects, final URL: %s",
                    $redirectResults['redirect_count'],
                    $redirectResults['final_url']
                ));

                // Wait for page to fully load with extended timeout
                $this->browserService->waitForPageToLoad(90);

            } catch (\Throwable $e) {
                $this->logError(sprintf("Error during funnel page navigation: %s", $e->getMessage()), $e);
                $this->browserService->takeScreenshot('funnel_navigation_error');
                throw new \RuntimeException(
                    sprintf('Failed to navigate to sales funnel page "%s": %s', $funnelId, $e->getMessage()),
                    0,
                    $e
                );
            }

            // Detect final landing page
            $finalUrl = $this->browserService->getCurrentUrl();
            $this->stateService->set('funnel.final_url', $finalUrl);

            // Analyze final URL to determine what page we landed on
            $landingPageType = $this->determineLandingPageType($finalUrl);
            $this->stateService->set('funnel.landing_page_type', $landingPageType);

            $this->logInfo(sprintf("Detected landing page type: %s", $landingPageType));

            // Wait for specific elements based on landing page type
            $this->waitForLandingPageElements($landingPageType);
            
            // Store current funnel info in state
            $funnelData = $this->stateService->get('currentFunnel');
            if (!$funnelData) {
                throw new \RuntimeException(sprintf('Funnel data not found for ID: %s', $funnelId));
            }

            $this->stateService->set('funnel.current_funnel', [
                'entry' => [
                    'url' => 'specials/start/' . $funnelId,
                    'product' => $funnelData['initial_product']['name'] ?? 'Total Harmony'
                ],
                'upsell' => [
                    'url' => 'specials/start/' . $funnelId . '/upsell',
                    'product' => $funnelData['upsell_product']['name'] ?? 'Ancient Roots',
                    'quantity' => 'minimum'
                ]
            ]);

            $this->stateService->set('page.current', $landingPageType);
            $this->logInfo(sprintf(
                "Successfully navigated to sales funnel page: %s and landed on %s page",
                $funnelUrl,
                $landingPageType
            ));
        } catch (\Throwable $e) {
            $this->logError(
                sprintf('Failed to navigate to sales funnel page "%s"', $funnelId),
                $e
            );
            throw new \RuntimeException(
                sprintf('Failed to navigate to sales funnel page "%s": %s', $funnelId, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Determine the type of page we landed on after funnel entry
     *
     * @param string $url The URL to analyze
     * @return string Page type identifier
     */
    private function determineLandingPageType(string $url): string
    {
        // Check for common URL patterns
        if (strpos($url, '/checkout') !== false || strpos($url, '/finalize') !== false) {
            return 'checkout';
        }

        if (strpos($url, '/upsell') !== false) {
            return 'upsell';
        }

        if (strpos($url, '/product') !== false) {
            return 'product';
        }

        if (strpos($url, '/cart') !== false) {
            return 'cart';
        }

        if (strpos($url, '/confirmation') !== false || strpos($url, '/thank-you') !== false) {
            return 'confirmation';
        }

        // If URL patterns don't match, try to detect by page content
        try {
            // Check for checkout page elements
            $checkoutElements = ['.checkout-container', '#checkout-form', '.shipping-information'];
            foreach ($checkoutElements as $selector) {
                if ($this->browserService->elementExists($selector)) {
                    return 'checkout';
                }
            }

            // Check for product page elements
            $productElements = ['.product-details', '.product-description', '.add-to-cart'];
            foreach ($productElements as $selector) {
                if ($this->browserService->elementExists($selector)) {
                    return 'product';
                }
            }

            // Check for cart page elements
            $cartElements = ['.cart-items', '.cart-summary', '.proceed-to-checkout'];
            foreach ($cartElements as $selector) {
                if ($this->browserService->elementExists($selector)) {
                    return 'cart';
                }
            }
        } catch (\Throwable $e) {
            $this->logWarning("Error during page type detection: " . $e->getMessage());
        }

        // Default to funnel as we don't know exactly
        return 'funnel';
    }

    /**
     * Wait for specific elements based on the landing page type
     *
     * @param string $pageType The page type to wait for
     * @return void
     */
    private function waitForLandingPageElements(string $pageType): void
    {
        $selectors = [];
        $timeout = 60;

        switch ($pageType) {
            case 'checkout':
                $selectors = [
                    '.checkout-container',
                    '#checkout-form',
                    '.shipping-information',
                    'form[name="checkout"]'
                ];
                break;

            case 'product':
                $selectors = [
                    '.product-details',
                    '.product-description',
                    '.add-to-cart',
                    '.product-container'
                ];
                break;

            case 'cart':
                $selectors = [
                    '.cart-items',
                    '.cart-summary',
                    '.proceed-to-checkout',
                    '.cart-container'
                ];
                break;

            case 'upsell':
                $selectors = [
                    '.upsell-container',
                    '.upsell-product',
                    '.upsell-offer',
                    '.accept-offer',
                    '.decline-offer'
                ];
                break;

            case 'confirmation':
                $selectors = [
                    '.order-confirmation',
                    '.thank-you-message',
                    '.order-details',
                    '.confirmation-page'
                ];
                break;

            default:
                // For unknown page types, wait for any visible content
                $selectors = [
                    'main',
                    '#content',
                    '.page-content',
                    '.main-content'
                ];
        }

        $this->logInfo(sprintf("Waiting for %s page elements", $pageType));

        $elementFound = false;
        foreach ($selectors as $selector) {
            if ($this->browserService->waitForElementVisible($selector, 10)) {
                $this->logInfo(sprintf("Found %s page element: %s", $pageType, $selector));
                $elementFound = true;
                break;
            }
        }

        if (!$elementFound) {
            $this->logWarning(sprintf(
                "No specific %s page elements found. Page might not have loaded correctly.",
                $pageType
            ));
            $this->browserService->takeScreenshot(sprintf('missing_%s_elements', $pageType));
        }
    }

    /**
     * Load funnel data for a specific funnel ID
     *
     * @param string $funnelId Funnel ID
     * @throws \RuntimeException When funnel data is not found
     */
    private function loadFunnelData(string $funnelId): void
    {
        // Try to load from the test data service first
        try {
            $testDataService = $this->container->get('App\Service\Data\TestDataServiceInterface');
            if ($testDataService && $testDataService->hasData($funnelId)) {
                $funnelData = $testDataService->getData($funnelId);
                $this->stateService->set('currentFunnel', $funnelData);
                $this->logInfo(sprintf('Loaded funnel data for ID: %s from test data service', $funnelId));
                return;
            }

            if ($testDataService && $testDataService->hasData('funnel.' . $funnelId)) {
                $funnelData = $testDataService->getData('funnel.' . $funnelId);
                $this->stateService->set('currentFunnel', $funnelData);
                $this->logInfo(sprintf('Loaded funnel data for ID: %s from test data service with prefix', $funnelId));
                return;
            }
        } catch (\Throwable $e) {
            $this->logWarning(sprintf('Failed to load funnel data from test data service: %s', $e->getMessage()));
        }

        // Fall back to hardcoded test data
        $funnels = [
            'total-harmony-funnel' => [
                'code' => 'total-harmony-funnel',
                'initial_product' => [
                    'name' => 'Total Harmony',
                    'price' => 134.85
                ],
                'upsell_product' => [
                    'name' => 'Ancient Roots',
                    'price' => 34.95
                ],
                'shipping_threshold' => 100.00
            ],
            'total-harmony-funnel' => [
                'code' => 'total-harmony-funnel',
                'initial_product' => [
                    'name' => 'Total Harmony',
                    'price' => 89.00
                ],
                'upsell_product' => [
                    'name' => 'Ancient Roots',
                    'price' => 29.00
                ],
                'shipping_threshold' => 100.00
            ],
            'ancient-roots-small' => [
                'code' => 'ancient-roots-small',
                'initial_product' => [
                    'name' => 'Ancient Roots',
                    'price' => 49.00
                ],
                'upsell_product' => [
                    'name' => 'Daily Vitality',
                    'price' => 19.00
                ],
                'shipping_threshold' => 150.00
            ],
            'natures-gift-basic' => [
                'code' => 'natures-gift-basic',
                'initial_product' => [
                    'name' => 'Nature\'s Gift',
                    'price' => 79.00
                ],
                'upsell_product' => [
                    'name' => 'Daily Wellness',
                    'price' => 29.00
                ],
                'shipping_threshold' => 100.00,
                'restrictions' => [
                    'dietary' => [
                        'Contains nuts - not suitable for nut allergies',
                        'Not suitable for pregnant women'
                    ],
                    'health' => [
                        'Consult doctor before use if taking medication'
                    ]
                ]
            ],
            'premium-journey' => [
                'code' => 'premium-journey',
                'initial_product' => [
                    'name' => 'Premium Wellness Kit',
                    'price' => 129.00
                ],
                'multi_step' => true,
                'upsells' => [
                    [
                        'name' => 'Daily Supplement',
                        'price' => 39.00,
                        'step' => 1
                    ],
                    [
                        'name' => 'Premium Travel Case',
                        'price' => 29.00,
                        'step' => 2
                    ]
                ],
                'shipping_threshold' => 150.00
            ],
            'monthly-harmony' => [
                'code' => 'monthly-harmony',
                'initial_product' => [
                    'name' => 'Total Harmony',
                    'price' => 89.00
                ],
                'upsell_product' => [
                    'name' => 'Wellness Boost',
                    'price' => 29.00
                ],
                'subscription_enabled' => true,
                'frequencies' => [
                    '30 days' => 15,
                    '60 days' => 10,
                    '90 days' => 5
                ],
                'shipping_threshold' => 100.00
            ],
            'premium-package' => [
                'code' => 'premium-package',
                'initial_product' => [
                    'name' => 'Premium Package',
                    'price' => 149.00
                ],
                'high_value' => true,
                'high_value_threshold' => 200.00,
                'upsells' => [
                    [
                        'name' => 'VIP Supplement',
                        'price' => 59.00,
                        'step' => 1
                    ],
                    [
                        'name' => 'Luxury Travel Kit',
                        'price' => 49.00,
                        'step' => 2
                    ]
                ],
                'shipping_threshold' => 100.00
            ],
            'demo-dsv-1' => [
                'code' => 'demo-dsv-1',
                'initial_product' => [
                    'name' => 'Dark Spot Vanish',
                    'price' => 89.00
                ],
                'upsell_product' => [
                    'name' => 'Relax + Restore',
                    'price' => 10.00
                ],
                'shipping_threshold' => 100.00
            ]
        ];

        if (!isset($funnels[$funnelId])) {
            throw new \RuntimeException(sprintf('Funnel data not found for ID: %s', $funnelId));
        }

        $this->stateService->set('currentFunnel', $funnels[$funnelId]);
        $this->logInfo(sprintf('Loaded funnel data for ID: %s', $funnelId));
    }

    protected function getPageFactory(): object
    {
        return $this->container->get('app.page_factory');
    }
}
