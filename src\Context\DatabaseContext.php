<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use PDO;
use PDOStatement;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for database operations
 */
class DatabaseContext extends BaseContext
{
    private ConfigurationServiceInterface $configService;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;
    private ?PDO $connection = null;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param ConfigurationServiceInterface|null $configService Configuration service
     * @param TestDataServiceInterface|null $dataService Test data service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface            $container = null,
        ?ConfigurationServiceInterface $configService = null,
        ?TestDataServiceInterface      $dataService = null,
        ?SharedStateServiceInterface   $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->configService = $configService ?? $container->get(ConfigurationServiceInterface::class);
            $this->dataService = $dataService ?? $container->get(TestDataServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->configService = $configService ?? $this->createMockConfigService();
            $this->dataService = $dataService ?? $this->createMockDataService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $brand = $this->configService->getCurrentBrand();
        $env = $this->configService->getCurrentEnvironment();
        $this->logInfo(sprintf(
            'Initialized DatabaseContext for brand "%s" in environment "%s"',
            $brand, $env
        ));
    }

    /**
     * Create a mock configuration service for testing
     *
     * @return ConfigurationServiceInterface
     */
    private function createMockConfigService(): ConfigurationServiceInterface
    {
        return new class implements ConfigurationServiceInterface {
            private string $currentBrand = 'aeons';
            private string $currentEnvironment = 'stage';
            private array $config = [
                'base_url' => 'https://aeonstest.info',
                'database' => [
                    'host' => 'localhost',
                    'port' => 3306,
                    'name' => 'test_db',
                    'user' => 'test_user',
                    'password' => 'test_password'
                ]
            ];

            public function getBrandConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getEnvironmentConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getCurrentBrand(): string
            {
                return $this->currentBrand;
            }

            public function getCurrentEnvironment(): string
            {
                return $this->currentEnvironment;
            }

            public function setBrand(string $brand): void
            {
                $this->currentBrand = $brand;
            }

            public function setEnvironment(string $environment): void
            {
                $this->currentEnvironment = $environment;
            }

            public function getConfigValue(string $key)
            {
                return $this->config[$key] ?? null;
            }
        };
    }

    /**
     * Create a mock test data service for testing
     *
     * @return TestDataServiceInterface
     */
    private function createMockDataService(): TestDataServiceInterface
    {
        return new class implements TestDataServiceInterface {
            private array $testData = [];

            public function loadTestData(string $brand, string $type, ?string $key = null): array
            {
                return [];
            }

            public function getTestData(string $type, ?string $key = null): array
            {
                return [];
            }

            public function getRandomTestData(string $type): array
            {
                return [];
            }

            public function validateTestData(string $type, array $data): bool
            {
                return true;
            }

            public function registerData(string $key, array $data): void
            {
                $this->testData[$key] = $data;
            }

            public function getData(string $key)
            {
                return $this->testData[$key] ?? null;
            }

            public function hasData(string $key): bool
            {
                return isset($this->testData[$key]);
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @When I execute database query :query
     */
    public function iExecuteDatabaseQuery(string $query): void
    {
        try {
            $result = $this->executeQuery($query);
            $rows = $result->fetchAll(PDO::FETCH_ASSOC);

            $this->stateService->set('database.last_query', $query);
            $this->stateService->set('database.last_result', $rows);
            $this->stateService->set('database.last_row_count', count($rows));

            $this->logInfo(sprintf('Executed database query: %s', $query));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to execute database query: %s', $query), $e);
            throw new \RuntimeException(
                sprintf('Failed to execute database query: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Execute a database query
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return PDOStatement Query result
     * @throws \RuntimeException When query execution fails
     */
    public function executeQuery(string $sql, array $params = []): PDOStatement
    {
        try {
            $connection = $this->getConnection();
            $statement = $connection->prepare($sql);
            $statement->execute($params);

            return $statement;
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to execute query: %s', $sql), $e);
            throw new \RuntimeException(
                sprintf('Failed to execute query: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Get database connection
     *
     * @return PDO Database connection
     * @throws \RuntimeException When connection fails
     */
    private function getConnection(): PDO
    {
        if ($this->connection) {
            return $this->connection;
        }

        // Check if we're using mock services
        if ($this->configService instanceof \Closure ||
            (get_class($this->configService) === 'class@anonymous' &&
                $this->configService->getCurrentBrand() === 'aeons' &&
                $this->configService->getCurrentEnvironment() === 'stage')) {
            // Create a mock PDO connection for testing
            $this->connection = $this->createMockPDOConnection();
            $this->logInfo('Created mock database connection for testing');
            return $this->connection;
        }

        try {
            $dbConfig = $this->configService->getEnvironmentConfig('database');

            if (!$dbConfig) {
                throw new \RuntimeException('Database configuration not found');
            }

            $dsn = sprintf(
                'mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4',
                $dbConfig['host'],
                $dbConfig['port'] ?? 3306,
                $dbConfig['name']
            );

            $this->connection = new PDO(
                $dsn,
                $dbConfig['user'],
                $dbConfig['password'],
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]
            );

            $this->logInfo(sprintf('Connected to database "%s" on "%s"', $dbConfig['name'], $dbConfig['host']));

            return $this->connection;
        } catch (\Throwable $e) {
            $this->logError('Failed to connect to database', $e);
            throw new \RuntimeException(
                sprintf('Failed to connect to database: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Create a mock PDO connection for testing
     *
     * @return PDO Mock PDO connection
     */
    private function createMockPDOConnection(): PDO
    {
        // Create a mock PDO connection using SQLite in-memory database
        $pdo = new PDO('sqlite::memory:');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create some basic tables for testing
        $pdo->exec('CREATE TABLE sylius_customer (id INTEGER PRIMARY KEY, email TEXT)');
        $pdo->exec('CREATE TABLE sylius_shop_user (id INTEGER PRIMARY KEY, email TEXT, customer_id INTEGER)');
        $pdo->exec('CREATE TABLE sylius_order (id INTEGER PRIMARY KEY, number TEXT, customer_id INTEGER, state TEXT, created_at TEXT)');

        // Insert some test data
        $pdo->exec("INSERT INTO sylius_customer (id, email) VALUES (1, '<EMAIL>')");
        $pdo->exec("INSERT INTO sylius_shop_user (id, email, customer_id) VALUES (1, '<EMAIL>', 1)");
        $pdo->exec("INSERT INTO sylius_order (id, number, customer_id, state, created_at) VALUES (1, 'ORDER-001', 1, 'new', '" . date('Y-m-d H:i:s') . "')");

        return $pdo;
    }

    /**
     * @Then the database query should return :count rows
     */
    public function theDatabaseQueryShouldReturnRows(int $count): void
    {
        $rowCount = $this->stateService->get('database.last_row_count');

        if ($rowCount !== $count) {
            throw new \RuntimeException(
                sprintf('Expected %d rows, but got %d', $count, $rowCount)
            );
        }

        $this->logInfo(sprintf('Verified database query returned %d rows', $count));
    }

    /**
     * @Then the database query result should contain :value
     */
    public function theDatabaseQueryResultShouldContain(string $value): void
    {
        $result = $this->stateService->get('database.last_result');

        if (!$result) {
            throw new \RuntimeException('No database query result available');
        }

        $found = false;
        foreach ($result as $row) {
            foreach ($row as $column) {
                if (strpos((string)$column, $value) !== false) {
                    $found = true;
                    break 2;
                }
            }
        }

        if (!$found) {
            throw new \RuntimeException(
                sprintf('Value "%s" not found in database query result', $value)
            );
        }

        $this->logInfo(sprintf('Verified database query result contains "%s"', $value));
    }

    /**
     * Finds a user by email in the database
     *
     * @param string $email User email to find
     * @return array|null User data if found, null otherwise
     * @throws \RuntimeException When database query fails
     */
    public function findUserByEmail(string $email): ?array
    {
        try {
            $sql = "SELECT * FROM sylius_shop_user WHERE email = ?";
            $result = $this->executeQuery($sql, [$email]);
            $user = $result->fetch(PDO::FETCH_ASSOC) ?: null;

            if ($user) {
                $this->logInfo(sprintf('Found user with email "%s"', $email));
            } else {
                $this->logInfo(sprintf('User with email "%s" not found', $email));
            }

            return $user;
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to find user by email "%s"', $email), $e);
            throw new \RuntimeException(
                sprintf('Failed to find user by email "%s": %s', $email, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets the current order ID from the database
     *
     * @return string Order ID
     * @throws \RuntimeException When order not found or query fails
     */
    public function getCurrentOrderId(): string
    {
        try {
            $userEmail = $this->getUserEmail();

            $sql = "SELECT number FROM sylius_order
                    WHERE customer_id = (SELECT id FROM sylius_customer WHERE email = ?)
                    ORDER BY created_at DESC LIMIT 1";

            $result = $this->executeQuery($sql, [$userEmail]);
            $orderId = $result->fetchColumn();

            if (!$orderId) {
                throw new \RuntimeException('No order found for current user');
            }

            // Store in shared state for other contexts to use
            $this->stateService->set('order.number', $orderId);
            $this->logInfo(sprintf('Retrieved current order ID: %s', $orderId));

            return $orderId;
        } catch (\Throwable $e) {
            $this->logError('Failed to get current order ID', $e);
            throw new \RuntimeException(
                sprintf('Failed to get current order ID: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Get user email from shared state
     *
     * @return string User email
     * @throws \RuntimeException When user email is not found
     */
    private function getUserEmail(): string
    {
        $userData = $this->stateService->get('user.data');

        if (!$userData || !isset($userData['email'])) {
            throw new \RuntimeException('User email not found in shared state');
        }

        return $userData['email'];
    }

    /**
     * Gets the order status from the database
     *
     * @param string $orderId Order ID to check
     * @return string Order status
     * @throws \RuntimeException When order not found or query fails
     */
    public function getOrderStatus(string $orderId): string
    {
        try {
            $sql = "SELECT state FROM sylius_order WHERE number = ?";
            $result = $this->executeQuery($sql, [$orderId]);
            $status = $result->fetchColumn();

            if ($status === false) {
                throw new \RuntimeException(sprintf('Order "%s" not found', $orderId));
            }

            $this->logInfo(sprintf('Retrieved status "%s" for order "%s"', $status, $orderId));
            return $status;
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to get status for order "%s"', $orderId), $e);
            throw new \RuntimeException(
                sprintf('Failed to get status for order "%s": %s', $orderId, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets the count of cancelled orders for a user
     *
     * @param string $userEmail User email to check
     * @return int Count of cancelled orders
     * @throws \RuntimeException When query fails
     */
    public function getCancelledOrderCount(string $userEmail): int
    {
        try {
            $sql = "SELECT COUNT(*) FROM sylius_order
                    WHERE customer_id = (SELECT id FROM sylius_customer WHERE email = ?)
                    AND state = 'cancelled'";

            $result = $this->executeQuery($sql, [$userEmail]);
            $count = (int)$result->fetchColumn();

            $this->logInfo(sprintf('Found %d cancelled orders for user "%s"', $count, $userEmail));
            return $count;
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to get cancelled order count for user "%s"', $userEmail), $e);
            throw new \RuntimeException(
                sprintf('Failed to get cancelled order count for user "%s": %s', $userEmail, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets orders by email and status
     *
     * @param string $email User email
     * @param string $status Order status to filter by
     * @return array Orders matching the criteria
     * @throws \RuntimeException When query fails
     */
    public function getOrdersByEmail(string $email, string $status): array
    {
        try {
            $sql = "SELECT number, state, created_at
                    FROM sylius_order
                    WHERE customer_id = (SELECT id FROM sylius_customer WHERE email = ?)
                    AND state = ?
                    ORDER BY created_at DESC";

            $result = $this->executeQuery($sql, [$email, $status]);
            $orders = $result->fetchAll(PDO::FETCH_ASSOC);

            $this->logInfo(sprintf('Found %d orders with status "%s" for user "%s"', count($orders), $status, $email));
            return $orders;
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to get orders for user "%s" with status "%s"', $email, $status), $e);
            throw new \RuntimeException(
                sprintf('Failed to get orders for user "%s" with status "%s": %s', $email, $status, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Updates the order timestamp for testing purposes
     *
     * @param string $orderId Order ID to update
     * @param int $hoursDiff Hours to adjust the timestamp by (positive or negative)
     * @throws \RuntimeException When update fails
     */
    public function updateOrderTimestamp(string $orderId, int $hoursDiff): void
    {
        try {
            $sql = "UPDATE sylius_order
                    SET created_at = DATE_ADD(created_at, INTERVAL ? HOUR)
                    WHERE number = ?";

            $this->executeQuery($sql, [$hoursDiff, $orderId]);

            $this->logInfo(sprintf('Updated timestamp for order "%s" by %d hours', $orderId, $hoursDiff));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to update timestamp for order "%s"', $orderId), $e);
            throw new \RuntimeException(
                sprintf('Failed to update timestamp for order "%s": %s', $orderId, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Runs the abandoned cart cleanup job
     *
     * @throws \RuntimeException When job fails
     */
    public function runCleanupJob(): void
    {
        try {
            $sql = "UPDATE sylius_order
                    SET state = 'expired'
                    WHERE state = 'abandoned'
                    AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)";

            $result = $this->executeQuery($sql);
            $affectedRows = $result->rowCount();

            $this->logInfo(sprintf('Ran cleanup job, affecting %d abandoned orders', $affectedRows));
        } catch (\Throwable $e) {
            $this->logError('Failed to run cleanup job', $e);
            throw new \RuntimeException(
                sprintf('Failed to run cleanup job: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }
}
