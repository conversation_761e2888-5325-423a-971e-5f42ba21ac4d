<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Process\Process;

/**
 * Context for SSH operations
 */
class SSHContext extends BaseContext
{
    private SharedStateServiceInterface $stateService;
    private string $host;
    private string $username;
    private string $projectRoot;
    private bool $isCI;
    private string $sshKeyPath;
    private array $sshConfig = [];
    private bool $createdNewKey = false;
    private bool $sshAvailable = false;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface          $container = null,
        ?SharedStateServiceInterface $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->stateService = $stateService ?? $this->createMockStateService();
        }
        $this->projectRoot = dirname(__DIR__, 2);

        // Load SSH configuration
        $this->host = getenv('SSH_HOST');
        $this->username = getenv('SSH_USER');

        if (!$this->host || !$this->username) {
            if ($this->isCI) {
                // In CI, we expect SSH configuration to be available
                $this->logError('SSH configuration not complete in CI environment');
                throw new \RuntimeException('SSH configuration not complete. Please check your CI/CD variables.');
            } else if ($container === null) {
                // Use mock values when running in test environment without container
                $this->host = 'test.example.com';
                $this->username = 'testuser';
                $this->logInfo('Using mock SSH configuration for testing');
                $this->sshAvailable = true;
            } else {
                // In local environment, make SSH optional
                $this->logInfo('SSH configuration not complete. SSH-related steps will be skipped.');
                $this->sshAvailable = false;
                return; // Skip the rest of the initialization
            }
        } else {
            // SSH configuration is complete
            $this->sshAvailable = true;
        }

        // Detect if running in CI environment
        $this->isCI = getenv('CI') === 'true';

        // Setup SSH configuration
        try {
            $this->setupSSH();

            // Check if we have valid SSH configuration
            if ($this->host === 'dummy' || $this->username === 'dummy') {
                $this->logInfo('Using dummy SSH configuration. SSH-related steps will be skipped.');
                $this->sshAvailable = false;
                return; // Skip the rest of the initialization
            }

            // Test connection on initialization
            $this->testConnection();
            $this->logConnection();

            $this->logInfo(sprintf('SSH Context initialized with host: %s, user: %s', $this->host, $this->username));
            $this->sshAvailable = true;
        } catch (\Throwable $e) {
            if ($this->isCI) {
                // In CI, we expect SSH configuration to be available
                $this->logError('Failed to initialize SSH context', $e);
                throw new \RuntimeException(
                    sprintf('Failed to initialize SSH context: %s', $e->getMessage()),
                    0,
                    $e
                );
            } else {
                // In local environment, make SSH optional
                $this->logInfo('SSH connection failed. SSH-related steps will be skipped.');
                $this->sshAvailable = false;
            }
        }
    }

    /**
     * Set up SSH configuration based on environment
     *
     * @throws \RuntimeException When SSH key setup fails
     */
    private function setupSSH(): void
    {
        try {
            // Check if we're using mock values
            if ($this->host === 'test.example.com' && $this->username === 'testuser') {
                $this->setupMockEnvironment();
                $this->logInfo('Using mock SSH configuration');
                return;
            }

            if ($this->isCI) {
                $this->setupCIEnvironment();
            } else {
                $this->setupLocalEnvironment();
            }

            // Verify the key file exists and is readable
            if (!file_exists($this->sshKeyPath)) {
                throw new \RuntimeException("SSH key file not found at: " . $this->sshKeyPath);
            }

            if (!is_readable($this->sshKeyPath)) {
                throw new \RuntimeException("SSH key file is not readable at: " . $this->sshKeyPath);
            }

            $this->logInfo('SSH configuration setup completed successfully');
        } catch (\Throwable $e) {
            $this->logError('Failed to setup SSH configuration', $e);
            throw new \RuntimeException(
                sprintf('Failed to setup SSH configuration: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Set up SSH configuration for CI environment
     */
    private function setupCIEnvironment(): void
    {
        $this->sshKeyPath = '~/.ssh/id_rsa';
        $this->sshConfig = [
            '-i', $this->sshKeyPath,
            '-o', 'StrictHostKeyChecking=no',
            '-o', 'UserKnownHostsFile=/dev/null'
        ];
        $this->logInfo('SSH configuration setup for CI environment');
    }

    /**
     * Set up SSH configuration for mock environment
     */
    private function setupMockEnvironment(): void
    {
        $this->sshKeyPath = 'mock_ssh_key';
        $this->sshConfig = [
            '-i', $this->sshKeyPath,
            '-o', 'StrictHostKeyChecking=no',
            '-o', 'UserKnownHostsFile=/dev/null'
        ];
        $this->logInfo('SSH configuration setup for mock environment');
    }

    /**
     * Set up SSH configuration for local environment
     *
     * @throws \RuntimeException When SSH key setup fails
     */
    private function setupLocalEnvironment(): void
    {
        try {
            $isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
            $homeDir = $isWindows ? getenv('USERPROFILE') : getenv('HOME');
            $sshDir = $homeDir . DIRECTORY_SEPARATOR . '.ssh';

            if (!is_dir($sshDir)) {
                mkdir($sshDir, 0700, true);
            }

            $this->sshKeyPath = $sshDir . DIRECTORY_SEPARATOR . 'id_rsa';

            if (!$isWindows) {
                $this->setupUnixKeyFile();
            }

            $this->sshConfig = [
                '-i', $this->sshKeyPath,
                '-o', 'StrictHostKeyChecking=no',
                '-o', 'UserKnownHostsFile=/dev/null',
                '-o', 'PubkeyAcceptedKeyTypes=+ssh-rsa',
                '-o', 'IdentitiesOnly=yes',
                '-o', 'PreferredAuthentications=publickey',
                '-v'
            ];

            $this->logInfo('SSH configuration setup for local environment');
        } catch (\Throwable $e) {
            $this->logError('Failed to setup local SSH environment', $e);
            throw new \RuntimeException(
                sprintf('Failed to setup local SSH environment: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Set up SSH key file for Unix environments
     *
     * @throws \RuntimeException When key file creation fails
     */
    private function setupUnixKeyFile(): void
    {
        try {
            $key = getenv('SSH_KEY');
            if (!$key) {
                throw new \RuntimeException('SSH_KEY environment variable is not set');
            }

            // Process and write the key
            $key = str_replace(['\\n', '\n'], "\n", $key);
            $key = trim($key);

            if (file_put_contents($this->sshKeyPath, $key) === false) {
                throw new \RuntimeException('Failed to write SSH key file');
            }

            chmod($this->sshKeyPath, 0600);
            $this->createdNewKey = true;
            $this->logInfo('SSH key file created successfully');
        } catch (\Throwable $e) {
            $this->logError('Failed to setup Unix SSH key file', $e);
            throw new \RuntimeException(
                sprintf('Failed to setup Unix SSH key file: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Test SSH connection
     *
     * @throws \RuntimeException When connection test fails
     */
    private function testConnection(): void
    {
        // Skip actual connection test for mock environment
        if ($this->host === 'test.example.com' && $this->username === 'testuser') {
            $this->stateService->set('ssh.debug_info', 'Mock SSH debug info');
            $this->logInfo('Mock SSH connection test succeeded');
            return;
        }

        try {
            // Add debug command to see SSH version and key details
            $debugCmd = array_merge(
                ['ssh', '-V'],
                $this->sshConfig,
                ['-T', sprintf('%s@%s', $this->username, $this->host)]
            );

            $process = new Process($debugCmd);
            $process->run();
            $this->stateService->set('ssh.debug_info', $process->getOutput() . $process->getErrorOutput());

            // Actual connection test
            $testCmd = array_merge(
                ['ssh'],
                $this->sshConfig,
                [sprintf('%s@%s', $this->username, $this->host), 'echo "Connection test successful"']
            );

            $process = new Process($testCmd);
            $process->setTimeout(30);
            $process->run();

            if (!$process->isSuccessful()) {
                throw new \RuntimeException(sprintf(
                    "SSH connection test failed:\nError: %s\nOutput: %s\nCommand: %s",
                    $process->getErrorOutput(),
                    $process->getOutput(),
                    implode(' ', $testCmd)
                ));
            }

            $this->logInfo('SSH connection test succeeded');
        } catch (\Throwable $e) {
            $error = sprintf(
                "SSH Connection Error Details:\nHost: %s\nUsername: %s\nKey Path: %s\nKey exists: %s\nKey readable: %s\nError: %s",
                $this->host,
                $this->username,
                $this->sshKeyPath,
                file_exists($this->sshKeyPath) ? 'yes' : 'no',
                is_readable($this->sshKeyPath) ? 'yes' : 'no',
                $e->getMessage()
            );
            $this->stateService->set('ssh.connection_error', $error);
            $this->logError('SSH connection test failed', $e);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * Log SSH connection details
     */
    private function logConnection(): void
    {
        try {
            $logDir = $this->projectRoot . '/logs/ssh';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0777, true);
            }

            $logFile = sprintf('%s/ssh_%s.log', $logDir, date('Y-m-d'));
            $logEntry = sprintf(
                "[%s] Connected to %s@%s (Environment: %s)\n",
                date('Y-m-d H:i:s'),
                $this->username,
                $this->host,
                $this->isCI ? 'CI' : 'Local'
            );

            file_put_contents($logFile, $logEntry, FILE_APPEND);
            $this->stateService->set('ssh.connection_log', $logEntry);
            $this->logInfo('SSH connection details logged');
        } catch (\Throwable $e) {
            // Just log error but don't throw exception for logging issues
            $this->logError('Failed to log SSH connection details', $e);
        }
    }

    /**
     * @When I execute SSH command :command
     */
    public function iExecuteSSHCommand(string $command): void
    {
        if (!$this->sshAvailable) {
            // Skip in local environment, but fail in CI if SSH is required
            if ($this->isCI) {
                throw new \RuntimeException('SSH connection required in CI environment but not available');
            }

            $this->logInfo('SSH not available, skipping step.');
            return;
        }
        try {
            $output = $this->executeCommand($command);
            $this->stateService->set('ssh.last_command', $command);
            $this->stateService->set('ssh.last_output', $output);
            $this->logInfo(sprintf('Executed SSH command: %s', $command));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to execute SSH command: %s', $command), $e);
            throw $e;
        }
    }

    /**
     * Execute a command on the remote server
     *
     * @param string $command Command to execute
     * @return string Command output
     * @throws \RuntimeException When command execution fails
     */
    public function executeCommand(string $command): string
    {
        try {
            $this->stateService->set('ssh.current_command', $command);

            $sshCommand = array_merge(
                ['ssh'],
                $this->sshConfig,
                [
                    sprintf('%s@%s', $this->username, $this->host),
                    $command
                ]
            );

            $process = new Process($sshCommand);
            $process->setTimeout(300);
            $process->run();

            if (!$process->isSuccessful()) {
                $error = sprintf(
                    'SSH command failed: %s%sCommand: %s%sOutput: %s',
                    $process->getErrorOutput(),
                    PHP_EOL,
                    $command,
                    PHP_EOL,
                    $process->getOutput()
                );
                $this->stateService->set('ssh.last_error', $error);
                throw new \RuntimeException($error);
            }

            $output = $process->getOutput();
            $this->logCommand($command, $output);
            $this->stateService->set('ssh.last_output', $output);

            $this->logInfo(sprintf('Successfully executed SSH command: %s', substr($command, 0, 50) . (strlen($command) > 50 ? '...' : '')));

            return $output;
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to execute SSH command: %s', $command), $e);
            throw new \RuntimeException(
                sprintf('Failed to execute SSH command: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Log SSH command execution
     *
     * @param string $command Executed command
     * @param string $result Command result
     */
    private function logCommand(string $command, string $result): void
    {
        try {
            $logDir = $this->projectRoot . '/logs/ssh';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0777, true);
            }

            $logFile = sprintf('%s/commands_%s.log', $logDir, date('Y-m-d'));
            $logEntry = sprintf(
                "[%s] Command: %s\nResult: %s\n\n",
                date('Y-m-d H:i:s'),
                $command,
                $result
            );

            file_put_contents($logFile, $logEntry, FILE_APPEND);
            $this->stateService->set('ssh.command_log', $logEntry);
        } catch (\Throwable $e) {
            // Just log error but don't throw exception for logging issues
            $this->logError('Failed to log SSH command', $e);
        }
    }

    /**
     * @Then the SSH command output should contain :text
     */
    public function theSSHCommandOutputShouldContain(string $text): void
    {
        if (!$this->sshAvailable) {
            // Skip in local environment, but fail in CI if SSH is required
            if ($this->isCI) {
                throw new \RuntimeException('SSH connection required in CI environment but not available');
            }

            $this->logInfo('SSH not available, skipping step.');
            return;
        }
        $lastOutput = $this->stateService->get('ssh.last_output');

        if (!$lastOutput) {
            throw new \RuntimeException('No SSH command output available');
        }

        if (strpos($lastOutput, $text) === false) {
            throw new \RuntimeException(
                sprintf('SSH command output does not contain "%s". Output: %s', $text, $lastOutput)
            );
        }

        $this->logInfo(sprintf('SSH command output contains: %s', $text));
    }

    /**
     * Clean up resources on object destruction
     */
    public function __destruct()
    {
        // Only delete the key file if:
        // 1. We're not in CI environment
        // 2. We created a new key file (not using existing one)
        // 3. We're not on Windows
        if (!$this->isCI && $this->createdNewKey && strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
            @unlink($this->sshKeyPath);
            $this->logInfo('Removed temporary SSH key file');
        }
    }
}
