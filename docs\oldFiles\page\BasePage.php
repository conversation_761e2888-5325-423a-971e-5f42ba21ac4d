<?php

namespace Features\Bootstrap\Page;

use Sensio<PERSON><PERSON>s\Behat\PageObjectExtension\PageObject\Page;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\DriverException;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\Mink\Exception\ExpectationException;
use Behat\Mink\Exception\UnsupportedDriverActionException;
use Behat\Mink\Session;

/**
 * BasePage provides common functionalities for page objects.
 */
abstract class BasePage extends Page
{
    /**
     * The base URL for the current brand and environment
     *
     * @var string
     */
    protected $baseUrl;

    /**
     * Initializes the BasePage.
     * 
     * @param string|Session $baseUrl The base URL for the current brand and environment, or a Session object
     */
    public function __construct($baseUrl)
    {
        // If baseUrl is a Session object, extract the base URL from environment variables
        if ($baseUrl instanceof Session) {
            $this->baseUrl = getenv('TEST_BASE_URL') ?: 'https://aeonstest.info';
            
            // Set the session property (inherited from <PERSON>)
            $this->session = $baseUrl;
        } else {
            $this->baseUrl = (string)$baseUrl;
        }
    }

    /**
     * Verifies that we're on the expected page.
     * Required by PageObjectExtension.
     */
    protected function verifyPage(): void
    {
        $this->waitForPageToLoad();
    }

    /**
     * Gets the Mink session.
     *
     * @return Session
     */
    public function getSession(): Session
    {
        return parent::getSession();
    }

    /**
     * Opens the page.
     *
     * @param array $urlParameters
     * @return void
     */
    public function open(array $urlParameters = []): void
    {
        parent::open($urlParameters);
        $this->waitForPageToLoad();
    }

    /**
     * Gets the URL of the page.
     *
     * @param array $urlParameters
     * @return string
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Waits for the page to fully load.
     *
     * @param int $timeout The maximum time to wait in milliseconds.
     */
    public function waitForPageToLoad(int $timeout = 5000): void
    {
        $this->session->wait($timeout, "document.readyState === 'complete'");
    }

    /**
     * Clicks on a web element identified by the provided selector.
     *
     * @param string $selector The element selector.
     * @throws ElementNotFoundException
     */
    public function clickElement(string $selector): void
    {
        $element = $this->findElement($selector);
       //$this->scrollToElement($element);
        $element->click();
    }

    /**
     * Finds and returns a web element using the provided selector.
     *
     * @param string $selector The selector (CSS or XPath).
     * @param int $timeout The maximum time to wait in milliseconds.
     * @return NodeElement The found element.
     * @throws ElementNotFoundException If the element is not found within the timeout.
     */
    protected function findElement(string $selector, int $timeout = 5000): NodeElement
    {
        $selectorType = $this->determineSelectorType($selector);
        
        // Use PageObjectExtension's getDocument() instead of direct session access
        $page = $this->getDocument();

        // For CSS selectors, use querySelector in JavaScript
        if ($selectorType === 'css') {
            $script = sprintf(
                "document.querySelector('%s') !== null",
                addslashes($selector)
            );
        } else {
            // For XPath, use evaluate
            $script = sprintf(
                "document.evaluate('%s', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue !== null",
                addslashes($selector)
            );
        }

        $element = $this->getSession()->wait($timeout, $script);

        if (!$element) {
            throw new ElementNotFoundException(
                $this->getSession(),
                'element',
                $selectorType,
                $selector
            );
        }

        return $page->find($selectorType, $selector);
    }

    /**
 * Scrolls the page to bring an element into view with fallback for older browsers.
 *
 * @param NodeElement $element The element to scroll to.
 * @throws \RuntimeException If the element cannot be scrolled to.
 */
protected function scrollToElement(NodeElement $element): void
{
    // Ensure the element exists
    if (!$element || !$element->isVisible()) {
        throw new \RuntimeException('Unable to scroll to element. Element may not be visible or may not exist.');
    }

    // JavaScript to scroll into view with a fallback
    $script = <<<JS
    (function(element) {
        if (!element) return false;

        if (typeof element.scrollIntoView === 'function') {
            try {
                element.scrollIntoView({behavior: 'smooth', block: 'center', inline: 'center'});
            } catch (e) {
                // Fallback for older browsers
                element.scrollIntoView(true);
            }
            return true;
        }

        // Fallback for no scrollIntoView support
        var rect = element.getBoundingClientRect();
        window.scrollBy({
            top: rect.top + window.pageYOffset - (window.innerHeight / 2),
            behavior: 'smooth'
        });
        return true;
    })(arguments[0]);
    JS;

    $result = $this->getSession()->evaluateScript($script, [$element]);

    if (!$result) {
        throw new \RuntimeException('Unable to scroll to element. Element may not be visible or may not exist.');
    }

    // Dynamically wait for the element to be scrolled into view
    $this->getSession()->wait(1000, <<<JS
        (function(element) {
            if (!element) return false;
            var rect = element.getBoundingClientRect();
            return rect.top >= 0 && rect.bottom <= window.innerHeight;
        })(arguments[0]);
    JS, [$element]);
}


    /**
     * Enters text into an input field identified by the provided selector.
     *
     * @param string $selector The CSS selector.
     * @param string $text The text to enter.
     * @throws ElementNotFoundException
     */
    public function enterText(string $selector, string $text): void
    {
        $element = $this->findElement($selector);
        $this->scrollToElement($element);  // Restore this line
        $element->setValue($text);
    }

    /**
     * Gets the text content of an element identified by the provided selector.
     *
     * @param string $selector The CSS selector.
     * @return string The text content of the element.
     * @throws ElementNotFoundException
     */
    public function getElementText(string $selector): string
    {
        $element = $this->findElement($selector);
        $this->scrollToElement($element);  // Restore this line
        return $element->getText();
    }

    /**
     * Checks if an element is visible on the page.
     *
     * @param string $selector The CSS selector.
     * @return bool True if the element is visible, false otherwise.
     */
    public function isElementVisible(string $selector): bool
    {
        try {
            $element = $this->findElement($selector);
            return $element->isVisible();
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Checks if an element is present in the DOM.
     *
     * @param string $selector The CSS selector.
     * @return bool True if the element is present, false otherwise.
     */
    public function isElementPresent(string $selector): bool
    {
        $element = $this->session->getPage()->find('css', $selector);
        return $element !== null;
    }

    /**
     * Selects an option from a dropdown menu by visible text.
     *
     * @param string $selector The CSS selector of the select field.
     * @param string $optionText The visible text of the option to select.
     * @throws ElementNotFoundException
     */
    public function selectDropdownOption(string $selector, string $optionText): void
    {
        $element = $this->findElement($selector);
        //$this->scrollToElement($element);
        $element->selectOption($optionText);
    }

    /**
     * Gets the title of the current page.
     *
     * @return string The page title.
     */
    public function getPageTitle(): string
    {
        return $this->session->getPage()->find('css', 'title')->getText();
    }

    /**
     * Waits for the browser to navigate to a specific URL.
     *
     * @param string $url The expected URL.
     * @param int $timeout The maximum time to wait in milliseconds.
     * @throws ExpectationException If the URL does not match within the timeout.
     */
    public function waitForUrlToBe(string $url, int $timeout = 5000): void
    {
        $this->session->wait($timeout, "window.location.href === '$url'");
        if ($this->getCurrentUrl() !== $url) {
            throw new ExpectationException("Expected URL to be '$url', but found '{$this->getCurrentUrl()}'", $this->session);
        }
    }

    /**
     * Gets the URL of the current page.
     *
     * @return string The current URL.
     */
    public function getCurrentUrl(): string
    {
        return $this->session->getCurrentUrl();
    }

    /**
     * Gets the value of an attribute from an element.
     *
     * @param string $selector The CSS selector.
     * @param string $attributeName The name of the attribute.
     * @return string|null The value of the attribute or null if not found.
     * @throws ElementNotFoundException
     */
    public function getElementAttribute(string $selector, string $attributeName): ?string
    {
        $element = $this->findElement($selector);
        $this->scrollToElement($element);
        return $element->getAttribute($attributeName);
    }

    /**
     * Checks if an element is not present on the page.
     *
     * @param string $selector The CSS selector.
     * @param int $timeout The maximum time to wait in milliseconds.
     * @return bool True if the element is not present, false otherwise.
     */
    public function isElementNotPresent(string $selector, int $timeout = 5000): bool
    {
        try {
            $this->waitForElementToDisappear($selector, $timeout);
            return true;
        } catch (ExpectationException $e) {
            return false;
        }
    }

    /**
     * Waits until the specified element disappears from the page.
     *
     * @param string $selector The CSS selector.
     * @param int $timeout The maximum time to wait in milliseconds.
     */
    public function waitForElementToDisappear(string $selector, int $timeout = 5000): void
    {
        $this->session->wait($timeout, "document.querySelector('$selector') === null");
    }

    /**
     * Safely collects browser logs if available.
     *
     * @return array Browser logs or empty array if logs are unavailable
     */
    public function getBrowserLogs(): array
    {
        try {
            $webDriver = $this->getSession()->getDriver()->getWebDriverSession();
            if (method_exists($webDriver, 'getLog')) {
                return $webDriver->getLog('browser');
            }
        } catch (\Exception $e) {
            error_log("Unable to collect browser logs: " . $e->getMessage());
        }
        return [];
    }

    /**
     * Switches to a Stripe iframe
     *
     * @param string $frameTitle The title of the iframe to switch to
     */
    public function switchToStripeFrame(string $frameTitle): void
    {
        try {
            // Wait for iframe using JavaScript
            $this->getSession()->wait(5000,
                "document.querySelector('iframe[title=\"$frameTitle\"]') !== null"
            );

            // Find all iframes and get the index of our target iframe
            $frameIndex = $this->getSession()->evaluateScript(
                "return Array.from(document.getElementsByTagName('iframe'))
                    .findIndex(frame => frame.title === '$frameTitle');"
            );

            if ($frameIndex === -1) {
                throw new ElementNotFoundException($this->getSession(), 'iframe', 'title', $frameTitle);
            }

            // Switch to iframe using its index
            $this->getSession()->switchToIFrame($frameIndex);

            // Wait for frame content to load
            $this->getSession()->wait(1000);

        } catch (\Exception $e) {
            // Always switch back to main content in case of error
            $this->getSession()->switchToIFrame(null);
            throw new \RuntimeException(sprintf(
                'Failed to switch to Stripe iframe "%s": %s',
                $frameTitle,
                $e->getMessage()
            ));
        }
    }

    /**
     * Verifies if Google Tag Manager script is present in the page source
     *
     * @return bool
     * @throws \Exception if GTM script is not found
     */
    public function verifyGoogleTagManager(): bool
    {
        $pageSource = $this->getSession()->getPage()->getContent();

        if (strpos($pageSource, 'https://www.googletagmanager.com') === false) {
            throw new \Exception('Google Tag Manager script not found on page: ' . $this->getCurrentUrl());
        }

        return true;
    }

    /**
     * Finds and returns a list of web elements using the provided locator.
     *
     * @param string $selector The selector (CSS or XPath).
     * @return NodeElement[] An array of found elements.
     */
    protected function findElements(string $selector): array
    {
        $selectorType = $this->determineSelectorType($selector);
        return $this->session->getPage()->findAll($selectorType, $selector);
    }

    /**
     * Determines the selector type (CSS or XPath)
     */
    protected function determineSelectorType(string $selector): string
    {
        // If selector starts with // or .// or contains :: it's likely XPath
        return (preg_match('/^\/\/|^\.\//i', $selector) || strpos($selector, '::') !== false)
            ? 'xpath'
            : 'css';
    }

    /**
     * Switches focus to an iframe on the page.
     *
     * @param string $frameSelector
     * @throws ElementNotFoundException
     */
    protected function switchToFrame(string $frameSelector): void
    {
        $frame = $this->findElement($frameSelector);
        if (!$frame) {
            throw new ElementNotFoundException($this->session, 'frame', 'css', $frameSelector);
        }
        $this->session->switchToIFrame($frame);
    }

    /**
     * @throws DriverException
     * @throws UnsupportedDriverActionException
     * @throws \Exception
     */
    protected function switchToDefaultContent(): void
    {
        $driver = $this->getSession()->getDriver();
        if ($driver instanceof \Behat\Mink\Driver\Selenium2Driver) {
            $driver->switchToIFrame(null);
        } else {
            throw new \Exception('Unsupported driver for switching to default content');
        }
    }

    /**
     * Waits for an element to be visible on the page.
     *
     * @param string $selector The CSS or XPath selector.
     * @param int $timeout The maximum time to wait in milliseconds.
     * @throws ElementNotFoundException If the element is not visible within the timeout.
     */
    protected function waitForElementVisible(string $selector, int $timeout = 5000): void
    {
        $selectorType = $this->determineSelectorType($selector);

        $endTime = microtime(true) + $timeout / 1000;
        while (microtime(true) < $endTime) {
            $element = $this->session->getPage()->find($selectorType, $selector);
            if ($element && $element->isVisible()) {
                return;
            }
            usleep(100000); // Wait 100ms before checking again
        }
        throw new ElementNotFoundException($this->session, 'element', $selectorType, $selector);
    }

    /**
     * Waits for AJAX requests to complete
     *
     * @param int $timeout Maximum time to wait in milliseconds
     * @throws \RuntimeException if timeout is reached
     */
    public function waitForAjaxToComplete(int $timeout = 10000): void
    {
        $this->getSession()->wait($timeout,
            // Wait for jQuery AJAX requests
            "(typeof jQuery === 'undefined' || jQuery.active === 0) && " .
            // Wait for native XMLHttpRequest
            "(typeof window.XMLHttpRequest === 'undefined' || " .
            "Array.from(window.XMLHttpRequest.prototype).every(r => r.readyState === 4 || r.readyState === 0)) && " .
            // Wait for fetch requests
            "(typeof window.fetch === 'undefined' || " .
            "!window._pendingFetchRequests || window._pendingFetchRequests === 0)"
        );

        // Additional small wait to ensure DOM updates are complete
        usleep(100000); // 100ms
    }

    /**
     * Gets the standard Stripe iframe title for a given field type
     *
     * @param string $fieldType The type of field ('cardNumber', 'cardExpiry', 'cardCvc')
     * @return string The expected iframe title
     */
    protected function getStripeFrameTitle(string $fieldType): string
    {
        $titleMap = [
            'cardNumber' => 'Secure card number input frame',
            'cardExpiry' => 'Secure expiration date input frame',
            'cardCvc' => 'Secure CVC input frame'
        ];

        if (!isset($titleMap[$fieldType])) {
            throw new \InvalidArgumentException(
                sprintf('Unknown Stripe field type: %s', $fieldType)
            );
        }

        return $titleMap[$fieldType];
    }

    // Consider adding this helper method if not already present

    protected function scrollToElementWithRetry($element, $maxAttempts = 3): void
    {
        $attempts = 0;
        while ($attempts < $maxAttempts) {
            try {
                $this->scrollToElement($element);
                return;
            } catch (\Exception $e) {
                $attempts++;
                if ($attempts === $maxAttempts) {
                    throw $e;
                }
                usleep(500000); // Wait 500ms between attempts
            }
        }
    }

    /**
     * Gets the base URL from brand configuration.
     *
     * @return string The base URL for the current brand and environment
     */
    protected function getBaseUrl(): string
    {
        return getenv('TEST_BASE_URL') ?? throw new RuntimeException('TEST_BASE_URL not set');
    }

    /**
    * Checks a radio button by its selector.
    *
    * @param string $selector CSS selector for the radio button.
    * @throws \RuntimeException If the radio button is not found or not checkable.
    */
    public function checkRadioButton(string $selector): void
    {
        $session = $this->getSession();
    $page = $session->getPage();

    // Find the radio button using the provided selector
    $radioButton = $page->find('css', $selector);

    if (!$radioButton) {
        throw new \RuntimeException(sprintf('Radio button with selector "%s" not found.', $selector));
    }

    // Ensure it's a radio input field
    if ($radioButton->getAttribute('type') !== 'radio') {
        throw new \RuntimeException(sprintf('Element with selector "%s" is not a radio button.', $selector));
    }

    // Check the radio button
    $radioButton->check();

    // Verify that the radio button is selected
        if (!$radioButton->isChecked()) {
            throw new \RuntimeException(sprintf('Failed to check the radio button with selector "%s".', $selector));
        }
    }


}
