<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Tester\Exception\PendingException;
use Behat\Gherkin\Node\TableNode;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Process\Process;

/**
 * Context for admin command functionality
 */
class AdminCommandContext extends BaseContext
{
    protected ?string $lastCommandOutput = null;
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param ConfigurationServiceInterface|null $configService Configuration service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface            $container = null,
        ?ConfigurationServiceInterface $configService = null,
        ?SharedStateServiceInterface   $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->configService = $configService ?? $container->get(ConfigurationServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->configService = $configService ?? $this->createMockConfigService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $this->logInfo("AdminCommandContext initialized");
    }

    /**
     * Create a mock configuration service for testing
     *
     * @return ConfigurationServiceInterface
     */
    private function createMockConfigService(): ConfigurationServiceInterface
    {
        return new class implements ConfigurationServiceInterface {
            private string $currentBrand = 'aeons';
            private string $currentEnvironment = 'stage';
            private array $config = [
                'base_url' => 'https://aeonstest.info'
            ];

            public function getBrandConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getEnvironmentConfig(string $key)
            {
                return $this->config[$key] ?? null;
            }

            public function getCurrentBrand(): string
            {
                return $this->currentBrand;
            }

            public function getCurrentEnvironment(): string
            {
                return $this->currentEnvironment;
            }

            public function setBrand(string $brand): void
            {
                $this->currentBrand = $brand;
            }

            public function setEnvironment(string $environment): void
            {
                $this->currentEnvironment = $environment;
            }

            public function getConfigValue(string $key)
            {
                return $this->config[$key] ?? null;
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @When I run the admin command :command
     */
    public function iRunTheAdminCommand(string $command): void
    {
        try {
            $this->executeCommand($command);
            $this->stateService->set('admin.last_command', $command);
            $this->stateService->set('admin.last_output', $this->lastCommandOutput);
            $this->logInfo(sprintf('Executed admin command: %s', $command));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to execute admin command: %s', $command), $e);
            throw $e;
        }
    }

    /**
     * Execute a command
     *
     * @param string $command Command to execute
     * @return void
     * @throws \RuntimeException When command execution fails
     */
    private function executeCommand(string $command): void
    {
        $this->logInfo(sprintf('Executing command: %s', $command));

        $process = Process::fromShellCommandline($command);
        $process->setTimeout(60);
        $process->run();

        $this->lastCommandOutput = $process->getOutput();
        $exitCode = $process->getExitCode();

        $this->stateService->set('admin.last_exit_code', $exitCode);

        if ($exitCode !== 0) {
            $this->logError(sprintf(
                'Command failed with exit code %d. Error output: %s',
                $exitCode,
                $process->getErrorOutput()
            ));
        }
    }

    /**
     * @When I run the admin command :command with parameters:
     */
    public function iRunTheAdminCommandWithParameters(string $command, TableNode $table): void
    {
        try {
            $parameters = [];
            foreach ($table->getRows() as $row) {
                if (count($row) >= 2) {
                    $parameters[$row[0]] = $row[1];
                }
            }

            $fullCommand = $this->buildCommand($command, $parameters);
            $this->executeCommand($fullCommand);

            $this->stateService->set('admin.last_command', $fullCommand);
            $this->stateService->set('admin.last_output', $this->lastCommandOutput);
            $this->stateService->set('admin.last_parameters', $parameters);

            $this->logInfo(sprintf('Executed admin command with parameters: %s', $fullCommand));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to execute admin command with parameters: %s', $command), $e);
            throw $e;
        }
    }

    /**
     * Build a command with parameters
     *
     * @param string $command Base command
     * @param array $parameters Command parameters
     * @return string Full command
     */
    private function buildCommand(string $command, array $parameters): string
    {
        $parts = [$command];

        foreach ($parameters as $key => $value) {
            if (strlen($key) === 1) {
                $parts[] = sprintf('-%s %s', $key, escapeshellarg($value));
            } else {
                $parts[] = sprintf('--%s=%s', $key, escapeshellarg($value));
            }
        }

        return implode(' ', $parts);
    }

    /**
     * @Then the command output should contain :text
     */
    public function theCommandOutputShouldContain(string $text): void
    {
        if (!$this->lastCommandOutput) {
            throw new \RuntimeException('No command output available');
        }

        if (strpos($this->lastCommandOutput, $text) === false) {
            throw new \RuntimeException(
                sprintf('Command output does not contain "%s". Output: %s', $text, $this->lastCommandOutput)
            );
        }

        $this->logInfo(sprintf('Command output contains: %s', $text));
    }

    /**
     * @Then the command should be successful
     */
    public function theCommandShouldBeSuccessful(): void
    {
        $exitCode = $this->stateService->get('admin.last_exit_code');

        if ($exitCode !== 0) {
            throw new \RuntimeException(
                sprintf('Command failed with exit code %d. Output: %s', $exitCode, $this->lastCommandOutput)
            );
        }

        $this->logInfo('Command was successful');
    }

    /**
     * @When /^I execute app:reorder command$/
     */
    public function iExecuteAppReorderCommand()
    {
        throw new PendingException();
    }
}
