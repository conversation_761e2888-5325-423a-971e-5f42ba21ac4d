# Service Interfaces Documentation

## Overview

This document provides detailed documentation of the service interfaces used in the Malaberg test automation framework. Service interfaces define the contract between service providers and consumers, ensuring loose coupling and enabling dependency injection.

## Core Service Interfaces

### BrowserServiceInterface

The `BrowserServiceInterface` defines methods for interacting with the browser.

```php
namespace App\Service\Browser;

use Behat\Mink\Element\NodeElement;
use Behat\Mink\Session;

interface BrowserServiceInterface
{
    /**
     * Get the Mink session
     *
     * @return Session
     */
    public function getSession(): Session;

    /**
     * Visit a URL
     *
     * @param string $url URL to visit
     * @return void
     */
    public function visit(string $url): void;

    /**
     * Take a screenshot
     *
     * @param string|null $name Screenshot name
     * @return string Path to the screenshot
     */
    public function takeScreenshot(string $name = null): string;

    /**
     * Wait for an element to be present
     *
     * @param string $selector Element selector
     * @param int $timeout Timeout in seconds
     * @return void
     * @throws \RuntimeException When element is not found within timeout
     */
    public function waitForElement(string $selector, int $timeout = 30): void;

    /**
     * Execute JavaScript
     *
     * @param string $script JavaScript to execute
     * @return mixed Result of the script
     */
    public function executeScript(string $script);

    /**
     * Find all elements matching a selector
     *
     * @param string $selector CSS selector
     * @return NodeElement[] Array of matching elements
     */
    public function findElements(string $selector): array;

    /**
     * Wait for an element to be visible
     *
     * @param string $selector Element selector
     * @param int $timeout Timeout in seconds
     * @return bool True if element became visible within timeout
     */
    public function waitForElementVisible(string $selector, int $timeout = 30): bool;

    /**
     * Click on an element
     *
     * @param string $selector Element selector
     * @return void
     */
    public function clickElement(string $selector): void;

    /**
     * Fill a field with a value
     *
     * @param string $selector Field selector
     * @param string $value Value to fill
     * @return void
     */
    public function fillField(string $selector, string $value): void;

    /**
     * Select an option from a select field
     *
     * @param string $selector Select field selector
     * @param string $value Option value to select
     * @return void
     */
    public function selectOption(string $selector, string $value): void;

    /**
     * Get text from an element
     *
     * @param string $selector Element selector
     * @return string Element text
     */
    public function getElementText(string $selector): string;

    /**
     * Check if an element is visible
     *
     * @param string $selector Element selector
     * @return bool True if element is visible
     */
    public function isElementVisible(string $selector): bool;

    /**
     * Wait for page to load completely
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForPageToLoad(int $timeout = 30): void;

    /**
     * Wait for document to be ready
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForDocumentReady(int $timeout = 30): void;

    /**
     * Wait for AJAX requests to complete
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForAjaxToComplete(int $timeout = 30): void;

    /**
     * Check if an element exists
     *
     * @param string $selector Element selector
     * @return bool True if element exists
     */
    public function elementExists(string $selector): bool;

    /**
     * Get the driver type (e.g., Selenium2Driver, ChromeDriver)
     *
     * @return string The driver type
     */
    public function getDriverType(): string;

    /**
     * Navigate back in the browser history
     *
     * @return void
     */
    public function navigateBack(): void;

    /**
     * Check if the browser session is active
     *
     * @return bool True if the session is active
     */
    public function isSessionActive(): bool;

    /**
     * Check if the current session is a BrowserStack session
     *
     * @return bool True if running in BrowserStack
     */
    public function isBrowserStackSession(): bool;

    /**
     * Get the current page title
     *
     * @return string The page title
     */
    public function getPageTitle(): string;

    /**
     * Wait for a specified number of seconds
     *
     * @param int $seconds Number of seconds to wait
     * @return void
     */
    public function wait(int $seconds): void;

    /**
     * Get the current URL
     *
     * @return string The current URL
     */
    public function getCurrentUrl(): string;

    /**
     * Find a single element matching a selector
     *
     * @param string $selector CSS selector
     * @return NodeElement|null The matching element or null if not found
     */
    public function findElement(string $selector): ?NodeElement;
}
```

### ConfigurationServiceInterface

The `ConfigurationServiceInterface` defines methods for accessing configuration settings.

```php
namespace App\Service\Configuration;

interface ConfigurationServiceInterface
{
    /**
     * Get a brand configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     */
    public function getBrandConfig(string $key);

    /**
     * Get an environment configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     */
    public function getEnvironmentConfig(string $key);

    /**
     * Get the current brand
     *
     * @return string Current brand
     */
    public function getCurrentBrand(): string;

    /**
     * Get the current environment
     *
     * @return string Current environment
     */
    public function getCurrentEnvironment(): string;

    /**
     * Set the current brand
     *
     * @param string $brand Brand to set
     * @return void
     */
    public function setBrand(string $brand): void;

    /**
     * Set the current environment
     *
     * @param string $environment Environment to set
     * @return void
     */
    public function setEnvironment(string $environment): void;
}
```

### SharedStateServiceInterface

The `SharedStateServiceInterface` defines methods for managing shared state between contexts.

```php
namespace App\Service\State;

interface SharedStateServiceInterface
{
    /**
     * Set a value in the shared state
     *
     * @param string $key The key
     * @param mixed $value The value
     * @param string $scope The scope (scenario, feature, global)
     * @return void
     */
    public function set(string $key, $value, string $scope = 'scenario'): void;

    /**
     * Get a value from the shared state
     *
     * @param string $key The key
     * @param string $scope The scope (scenario, feature, global)
     * @return mixed The value or null if not found
     */
    public function get(string $key, string $scope = 'scenario');

    /**
     * Check if a key exists in the shared state
     *
     * @param string $key The key
     * @param string $scope The scope (scenario, feature, global)
     * @return bool True if the key exists, false otherwise
     */
    public function has(string $key, string $scope = 'scenario'): bool;

    /**
     * Get all values in a scope
     *
     * @param string $scope The scope (scenario, feature, global)
     * @return array All values in the scope
     */
    public function getAll(string $scope = 'scenario'): array;

    /**
     * Reset a scope
     *
     * @param string $scope The scope to reset (scenario, feature, global)
     * @return void
     */
    public function reset(string $scope = 'scenario'): void;
}
```

### TestDataServiceInterface

The `TestDataServiceInterface` defines methods for accessing test data.

```php
namespace App\Service\Data;

interface TestDataServiceInterface
{
    /**
     * Load test data from a file
     *
     * @param string $brand Brand name
     * @param string $type Data type (e.g., 'products', 'users')
     * @param string|null $key Specific data key to load
     * @return array Test data
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array;

    /**
     * Get test data for a specific brand, type, and key
     *
     * @param string $brand Brand name
     * @param string $type Data type (e.g., 'products', 'users')
     * @param string $key Data key
     * @return array Test data
     */
    public function getTestData(string $brand, string $type, string $key): array;

    /**
     * Check if test data exists for a specific brand, type, and key
     *
     * @param string $brand Brand name
     * @param string $type Data type (e.g., 'products', 'users')
     * @param string $key Data key
     * @return bool
     */
    public function hasTestData(string $brand, string $type, string $key): bool;
}
```

### PageFactoryInterface

The `PageFactoryInterface` defines methods for creating page objects.

```php
namespace App\Service\Page;

use App\Page\Base\BasePageInterface;

interface PageFactoryInterface
{
    /**
     * Create a page object
     *
     * @param string $pageClass Page class name
     * @param array $parameters Constructor parameters
     * @return BasePageInterface
     */
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface;

    /**
     * Get a page object by name
     *
     * @param string $pageName Page name (e.g., 'HomePage')
     * @param array $parameters Constructor parameters
     * @return BasePageInterface
     */
    public function getPage(string $pageName, array $parameters = []): BasePageInterface;

    /**
     * Check if a page exists
     *
     * @param string $pageName Page name
     * @return bool
     */
    public function hasPage(string $pageName): bool;
}
```

### ValidationServiceInterface

The `ValidationServiceInterface` defines methods for validating data.

```php
namespace App\Service\Validation;

interface ValidationServiceInterface
{
    /**
     * Validate data against a schema
     *
     * @param array $data Data to validate
     * @param string $schema Schema name
     * @return bool True if data is valid
     */
    public function validate(array $data, string $schema): bool;

    /**
     * Get validation errors
     *
     * @return array Validation errors
     */
    public function getErrors(): array;

    /**
     * Check if a schema exists
     *
     * @param string $schema Schema name
     * @return bool True if schema exists
     */
    public function hasSchema(string $schema): bool;
}
```

## Cache Service Interface

### CacheServiceInterface

The `CacheServiceInterface` provides a standardized way to interact with different cache implementations in the test
framework.

```php
namespace App\Service\Cache;

interface CacheServiceInterface
{
    /**
     * Retrieve a value from cache
     *
     * @param string $key The cache key
     * @return mixed The cached value or null if not found
     */
    public function get(string $key): mixed;

    /**
     * Store a value in cache
     *
     * @param string $key The cache key
     * @param mixed $value The value to cache
     * @param int|null $lifetime Cache lifetime in seconds (null for default)
     */
    public function set(string $key, mixed $value, ?int $lifetime = null): void;

    /**
     * Delete a value from cache
     *
     * @param string $key The cache key to delete
     */
    public function delete(string $key): void;

    /**
     * Delete all values matching a pattern
     *
     * @param string $pattern The pattern to match (e.g., 'prefix_*')
     */
    public function deletePattern(string $pattern): void;

    /**
     * Check if a key exists in cache
     *
     * @param string $key The cache key to check
     * @return bool True if key exists, false otherwise
     */
    public function has(string $key): bool;
}
```

### Implementations

#### SymfonyCacheService

Primary implementation using Symfony's Cache component.

```php
class SymfonyCacheService implements CacheServiceInterface
{
    public function __construct(private readonly AdapterInterface $cache) {}
    
    // Implementation details...
}
```

Key features:

- Supports multiple cache backends (Array, Filesystem, Redis)
- Handles serialization/deserialization
- Implements TTL (Time To Live) functionality
- Thread-safe operations

Usage example:

```php
// Using array cache for testing
$cache = new SymfonyCacheService(new ArrayAdapter());

// Store a value
$cache->set('my_key', ['data' => 'value'], 3600);

// Retrieve the value
$value = $cache->get('my_key');
```

#### LoggingCacheDecorator

Decorator that adds logging capabilities to any cache service implementation.

```php
class LoggingCacheDecorator implements CacheServiceInterface
{
    public function __construct(
        private readonly CacheServiceInterface $decorated,
        private readonly LoggerInterface $logger
    ) {}
    
    // Implementation details...
}
```

Features:

- Logs all cache operations
- Tracks cache hits and misses
- Provides debugging information
- Zero performance overhead when logging is disabled

Usage example:

```php
// Create decorated cache service
$cache = new LoggingCacheDecorator(
    new SymfonyCacheService($adapter),
    $logger
);

// Operations are now logged
$cache->get('my_key'); // Logs: "Cache hit/miss for key: my_key"
```

### Service Registration

Cache services are registered in the service container with appropriate tags:

```yaml
services:
    App\Service\Cache\SymfonyCacheService:
        arguments:
            $cache: '@cache.adapter.array'
        tags: ['app.cacheable.data']

    App\Service\Cache\LoggingCacheDecorator:
        decorates: 'App\Service\Cache\SymfonyCacheService'
        arguments:
            $decorated: '@.inner'
            $logger: '@logger'
```

### Best Practices

1. **Key Naming**
    - Use descriptive, namespaced keys
    - Include service name in key
    - Use consistent separator (e.g., dots)
      Example: `browser.session.user_123`

2. **Lifetime Management**
    - Use appropriate lifetimes for data type
    - Consider volatility of cached data
    - Use null lifetime for permanent cache

3. **Error Handling**
    - Always handle cache misses gracefully
    - Implement fallback mechanisms
    - Log cache failures appropriately

4. **Pattern Usage**
    - Use patterns for bulk operations
    - Keep patterns specific and targeted
    - Document pattern conventions

### Integration Examples

1. **With Browser Service**

```php
class BrowserService
{
    public function __construct(
        private CacheServiceInterface $cache
    ) {}

    public function getSession(string $id): ?Session
    {
        return $this->cache->get("browser.session.$id");
    }
}
```

2. **With Configuration Service**

```php
class ConfigurationService
{
    public function __construct(
        private CacheServiceInterface $cache
    ) {}

    public function getBrandConfig(string $brand): array
    {
        return $this->cache->get("config.brand.$brand") 
            ?? $this->loadBrandConfig($brand);
    }
}
```

## Page Object Interfaces

### BasePageInterface

The `BasePageInterface` defines methods that all page objects must implement.

```php
namespace App\Page\Base;

interface BasePageInterface
{
    /**
     * Open the page
     *
     * @param array $urlParameters Parameters to include in the URL
     * @return void
     */
    public function open(array $urlParameters = []): void;

    /**
     * Get the URL of the page
     *
     * @param array $urlParameters Parameters to include in the URL
     * @return string
     */
    public function getUrl(array $urlParameters = []): string;

    /**
     * Check if the page is open
     *
     * @return bool
     */
    public function isOpen(): bool;

    /**
     * Wait for the page to load
     *
     * @param int $timeout Timeout in seconds
     * @return void
     */
    public function waitForPageToLoad(int $timeout = 30): void;

    /**
     * Get the page title
     *
     * @return string
     */
    public function getTitle(): string;

    /**
     * Take a screenshot of the page
     *
     * @param string|null $name Name for the screenshot
     * @return string Path to the screenshot
     */
    public function takeScreenshot(?string $name = null): string;

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int;

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int;

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array;

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array;
}
```

### ProductPageInterface

The `ProductPageInterface` defines methods specific to product pages.

```php
namespace App\Page\Base;

interface ProductPageInterface
{
    /**
     * Select supply duration/frequency
     *
     * @param string $frequency The frequency to select
     * @return int Days between deliveries
     */
    public function selectSupplyDuration(string $frequency);

    /**
     * Select product quantity
     *
     * @param mixed $quantity The quantity to select
     * @return void
     */
    public function selectQuantity($quantity);
}
```

### PaymentPageInterface

The `PaymentPageInterface` defines methods specific to payment pages.

```php
namespace App\Page\Base;

interface PaymentPageInterface
{
    /**
     * Complete the PayPal checkout process
     *
     * @return void
     */
    public function completePayPalCheckout();

    /**
     * Handle 3D Secure authentication
     *
     * @return void
     */
    public function handle3DSecureAuthentication();
}
```

## Context Interfaces

### ServiceAwareContext

The `ServiceAwareContext` interface marks a context as requiring access to the service container.

```php
namespace App\Context\Base;

use Behat\Behat\Context\Context;
use Symfony\Component\DependencyInjection\ContainerInterface;

interface ServiceAwareContext extends Context
{
    /**
     * Set the service container
     *
     * @param ContainerInterface $container Service container
     * @return void
     */
    public function setContainer(ContainerInterface $container): void;
}
```

## Implementation Examples

### BrowserService Implementation

```php
namespace App\Service\Browser;

use App\Service\AbstractService;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Session;
use DMore\ChromeDriver\ChromeDriver;
use Psr\Log\LoggerInterface;
use RuntimeException;

class BrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    private int $defaultTimeout = 30;

    public function __construct(
        Session $session,
        string $screenshotsDir,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->session = $session;
        $this->screenshotsDir = $screenshotsDir;
        // ...
    }

    public function visit(string $url): void
    {
        $this->logInfo(sprintf("Visiting URL: %s", $url));

        try {
            $this->session->visit($url);
            $this->waitForPageToLoad();
        } catch (\Exception $e) {
            $this->logError(sprintf("Error visiting URL: %s", $url), $e);
            $this->takeScreenshot('visit_error');
            throw $e;
        }
    }

    // Implementation of other methods...
}
```

### SharedStateService Implementation

```php
namespace App\Service\State;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;

class SharedStateService extends AbstractService implements SharedStateServiceInterface
{
    private array $scenarioState = [];
    private array $featureState = [];
    private array $globalState = [];

    public function __construct(?LoggerInterface $logger = null)
    {
        parent::__construct($logger);
    }

    public function set(string $key, $value, string $scope = 'scenario'): void
    {
        $this->logInfo(sprintf("Setting '%s' in '%s' scope", $key, $scope));

        switch ($scope) {
            case 'scenario':
                $this->scenarioState[$key] = $value;
                break;
            case 'feature':
                $this->featureState[$key] = $value;
                break;
            case 'global':
                $this->globalState[$key] = $value;
                break;
            default:
                throw new \InvalidArgumentException(
                    sprintf("Invalid scope: %s. Expected 'scenario', 'feature', or 'global'", $scope)
                );
        }
    }

    // Implementation of other methods...
}
```

### PageFactory Implementation

```php
namespace App\Service\Page;

use App\Page\Base\BasePageInterface;
use App\Service\AbstractService;
use App\Service\Browser\BrowserServiceInterface;
use RuntimeException;
use Symfony\Component\DependencyInjection\ContainerInterface;

class PageFactory extends AbstractService implements PageFactoryInterface
{
    private ContainerInterface $container;
    private BrowserServiceInterface $browserService;
    private string $baseUrl;
    private array $pageNamespaces = [
        'App\\Page\\',
        'Features\\Bootstrap\\Page\\' // For backward compatibility
    ];

    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        ?string $baseUrl = null
    ) {
        $this->container = $container;
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    public function getPage(string $pageName, array $parameters = []): BasePageInterface
    {
        // Implementation...
    }

    // Implementation of other methods...
}
```

## Service Registration

Services are registered in the service container using YAML configuration files:

```yaml
# config/services/core.yml
services:
  # Browser Service
  App\Service\Browser\BrowserServiceInterface:
    alias: App\Service\Browser\BrowserService
    public: true

  App\Service\Browser\BrowserService:
    arguments:
      $session: '@mink.session'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: true

  # Shared State Service
  App\Service\State\SharedStateServiceInterface:
    alias: App\Service\State\SharedStateService
    public: true

  App\Service\State\SharedStateService:
    public: true
    tags:
      - { name: kernel.event_subscriber }

  # Page Factory
  App\Service\Page\PageFactoryInterface:
    alias: App\Service\Page\PageFactory
    public: true

  App\Service\Page\PageFactory:
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
```

## Service Usage in Contexts

Services are used in contexts through dependency injection:

```php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class ExampleContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private PageFactoryInterface $pageFactory;
    private SharedStateServiceInterface $stateService;

    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        PageFactoryInterface $pageFactory,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->browserService = $browserService;
        $this->pageFactory = $pageFactory;
        $this->stateService = $stateService;
    }

    /**
     * @Given I am on the homepage
     */
    public function iAmOnTheHomepage(): void
    {
        try {
            $this->browserService->visit($this->getConfigService()->getEnvironmentConfig('base_url'));
            $this->stateService->set('page.current', 'homepage');
            $this->logInfo("Navigated to homepage");
        } catch (\Throwable $e) {
            $this->logError("Failed to navigate to homepage", $e);
            throw $e;
        }
    }

    /**
     * @When I navigate to the product page for :productName
     */
    public function iNavigateToTheProductPageFor(string $productName): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $productPage->loadWithName($productName);
            $this->stateService->set('page.current', 'product');
            $this->stateService->set('product.current', $productName);
            $this->logInfo(sprintf("Navigated to product page for: %s", $productName));
        } catch (\Throwable $e) {
            $this->logError(sprintf("Failed to navigate to product page for: %s", $productName), $e);
            throw $e;
        }
    }
}
```

## Service Usage in Page Objects

Services are used in page objects through dependency injection:

```php
namespace App\Page;

use App\Page\Base\BasePage;
use App\Page\Base\ProductPageInterface;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;

class ProductPage extends BasePage implements ProductPageInterface
{
    protected string $path = '/product/{slug}';
    private TestDataServiceInterface $dataService;
    private array $productSlugs = [
        'Total Harmony' => 'aeons-total-harmony',
        'Ancient Roots' => 'aeons-ancient-roots',
        // ...
    ];

    public function __construct(
        BrowserServiceInterface $browserService,
        TestDataServiceInterface $dataService,
        ?string $baseUrl = null
    ) {
        parent::__construct($browserService, $baseUrl);
        $this->dataService = $dataService;
    }

    public function loadWithName(string $productName, ?array $productData = null): void
    {
        // Implementation...
    }

    // Implementation of other methods...
}
```

## Conclusion

The service interfaces in the Malaberg test automation framework provide a clear contract between service providers and consumers. By depending on interfaces rather than concrete implementations, the framework achieves loose coupling and enables dependency injection, making it more maintainable and testable.
