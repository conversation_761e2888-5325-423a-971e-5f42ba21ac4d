<?php

namespace App\Service\Data;

use App\Service\AbstractService;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Service for managing test data
 */
class TestDataService extends AbstractService implements TestDataServiceInterface
{
    private string $fixturesDir;
    private array $testData = [];
    private array $cache = [];
    private ValidationServiceInterface $validator;
    private ConfigurationServiceInterface $configService;

    /**
     * Constructor
     *
     * @param string $fixturesDir Fixtures directory path
     * @param ValidationServiceInterface $validator Validation service
     * @param ConfigurationServiceInterface $configService Configuration service
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        string                        $fixturesDir,
        ValidationServiceInterface    $validator,
        ConfigurationServiceInterface $configService,
        ?LoggerInterface              $logger = null
    )
    {
        parent::__construct($logger);

        $this->fixturesDir = $fixturesDir;
        $this->validator = $validator;
        $this->configService = $configService;

        $this->logInfo(sprintf("Initializing TestDataService with fixtures directory: %s", $fixturesDir));
    }

    /**
     * {@inheritdoc}
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        $cacheKey = sprintf('%s_%s_%s', $brand, $type, $key ?? 'all');
        $this->logInfo(sprintf("Loading test data - Brand: %s, Type: %s, Key: %s", $brand, $type, $key ?? 'all'));

        if (isset($this->cache[$cacheKey])) {
            $this->logInfo(sprintf("Returning cached data for key: %s", $cacheKey));
            return $this->cache[$cacheKey];
        }

        // Try multiple possible paths
        $paths = [
            sprintf('%s/brands/%s/%s.yml', $this->fixturesDir, $brand, $type),
            sprintf('%s/brands/%s/data/%s.yml', $this->fixturesDir, $brand, $type),
            sprintf('%s/brands/%s/fixtures/%s.yml', $this->fixturesDir, $brand, $type),
        ];

        $loadedPath = null;
        $data = null;

        foreach ($paths as $path) {
            $this->logInfo(sprintf("Trying path: %s", $path));
            if (file_exists($path)) {
                $this->logInfo(sprintf("Loading file: %s", $path));
                $loadedPath = $path;
                break;
            }
        }

        if (!$loadedPath) {
            $this->logError(sprintf("No valid fixture file found. Tried: %s", implode(', ', $paths)));
            throw new RuntimeException(
                sprintf('Test data file not found. Tried: %s', implode(', ', $paths))
            );
        }

        try {
            $fileData = Yaml::parseFile($loadedPath);

            // Extract data based on type
            switch ($type) {
                case 'products':
                    $data = $this->extractProductData($fileData, $key);
                    break;

                case 'users':
                    $data = $this->extractUserData($fileData, $key);
                    break;

                case 'shipping':
                    $data = $this->extractShippingData($fileData, $key);
                    break;

                case 'payment_methods':
                    $data = $this->extractPaymentMethodData($fileData, $key);
                    break;

                default:
                    // For other types, just return the raw data
                    $data = $fileData;
                    if ($key && isset($fileData[$key])) {
                        $data = $fileData[$key];
                    }
            }
        } catch (\Exception $e) {
            $this->logError(sprintf("Error parsing test data file: %s", $loadedPath), $e);
            throw new RuntimeException(
                sprintf('Error parsing test data file: %s', $loadedPath),
                0,
                $e
            );
        }

        $this->validateData($type, $data);

        $this->cache[$cacheKey] = $data;
        $this->logInfo(sprintf("Data cached with key: %s", $cacheKey));

        return $data;
    }

    /**
     * Extract product data from file data
     *
     * @param array $fileData Raw file data
     * @param string|null $key Optional product key
     * @return array Extracted product data
     * @throws RuntimeException When product data cannot be extracted
     */
    private function extractProductData(array $fileData, ?string $key = null): array
    {
        $this->logInfo("Extracting product data");

        // Check if the file has the old format (with 'products' key) or new format (direct key-value pairs)
        $isOldFormat = isset($fileData['products']);

        if ($isOldFormat) {
            // Old format handling
            if ($key) {
                // Find product by slug or ID
                foreach ($fileData['products'] as $product) {
                    if (
                        (isset($product['slug']) && $product['slug'] === $key) ||
                        (isset($product['id']) && $product['id'] === $key)
                    ) {
                        return $product;
                    }
                }

                throw new RuntimeException(sprintf('Product not found with key: %s', $key));
            }

            return $fileData['products'];
        } else {
            // New format handling - direct key-value pairs
            if ($key) {
                // Check if the key exists directly in the file data
                if (isset($fileData[$key])) {
                    $productData = $fileData[$key];
                    // Add the key as an ID if not present
                    if (!isset($productData['id'])) {
                        $productData['id'] = $key;
                    }
                    return $productData;
                }

                // If not found by direct key, try to find by slug or id in values
                foreach ($fileData as $productKey => $product) {
                    if (
                        (isset($product['slug']) && $product['slug'] === $key) ||
                        (isset($product['id']) && $product['id'] === $key)
                    ) {
                        // Add the key as an ID if not present
                        if (!isset($product['id'])) {
                            $product['id'] = $productKey;
                        }
                        return $product;
                    }
                }

                throw new RuntimeException(sprintf('Product not found with key: %s', $key));
            }

            // Return all products
            $products = [];
            foreach ($fileData as $productKey => $product) {
                // Add the key as an ID if not present
                if (!isset($product['id'])) {
                    $product['id'] = $productKey;
                }
                $products[] = $product;
            }
            return $products;
        }
    }

    /**
     * Extract user data from file data
     *
     * @param array $fileData Raw file data
     * @param string|null $key Optional user key
     * @return array Extracted user data
     * @throws RuntimeException When user data cannot be extracted
     */
    private function extractUserData(array $fileData, ?string $key = null): array
    {
        $this->logInfo("Extracting user data");

        if (!isset($fileData['users'])) {
            throw new RuntimeException('Invalid user data file: missing "users" key');
        }

        if ($key) {
            if ($key === 'default' && isset($fileData['users']['default'])) {
                return $fileData['users']['default'];
            }

            // Find user by ID or email
            foreach ($fileData['users'] as $userId => $user) {
                if (
                    $userId === $key ||
                    (isset($user['email']) && $user['email'] === $key)
                ) {
                    return $user;
                }
            }

            throw new RuntimeException(sprintf('User not found with key: %s', $key));
        }

        return $fileData['users'];
    }

    /**
     * Extract shipping data from file data
     *
     * @param array $fileData Raw file data
     * @param string|null $key Optional shipping key
     * @return array Extracted shipping data
     * @throws RuntimeException When shipping data cannot be extracted
     */
    private function extractShippingData(array $fileData, ?string $key = null): array
    {
        $this->logInfo("Extracting shipping data");

        if (!isset($fileData['shipping'])) {
            throw new RuntimeException('Invalid shipping data file: missing "shipping" key');
        }

        if ($key) {
            if (!isset($fileData['shipping'][$key])) {
                throw new RuntimeException(sprintf('Shipping data not found with key: %s', $key));
            }

            return $fileData['shipping'][$key];
        }

        return $fileData['shipping'];
    }

    /**
     * Extract payment method data from file data
     *
     * @param array $fileData Raw file data
     * @param string|null $key Optional payment method key
     * @return array Extracted payment method data
     * @throws RuntimeException When payment method data cannot be extracted
     */
    private function extractPaymentMethodData(array $fileData, ?string $key = null): array
    {
        $this->logInfo("Extracting payment method data");

        if (!isset($fileData['payment_methods'])) {
            throw new RuntimeException('Invalid payment method data file: missing "payment_methods" key');
        }

        if ($key) {
            if (!isset($fileData['payment_methods'][$key])) {
                throw new RuntimeException(sprintf('Payment method not found with key: %s', $key));
            }

            return $fileData['payment_methods'][$key];
        }

        return $fileData['payment_methods'];
    }

    /**
     * Validate data based on type
     *
     * @param string $type Data type
     * @param array $data Data to validate
     * @throws RuntimeException When validation fails
     */
    private function validateData(string $type, array $data): void
    {
        $this->logInfo(sprintf("Validating %s data", $type));

        switch ($type) {
            case 'products':
                if (isset($data['slug'])) {
                    // Single product validation
                    $this->validator->validateProductData($data);
                } else {
                    // Product collection validation
                    foreach ($data as $product) {
                        $this->validator->validateProductData($product);
                    }
                }
                break;

            case 'users':
                if (isset($data['email'])) {
                    // Single user validation
                    $this->validator->validateUserData($data);
                } else {
                    // User collection validation
                    foreach ($data as $user) {
                        $this->validator->validateUserData($user);
                    }
                }
                break;

            case 'shipping':
                $this->validator->validateShippingData($data);
                break;

            case 'payment_methods':
                if (isset($data['type'])) {
                    // Single payment method validation
                    $this->validator->validateSchema($data, 'payment_method');
                } else {
                    // Payment method collection validation
                    foreach ($data as $method) {
                        $this->validator->validateSchema($method, 'payment_method');
                    }
                }
                break;
        }

        $this->logInfo("Data validation completed successfully");
    }

    /**
     * {@inheritdoc}
     */
    public function registerData(string $key, array $data): void
    {
        $this->logInfo(sprintf("Registering data with key: %s", $key));
        $this->testData[$key] = $data;
    }

    /**
     * {@inheritdoc}
     */
    public function getData(string $key)
    {
        if (!isset($this->testData[$key])) {
            $this->logError(sprintf("Test data not found for key: %s", $key));
            throw new RuntimeException(
                sprintf('Test data not found for key: %s', $key)
            );
        }

        return $this->testData[$key];
    }

    /**
     * {@inheritdoc}
     */
    public function hasData(string $key): bool
    {
        return isset($this->testData[$key]);
    }
}
