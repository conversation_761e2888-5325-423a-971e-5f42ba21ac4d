<?php
/**
 * Behat bootstrap file to load environment variables
 */

use Symfony\Component\Dotenv\Dotenv;

// Load Composer's autoloader
require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables from .env file
if (file_exists(__DIR__ . '/.env')) {
    $dotenv = new Dotenv();
    $dotenv->load(__DIR__ . '/.env');
}

// Set default values for required environment variables if not set
if (!getenv('TEST_BASE_URL')) {
    putenv('TEST_BASE_URL=https://aeonstest.info');
}

if (!getenv('BROWSER_NAME')) {
    putenv('BROWSER_NAME=chrome');
}

if (!getenv('BROWSER_VERSION')) {
    putenv('BROWSER_VERSION=latest');
}

if (!getenv('PLATFORM')) {
    putenv('PLATFORM=Windows');
}

if (!getenv('PLATFORM_VERSION')) {
    putenv('PLATFORM_VERSION=11');
}

if (!getenv('BUILD_NUMBER')) {
    putenv('BUILD_NUMBER=local_build_' . date('YmdHis'));
}

// BrowserStack credentials need to be set
if (!getenv('BROWSERSTACK_USERNAME') || !getenv('BROWSERSTACK_ACCESS_KEY')) {
    echo "Warning: BrowserStack credentials not set in environment variables.\n";
}
