<?php

namespace App\Context;

use App\Service\Data\TestDataServiceInterface;

/**
 * Extension for TestDataContext to allow setting the TestDataService
 */
trait TestDataContextExtension
{
    /**
     * Set the test data service
     *
     * @param TestDataServiceInterface $dataService Test data service
     * @return void
     */
    public function setTestDataService(TestDataServiceInterface $dataService): void
    {
        $this->dataService = $dataService;
        $this->logInfo('TestDataService replaced with custom implementation');
    }
}
