# Page Object Pattern Rules & Guidelines
Version: 2.0 (Migration Update 2024)

## Core Principles

### 1. Page Object Structure
- All page objects must extend `FriendsOfBehat\PageObjectExtension\Page\Page`
- Pages represent a single page or component
- Each page should have a defined URL path
- Factory pattern for page object creation
- No manual session management needed

### 2. Migration-Specific Rules
```php
use FriendsOfBehat\PageObjectExtension\Page\Page;

class SomePage extends Page
{
    // Required: Define the path for the page
    /**
     * @var string
     */
    protected $path = '/some-path';

    // Required: Implement page verification
    protected function verifyPage(): void
    {
        $this->getDocument()->waitFor(3, function() {
            return $this->hasElement('unique_element');
        });
    }

    // Optional: Define custom open parameters
    public function open(array $urlParameters = []): void
    {
        parent::open($urlParameters);
        $this->waitForPageToLoad();
    }
}
```

### 3. Service Configuration
```yaml
# behat.yml
default:
  extensions:
    FriendsOfBehat\PageObjectExtension: ~

  suites:
    default:
      services:
        page.some_page:
          class: 'Features\Bootstrap\Page\SomePage'
          parent: FriendsOfBehat\PageObjectExtension\Page\Page
          arguments:
            $baseUrl: '%test.base_url%'
          tags: ['page.service']
```

## Implementation Guidelines

### 1. Property Definitions
```php
class ProductPage extends Page
{
    /**
     * @var string
     */
    protected $path = '/product';

    // Selectors as private constants
    private const SELECTORS = [
        'ADD_TO_CART' => '.add-to-cart-button',
        'QUANTITY' => '#quantity-input',
        'PRICE' => '.product-price'
    ];

    // Optional: Define element mappings
    protected array $elements = [
        'Add to Cart' => '.add-to-cart-button',
        'Quantity' => '#quantity-input'
    ];
}
```

### 2. Element Interaction
```php
public function addToCart(): void
{
    try {
        $this->getElement('Add to Cart')->click();
        $this->waitForAjaxToComplete();
    } catch (ElementNotFoundException $e) {
        throw new RuntimeException(
            'Add to cart button not found: ' . $e->getMessage()
        );
    }
}
```

### 3. Wait Conditions
```php
public function waitForPageToLoad(int $timeout = 5000): void
{
    parent::waitForPageToLoad($timeout);
    $this->waitForElementVisible(self::SELECTORS['MAIN_CONTENT']);
    $this->waitForAjaxToComplete();
}

protected function waitForAjaxToComplete(int $timeout = 10000): void
{
    $this->getSession()->wait($timeout, 
        "jQuery.active == 0 && document.readyState === 'complete'"
    );
}
```

### 4. Error Handling
```php
protected function findElement(string $selector, int $timeout = 10): NodeElement
{
    try {
        $element = $this->waitForElement($selector, $timeout);
        if (!$element) {
            throw new ElementNotFoundException(
                $this->getSession(),
                'element',
                'css',
                $selector
            );
        }
        return $element;
    } catch (Exception $e) {
        throw new RuntimeException(
            sprintf('Failed to find element "%s": %s', $selector, $e->getMessage())
        );
    }
}
```

## Migration Best Practices

### 1. Selector Management
- Define all selectors as constants
- Group related selectors
- Use meaningful names
- Document selector purpose
- Keep selectors maintainable
- Consider creating a SelectorRegistry for shared selectors

### 2. Page Verification
```php
public function isOnPage(): bool
{
    return $this->getCurrentUrl() === $this->path
        && $this->isElementVisible(self::SELECTORS['UNIQUE_ELEMENT']);
}

protected function verifyPage(): void
{
    if (!$this->isOnPage()) {
        throw new UnexpectedPageException(
            sprintf('Expected to be on "%s" but was on "%s"',
                $this->path,
                $this->getCurrentUrl()
            )
        );
    }
}
```

### 3. Data Handling
- Use type-hinted methods
- Return structured data
- Validate input parameters
- Document data formats
- Normalize data before returning
- Use DTOs for complex data structures

### 4. AJAX Handling
```php
public function submitForm(): void
{
    $this->clickElement(self::SELECTORS['SUBMIT']);
    $this->waitForAjaxToComplete();
    $this->validateSubmission();
}
```

## Migration Anti-patterns

### 1. Avoid Direct Session Access
```php
// INCORRECT
public function doSomething(): void
{
    $this->session->getPage()->find(...);
}

// CORRECT
public function doSomething(): void
{
    $this->findElement(self::SELECTORS['ELEMENT']);
}
```

### 2. Avoid Business Logic
```php
// INCORRECT
public function processCheckout(): void
{
    // Business logic here
    $this->calculateTotals();
    $this->validateOrder();
}

// CORRECT
public function getOrderDetails(): array
{
    return [
        'total' => $this->getTotal(),
        'items' => $this->getItems()
    ];
}
```

### 3. Avoid Cross-Page Navigation
```php
// INCORRECT
public function goToCheckout(): CheckoutPage
{
    $this->clickElement(self::SELECTORS['CHECKOUT']);
    return new CheckoutPage($this->baseUrl);
}

// CORRECT
public function clickCheckout(): void
{
    $this->clickElement(self::SELECTORS['CHECKOUT']);
}
```

## Migration Documentation Requirements

### 1. Class Documentation
```php
/**
 * Represents the Product page and handles all product-related interactions.
 *
 * Migration Status: Completed
 * Last Updated: 2024-01-20
 *
 * Responsibilities:
 * - Product information display
 * - Add to cart functionality
 * - Quantity selection
 * - Price display
 *
 * Migration Notes:
 * - Updated to use new Page extension v0.3.2
 * - Implemented verifyPage method
 * - Added proper error handling
 */
class ProductPage extends Page
{
```

### 2. Method Documentation
```php
/**
 * Adds the current product to cart with specified quantity.
 *
 * Migration Note: Updated to use new element interaction pattern
 *
 * @param int $quantity The quantity to add
 * @throws RuntimeException If add to cart button is not found
 * @throws InvalidArgumentException If quantity is invalid
 */
public function addToCart(int $quantity = 1): void
{
```

### 3. Selector Documentation
```php
/**
 * CSS Selectors used throughout the page
 * Migration Note: Centralized selectors as constants
 */
private const SELECTORS = [
    // Product Information
    'TITLE' => '.product-title',
    'PRICE' => '.product-price',
    
    // Action Buttons
    'ADD_TO_CART' => '.add-to-cart-button',
    'WISHLIST' => '.wishlist-button'
];
```

### Migration Session Management
- Session is injected via setter
- Always verify session before use
- Use getSession() method for access
- Implement proper cleanup in tearDown

### Migration Progress Tracking
```php
/**
 * @MigrationStatus: IN_PROGRESS
 * @MigrationVersion: 0.3.2
 * @LastUpdated: 2024-01-20
 * @UpdatedBy: AI Assistant
 */
```
