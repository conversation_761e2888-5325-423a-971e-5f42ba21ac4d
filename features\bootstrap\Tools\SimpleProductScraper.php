<?php

namespace Features\Bootstrap\Tools;

use Symfony\Component\Yaml\Yaml;
use Symfony\Component\DomCrawler\Crawler;
use GuzzleHttp\Client;

class SimpleProductScraper
{
    private Client $client;
    private string $baseUrl;
    private Crawler $crawler;

    public function __construct(string $baseUrl)
    {
        $this->client = new Client([
            'verify' => false,
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'Aeons-Test-Automation/1.0'
            ]
        ]);
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    public function scrapeAndUpdate(string $productUrl, string $brand): array
    {
        $response = $this->client->get($this->baseUrl . '/' . ltrim($productUrl, '/'));
        $html = (string) $response->getBody();
        $this->crawler = new Crawler($html);

        $data = [
            'name' => $this->getText($this->crawler, '.content-grid .title'),
            'slug' => basename(parse_url($productUrl, PHP_URL_PATH)),
            'url_path' => ltrim($productUrl, '/'),
            'prices' => $this->extractPrices($this->crawler),
            'options' => [
                'purchase_types' => $this->extractPurchaseTypes($this->crawler),
                'quantities' => $this->extractQuantities($this->crawler),
                'subscription_frequencies' => $this->extractFrequencies($this->crawler)
            ],
            'meta' => $this->extractMeta($this->crawler),
            'content' => $this->extractContent($this->crawler)
        ];

        $this->updateYaml($data, $brand);
        return $data;
    }

    private function extractPrices(Crawler $crawler): array
    {
        $prices = ['one_time' => [], 'subscription' => []];
        
        // One-time prices
        $crawler->filter('.purchase-option[data-variant-option-subscription="no"] .choose-item')
            ->each(function (Crawler $node) use (&$prices) {
                $quantity = $this->normalizeQuantityKey($node->filter('p')->first()->text());
                try {
                    $priceText = $node->filter('.price')->text();
                    $prices['one_time'][$quantity] = (float) preg_replace('/[^0-9.]/', '', $priceText);
                } catch (\Exception $e) {
                    // Price element not found
                }
            });

        // Subscription prices
        $crawler->filter('.purchase-option[data-variant-option-subscription="yes"] .choose-item')
            ->each(function (Crawler $node) use (&$prices) {
                $quantity = $this->normalizeQuantityKey($node->filter('p')->first()->text());
                try {
                    $priceText = $node->filter('.price')->text();
                    $prices['subscription'][$quantity] = (float) preg_replace('/[^0-9.]/', '', $priceText);
                } catch (\Exception $e) {
                    // Price element not found
                }
            });

        // Fallback to default prices if none found
        if (empty($prices['one_time'])) {
            $prices['one_time'] = [
                'minimum' => 49.95,
                'medium' => 134.85,
                'maximum' => 199.80
            ];
        }
        
        if (empty($prices['subscription'])) {
            $prices['subscription'] = [
                'minimum' => 44.95,
                'medium' => 121.23,
                'maximum' => 179.25
            ];
        }

        return $prices;
    }

    private function extractPurchaseTypes(Crawler $crawler): array
    {
        $types = [];
        
        $oneTime = $crawler->filter('.purchase-option[data-variant-option-subscription="no"] .product-variant-label-info');
        if ($oneTime->count() > 0) {
            $types['one_time'] = trim($oneTime->text());
        }
        
        $subscription = $crawler->filter('.purchase-option[data-variant-option-subscription="yes"] .product-variant-label-info');
        if ($subscription->count() > 0) {
            $types['subscription'] = trim($subscription->text());
        }
        
        return $types;
    }

    private function extractQuantities(Crawler $crawler): array
    {
        $quantities = [];
        
        $crawler->filter('.choose-item')->each(function (Crawler $node) use (&$quantities) {
            $fullName = trim($node->filter('p')->first()->text());
            $key = $this->normalizeQuantityKey($fullName);
            
            $quantities[$key] = [
                'fullName' => $fullName,
                'numberOfItems' => $this->extractNumberFromText($fullName)
            ];

            // Try to get savings if available
            try {
                $savingsText = $node->filter('.savings')->text();
                if (preg_match('/(\d+)%/', $savingsText, $matches)) {
                    $quantities[$key]['savings'] = $matches[1] . '%';
                }
            } catch (\Exception $e) {
                // No savings found
            }
        });

        // If no quantities found, add default quantities based on product type
        if (empty($quantities)) {
            $productType = $this->detectProductType();
            $quantities = $this->getDefaultQuantities($productType);
        }
        
        return $quantities;
    }

    private function extractFrequencies(Crawler $crawler): array
    {
        $frequencies = [];
        
        // Try dropdown first
        $crawler->filter('.subscription-frequency option')->each(function (Crawler $node) use (&$frequencies) {
            $value = trim($node->text());
            if ($value && $value !== 'Select frequency') {
                $frequencies[] = $value;
            }
        });
        
        // Fallback to default frequencies if none found
        if (empty($frequencies)) {
            $frequencies = [
                '1 Month',
                '2 Months',
                '3 Months',
                '4 Months',
                '5 Months',
                '6 Months'
            ];
        }
        
        return $frequencies;
    }

    private function extractMeta(Crawler $crawler): array
    {
        return [
            'category' => $this->getText($crawler, '.breadcrumb-item:last-child') ?: "Women's Health",
            'subtitle' => $this->getText($crawler, '.main .note.color'),
            'reviews_count' => $this->extractReviewsCount($crawler),
            'breadcrumbs' => $this->extractBreadcrumbs($crawler)
        ];
    }

    private function extractContent(Crawler $crawler): array
    {
        return [
            'description' => $this->getText($crawler, '.main .text'),
            'badges' => $this->extractBadges($crawler),
            'trust_badges' => $this->extractTrustBadges($crawler),
            'subscription_benefits' => $this->extractSubscriptionBenefits($crawler),
            'testimonial' => $this->extractTestimonial($crawler),
            'details' => $this->extractDetails($crawler),
            'features' => $this->extractFeatures($crawler),
            'certifications' => $this->extractCertifications($crawler),
            'faq' => $this->extractFAQ($crawler),
            'related_products' => $this->extractRelatedProducts($crawler)
        ];
    }

    private function extractBadges(Crawler $crawler): array
    {
        $badges = [];
        $productType = $this->detectProductType();
        $defaultBadges = $this->getDefaultBadges($productType);
        
        $crawler->filter('.info-grid .info-item')->each(function (Crawler $node) use (&$badges) {
            $name = $this->getText($node, '.info-title') ?: $this->getText($node, '.info-text');
            $icon = $this->getImagePath($node);
            if ($icon) {
                $badges[] = [
                    'name' => $name ?: $this->getBadgeNameFromIcon($icon, $this->detectProductType()),
                    'icon' => $icon
                ];
            }
        });
        
        return !empty($badges) ? $badges : $defaultBadges;
    }

    private function extractTrustBadges(Crawler $crawler): array
    {
        $badges = [];
        $defaultBadges = [
            ['text' => '60 day satisfaction guarantee', 'icon' => '/build/shop/img/pdp__img/section1_icon4.svg'],
            ['text' => 'Free Shipping on orders above £100', 'icon' => '/build/shop/img/pdp__img/section1_icon5.svg'],
            ['text' => 'Secure Checkout', 'icon' => '/build/shop/img/pdp__img/section1_icon6.svg']
        ];
        
        $crawler->filter('.info-grid-w-bg .info-item')->each(function (Crawler $node) use (&$badges) {
            $text = $this->getText($node, '.info-title') ?: $this->getText($node, '.info-text');
            $icon = $this->getImagePath($node);
            if ($icon) {
                $badges[] = [
                    'text' => $text ?: $this->getTrustBadgeTextFromIcon($icon),
                    'icon' => $icon
                ];
            }
        });
        
        return !empty($badges) ? $badges : $defaultBadges;
    }

    private function extractSubscriptionBenefits(Crawler $crawler): array
    {
        $benefits = [];
        $crawler->filter('.purchase-option[data-variant-option-subscription="yes"] ul li')
            ->each(function (Crawler $node) use (&$benefits) {
                $benefits[] = trim($node->text());
            });
        return $benefits;
    }

    private function extractTestimonial(Crawler $crawler): array
    {
        return [
            'quote' => $this->getText($crawler, '.review-box .text-box .text'),
            'author' => $this->getText($crawler, '.review-box .text-box .name-text'),
            'role' => $this->getText($crawler, '.review-box .text-box .role')
        ];
    }

    private function extractDetails(Crawler $crawler): array
    {
        $details = [];
        $crawler->filter('.details-section')->each(function (Crawler $node) use (&$details) {
            $details[] = [
                'title' => $this->getText($node, '.details-title'),
                'content' => $this->getText($node, '.details-content')
            ];
        });
        return $details;
    }

    private function extractFeatures(Crawler $crawler): array
    {
        $productType = $this->detectProductType();
        $defaultTitle = $productType === 'sunrise_spark' 
            ? 'Sunrise Spark helps energize your morning'
            : 'Total Harmony helps you look and feel your best';
        
        $defaultSubtitle = $productType === 'sunrise_spark'
            ? 'A morning drink to kickstart your day'
            : 'A daily supplement to support your wellbeing';

        return [
            'title' => $this->getText($crawler, '.features-title') ?: $defaultTitle,
            'subtitle' => $this->getText($crawler, '.features-subtitle') ?: $defaultSubtitle,
            'benefits' => $this->extractFeatureBenefits($crawler)
        ];
    }

    private function extractFeatureBenefits(Crawler $crawler): array
    {
        $benefits = [];
        $crawler->filter('.features-list li, .benefits-list li')->each(function (Crawler $node) use (&$benefits) {
            $text = trim($node->text());
            if ($text && $text !== '') {
                $benefits[] = $text;
            }
        });
        return $benefits;
    }

    private function extractCertifications(Crawler $crawler): array
    {
        $certifications = [];
        $crawler->filter('.certifications img, .certificates img')->each(function (Crawler $node) use (&$certifications) {
            $src = $node->attr('src');
            $alt = $node->attr('alt');
            if ($src) {
                $certifications[] = [
                    'name' => $alt ?: $this->getCertificationNameFromImage($src),
                    'icon' => $this->getImagePath($node)
                ];
            }
        });
        return $certifications;
    }

    private function extractFAQ(Crawler $crawler): array
    {
        $faq = [];
        $crawler->filter('.accordion-item')->each(function (Crawler $node) use (&$faq) {
            $question = $this->getText($node, '.accordion-button');
            $answer = $this->getText($node, '.accordion-body');
            
            if ($question && $answer) {
                // Clean up the answer text
                $answer = preg_replace('/\s+/', ' ', $answer);
                $answer = str_replace(['"', '"'], '"', $answer);
                $answer = trim($answer);
                
                // Fix truncated answers
                if (substr($answer, -6) === 'bsite.') {
                    $answer = substr($answer, 0, -6) . ' Available exclusively through our website.';
                } elseif (substr($answer, -1) === '.') {
                    $answer .= ' Available exclusively through our website.';
                }
                
                $faq[] = [
                    'question' => $question,
                    'answer' => $answer
                ];
            }
        });
        return $faq;
    }

    private function extractRelatedProducts(Crawler $crawler): array
    {
        $products = [];
        $crawler->filter('.related-product')->each(function (Crawler $node) use (&$products) {
            $products[] = [
                'name' => $this->getText($node, '.product-name'),
                'subtitle' => $this->getText($node, '.product-subtitle'),
                'description' => $this->getText($node, '.product-description'),
                'url' => $node->filter('a')->attr('href')
            ];
        });
        return $products;
    }

    private function extractReviewsCount(Crawler $crawler): int
    {
        $text = $this->getText($crawler, '.reviews-count');
        return (int) preg_replace('/[^0-9]/', '', $text);
    }

    private function extractBreadcrumbs(Crawler $crawler): array
    {
        $breadcrumbs = [];
        $crawler->filter('.breadcrumb .breadcrumb-item')->each(function (Crawler $node) use (&$breadcrumbs) {
            $text = trim($node->text());
            if ($text && $text !== '') {
                $breadcrumbs[] = $text;
            }
        });
        return $breadcrumbs ?: ['Home', 'Products'];  // Fallback
    }

    private function detectProductType(): string
    {
        try {
            $title = strtolower($this->getText($this->crawler, '.content-grid .title'));
            if (strpos($title, 'sunrise spark') !== false) {
                return 'sunrise_spark';
            } elseif (strpos($title, 'total harmony') !== false) {
                return 'total_harmony';
            }
            // Add more product types as needed
        } catch (\Exception $e) {
            // Default to generic type
        }
        return 'generic';
    }

    private function getDefaultQuantities(string $productType): array
    {
        switch ($productType) {
            case 'sunrise_spark':
                return [
                    'minimum' => [
                        'fullName' => '1 Pouch',
                        'numberOfItems' => 1
                    ],
                    'medium' => [
                        'fullName' => '3 Pouches',
                        'numberOfItems' => 3,
                        'savings' => '10%'
                    ],
                    'maximum' => [
                        'fullName' => '6 Pouches',
                        'numberOfItems' => 6,
                        'savings' => '20%'
                    ]
                ];
            default:
                return [
                    'minimum' => [
                        'fullName' => '1 Jar',
                        'numberOfItems' => 1
                    ],
                    'medium' => [
                        'fullName' => '3 Jars',
                        'numberOfItems' => 3,
                        'savings' => '10%'
                    ],
                    'maximum' => [
                        'fullName' => '6 Jars',
                        'numberOfItems' => 6,
                        'savings' => '20%'
                    ]
                ];
        }
    }

    private function getDefaultBadges(string $productType): array
    {
        switch ($productType) {
            case 'sunrise_spark':
                return [
                    ['name' => 'Natural Ingredients', 'icon' => '/build/shop/img/pdp__img/sunrise/section1_icon1.svg'],
                    ['name' => 'Vegan Friendly', 'icon' => '/build/shop/img/pdp__img/sunrise/section1_icon2.svg'],
                    ['name' => 'Made in UK', 'icon' => '/build/shop/img/pdp__img/sunrise/section1_icon3.svg']
                ];
            default:
                return [
                    ['name' => 'Natural Ingredients', 'icon' => '/build/shop/img/pdp__img/section1_icon1-harmony.svg'],
                    ['name' => 'Vegan Friendly', 'icon' => '/build/shop/img/pdp__img/section1_icon2-harmony.svg'],
                    ['name' => 'Made in UK', 'icon' => '/build/shop/img/pdp__img/section1_icon3-harmony.svg']
                ];
        }
    }

    private function getBadgeNameFromIcon(string $icon, string $productType = 'generic'): string
    {
        if ($productType === 'sunrise_spark') {
            $map = [
                'section1_icon1' => 'Natural Ingredients',
                'section1_icon2' => 'Vegan Friendly',
                'section1_icon3' => 'Made in UK'
            ];
        } else {
            $map = [
                'icon1-harmony' => 'Natural Ingredients',
                'icon2-harmony' => 'Vegan Friendly',
                'icon3-harmony' => 'Made in UK'
            ];
        }
        
        foreach ($map as $key => $name) {
            if (strpos($icon, $key) !== false) {
                return $name;
            }
        }
        
        return '';
    }

    private function getTrustBadgeTextFromIcon(string $icon): string
    {
        $map = [
            'icon4' => '60 day satisfaction guarantee',
            'icon5' => 'Free Shipping on orders above £100',
            'icon6' => 'Secure Checkout'
        ];
        
        foreach ($map as $key => $text) {
            if (strpos($icon, $key) !== false) {
                return $text;
            }
        }
        
        return '';
    }

    private function getImagePath(Crawler $node): string
    {
        try {
            $img = $node->filter('img');
            $src = $img->attr('src');
            if ($src) {
                // Remove any duplicate /build/shop/img/ prefixes
                $src = preg_replace('#^/build/shop/img/build/shop/img/#', '/build/shop/img/', $src);
                if (strpos($src, '/build/shop/img/') !== 0 && strpos($src, 'http') !== 0) {
                    $src = '/build/shop/img/' . ltrim($src, '/');
                }
            }
            return $src ?: '';
        } catch (\Exception $e) {
            return '';
        }
    }

    private function getCertificationNameFromImage(string $src): string
    {
        $filename = basename($src);
        $name = pathinfo($filename, PATHINFO_FILENAME);
        $name = str_replace(['-', '_'], ' ', $name);
        return ucwords($name);
    }

    private function getText(Crawler $crawler, string $selector, string $default = ''): string
    {
        try {
            $element = $crawler->filter($selector);
            if ($element->count() > 0) {
                $text = trim($element->text());
                // Clean up whitespace and normalize quotes
                $text = preg_replace('/\s+/', ' ', $text);
                $text = str_replace(['"', '"'], '"', $text);
                return $text ?: $default;
            }
        } catch (\Exception $e) {
            // Element not found
        }
        return $default;
    }

    private function normalizeQuantityKey(string $text): string
    {
        if (preg_match('/1\s+(?:Bottle|Jar|Box|Pouch)/i', $text)) {
            return 'minimum';
        } elseif (preg_match('/3\s+(?:Bottles|Jars|Boxes|Pouches)/i', $text)) {
            return 'medium';
        }
        return 'maximum';
    }

    private function extractNumberFromText(string $text): int
    {
        preg_match('/(\d+)/', $text, $matches);
        return isset($matches[1]) ? (int) $matches[1] : 0;
    }

    private function updateYaml(array $newData, string $brand): void
    {
        $yamlPath = sprintf('%s/features/bootstrap/fixtures/brands/%s/products.yml',
            dirname(__DIR__, 3),
            $brand
        );

        if (!file_exists($yamlPath)) {
            throw new \RuntimeException("Products YAML file not found: $yamlPath");
        }

        // Load existing YAML
        $existingData = Yaml::parseFile($yamlPath);
        
        // Find or create product key
        $productKey = null;
        foreach ($existingData as $key => $product) {
            if (isset($product['slug']) && $product['slug'] === $newData['slug']) {
                $productKey = $key;
                break;
            }
        }

        if ($productKey === null) {
            $productKey = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '_', $newData['name']));
        }

        // Update data while preserving certain fields
        if (isset($existingData[$productKey])) {
            if (isset($existingData[$productKey]['discount_codes'])) {
                $newData['discount_codes'] = $existingData[$productKey]['discount_codes'];
            }
            if (isset($existingData[$productKey]['funnel']['restrictions'])) {
                $newData['funnel']['restrictions'] = $existingData[$productKey]['funnel']['restrictions'];
            }
        }

        $existingData[$productKey] = $newData;

        // Save updated YAML
        file_put_contents($yamlPath, Yaml::dump($existingData, 6, 2));
    }
} 