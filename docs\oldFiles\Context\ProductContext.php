<?php

namespace Features\Bootstrap\Context;

use Assert;
use ElementNotFoundException;
use Exception;
use Features\Bootstrap\Page\ProductPage;
use App\Service\State\SharedStateServiceInterface;
use InvalidArgumentException;
use RuntimeException;
use Throwable;

/**
 * Handles all product-related step definitions
 */
class ProductContext extends BaseContext
{
    private ProductPage $productPage;

    /**
     * @param ProductPage $productPage
     */
    public function __construct(ProductPage $productPage)
    {
        parent::__construct();
        $this->productPage = $productPage;
    }

    /**
     * @When /^I select flavor "([^"]*)"$/
     */
    public function iSelectFlavor(string $flavor): void
    {
        try {
            $this->productPage->selectFlavor($flavor);
            $this->logInfo(sprintf('Selected flavor: %s', $flavor));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to select flavor "%s"', $flavor), $e);
            throw $e;
        }
    }

    /**
     * @Then /^The selected flavor should be "([^"]*)"$/
     */
    public function theSelectedFlavorShouldBe(string $expectedFlavor): void
    {
        try {
            $actualFlavor = $this->productPage->getSelectedFlavor();

            // If product doesn't have flavors, skip the check
            if ($actualFlavor === null && !$this->productPage->hasFlavorOptions()) {
                return;
            }

            if ($actualFlavor !== $expectedFlavor) {
                throw new RuntimeException(
                    sprintf('Expected flavor "%s", but got "%s"', $expectedFlavor, $actualFlavor)
                );
            }
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify selected flavor "%s"', $expectedFlavor), $e);
            throw $e;
        }
    }

    /**
     * @Then /^The product should have flavor options$/
     */
    public function theProductShouldHaveFlavorOptions(): void
    {
        try {
            if (!$this->productPage->hasFlavorOptions()) {
                throw new RuntimeException('Expected product to have flavor options, but none were found');
            }
        } catch (Throwable $e) {
            $this->logError('Failed to verify product flavor options', $e);
            throw $e;
        }
    }

    /**
     * @Given /^I am on the "([^"]*)" product page$/
     * @throws ElementNotFoundException
     * @sets product.name
     */
    public function iAmOnTheSpecificProductPage(string $productName): void
    {
        try {
            // Store product name in shared context for later verification
            $this->stateService->set('product.name', $productName);

            // Load the product page
            $this->productPage->loadWithName($productName);

            // Get actual product name and normalize it
            $actualProductName = $this->productPage->getProductName();

            Assert::assertEquals(
                ucwords(strtolower(trim($productName))),  // Normalize expected name
                $actualProductName,
                sprintf('Expected to be on "%s" product page, but found "%s"',
                    $productName,
                    $actualProductName
                )
            );

            $this->logInfo(sprintf('Successfully loaded product page for "%s"', $productName));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to load product page for "%s"', $productName), $e);
            throw $e;
        }
    }

    /**
     * @When /^I select the "([^"]*)" quantity option$/
     * @throws ElementNotFoundException
     * @sets product.quantity
     */
    public function iSelectTheQuantityOption(string $quantity): void
    {
        try {
            $this->productPage->selectQuantity($quantity);
            $this->stateService->set('product.quantity', $quantity);
            $this->logInfo(sprintf('Selected quantity: %s', $quantity));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to select quantity "%s"', $quantity), $e);
            throw new RuntimeException(
                sprintf('Failed to select quantity "%s": %s', $quantity, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I select "([^"]*)" purchase$/
     * @throws ElementNotFoundException
     * @sets product.purchase_type
     */
    public function iSelectPurchaseType(string $purchaseType): void
    {
        try {
            $this->productPage->selectPurchaseType($purchaseType);
            $this->stateService->set('product.purchase_type', $purchaseType);
            $this->logInfo(sprintf('Selected purchase type: %s', $purchaseType));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to select purchase type "%s"', $purchaseType), $e);
            throw new RuntimeException(
                sprintf('Failed to select purchase type "%s": %s', $purchaseType, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I set the supply to "([^"]*)"$/
     * @throws ElementNotFoundException
     * @sets product.subscription_frequency_days, product.subscription_frequency_display
     */
    public function iSetTheSupplyTo(string $frequency): void
    {
        try {
            $days = $this->productPage->selectSupplyDuration($frequency);

            // Store both the display format and the actual days value
            $this->stateService->set('product.subscription_frequency_days', $days);
            $this->stateService->set('product.subscription_frequency_display', $frequency);

            $this->logInfo(sprintf('Set supply duration to: %s (%d days)', $frequency, $days));
        } catch (ElementNotFoundException $e) {
            $this->logError(sprintf('Failed to set supply duration to "%s"', $frequency), $e);
            throw new RuntimeException(
                sprintf('Failed to set supply duration to "%s": %s', $frequency, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I subscribe to product$/
     * @throws ElementNotFoundException
     */
    public function userSubscribesToProduct(): void
    {
        try {
            $this->productPage->clickToSubscribe();
            $this->logInfo('Subscribed to product');
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to subscribe to product', $e);
            throw new RuntimeException(
                'Failed to subscribe to product: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I set the quantity to "([^"]*)"$/
     * @throws RuntimeException
     */
    public function selectQuantity(string $quantityKey): void
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('Product data not loaded');
        }

        try {
            $this->productPage->selectSize($quantityKey, $productData);

            // Store selected quantity data for later verification
            $selectedQuantity = $productData['options']['quantities'][$quantityKey];
            $this->stateService->set('selectedQuantity', [
                'key' => $quantityKey,
                'fullName' => $selectedQuantity['fullName'],
                'numberOfItems' => $selectedQuantity['numberOfItems']
            ]);
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException(
                sprintf('Failed to select quantity "%s": %s', $quantityKey, $e->getMessage())
            );
        }
    }

    /**
     * @When /^I select "([^"]*)"$/
     * @throws RuntimeException
     * @throws InvalidArgumentException
     */
    public function userSelectsPurchaseType(string $purchaseOption): void
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData || !isset($productData['options']['purchase_types'])) {
            throw new RuntimeException('Product data or purchase options not loaded');
        }

        // Validate purchase option against test data
        $availableOptions = array_values($productData['options']['purchase_types']);
        if (!in_array($purchaseOption, $availableOptions)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Invalid purchase option "%s". Available options: %s',
                    $purchaseOption,
                    implode(', ', $availableOptions)
                )
            );
        }

        try {
            $this->productPage->selectPurchaseOption($purchaseOption);
            $this->stateService->set('selectedPurchaseOption', $purchaseOption);
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException(
                sprintf('Failed to select purchase option "%s": %s', $purchaseOption, $e->getMessage())
            );
        }
    }

    /**
     * @Then /^the purchase option "([^"]*)" should be selected$/
     * @throws RuntimeException
     */
    public function verifySelectedPurchaseOption(string $expectedOption): void
    {
        try {
            $selectedOption = $this->productPage->getSelectedPurchaseOption();
            Assert::assertEquals(
                $expectedOption,
                $selectedOption,
                sprintf('Expected purchase option "%s" but got "%s"', $expectedOption, $selectedOption)
            );
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException(
                sprintf('Failed to verify selected purchase option: %s', $e->getMessage())
            );
        }
    }

    /**
     * @When /^(?:I|user) click "Add to cart"$/
     * @throws RuntimeException
     */
    public function userAddsTheProductToTheCart(): void
    {
        try {
            $this->productPage->addToCart();
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to add product to cart: ' . $e->getMessage());
        }
    }

    /**
     * @When /^I add the product "([^"]*)" to the cart$/
     * @throws RuntimeException
     */
    public function userAddsSpecificProductToTheCart(string $productName): void
    {
        try {
            $this->productPage->load($productName);
            $this->productPage->addToCart();
        } catch (Exception $e) {
            throw new RuntimeException(
                sprintf('Failed to add product "%s" to cart: %s', $productName, $e->getMessage())
            );
        }
    }

    /**
     * @Then /^Expected sum of products should be calculated correctly$/
     * @throws RuntimeException
     */
    public function expectedSumOfProductsShouldBeCalculatedCorrectly(): void
    {
        try {
            $this->productPage->verifySumOfProducts();
        } catch (Exception $e) {
            throw new RuntimeException('Failed to verify sum of products: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify product content matches configuration$/
     * @throws RuntimeException
     */
    public function iVerifyProductContentMatchesConfiguration(): void
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('No product data loaded');
        }

        try {
            $errors = [];
            $errors = array_merge($errors, $this->collectProductBasicInfoErrors($productData));
            $errors = array_merge($errors, $this->collectPricingOptionsErrors($productData));
            $errors = array_merge($errors, $this->collectProductBadgesErrors($productData));
            $errors = array_merge($errors, $this->collectSubscriptionBenefitsErrors($productData));

            if (!empty($errors)) {
                throw new RuntimeException("Product content validation failed:\n" . implode("\n", $errors));
            }
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify product content: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify product images are loaded$/
     * @throws RuntimeException
     */
    public function iVerifyProductImages(): void
    {
        try {
            $this->productPage->verifyImagesLoaded();
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify product images: ' . $e->getMessage());
        }
    }

    /**
     * @When /^I click add to cart$/
     */
    public function iClickAddToCart(): void
    {
        try {
            // If we need to access methods from other contexts, use the context manager
            if ($this->contextManager !== null) {
                $cartContext = $this->contextManager->getContext(CartContext::class);
                $cartContext->addToCart();
            } else {
                $this->productPage->addToCart();
            }
            $this->logInfo('Added product to cart');
        } catch (Throwable $e) {
            $this->logError('Failed to add product to cart', $e);
            throw new RuntimeException('Failed to add product to cart: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @When /^I select the "([^"]*)" purchase type$/
     */
    public function iSelectThePurchaseType(string $purchaseType): void
    {
        $selector = match ($purchaseType) {
            'One-Time Purchase' => '.one-time-purchase',
            'Subscribe & Save' => '.subscription-purchase',
            default => throw new \InvalidArgumentException("Unknown purchase type: $purchaseType")
        };

        $this->getSession()->getPage()->find('css', $selector)->click();
        $this->waitForAjax();
    }

    /**
     * @When /^I select "([^"]*)" quantity$/
     */
    public function iSelectQuantity(string $quantity): void
    {
        $this->productPage->selectQuantity($quantity);
        $this->waitForAjaxToComplete();
    }

    /**
     * @Given /^I am on the product page$/
     * @throws ElementNotFoundException
     * @throws Exception
     */
    public function iAmOnTheProductPage(): void
    {
        // Get product data from shared context
        $productData = this->stateService->get('currentProduct');
        if (!$productData) {
            throw new \Exception('No product data loaded');
        }

        $this->productPage->loadWithName($productData['name'], $productData);
    }
}