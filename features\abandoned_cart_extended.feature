Feature: Extended Abandoned Cart Recovery
        As an online store owner
        I want to handle various abandoned cart scenarios
        So that I can maximize sales recovery opportunities

    Background:
        Given I load brand configuration
          And I load product data

    @abandoned_cart @subscription
    Scenario: Customer abandons subscription cart
        Given I am on the product page
         When I select "Subscribe & Save"
          And I select the "medium" quantity option
          And I set the supply to "2 Months"
          And I add the product to the cart
         When I proceed to checkout
         Then I verify Google Tag Manager is present
          And I fill in the shipping information with "default" user data
          And I abandon the checkout
         Then I should see that the order status is "abandoned" in the database
          And I should receive an abandoned cart email with subject "Don't miss out on your subscription savings"

    @abandoned_cart @mixed_cart
    Scenario: Customer abandons mixed cart with subscription and one-time items
        Given I am on the product page
         When I select "Subscribe & Save"
          And I select the "medium" quantity option
          And I set the supply to "2 Months"
          And I add the product to the cart
          And I am on the product page
         When I select "One-Time Purchase"
          And I select the "minimum" quantity option
          And I add the product to the cart
         When I proceed to checkout
         Then I verify Google Tag Manager is present
          And I fill in the shipping information with "default" user data
          And I abandon the checkout
         Then I should see that the order status is "abandoned" in the database
          And I should receive an abandoned cart email
          And the email should contain both subscription and one-time items

    @abandoned_cart @coupon
    Scenario: Customer abandons cart with applied coupon
        Given I am on the product page
         When I select "One-Time Purchase"
          And I select the "medium" quantity option
          And I add the product to the cart
          And I apply a valid coupon code "AEONS15"
         When I proceed to checkout
         Then I verify Google Tag Manager is present
          And I fill in the shipping information with "default" user data
          And I abandon the checkout
         Then I should see that the order status is "abandoned" in the database
          And I should receive an abandoned cart email
          And the email should mention the applied discount

    @abandoned_cart @payment_failed
    Scenario: Cart is not abandoned when payment fails
        Given I am on the product page
         When I select "One-Time Purchase"
          And I select the "medium" quantity option
          And I add the product to the cart
         When I proceed to checkout
         Then I verify Google Tag Manager is present
          And I fill in the shipping information with "default" user data
          And I enter "stripe_declined" payment details
          And I complete the purchase
         Then I should see an error message indicating the payment was declined
          And I should NOT see an abandoned cart email
          And the order status should be "payment_failed" in the database

    @abandoned_cart @recovery_with_changes
    Scenario: Customer recovers cart and modifies items
        Given I have an abandoned cart
          And I received an abandoned cart recovery email
         When I follow the recovery link in the email
         Then my cart should be restored
         When I decrease the quantity to "minimum"
          And I proceed to checkout
          And I fill in the shipping information with "default" user data
          And I enter "stripe_valid" payment details
          And I complete the purchase
         Then I should see the order confirmation
          And the order status should be "completed" in the database

    @abandoned_cart @multiple_recovery_attempts
    Scenario: Multiple recovery attempts for same cart
        Given I have an abandoned cart
          And I received an abandoned cart recovery email
         When I follow the recovery link in the email
         Then my cart should be restored
         When I abandon the checkout
          And I follow the recovery link in the email again
         Then my cart should be restored
          And I should see a message "Complete your purchase now to avoid losing your items"

    @abandoned_cart @high_value
    Scenario: High-value cart gets priority recovery email
        Given I am on the product page
         When I select "One-Time Purchase"
          And I select the "maximum" quantity option
          And I set the quantity to "10"
          And I add the product to the cart
         When I proceed to checkout
         Then I verify Google Tag Manager is present
          And I fill in the shipping information with "default" user data
          And I abandon the checkout
         Then I should see that the order status is "abandoned" in the database
          And I should receive a priority abandoned cart email within "30" minutes
          And the email should contain a special discount offer

    @abandoned_cart @guest_checkout
    Scenario: Guest user cart creates shop user
        Given I am on the product page
         When I select "One-Time Purchase"
          And I select the "medium" quantity option
          And I add the product to the cart
         When I proceed to checkout
         Then I verify Google Tag Manager is present
          And I fill in the shipping information with "new" user data
          And I abandon the checkout
         Then I should see that the order status is "abandoned" in the database
          And a shop user should be created with the provided email
          And I should receive an abandoned cart email
          And the email should contain account creation information 