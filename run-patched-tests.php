<?php

// Apply patches
require_once __DIR__ . '/apply-patches.php';

// Set environment variables for the test
putenv("TEST_BRAND=aeons");
putenv("TEST_ENV=stage");
putenv("TEST_BASE_URL=https://aeonstest.info");
putenv("BROWSER_NAME=chrome");
putenv("BROWSER_VERSION=latest");
putenv("WEBDRIVER_HOST=http://localhost:9222");

// Set critical environment variables to force real services
putenv("USE_MOCKS=false");
putenv("TEST_MODE=real");
putenv("FORCE_REAL_SERVICES=true");
putenv("DISABLE_MOCKS=true");

// Print environment variables for debugging
echo "Environment variables set:\n";
echo "USE_MOCKS: " . getenv("USE_MOCKS") . "\n";
echo "TEST_MODE: " . getenv("TEST_MODE") . "\n";
echo "FORCE_REAL_SERVICES: " . getenv("FORCE_REAL_SERVICES") . "\n";
echo "DISABLE_MOCKS: " . getenv("DISABLE_MOCKS") . "\n";
echo "TEST_BRAND: " . getenv("TEST_BRAND") . "\n";
echo "TEST_ENV: " . getenv("TEST_ENV") . "\n";
echo "TEST_BASE_URL: " . getenv("TEST_BASE_URL") . "\n";
echo "WEBDRIVER_HOST: " . getenv("WEBDRIVER_HOST") . "\n";

// Run the tests
$feature = 'features/salesFunnel.feature';
$command = "php bin/run-tests.php --brand=aeons --env=stage --feature=$feature";

echo "Running command: $command\n";
passthru($command, $exitCode);

exit($exitCode);
