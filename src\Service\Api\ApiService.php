<?php

namespace App\Service\Api;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;

/**
 * Default implementation of the API service
 */
class ApiService extends AbstractService implements ApiServiceInterface
{
    /**
     * API base URL
     *
     * @var string
     */
    private string $baseUrl;

    /**
     * Constructor
     *
     * @param string|null $baseUrl Base URL for API requests
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?string $baseUrl = null, ?LoggerInterface $logger = null)
    {
        parent::__construct($logger);
        $this->baseUrl = $baseUrl ?? getenv('API_BASE_URL') ?? 'https://api.aeonstest.info';
    }

    /**
     * {@inheritdoc}
     */
    public function sendRequest(string $endpoint, array $parameters = [], string $method = 'GET')
    {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        $this->logInfo(sprintf("Sending %s request to %s", $method, $url));

        // Placeholder for actual API request implementation
        // In a real implementation, this would use cURL, Guzzle, or another HTTP client

        // For now, just log the request and return a dummy response
        $this->logInfo(sprintf("Request parameters: %s", json_encode($parameters)));

        return [
            'success' => true,
            'message' => 'This is a placeholder response',
            'data' => []
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    /**
     * {@inheritdoc}
     */
    public function setBaseUrl(string $baseUrl): void
    {
        $this->baseUrl = $baseUrl;
    }
}
