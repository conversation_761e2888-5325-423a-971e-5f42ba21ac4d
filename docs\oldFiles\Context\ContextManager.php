<?php

namespace Features\Bootstrap;

use Features\Bootstrap\Context\BaseContext;
use Psr\Container\ContainerInterface;
use RuntimeException;

/**
 * Manages access to contexts dynamically.
 * Instantiated by the DIC. Avoid direct use if possible; prefer injecting specific contexts.
 */
class ContextManager
{
    private array $contexts = [];
    private SharedStateServiceInterface $stateService;
    private ContainerInterface $container;

    public function __construct(ContainerInterface $container, SharedDataContext $sharedData)
    {
        $this->container = $container;
        $this->sharedData = $sharedData;
        $this->initializeContexts();
    }

    private function initializeContexts(): void
    {
        // Get all services tagged with 'context.service'
        $contextServices = $this->container->findTaggedServiceIds('context.service');

        foreach ($contextServices as $serviceId => $tags) {
            if ($serviceId === self::class) {
                continue;
            }

            if ($this->container->has($serviceId)) {
                $context = $this->container->get($serviceId);
                if ($context instanceof BaseContext) {
                    $this->contexts[get_class($context)] = $context;
                }
            }
        }
    }

    public function getContext(string $className): BaseContext
    {
        if (isset($this->contexts[$className])) {
            return $this->contexts[$className];
        }

        if ($this->container->has($className)) {
            $context = $this->container->get($className);
            if ($context instanceof BaseContext) {
                $this->contexts[$className] = $context;
                return $context;
            }
        }

        throw new RuntimeException(sprintf("Context '%s' not found or could not be retrieved.", $className));
    }

    public function getSharedData(): SharedDataContext
    {
        return $this->sharedData;
    }

    /**
     * @BeforeScenario
     * Reset contexts if necessary. Note: SharedDataContext resets itself.
     * This might not be needed if contexts don't hold scenario-specific state
     * beyond what SharedDataContext manages.
     */
    public function resetContexts(): void
    {
        $this->contexts = [];
    }
} 