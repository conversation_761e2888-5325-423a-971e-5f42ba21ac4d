# E-commerce Test Automation Framework
## Documentation & Guidelines
Version: 1.2
Last Updated: 2025-03-27

---

## Table of Contents
1. [Project Overview & Setup](#1-project-overview--setup)
2. [Core Framework Components](#2-core-framework-components)
3. [Test Architecture & Patterns](#3-test-architecture--patterns)
4. [Local Browser Testing](#4-local-browser-testing)
5. [Code Documentation Standards](#5-code-documentation-standards)
6. [Test Result Analysis Framework](#6-test-result-analysis-framework)
7. [Troubleshooting & Maintenance](#7-troubleshooting--maintenance)
8. [AI Agent Lessons & Patterns](#8-ai-agent-lessons--patterns)

---

## 1. Project Overview & Setup

### 1.1 Core Technologies

- **PHP/Behat**: Core testing framework for BDD-style tests
  - Version: PHP ^8.1, Behat ^3.12
  - Purpose: Behavior-driven development and test execution
  - Dependencies:
    - behat/behat: ^3.12
    - friends-of-behat/test-context: ^1.3
    - friends-of-behat/mink-extension: ^2.7
    - phpunit/phpunit: ^9.5

- **MinkExtension**: Web testing extension for Behat
  - Purpose: Browser interaction and UI testing
  - Integration: Seamless integration with Behat contexts
  - Dependencies:
    - friends-of-behat/mink: 1.10.0
    - friends-of-behat/mink-browserkit-driver: ^1.6
    - friends-of-behat/mink-extension: ^2.7
    - behat/mink-selenium2-driver: 1.6.0

- **Sylius**: PHP e-commerce framework
  - Base: Symfony framework
  - Purpose: Backend e-commerce functionality
  - Integration: Command-line interface for backend operations
  - Dependencies:
    - symfony/process: ^6.4
    - symfony/yaml: ^6.4
    - symfony/dom-crawler: ^6.4

- **Additional Dependencies**:
  - guzzlehttp/guzzle: ^7.9 (HTTP client)
  - phpseclib/phpseclib: ^3.0 (Security library)
  - ext-curl: * (PHP CURL extension)

### 1.2 Repository Structure

```
project_root/
├── features/ # Behat features and contexts
│ ├── purchase.feature # Feature file for product purchase
│ ├── abandoned_cart.feature # Feature file for abandoned cart scenarios
│ ├── abandoned_cart_extended.feature # Extended scenarios for abandoned cart
│ ├── mixedCart.feature # Feature file for mixed cart scenarios
│ ├── productPage.feature # Feature file for product page interactions
│ ├── salesFunnel.feature # Feature file for sales funnel scenarios
│ ├── subscription_reorder.feature # Feature file for subscription reorder scenarios
│ ├── bootstrap/ # Bootstrap directory for Behat contexts and helpers
│ │ ├── Context/ # Behat context classes
│ │ │ ├── AbandonedCartContext.php # Abandoned cart context
│ │ │ ├── AdminCommandContext.php # Admin command context
│ │ │ ├── BaseContext.php # Base context
│ │ │ ├── BrandContext.php # Brand context
│ │ │ ├── CartContext.php # Cart context
│ │ │ ├── CheckoutContext.php # Checkout context
│ │ │ ├── DatabaseContext.php # Database context
│ │ │ ├── EmailContext.php # Email context
│ │ │ ├── NavigationContext.php # Navigation context
│ │ │ ├── PaymentContext.php # Payment context
│ │ │ ├── ProductContext.php # Product context
│ │ │ ├── SSHContext.php # SSH context
│ │ │ ├── TestDataContext.php # Test data context
│ │ │ └── ValidationContext.php # Validation context
│ │ ├── Core/ # Core utilities and configurations
│ │ │ ├── ConfigurationManager.php # Configuration management
│ │ │ ├── DataValidator.php # Data validation utilities
│ │ │ └── TestDataRegistry.php # Test data registry
│ │ ├── fixtures/ # Test data
│ │ │ └── brands/ # Brand-specific test data
│ │ │ ├── aeons/
│ │ │ │ ├── products.yml
│ │ │ │ └── test_data.yml
│ │ │ └── dss/
│ │ │ ├── products.yml
│ │ │ └── test_data.yml
│ │ ├── Helper/ # Helper classes
│ │ │ ├── DatabaseHelper.php # Database interaction helpers
│ │ │ └── SSHTunnel.php # SSH tunneling utilities
│ │ ├── Page/ # Page Objects
│ │ │ ├── BasePage.php # Base page object
│ │ │ ├── CartPage.php # Cart page object
│ │ │ ├── CheckoutPage.php # Checkout page object
│ │ │ ├── HomePage.php # Home page object
│ │ │ ├── ProductPage.php # Product page object
│ │ │ ├── ConfirmationPage.php # Confirmation page object
│ │ │ ├── PayPalPage.php # PayPal page object
│ │ │ ├── Stripe3DSPage.php # Stripe 3DS page object
│ │ │ └── UpsellPage.php # Upsell page object
│ │ ├── Tools/ # Test maintenance tools
│ │ │ ├── ProductScraper.php
│ │ │ ├── SelectorUpdater.php
│ │ │ ├── update-product-data.php
│ │ │ ├── check_latest_emails.php
│ │ │ ├── check_mysql.php
│ │ │ ├── check_remote.php
│ │ │ ├── diagnose_connection.php
│ │ │ ├── db_verify.php
│ │ │ ├── EmailVerificationTool.php
│ │ │ └── verify_db.php
│ │ ├── SharedDataContext.php # Shared data context for cross-step data sharing
│ └── local/ # Local feature files
│ │ └── local.feature # Local testing scenarios
│ └── single/ # Single feature files
│ │ └── single.feature # Single testing scenarios
├── lib/ # Library files
│ ├
│ └
└── vendor/ # Composer dependencies
    ├── autoload.php # Composer autoload file
    └── behat/ # Behat dependencies
```

### 1.3 Environment Setup

#### Required Environment Variables
```bash
# Test Configuration
BUILD_NUMBER=local_build
TEST_BRAND=aeons
TEST_ENV=stage
TEST_BASE_URL=https://stage.example.com
TEST_PRODUCT=product_name

# Mailtrap Configuration
MAILTRAP_ACCOUNT_ID=your_account_id
MAILTRAP_TOKEN=your_token
MAILTRAP_AEONS_INBOX_ID=your_inbox_id

# SSH Configuration
SSH_HOST=your_host
SSH_USER=your_user
SSH_KEY=your_private_key

# Database Configuration
DB_HOST=your_db_host
DB_PORT=3306
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Browser Configuration
BROWSER_NAME=chrome
BROWSER_VERSION=latest
```

### 1.4 Test Execution

#### Test Runner Script
The `bin/run-tests` script provides the following functionality:

1. **Environment Management**
   - Brand selection (aeons, dss, odd, etc.)
   - Environment selection (dev, stage, prod)
   - Product selection for testing

2. **Directory Validation**
   - Validates required directory structure
   - Checks for brand fixtures
   - Ensures proper configuration files

3. **Test Execution Modes**
```bash
# Run all products for a brand
./bin/run-tests --brand=aeons --env=stage --all-products

# Run specific test tags
./bin/run-tests --brand=aeons --env=stage --tags=@smoke_one_time

# Run specific product tests
./bin/run-tests --brand=aeons --env=stage --product=golden_harvest

# Run tests with specific tags for a specific product
./bin/run-tests --brand=aeons --env=stage --product=golden_harvest --tags=@subscription
```

{{ ... }}

## 4. Local Browser Testing

### 4.1 Browser Driver Setup

#### Chrome Driver
- **Purpose**: Enables browser automation for Chrome
- **Setup**:
  - Download ChromeDriver from [ChromeDriver Website](https://chromedriver.chromium.org/downloads)
  - Ensure the version matches your installed Chrome browser
  - Add to system PATH or specify path in configuration

#### Firefox Driver (GeckoDriver)
- **Purpose**: Enables browser automation for Firefox
- **Setup**:
  - Download GeckoDriver from [GitHub Releases](https://github.com/mozilla/geckodriver/releases)
  - Add to system PATH or specify path in configuration

#### Selenium Server (Optional)
- **Purpose**: Provides centralized browser automation service
- **Usage**: Useful for complex test setups or remote execution
- **Setup**:
  - Download Selenium Server JAR from [Selenium Website](https://www.selenium.dev/downloads/)
  - Start the server: `java -jar selenium-server-standalone-x.xx.x.jar`

### 4.2 Configuration for Local Browser Testing

#### Basic Configuration (behat.yml)
```yaml
default:
  extensions:
    Behat\MinkExtension:
      base_url: 'https://stage.example.com'
      default_session: selenium2
      javascript_session: selenium2
      browser_name: chrome
      sessions:
        selenium2:
          selenium2:
            browser: chrome
            capabilities:
              acceptSslCerts: true
              javascriptEnabled: true
```

#### Advanced Configuration
```yaml
default:
  extensions:
    Behat\MinkExtension:
      base_url: '%base_url%'
      default_session: selenium2
      javascript_session: selenium2
      browser_name: '%browser_name%'
      sessions:
        selenium2:
          selenium2:
            browser: '%browser_name%'
            wd_host: '%wd_host%'
            capabilities:
              acceptSslCerts: true
              javascriptEnabled: true
              extra_capabilities:
                chromeOptions:
                  args:
                    - "--headless"
                    - "--disable-gpu"
                    - "--window-size=1920,1080"
```

### 4.3 Browser Testing Best Practices

#### Element Selection
- **Consistent Selectors**: Maintain a centralized repository of selectors
- **Selector Hierarchy**: Prefer ID > Name > CSS Path > XPath
- **Selector Maintenance**: Regularly update selectors when site UI changes

#### Wait Strategies
- **Explicit Waits**: Wait for specific conditions rather than fixed time
- **Expected Conditions**: Wait for elements to be clickable, visible, etc.
- **Page Load Completion**: Ensure page is fully loaded before interaction

#### Browser Session Management
- **Clean Session**: Start with a clean session for each test
- **Cookie Management**: Clear cookies between tests if needed
- **Session Reuse**: Reuse session when possible for performance

#### Screenshot Integration
- **Failure Screenshots**: Capture screenshots on test failures
- **Step Screenshots**: Optionally capture screenshots at critical steps
- **Screenshot Storage**: Organize screenshots by date and test name

{{ ... }}