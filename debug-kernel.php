<?php

require __DIR__ . '/vendor/autoload.php';

use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;
use Symfony\Component\DependencyInjection\Dumper\PhpDumper;

try {
    // Initialize container
    $container = new ContainerBuilder();
    
    // Set required parameters
    $projectRoot = __DIR__;
    $container->setParameter('kernel.project_dir', $projectRoot);
    $container->setParameter('paths.base', $projectRoot); 
    $container->setParameter('app.project_root', $projectRoot);
    $container->setParameter('app.config_dir', $projectRoot . '/config');
    $container->setParameter('app.fixtures_dir', $projectRoot . '/features/fixtures');
    $container->setParameter('app.cache_dir', $projectRoot . '/var/cache');
    $container->setParameter('app.logs_dir', $projectRoot . '/var/logs');
    $container->setParameter('app.downloads_dir', $projectRoot . '/downloads');
    $container->setParameter('kernel.debug', true);
    
    // Load services configuration
    $loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/config'));
    
    // Load only the core services first
    $loader->load('services/core.yml');
    
    // Try to compile the container with just core services
    $container->compile();
    
    echo "Core services compiled successfully!\n";
    
    // Now try to add other services one by one
    $serviceFiles = [
        'services/contexts.yml',
        'services/pages.yml',
        'services/compatibility.yml',
        'services/api.yml',
        'services/optimization.yml'
    ];
    
    foreach ($serviceFiles as $serviceFile) {
        echo "Testing $serviceFile...\n";
        
        // Initialize a new container for each test
        $testContainer = new ContainerBuilder();
        
        // Set required parameters
        $testContainer->setParameter('kernel.project_dir', $projectRoot);
        $testContainer->setParameter('paths.base', $projectRoot); 
        $testContainer->setParameter('app.project_root', $projectRoot);
        $testContainer->setParameter('app.config_dir', $projectRoot . '/config');
        $testContainer->setParameter('app.fixtures_dir', $projectRoot . '/features/fixtures');
        $testContainer->setParameter('app.cache_dir', $projectRoot . '/var/cache');
        $testContainer->setParameter('app.logs_dir', $projectRoot . '/var/logs');
        $testContainer->setParameter('app.downloads_dir', $projectRoot . '/downloads');
        $testContainer->setParameter('kernel.debug', true);
        
        // Load core services first
        $testLoader = new YamlFileLoader($testContainer, new FileLocator(__DIR__ . '/config'));
        $testLoader->load('services/core.yml');
        
        try {
            // Load the service file to test
            $testLoader->load($serviceFile);
            
            // Try to compile
            $testContainer->compile();
            echo "  ✓ $serviceFile compiled successfully\n";
        } catch (Exception $e) {
            echo "  ✗ Error with $serviceFile: " . $e->getMessage() . "\n";
        }
    }
    
    // Finally, try to load all services together
    echo "\nTesting all services together...\n";
    
    // Initialize a new container
    $fullContainer = new ContainerBuilder();
    
    // Set required parameters
    $fullContainer->setParameter('kernel.project_dir', $projectRoot);
    $fullContainer->setParameter('paths.base', $projectRoot); 
    $fullContainer->setParameter('app.project_root', $projectRoot);
    $fullContainer->setParameter('app.config_dir', $projectRoot . '/config');
    $fullContainer->setParameter('app.fixtures_dir', $projectRoot . '/features/fixtures');
    $fullContainer->setParameter('app.cache_dir', $projectRoot . '/var/cache');
    $fullContainer->setParameter('app.logs_dir', $projectRoot . '/var/logs');
    $fullContainer->setParameter('app.downloads_dir', $projectRoot . '/downloads');
    $fullContainer->setParameter('kernel.debug', true);
    
    // Load all services
    $fullLoader = new YamlFileLoader($fullContainer, new FileLocator(__DIR__ . '/config'));
    $fullLoader->load('services.yml');
    
    try {
        // Try to compile
        $fullContainer->compile();
        echo "  ✓ All services compiled successfully\n";
    } catch (Exception $e) {
        echo "  ✗ Error with all services: " . $e->getMessage() . "\n";
        
        // Print detailed information about the error
        echo "\nDetailed error information:\n";
        echo "Exception type: " . get_class($e) . "\n";
        echo "File: " . $e->getFile() . " (Line: " . $e->getLine() . ")\n";
        
        // Check for specific error patterns
        if (preg_match('/binding is configured for an argument named "([^"]+)"/', $e->getMessage(), $matches)) {
            $argumentName = $matches[1];
            echo "Problematic argument: $argumentName\n";
            
            // Search for the argument in service files
            $serviceFiles = glob(__DIR__ . '/config/services/*.yml');
            foreach ($serviceFiles as $file) {
                $content = file_get_contents($file);
                if (strpos($content, $argumentName) !== false) {
                    echo "Found in file: " . basename($file) . "\n";
                    
                    // Extract the relevant lines
                    $lines = explode("\n", $content);
                    foreach ($lines as $lineNumber => $line) {
                        if (strpos($line, $argumentName) !== false) {
                            echo "  Line " . ($lineNumber + 1) . ": " . trim($line) . "\n";
                        }
                    }
                }
            }
        }
    }
} catch (Exception $e) {
    echo "ERROR: " . get_class($e) . "\n";
    echo "Message: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " (Line: " . $e->getLine() . ")\n";
}
