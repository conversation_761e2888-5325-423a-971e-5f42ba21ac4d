{"mcpServers": {"github.com/pashpashpash/mcp-server-asana": {"autoApprove": ["asana_list_workspaces"], "disabled": false, "timeout": 60, "command": "node", "args": ["C:\\Users\\<USER>\\OneDrive\\Documents\\Cline\\MCP\\mcp-server-asana\\dist\\index.js"], "env": {"ASANA_ACCESS_TOKEN": "********************************************************************"}, "transportType": "stdio"}, "github.com/magnitudedev/magnitude/tree/main/packages/magnitude-mcp": {"autoApprove": [], "disabled": false, "command": "npx", "args": ["magnitude-mcp"], "transportType": "stdio"}}}