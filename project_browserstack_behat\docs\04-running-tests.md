# 04 Running Tests

## Local Execution

1. Ensure Docker (optional) or PHP CLI is available.
2. Install dependencies:
   ```bash
   composer install
   ```
3. Load environment:
   ```bash
   cp .env.example .env
   # edit .env as needed
   ```
4. Run all tests:
   ```bash
   vendor/bin/behat --strict
   ```

By default, tests run against BrowserStack (Mink driver from profile `default`).

## BrowserStack‑only Execution

To explicitly use BrowserStack profile and format reports:

```bash
vendor/bin/behat --profile=browserstack --format=pretty,junit --out=report.xml
```

- Screenshots for failures are saved in `screenshots/`
- Logs are captured in `logs/`
- JUnit report in `report.xml`

## Tags & Suites

- Run specific tags:
  ```bash
  vendor/bin/behat --tags="@smoke or @critical"
  ```
- Run a particular suite defined in `behat.yml`:
  ```bash
  vendor/bin/behat --suite=default
  ```

Next: see CI/CD integration in [05 CI Setup](05-ci-setup.md). 