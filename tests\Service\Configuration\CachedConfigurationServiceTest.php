<?php

namespace Tests\Service\Configuration;

use App\Service\Cache\CacheServiceInterface;
use App\Service\Configuration\CachedConfigurationService;
use App\Service\Configuration\ConfigurationServiceInterface;
use PHPUnit\Framework\TestCase;

class CachedConfigurationServiceTest extends TestCase
{
    private $innerService;
    private $cacheService;
    private $cachedService;

    protected function setUp(): void
    {
        $this->innerService = $this->createMock(ConfigurationServiceInterface::class);
        $this->cacheService = $this->createMock(CacheServiceInterface::class);
        $this->cachedService = new CachedConfigurationService($this->innerService, $this->cacheService);
    }

    public function testGetEnvironmentConfig(): void
    {
        // Test with cache miss
        $this->cacheService->expects($this->once())
            ->method('has')
            ->with('env_config_test_key')
            ->willReturn(false);

        $this->innerService->expects($this->once())
            ->method('getEnvironmentConfig')
            ->with('test_key')
            ->willReturn('test_value');

        $this->cacheService->expects($this->once())
            ->method('set')
            ->with('env_config_test_key', 'test_value', 60)
            ->willReturn(true);

        $result = $this->cachedService->getEnvironmentConfig('test_key');
        $this->assertEquals('test_value', $result);

        // Test with cache hit
        $this->cacheService = $this->createMock(CacheServiceInterface::class);
        $this->innerService = $this->createMock(ConfigurationServiceInterface::class);
        $this->cachedService = new CachedConfigurationService($this->innerService, $this->cacheService);

        $this->cacheService->expects($this->once())
            ->method('has')
            ->with('env_config_test_key')
            ->willReturn(true);

        $this->cacheService->expects($this->once())
            ->method('get')
            ->with('env_config_test_key')
            ->willReturn('cached_value');

        $this->innerService->expects($this->never())
            ->method('getEnvironmentConfig');

        $result = $this->cachedService->getEnvironmentConfig('test_key');
        $this->assertEquals('cached_value', $result);
    }

    public function testGetBrandConfig(): void
    {
        // Test with cache miss
        $this->cacheService->expects($this->once())
            ->method('has')
            ->with('brand_config_test_key')
            ->willReturn(false);

        $this->innerService->expects($this->once())
            ->method('getBrandConfig')
            ->with('test_key')
            ->willReturn('test_value');

        $this->cacheService->expects($this->once())
            ->method('set')
            ->with('brand_config_test_key', 'test_value', 60)
            ->willReturn(true);

        $result = $this->cachedService->getBrandConfig('test_key');
        $this->assertEquals('test_value', $result);

        // Test with cache hit
        $this->cacheService = $this->createMock(CacheServiceInterface::class);
        $this->innerService = $this->createMock(ConfigurationServiceInterface::class);
        $this->cachedService = new CachedConfigurationService($this->innerService, $this->cacheService);

        $this->cacheService->expects($this->once())
            ->method('has')
            ->with('brand_config_test_key')
            ->willReturn(true);

        $this->cacheService->expects($this->once())
            ->method('get')
            ->with('brand_config_test_key')
            ->willReturn('cached_value');

        $this->innerService->expects($this->never())
            ->method('getBrandConfig');

        $result = $this->cachedService->getBrandConfig('test_key');
        $this->assertEquals('cached_value', $result);
    }

    public function testGetCurrentBrand(): void
    {
        $this->innerService->expects($this->once())
            ->method('getCurrentBrand')
            ->willReturn('aeons');

        $result = $this->cachedService->getCurrentBrand();
        $this->assertEquals('aeons', $result);
    }

    public function testSetBrand(): void
    {
        $this->innerService->expects($this->once())
            ->method('setBrand')
            ->with('new_brand');

        $this->cacheService->expects($this->once())
            ->method('clear')
            ->willReturn(true);

        $this->cachedService->setBrand('new_brand');
    }

    public function testGetCurrentEnvironment(): void
    {
        $this->innerService->expects($this->once())
            ->method('getCurrentEnvironment')
            ->willReturn('stage');

        $result = $this->cachedService->getCurrentEnvironment();
        $this->assertEquals('stage', $result);
    }

    public function testSetEnvironment(): void
    {
        $this->innerService->expects($this->once())
            ->method('setEnvironment')
            ->with('new_env');

        $this->cacheService->expects($this->once())
            ->method('clear')
            ->willReturn(true);

        $this->cachedService->setEnvironment('new_env');
    }
}
