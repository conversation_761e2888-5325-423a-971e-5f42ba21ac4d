<?php

function loadConfig()
{
    // Try tool-specific .env first
    $toolEnv = __DIR__ . '/.env';
    $rootEnv = __DIR__ . '/../../../.env';

    if (file_exists($toolEnv)) {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    } else {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../..');
    }

    $dotenv->load();
    return [
        'webdriver_host' => getenv('WEBDRIVER_HOST') ?: 'http://localhost:4444/wd/hub',
        'browser_name' => getenv('BROWSER_NAME') ?: 'chrome',
        'base_url' => getenv('TEST_BASE_URL') ?: 'https://aeonstest.info'
    ];
} 