test_users:
  default:
    email: "<EMAIL>"
    first_name: "<PERSON>"
    last_name: "<PERSON>"
    phone: "1234567890"
    address: "789 Oak St"
    city: "Manchester"
    postcode: "M1 1AA"
    country: "GB"

product_options:
  ancient_roots:
    flavors:
      classic:
        name: "Classic"
        description: "Our best-seller"
      lemon:
        name: "Lemon"
        description: "Zesty and fresh"
      truffle:
        name: "Truffle"
        description: "Earthy and rich"

payment_methods:
  stripe_valid:
    card_number: "****************"
    expiry: "12/26"
    cvc: "123"
  stripe_valid_3dsecure:
    card_number: "****************"
    expiry: "12/26"
    cvc: "123"
  stripe_expired:
    card_number: "****************"
    expiry: "12/20"
    cvc: "123"
  stripe_invalid:
    card_number: "****************"
    expiry: "12/26"
    cvc: "123"
  paypal_sandbox:
    username: "<EMAIL>"
    password: "Ll<9/%n:"
  paypal_invalid:
    username: "invalid_username"
    password: "invalid_password"

shipping_methods:
  UK:
    method: "Domestic tracked"
    cost: "2.95"
  US:
    method: "International tracked"
    cost: "9.99"
  EU:
    method: "European tracked"
    cost: "5.99"
funnel_configurations:
  default:
    shipping_threshold: 100.00
    free_shipping_regions: ["GB", "US"]
  
  variants:
    one_time:
      medium:
        variant_id: 72
    subscription:
      medium:
        variant_id: 73

funnel_items:
  total_harmony_basic:
    entry:
      url: "total-harmony-funnel"
      product: "total_harmony"
      quantity: "medium"
      purchase_type: "one_time"
    upsell:
      url: "total-harmony-upsell"
      product: "ancient_roots"
      quantity: "minimum"
      purchase_type: "one_time"
  ancient_roots_small:
    entry:
      url: "ancient-roots-small"
      product: "ancient_roots"
      quantity: "minimum"
      purchase_type: "one_time"
    upsell:
      url: "ancient-roots-golden-harvest"
      product: "golden_harvest"
      quantity: "minimum"
      purchase_type: "one_time"

  natures_gift_basic:
    entry:
      url: "natures-gift-basic"
      product: "natures_gift_bone_broth"
      quantity: "minimum"
      purchase_type: "one_time"
    upsell:
      url: "natures-gift-golden-harvest"
      product: "golden_harvest"
      quantity: "minimum"
      purchase_type: "one_time"
