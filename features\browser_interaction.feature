Feature: Browser Interaction Verification
  As a test framework developer
  I want to verify that browser interactions work correctly
  So that I can ensure the framework works at the browser interaction layer

  Background:
    Given I load brand configuration
    And I load product data

  @browser @smoke
  Scenario: Basic navigation
    Given I am on the homepage
    When I navigate to the product page
    Then I should be on the product page
    When I navigate back
    Then I should be on the homepage
    When I refresh the page
    Then I should still be on the homepage

  @browser @smoke
  Scenario: Element interaction
    Given I am on the product page
    When I select the "medium" quantity option
    Then the "medium" quantity option should be selected
    When I select "One-Time Purchase"
    Then "One-Time Purchase" should be selected
    When I add the product to the cart
    Then I should be on the cart page
    And the cart should contain 1 item

  @browser @smoke
  Scenario: Element verification
    Given I am on the product page
    Then I should see the product name
    And I should see the product price
    And I should see the product description
    And I should see the quantity options
    And I should see the purchase type options

  @browser @smoke
  Scenario: Wait operations
    Given I am on the product page
    When I add the product to the cart
    Then I should be on the cart page
    When I proceed to checkout
    Then I should be on the checkout page
    And I should see the shipping form

  @browser @smoke
  Scenario: Screenshot capture
    Given I am on the product page
    When I take a screenshot named "product_page"
    Then the screenshot should be saved successfully
