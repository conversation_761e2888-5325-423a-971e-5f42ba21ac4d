services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: true
  # Base contexts
  App\Context\Base\ServiceAwareContext:
    abstract: true
    arguments:
      $container: '@service_container'

  App\Context\Base\BaseContext:
    abstract: true
    parent: App\Context\Base\ServiceAwareContext

  # Context service definitions with Behat-friendly service IDs
  behat.context.feature:
    class: App\Context\FeatureContext
    public: true
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
    tags: [ 'context.service' ]

  behat.context.brand:
    class: App\Context\BrandContext
    public: true
    arguments:
      $container: '@service_container'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
    tags: [ 'context.service' ]

  behat.context.product:
    class: App\Context\ProductContext
    public: true
    arguments:
      $container: '@service_container'
      $pageFactory: '@App\Service\Page\PageFactoryInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
    tags: [ 'context.service' ]

  behat.context.cart:
    class: App\Context\CartContext
    public: true
    arguments:
      $container: '@service_container'
      $pageFactory: '@App\Service\Page\PageFactoryInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
    tags: [ 'context.service' ]

  behat.context.checkout:
    class: App\Context\CheckoutContext
    public: true
    arguments:
      $container: '@service_container'
      $pageFactory: '@App\Service\Page\PageFactoryInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
      $validationService: '@App\Service\Validation\ValidationServiceInterface'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
    tags: [ 'context.service' ]

  behat.context.payment:
    class: App\Context\PaymentContext
    public: true
    arguments:
      $container: '@service_container'
      $pageFactory: '@App\Service\Page\PageFactoryInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
    tags: [ 'context.service' ]

  behat.context.test_data:
    class: App\Context\TestDataContext
    public: true
    arguments:
      $container: '@service_container'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
    tags: [ 'context.service' ]

  behat.context.email:
    class: App\Context\EmailContext
    public: true
    arguments:
      $container: '@service_container'
    tags: [ 'context.service' ]

  behat.context.sales_funnel:
    class: App\Context\SalesFunnelContext
    public: true
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
    tags: [ 'context.service' ]

  behat.context.upsell:
    class: App\Context\UpsellContext
    public: true
    arguments:
      $container: '@service_container'
    tags: [ 'context.service' ]

  behat.context.admin_command:
    class: App\Context\AdminCommandContext
    public: true
    arguments:
      $container: '@service_container'
    tags: [ 'context.service' ]

  behat.context.validation:
    class: App\Context\ValidationContext
    public: true
    arguments:
      $container: '@service_container'
      $validationService: '@App\Service\Validation\ValidationServiceInterface'
    tags: [ 'context.service' ]

  behat.context.ssh:
    class: App\Context\SSHContext
    public: true
    arguments:
      $container: '@service_container'
    tags: [ 'context.service' ]

  behat.context.abandoned_cart:
    class: App\Context\AbandonedCartContext
    public: true
    arguments:
      $container: '@service_container'
    tags: [ 'context.service' ]

  behat.context.database:
    class: App\Context\DatabaseContext
    public: true
    arguments:
      $container: '@service_container'
    tags: [ 'context.service' ]

  behat.context.browser:
    class: App\Context\BrowserContext
    public: true
    arguments:
      $container: '@service_container'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
      $pageFactory: '@App\Service\Page\PageFactoryInterface'
    tags: [ 'context.service' ]

  behat.context.validate_services:
    class: App\Context\ValidateServicesContext
    public: true
    arguments:
      $container: '@service_container'
      $stateService: '@App\Service\State\SharedStateServiceInterface'
      $pageFactory: '@App\Service\Page\PageFactoryInterface'
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
    tags: [ 'context.service' ]

  # Auto-register all context classes
  App\Context\:
    resource: '../../src/Context/*'
    exclude: '../../src/Context/{Base}/*'
    tags: [ 'context.service' ]
