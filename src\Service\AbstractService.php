<?php

namespace App\Service;

use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

/**
 * Base class for all services
 */
abstract class AbstractService
{
    protected LoggerInterface $logger;

    /**
     * Constructor
     *
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?LoggerInterface $logger = null)
    {
        $this->logger = $logger ?? new NullLogger();
    }

    /**
     * Log an informational message
     *
     * @param string $message Message to log
     * @param array $context Context data
     * @return void
     */
    protected function logInfo(string $message, array $context = []): void
    {
        $this->logger->info($message, $context);
    }

    /**
     * Log a warning message
     *
     * @param string $message Message to log
     * @param array $context Context data
     * @return void
     */
    protected function logWarning(string $message, array $context = []): void
    {
        $this->logger->warning($message, $context);
    }

    /**
     * Log an error message
     *
     * @param string $message Message to log
     * @param \Throwable|null $exception Exception that occurred
     * @param array $context Context data
     * @return void
     */
    protected function logError(string $message, ?\Throwable $exception = null, array $context = []): void
    {
        if ($exception) {
            $context['exception'] = $exception;
            $context['trace'] = $exception->getTraceAsString();
        }

        $this->logger->error($message, $context);
    }
}
