# Panther service configuration
services:
  # Default configuration for all services
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Browser Service - using BrowserStack
  # Changed from PantherBrowserService to CachedBrowserService as it's more compatible
  App\Service\Browser\BrowserServiceInterface:
    class: App\Service\Browser\CachedBrowserService
    arguments:
      $session: '@mink.session'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: true

  # Legacy service alias for backward compatibility
  browser.service:
    alias: App\Service\Browser\BrowserServiceInterface
    public: true
