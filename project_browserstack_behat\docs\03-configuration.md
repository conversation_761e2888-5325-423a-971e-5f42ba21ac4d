# 03 Configuration

## Environment Variables

Copy `.env.example` to `.env` and provide your own values:

```dotenv
TEST_BASE_URL=https://your-app.com
TEST_ENV=test
BROWSERSTACK_USERNAME=xyz123
BROWSERSTACK_ACCESS_KEY=abc456
# Additional: MAILTRAP_TOKEN, SSH_KEY, etc.
```

Behat and <PERSON><PERSON> will load these variables automatically via Dotenv.

## behat.yml Profiles

The root `behat.yml` defines two profiles:

### `default`

- Runs using <PERSON><PERSON>'s `selenium2` session pointing at BrowserStack by default.
- Ideal for local or CI runs when BrowserStack tests.

### `browserstack`

- Extends `default` but can override tags, formatter, etc.
- Example invocation:
  ```bash
  vendor/bin/behat --profile=browserstack --tags="@high-priority"
  ```

#### Key Mink settings in `behat.yml`

```yaml
Behat\MinkExtension:
  base_url: "%TEST_BASE_URL%"
  default_session: selenium2
  javascript_session: browser_stack
  browser_stack:
    username: "%BROWSERSTACK_USERNAME%"
    access_key: "%BROWSERSTACK_ACCESS_KEY%"
    browser: chrome
    os: Windows
    os_version: 10
    capabilities:
      browserstack.debug: true
      browserstack.tunnel: false
```

Other extensions:

- **Symfony2Extension** bootstraps the container.
- **PageObjectExtension** wires up page objects.

Next: see how to run tests locally and in CI in [04 Running Tests](04-running-tests.md). 