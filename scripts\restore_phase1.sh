#!/bin/bash

# Set error handling
set -e
set -o pipefail

# Check if backup directory is provided
if [ -z "$1" ]; then
    echo "Error: Backup directory not provided"
    echo "Usage: $0 <backup_directory>"
    exit 1
fi

BACKUP_DIR="$1"

# Verify backup directory exists
if [ ! -d "${BACKUP_DIR}" ]; then
    echo "Error: Backup directory ${BACKUP_DIR} not found"
    exit 1
fi

# Verify backup structure
required_dirs=("page_objects/original" "config" "documentation" "metadata")
for dir in "${required_dirs[@]}"; do
    if [ ! -d "${BACKUP_DIR}/${dir}" ]; then
        echo "Error: Required directory ${dir} not found in backup"
        exit 1
    fi
done

# Create temporary directory for safety
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: ${TEMP_DIR}"

# Function to cleanup on exit
cleanup() {
    echo "Cleaning up temporary directory..."
    rm -rf "${TEMP_DIR}"
}
trap cleanup EXIT

# Verify backup integrity
echo "Verifying backup integrity..."
if [ -f "${BACKUP_DIR}/metadata/backup_info.json" ]; then
    cp "${BACKUP_DIR}/metadata/backup_info.json" "${TEMP_DIR}/"
else
    echo "Warning: backup_info.json not found"
fi

# Restore page objects
echo "Restoring page objects..."
if [ -d "${BACKUP_DIR}/page_objects/original" ]; then
    mkdir -p features/bootstrap/Page
    cp -r "${BACKUP_DIR}/page_objects/original/"* features/bootstrap/Page/
else
    echo "Error: Original page objects not found in backup"
    exit 1
fi

# Restore configuration
echo "Restoring configuration files..."
if [ -f "${BACKUP_DIR}/config/behat.yml" ]; then
    cp "${BACKUP_DIR}/config/behat.yml" ./
else
    echo "Warning: behat.yml not found in backup"
fi

if [ -f "${BACKUP_DIR}/config/composer.json" ]; then
    cp "${BACKUP_DIR}/config/composer.json" ./
else
    echo "Warning: composer.json not found in backup"
fi

# Restore documentation
echo "Restoring documentation..."
if [ -f "${BACKUP_DIR}/documentation/page_object_rules.md" ]; then
    mkdir -p docs
    cp "${BACKUP_DIR}/documentation/page_object_rules.md" docs/
else
    echo "Warning: page_object_rules.md not found in backup"
fi

if [ -f "${BACKUP_DIR}/documentation/page_object_extennsion_instructions.md" ]; then
    mkdir -p docs
    cp "${BACKUP_DIR}/documentation/page_object_extennsion_instructions.md" docs/
else
    echo "Warning: page_object_extennsion_instructions.md not found in backup"
fi

# Reinstall dependencies
echo "Reinstalling dependencies..."
if command -v composer &> /dev/null; then
    composer install
else
    echo "Warning: Composer not found, skipping dependency installation"
fi

# Verify restoration
echo "Verifying restoration..."
if [ -f "vendor/bin/behat" ]; then
    vendor/bin/behat --tags=@smoke || echo "Warning: Smoke tests failed"
else
    echo "Warning: Behat not found, skipping verification"
fi

echo "Restoration completed"
echo "Please verify the restored files and run tests manually" 