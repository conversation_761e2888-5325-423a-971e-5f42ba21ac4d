# Context Migration Plan

## Phase 1: Core Infrastructure Setup

1. Create Core Framework Components:
```php
// Core/ConfigurationManager.php
class ConfigurationManager
{
    private static ?self $instance = null;
    private array $brandConfigs = [];
    private array $environmentConfigs = [];

    public static function getInstance(): self {...}
    public function getBrandConfig(string $brand, string $environment): array {...}
    private function loadBrandConfig(string $brand, string $environment): array {...}
}

// Core/TestDataRegistry.php
class TestDataRegistry
{
    private static ?self $instance = null;
    private array $testData = [];
    private array $cache = [];

    public static function getInstance(): self {...}
    public function registerTestData(string $type, string $key, array $data): void {...}
    public function getTestData(string $type, string $key): array {...}
}

// Core/DataValidator.php
class DataValidator
{
    public function validateProductData(array $data): void {...}
    public function validateTestUser(array $data): void {...}
    public function validateShippingData(array $data): void {...}
}
```

2. Update Directory Structure:
```bash
mkdir -p features/bootstrap/Core
mkdir -p features/bootstrap/fixtures/config
mv features/bootstrap/fixtures/brands/*/config.yml features/bootstrap/fixtures/config/
```

## Phase 2: Context Refactoring

1. Update BrandContext:
```php
class BrandContext extends BaseContext
{
    private ConfigurationManager $configManager;
    private array $brandConfig;

    public function __construct(string $brand = null, string $environment = null)
    {
        parent::__construct($brand, $environment);
        $this->configManager = ConfigurationManager::getInstance();
        $this->brandConfig = $this->configManager->getBrandConfig($this->brand, $this->environment);
    }

    // Brand-specific methods...
}
```

2. Update TestDataContext:
```php
class TestDataContext extends BaseContext
{
    private TestDataRegistry $registry;
    private DataValidator $validator;

    public function __construct(?string $brand = null, ?string $environment = null)
    {
        parent::__construct($brand, $environment);
        $this->registry = TestDataRegistry::getInstance();
        $this->validator = new DataValidator();
        $this->loadTestData();
    }

    // Test data access methods...
}
```

## Phase 3: Step Definition Migration

When migrating step definitions between contexts, follow these guidelines:

1. **Brand Configuration Access**:
```php
// Before
$brandConfig = $this->config['brand'];

// After
$brandConfig = $this->contextManager
    ->getContext('brand')
    ->getBrandConfig();
```

2. **Test Data Access**:
```php
// Before
$productData = $this->testData['products'][$productKey];

// After
$productData = $this->contextManager
    ->getContext('testData')
    ->getProductData($productKey);
```

3. **Shared Data Management**:
```php
// Before
$this->testData['current_product'] = $productData;

// After
$this->sharedContext->set('current_product', $productData);
```

4. **Data Validation**:
```php
// Before
if (!isset($data['required_field'])) {
    throw new RuntimeException('Missing required field');
}

// After
$this->validator->validateProductData($data);
```

## Phase 4: Testing and Validation

1. Run existing test suite to verify functionality
2. Add new tests for core components
3. Verify error handling
4. Check performance impact

## Phase 5: Documentation Update

1. Update main documentation
2. Create migration guides
3. Add code examples
4. Document best practices

## Timeline

1. Phase 1: 
   - Create core classes
   - Setup directory structure
   - Initial configuration

2. Phase 2:
   - Refactor BrandContext
   - Refactor TestDataContext
   - Update dependencies

3. Phase 3:
   - Migrate step definitions
   - Update data access
   - Add validation

4. Phase 4: 
   - Run tests
   - Fix issues
   - Performance optimization

5. Phase 5:
   - Update documentation
   - Create guides


## Risk Mitigation

1. Create backup of existing codebase
2. Implement changes in feature branches
3. Run tests after each phase
4. Document all changes
5. Create rollback plan

## Success Criteria

1. All tests pass
2. Improved data management
3. Better error handling
4. Updated documentation
5. Improved maintainability

## Post-Migration Tasks

1. Code cleanup
2. Performance optimization
3. Documentation review
4. Team training
5. Monitoring and feedback 