<?php

namespace Features\Bootstrap\Context;

use Features\Bootstrap\Core\ConfigurationManager;
use Features\Bootstrap\Core\DataValidator;
use Features\Bootstrap\Core\TestDataRegistry;
use App\Service\State\SharedStateServiceInterface;
use RuntimeException;
use Throwable;

/**
 * Manages test data loading and access
 */
class TestDataContext extends BaseContext
{
    private ConfigurationManager $configManager;
    private TestDataRegistry $testDataRegistry;
    private DataValidator $dataValidator;
    private array $currentTestData = [];

    /**
     * @param ConfigurationManager $configManager Configuration manager instance
     * @param TestDataRegistry $testDataRegistry Test data registry instance
     * @param DataValidator $dataValidator Data validator instance
     * @param SharedDataContext $sharedData Shared data context instance
     */
    public function __construct(
        ConfigurationManager $configManager,
        TestDataRegistry     $testDataRegistry,
        DataValidator        $dataValidator,
        SharedDataContext    $sharedData
    )
    {
        parent::__construct();
        $this->configManager = $configManager;
        $this->testDataRegistry = $testDataRegistry;
        $this->dataValidator = $dataValidator;
        $this->sharedData = $sharedData;
        $this->logInfo('TestDataContext initialized');
    }

    /**
     * @Given I load test data for product :productKey
     * @sets product.data
     */
    public function iLoadTestDataForProduct(string $productKey): void
    {
        try {
            $data = $this->getProductData($productKey);
            $this->stateService->set('product.data', $data);
            $this->stateService->set('product.key', $productKey);
            $this->logInfo(sprintf('Loaded and shared test data for product: %s', $productKey));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to load test data for product: %s', $productKey), $e);
            throw $e;
        }
    }

    /**
     * @Given I load test data for user :userKey
     * @sets user.data
     */
    public function iLoadTestDataForUser(string $userKey): void
    {
        try {
            $brand = $this->configManager->getCurrentBrand();
            $data = $this->testDataRegistry->loadTestData($brand, 'users', $userKey);

            // Validate user data structure
            $this->dataValidator->validateUserData($data);

            $this->testDataRegistry->registerData('current_user', $data);
            $this->currentTestData['user'] = $data;

            // Store in shared data for other contexts
            $this->stateService->set('user.data', $data);
            $this->stateService->set('user.key', $userKey);

            $this->logInfo(sprintf('Loaded and shared test data for user: %s', $userKey));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to load test data for user: %s', $userKey), $e);
            throw $e;
        }
    }

    /**
     * @Given I load shipping data
     * @sets shipping.data
     */
    public function iLoadShippingData(): void
    {
        try {
            $brand = $this->configManager->getCurrentBrand();
            $data = $this->testDataRegistry->loadTestData($brand, 'shipping');

            // Validate shipping data structure
            $this->dataValidator->validateShippingData($data);

            $this->testDataRegistry->registerData('shipping', $data);
            $this->currentTestData['shipping'] = $data;

            // Store in shared data for other contexts
            $this->stateService->set('shipping.data', $data);

            $this->logInfo('Loaded and shared shipping data');
        } catch (Throwable $e) {
            $this->logError('Failed to load shipping data', $e);
            throw $e;
        }
    }

    /**
     * @Given I load payment methods
     * @sets payment.methods
     */
    public function iLoadPaymentMethods(): void
    {
        try {
            $brand = $this->configManager->getCurrentBrand();
            $data = $this->testDataRegistry->loadTestData($brand, 'payment_methods');

            // Validate payment methods data structure
            $this->dataValidator->validatePaymentMethodsData($data);

            $this->testDataRegistry->registerData('payment_methods', $data);
            $this->currentTestData['payment_methods'] = $data;

            // Store in shared data for other contexts
            $this->stateService->set('payment.methods', $data);

            $this->logInfo('Loaded and shared payment methods data');
        } catch (Throwable $e) {
            $this->logError('Failed to load payment methods data', $e);
            throw $e;
        }
    }

    /**
     * Gets PayPal credentials for the specified type
     *
     * @param string $type The credential type to load
     * @return array Credential data
     * @throws RuntimeException When credentials cannot be loaded
     */
    public function getPayPalCredentials(string $type): array
    {
        try {
            $brand = $this->configManager->getCurrentBrand();

            try {
                $paymentMethods = $this->getPaymentMethods();

                foreach ($paymentMethods as $method) {
                    if ($method['type'] === 'paypal' && isset($method['credentials'][$type])) {
                        $credentials = $method['credentials'][$type];
                        $this->logInfo(sprintf('Found PayPal credentials of type "%s" in payment methods', $type));
                        return $credentials;
                    }
                }

                // If not found in payment methods, try loading directly
                $credentials = $this->testDataRegistry->loadTestData($brand, 'credentials', $type);
                $this->logInfo(sprintf('Loaded PayPal credentials of type "%s" from test data', $type));
                return $credentials;

            } catch (RuntimeException $e) {
                // Create mock credentials as fallback
                $this->logInfo(sprintf('Using fallback mock credentials for PayPal type "%s"', $type));
                return [
                    'username' => '<EMAIL>',
                    'password' => 'test_password'
                ];
            }
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get PayPal credentials for type "%s"', $type), $e);
            throw new RuntimeException(
                sprintf('Failed to get PayPal credentials for type "%s": %s', $type, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets current product data
     *
     * @return array Product data
     * @throws RuntimeException When no product data is loaded
     */
    public function getCurrentProduct(): array
    {
        // First try shared data
        $productData = $this->stateService->get('product.data');
        if ($productData) {
            return $productData;
        }

        // Then try local cache
        if (!isset($this->currentTestData['product'])) {
            $this->logError('No product data currently loaded');
            throw new RuntimeException('No product data currently loaded');
        }
        return $this->currentTestData['product'];
    }

    /**
     * Gets current user data
     *
     * @return array User data
     * @throws RuntimeException When no user data is loaded
     */
    public function getCurrentUser(): array
    {
        // First try shared data
        $userData = $this->stateService->get('user.data');
        if ($userData) {
            return $userData;
        }

        // Then try local cache
        if (!isset($this->currentTestData['user'])) {
            $this->logError('No user data currently loaded');
            throw new RuntimeException('No user data currently loaded');
        }
        return $this->currentTestData['user'];
    }

    /**
     * Gets shipping data
     *
     * @return array Shipping data
     * @throws RuntimeException When no shipping data is loaded
     */
    public function getShippingData(): array
    {
        // First try shared data
        $shippingData = $this->stateService->get('shipping.data');
        if ($shippingData) {
            return $shippingData;
        }

        // Then try local cache
        if (!isset($this->currentTestData['shipping'])) {
            $this->logError('No shipping data currently loaded');
            throw new RuntimeException('No shipping data currently loaded');
        }
        return $this->currentTestData['shipping'];
    }

    /**
     * Gets payment methods
     *
     * @return array Payment methods
     * @throws RuntimeException When no payment methods are loaded
     */
    public function getPaymentMethods(): array
    {
        // First try shared data
        $paymentMethods = $this->stateService->get('payment.methods');
        if ($paymentMethods) {
            return $paymentMethods;
        }

        // Then try local cache
        if (!isset($this->currentTestData['payment_methods'])) {
            $this->logError('No payment methods currently loaded');
            throw new RuntimeException('No payment methods currently loaded');
        }
        return $this->currentTestData['payment_methods'];
    }

    /**
     * Gets test data by key
     *
     * @param string $key Data key
     * @return mixed Test data
     * @throws RuntimeException When test data cannot be loaded
     */
    public function getTestData(string $key)
    {
        try {
            return $this->testDataRegistry->getData($key);
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to get test data for key "%s"', $key), $e);
            throw new RuntimeException(
                sprintf('Failed to get test data for key "%s": %s', $key, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Checks if test data exists
     *
     * @param string $key Data key
     * @return bool Whether the data exists
     */
    public function hasTestData(string $key): bool
    {
        return $this->testDataRegistry->hasData($key);
    }

    /**
     * @BeforeScenario
     */
    public function clearTestData(): void
    {
        try {
            $this->logInfo('Clearing test data');
            $this->currentTestData = [];
            $this->testDataRegistry->clearData();
            $this->logInfo('Test data cleared');
        } catch (Throwable $e) {
            $this->logError('Error clearing test data', $e);
            // Don't throw here as it's a cleanup method
        }
    }

    /**
     * Gets product data by key
     *
     * @param string $productKey Product key to load
     * @return array Product data
     * @throws RuntimeException When product data cannot be loaded
     */
    public function getProductData(string $productKey): array
    {
        try {
            $this->logInfo(sprintf('Loading product data for key: %s', $productKey));

            $brand = $this->configManager->getCurrentBrand();
            $this->logInfo(sprintf('Current brand: %s', $brand));

            $data = $this->testDataRegistry->loadTestData($brand, 'products', $productKey);
            $this->logInfo('Successfully loaded product data from registry');

            if (!is_array($data)) {
                $this->logError(sprintf('Invalid product data type: %s', gettype($data)));
                throw new RuntimeException(
                    sprintf('Invalid product data for key "%s". Expected array, got %s', $productKey, gettype($data))
                );
            }

            // Validate product data structure
            $this->dataValidator->validateProductData($data);

            $this->logInfo('Registering product data');
            $this->testDataRegistry->registerData('current_product', $data);
            $this->currentTestData['product'] = $data;
            $this->logInfo('Product data registered successfully');

            return $data;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to load product data: %s', $e->getMessage()), $e);
            throw new RuntimeException(
                sprintf('Failed to load product data for key "%s": %s', $productKey, $e->getMessage()),
                0,
                $e
            );
        }
    }
} 