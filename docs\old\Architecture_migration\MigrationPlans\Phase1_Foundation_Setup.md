# Phase 1: Foundation Setup

## Service-Oriented Test Architecture Migration

**Version:** 1.0  
**Last Updated:** 2025-04-10  
**Author:** AI Assistant

## Table of Contents

1. [Overview](#1-overview)
2. [Current Structure Analysis](#2-current-structure-analysis)
3. [Service Container Configuration](#3-service-container-configuration)
4. [Interface Definitions](#4-interface-definitions)
5. [Service Base Classes](#5-service-base-classes)
6. [Directory Structure Changes](#6-directory-structure-changes)
7. [Implementation Plan](#7-implementation-plan)
8. [Testing Strategy](#8-testing-strategy)
9. [Rollback Plan](#9-rollback-plan)

---

## 1. Overview

Phase 1 establishes the foundation for the Service-Oriented Test Architecture by setting up the core infrastructure
components. This phase focuses on creating the service container configuration, defining interfaces for core services,
and implementing base classes that will be used throughout the migration.

### 1.1 Objectives

- Set up a properly configured service container
- Define interfaces for all core services
- Create base classes for services and contexts
- Establish the directory structure for the new architecture
- Ensure backward compatibility with existing code

### 1.2 Timeline

- Estimated duration: 1 week
- Dependencies: None (this is the first phase)

---

## 2. Current Structure Analysis

### 2.1 Current Directory Structure

The current project has the following key directories and files:

```
project_root/
├── features/
│   ├── bootstrap/
│   │   ├── Context/              # Behat context classes
│   │   │   ├── BaseContext.php   # Base context class
│   │   │   ├── BrandContext.php  # Brand-specific context
│   │   │   └── ...
│   │   ├── Core/                 # Core framework classes
│   │   │   ├── ConfigurationManager.php
│   │   │   ├── TestDataRegistry.php
│   │   │   └── DataValidator.php
│   │   ├── Page/                 # Page object classes
│   │   │   ├── BasePage.php
│   │   │   └── ...
│   │   ├── SharedDataContext.php # Singleton for shared data
│   │   └── FeatureContext.php    # Main feature context
│   └── *.feature                 # Feature files
├── config/
│   ├── services.yml              # Service container configuration
│   └── brands/                   # Brand-specific configurations
├── bin/
│   └── run-tests.php             # Test runner script
└── behat.yml                     # Behat configuration
```

### 2.2 Current Service Container Configuration

The current `config/services.yml` file uses Symfony's Dependency Injection Container with the following structure:

```yaml
parameters:
  config_base_path: '%paths.base%/config'
  fixture_base_path: '%paths.base%/features/fixtures'
  env(TEST_BRAND): 'aeons'
  env(TEST_ENV): 'stage'
  env(SSH_HOST): ''
  env(SSH_USER): ''

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Core Services
  Features\Bootstrap\Core\ConfigurationManager:
    public: true
    arguments:
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
      $configBasePath: '%config_base_path%'

  Features\Bootstrap\Core\TestDataRegistry:
    public: true
    arguments:
      $fixtureBasePath: '%fixture_base_path%'

  Features\Bootstrap\Core\DataValidator:
    public: true

  # Shared Data Context (singleton)
  Features\Bootstrap\SharedDataContext:
    public: true
    # Currently using singleton pattern

  # Auto-configure Contexts
  Features\Bootstrap\Context\:
    resource: '../features/bootstrap/Context/*'
    public: true
    arguments:
      $sharedData: '@Features\Bootstrap\SharedDataContext'
    tags: [ 'context.service' ]
    exclude:
      - '../features/bootstrap/Context/BaseContext.php'
```

### 2.3 Current Dependency Management

The current architecture has several issues with dependency management:

1. **Singleton Pattern**: `SharedDataContext` uses a singleton pattern, making it difficult to test and creating tight
   coupling.
2. **Mixed Dependency Injection**: Some classes use constructor injection while others use static methods.
3. **Inconsistent Service Access**: Some contexts access services directly, others through a context manager.

---

## 3. Service Container Configuration

### 3.1 New Directory Structure

We'll create a new directory structure for the service-oriented architecture:

```
project_root/
├── src/                          # New source directory
│   ├── Service/                  # Service implementations
│   │   ├── Configuration/        # Configuration services
│   │   ├── Data/                 # Data services
│   │   ├── Validation/           # Validation services
│   │   ├── Browser/              # Browser services
│   │   └── State/                # State management services
│   ├── Context/                  # Context implementations
│   │   ├── Base/                 # Base context classes
│   │   └── ...                   # Specific contexts
│   └── Page/                     # Page object implementations
│       ├── Base/                 # Base page classes
│       └── ...                   # Specific pages
└── config/
    └── services/                 # Service configuration files
        ├── core.yml              # Core service definitions
        ├── contexts.yml          # Context service definitions
        └── pages.yml             # Page object service definitions
```

### 3.2 New Service Container Configuration

Create a new `config/services.yml` file with the following structure:

```yaml
parameters:
  # Global parameters
  app.project_root: '%paths.base%'
  app.config_dir: '%app.project_root%/config'
  app.fixtures_dir: '%app.project_root%/features/fixtures'

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false
    bind:
      $projectRoot: '%app.project_root%'
      $configDir: '%app.config_dir%'
      $fixturesDir: '%app.fixtures_dir%'

  # Import service definitions
  _imports:
    - { resource: 'services/*.yml' }

  # Auto-register services
  App\Service\:
    resource: '../src/Service/*'
    exclude: '../src/Service/{Base,Exception}/*'
```

### 3.3 Core Services Configuration

Create a new `config/services/core.yml` file:

```yaml
services:
  # Configuration Service
  App\Service\Configuration\ConfigurationServiceInterface:
    alias: App\Service\Configuration\ConfigurationService

  App\Service\Configuration\ConfigurationService:
    arguments:
      $configDir: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
    public: true

  # Test Data Service
  App\Service\Data\TestDataServiceInterface:
    alias: App\Service\Data\TestDataService

  App\Service\Data\TestDataService:
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
    public: true

  # Shared State Service
  App\Service\State\SharedStateServiceInterface:
    alias: App\Service\State\SharedStateService

  App\Service\State\SharedStateService:
    public: true
    tags:
      - { name: kernel.event_subscriber }

  # Validation Service
  App\Service\Validation\ValidationServiceInterface:
    alias: App\Service\Validation\ValidationService

  App\Service\Validation\ValidationService:
    public: true

  # Browser Service
  App\Service\Browser\BrowserServiceInterface:
    alias: App\Service\Browser\BrowserService

  App\Service\Browser\BrowserService:
    arguments:
      $screenshotsDir: '%app.project_root%/screenshots'
    public: true
```

### 3.4 Backward Compatibility Layer

Create a new `config/services/compatibility.yml` file to maintain backward compatibility:

```yaml
services:
  # Legacy service aliases
  Features\Bootstrap\Core\ConfigurationManager:
    alias: App\Service\Configuration\ConfigurationService
    public: true

  Features\Bootstrap\Core\TestDataRegistry:
    alias: App\Service\Data\TestDataService
    public: true

  Features\Bootstrap\Core\DataValidator:
    alias: App\Service\Validation\ValidationService
    public: true

  # SharedDataContext adapter
  App\Compatibility\SharedDataContextAdapter:
    arguments:
      $stateService: '@App\Service\State\SharedStateService'
    public: true

  Features\Bootstrap\SharedDataContext:
    factory: [ '@App\Compatibility\SharedDataContextAdapter', 'getInstance' ]
    public: true
```

---

## 4. Interface Definitions

### 4.1 Configuration Service Interface

Create `src/Service/Configuration/ConfigurationServiceInterface.php`:

```php
<?php

namespace App\Service\Configuration;

interface ConfigurationServiceInterface
{
    /**
     * Get a brand configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     */
    public function getBrandConfig(string $key);
    
    /**
     * Get an environment configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     */
    public function getEnvironmentConfig(string $key);
    
    /**
     * Get the current brand
     *
     * @return string Current brand
     */
    public function getCurrentBrand(): string;
    
    /**
     * Get the current environment
     *
     * @return string Current environment
     */
    public function getCurrentEnvironment(): string;
    
    /**
     * Set the current brand
     *
     * @param string $brand Brand to set
     * @return void
     */
    public function setBrand(string $brand): void;
    
    /**
     * Set the current environment
     *
     * @param string $environment Environment to set
     * @return void
     */
    public function setEnvironment(string $environment): void;
}
```

### 4.2 Test Data Service Interface

Create `src/Service/Data/TestDataServiceInterface.php`:

```php
<?php

namespace App\Service\Data;

interface TestDataServiceInterface
{
    /**
     * Load test data for a brand
     *
     * @param string $brand Brand identifier
     * @param string $type Data type (products, users, etc)
     * @param string|null $key Optional specific data key
     * @return array The loaded test data
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array;
    
    /**
     * Register data for later use
     *
     * @param string $key Data key
     * @param array $data Data to register
     * @return void
     */
    public function registerData(string $key, array $data): void;
    
    /**
     * Get registered data
     *
     * @param string $key Data key
     * @return mixed Registered data or null if not found
     */
    public function getData(string $key);
    
    /**
     * Check if data is registered
     *
     * @param string $key Data key
     * @return bool True if data is registered, false otherwise
     */
    public function hasData(string $key): bool;
}
```

### 4.3 Shared State Service Interface

Create `src/Service/State/SharedStateServiceInterface.php`:

```php
<?php

namespace App\Service\State;

interface SharedStateServiceInterface
{
    /**
     * Set a value in the shared state
     *
     * @param string $key The key
     * @param mixed $value The value
     * @param string $scope The scope (scenario, feature, global)
     * @return void
     */
    public function set(string $key, $value, string $scope = 'scenario'): void;
    
    /**
     * Get a value from the shared state
     *
     * @param string $key The key
     * @param string $scope The scope (scenario, feature, global)
     * @return mixed The value or null if not found
     */
    public function get(string $key, string $scope = 'scenario');
    
    /**
     * Check if a key exists in the shared state
     *
     * @param string $key The key
     * @param string $scope The scope (scenario, feature, global)
     * @return bool True if the key exists, false otherwise
     */
    public function has(string $key, string $scope = 'scenario'): bool;
    
    /**
     * Get all values in a scope
     *
     * @param string $scope The scope (scenario, feature, global)
     * @return array All values in the scope
     */
    public function getAll(string $scope = 'scenario'): array;
    
    /**
     * Reset a scope
     *
     * @param string $scope The scope to reset (scenario, feature, global)
     * @return void
     */
    public function reset(string $scope = 'scenario'): void;
}
```

### 4.4 Validation Service Interface

Create `src/Service/Validation/ValidationServiceInterface.php`:

```php
<?php

namespace App\Service\Validation;

interface ValidationServiceInterface
{
    /**
     * Validate product data structure
     *
     * @param array $data Product data to validate
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateProductData(array $data): void;
    
    /**
     * Validate user data structure
     *
     * @param array $data User data to validate
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateUserData(array $data): void;
    
    /**
     * Validate shipping data structure
     *
     * @param array $data Shipping data to validate
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateShippingData(array $data): void;
    
    /**
     * Validate data against a schema
     *
     * @param array $data Data to validate
     * @param string $schema Schema name
     * @return void
     * @throws \RuntimeException When validation fails
     */
    public function validateSchema(array $data, string $schema): void;
}
```

### 4.5 Browser Service Interface

Create `src/Service/Browser/BrowserServiceInterface.php`:

```php
<?php

namespace App\Service\Browser;

use Behat\Mink\Session;

interface BrowserServiceInterface
{
    /**
     * Get the Mink session
     *
     * @return Session
     */
    public function getSession(): Session;
    
    /**
     * Visit a URL
     *
     * @param string $url URL to visit
     * @return void
     */
    public function visit(string $url): void;
    
    /**
     * Take a screenshot
     *
     * @param string|null $name Screenshot name
     * @return string Path to the screenshot
     */
    public function takeScreenshot(string $name = null): string;
    
    /**
     * Wait for an element to be present
     *
     * @param string $selector Element selector
     * @param int $timeout Timeout in seconds
     * @return void
     * @throws \RuntimeException When element is not found within timeout
     */
    public function waitForElement(string $selector, int $timeout = 30): void;
    
    /**
     * Execute JavaScript
     *
     * @param string $script JavaScript to execute
     * @return mixed Result of the script
     */
    public function executeScript(string $script);
}
```

---

## 5. Service Base Classes

### 5.1 Abstract Service

Create `src/Service/AbstractService.php`:

```php
<?php

namespace App\Service;

use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

/**
 * Base class for all services
 */
abstract class AbstractService
{
    protected LoggerInterface $logger;
    
    /**
     * Constructor
     *
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?LoggerInterface $logger = null)
    {
        $this->logger = $logger ?? new NullLogger();
    }
    
    /**
     * Log an informational message
     *
     * @param string $message Message to log
     * @param array $context Context data
     * @return void
     */
    protected function logInfo(string $message, array $context = []): void
    {
        $this->logger->info($message, $context);
    }
    
    /**
     * Log an error message
     *
     * @param string $message Message to log
     * @param \Throwable|null $exception Exception that occurred
     * @param array $context Context data
     * @return void
     */
    protected function logError(string $message, ?\Throwable $exception = null, array $context = []): void
    {
        if ($exception) {
            $context['exception'] = $exception;
            $context['trace'] = $exception->getTraceAsString();
        }
        
        $this->logger->error($message, $context);
    }
}
```

### 5.2 Service-Aware Context

Create `src/Context/Base/ServiceAwareContext.php`:

```php
<?php

namespace App\Context\Base;

use Behat\Behat\Context\Context;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Base class for contexts that need access to services
 */
abstract class ServiceAwareContext implements Context
{
    protected ContainerInterface $container;
    
    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     */
    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }
    
    /**
     * Get a service from the container
     *
     * @param string $id Service ID
     * @return object Service instance
     * @throws \RuntimeException When service is not found
     */
    protected function getService(string $id)
    {
        if (!$this->container->has($id)) {
            throw new \RuntimeException(sprintf('Service "%s" not found in container', $id));
        }
        
        return $this->container->get($id);
    }
}
```

### 5.3 Base Context

Create `src/Context/Base/BaseContext.php`:

```php
<?php

namespace App\Context\Base;

use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Behat\Behat\Context\Context;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Base class for all contexts
 */
abstract class BaseContext extends ServiceAwareContext implements Context
{
    /**
     * Get the configuration service
     *
     * @return ConfigurationServiceInterface
     */
    protected function getConfigService(): ConfigurationServiceInterface
    {
        return $this->getService(ConfigurationServiceInterface::class);
    }
    
    /**
     * Get the test data service
     *
     * @return TestDataServiceInterface
     */
    protected function getTestDataService(): TestDataServiceInterface
    {
        return $this->getService(TestDataServiceInterface::class);
    }
    
    /**
     * Get the shared state service
     *
     * @return SharedStateServiceInterface
     */
    protected function getSharedStateService(): SharedStateServiceInterface
    {
        return $this->getService(SharedStateServiceInterface::class);
    }
    
    /**
     * Get the browser service
     *
     * @return BrowserServiceInterface
     */
    protected function getBrowserService(): BrowserServiceInterface
    {
        return $this->getService(BrowserServiceInterface::class);
    }
    
    /**
     * Get the validation service
     *
     * @return ValidationServiceInterface
     */
    protected function getValidationService(): ValidationServiceInterface
    {
        return $this->getService(ValidationServiceInterface::class);
    }
    
    /**
     * Log an informational message
     *
     * @param string $message Message to log
     * @param array $context Context data
     * @return void
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if ($this->container->has(LoggerInterface::class)) {
            $this->container->get(LoggerInterface::class)->info($message, $context);
        } else {
            error_log("[INFO] $message");
        }
    }
    
    /**
     * Log an error message
     *
     * @param string $message Message to log
     * @param \Throwable|null $exception Exception that occurred
     * @param array $context Context data
     * @return void
     */
    protected function logError(string $message, ?\Throwable $exception = null, array $context = []): void
    {
        if ($exception) {
            $context['exception'] = $exception;
            $context['trace'] = $exception->getTraceAsString();
        }
        
        if ($this->container->has(LoggerInterface::class)) {
            $this->container->get(LoggerInterface::class)->error($message, $context);
        } else {
            $logMessage = "[ERROR] $message";
            if ($exception) {
                $logMessage .= " - " . $exception->getMessage();
            }
            error_log($logMessage);
        }
    }
}
```

### 5.4 Backward Compatibility Adapter

Create `src/Compatibility/SharedDataContextAdapter.php`:

```php
<?php

namespace App\Compatibility;

use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Context\Context;

/**
 * Adapter to provide backward compatibility with SharedDataContext
 */
class SharedDataContextAdapter implements Context
{
    private static ?self $instance = null;
    private SharedStateServiceInterface $stateService;
    
    /**
     * Constructor
     *
     * @param SharedStateServiceInterface $stateService Shared state service
     */
    public function __construct(SharedStateServiceInterface $stateService)
    {
        $this->stateService = $stateService;
        self::$instance = $this;
    }
    
    /**
     * Get the singleton instance
     *
     * @return self
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            throw new \RuntimeException('SharedDataContextAdapter not initialized');
        }
        return self::$instance;
    }
    
    /**
     * @BeforeScenario
     */
    public function cleanup(): void
    {
        $this->stateService->reset('scenario');
    }
    
    /**
     * Set a value
     *
     * @param string $key The key
     * @param mixed $value The value
     * @return void
     */
    public function set(string $key, $value): void
    {
        $this->stateService->set($key, $value);
    }
    
    /**
     * Set multiple values
     *
     * @param array $data Key-value pairs
     * @return void
     */
    public function setMultiple(array $data): void
    {
        foreach ($data as $key => $value) {
            $this->stateService->set($key, $value);
        }
    }
    
    /**
     * Get a value
     *
     * @param string $key The key
     * @return mixed The value or null if not found
     */
    public function get(string $key)
    {
        return $this->stateService->get($key);
    }
    
    /**
     * Get multiple values
     *
     * @param array $keys The keys
     * @return array Key-value pairs
     */
    public function getMultiple(array $keys): array
    {
        $result = [];
        foreach ($keys as $key) {
            $result[$key] = $this->stateService->get($key);
        }
        return $result;
    }
    
    /**
     * Get all values
     *
     * @return array All values
     */
    public function getAll(): array
    {
        return $this->stateService->getAll();
    }
    
    /**
     * Set a funnel-specific value
     *
     * @param string $key The key
     * @param mixed $value The value
     * @return void
     */
    public function setFunnelData(string $key, $value): void
    {
        $this->stateService->set("funnel.$key", $value);
    }
    
    /**
     * Get a funnel-specific value
     *
     * @param string $key The key
     * @return mixed The value or null if not found
     */
    public function getFunnelData(string $key)
    {
        return $this->stateService->get("funnel.$key");
    }
    
    /**
     * Set the current funnel configuration
     *
     * @param array $funnelConfig The funnel configuration
     * @return void
     */
    public function setCurrentFunnel(array $funnelConfig): void
    {
        $this->setFunnelData('current_funnel', $funnelConfig);
    }
    
    /**
     * Get the current funnel configuration
     *
     * @return array|null The funnel configuration or null if not found
     */
    public function getCurrentFunnel(): ?array
    {
        return $this->getFunnelData('current_funnel');
    }
}
```

---

## 6. Directory Structure Changes

### 6.1 Create New Directories

Create the following directory structure:

```
project_root/
├── src/
│   ├── Service/
│   │   ├── Configuration/
│   │   ├── Data/
│   │   ├── Validation/
│   │   ├── Browser/
│   │   └── State/
│   ├── Context/
│   │   └── Base/
│   ├── Page/
│   │   └── Base/
│   └── Compatibility/
└── config/
    └── services/
```

### 6.2 Update Composer Autoloading

Update `composer.json` to include the new namespace:

```json
{
  "autoload": {
    "psr-4": {
      "Features\\Bootstrap\\": "features/bootstrap/",
      "App\\": "src/"
    }
  }
}
```

### 6.3 Update Behat Configuration

Update `behat.yml` to include the new service container configuration:

```yaml
default:
  autoload:
    '': '%paths.base%/features/bootstrap'
    'App\\': '%paths.base%/src'
  suites:
    default:
      paths:
        - features
      filters:
        tags: "~@ignore"
  extensions:
    FriendsOfBehat\ServiceContainerExtension\Extension:
      imports:
        - config/services.yml
    # ... other extensions
```

---

## 7. Implementation Plan

### 7.1 Step-by-Step Implementation

1. Create the new directory structure
2. Update composer.json and run `composer dump-autoload`
3. Create the service interfaces
4. Create the base classes
5. Create the service container configuration
6. Create the backward compatibility adapter
7. Update behat.yml

### 7.2 Code Mapping

Here's how the current code maps to the new architecture:

| Current Code                                   | New Code                                                                              |
|------------------------------------------------|---------------------------------------------------------------------------------------|
| `Features\Bootstrap\Core\ConfigurationManager` | `App\Service\Configuration\ConfigurationService`                                      |
| `Features\Bootstrap\Core\TestDataRegistry`     | `App\Service\Data\TestDataService`                                                    |
| `Features\Bootstrap\Core\DataValidator`        | `App\Service\Validation\ValidationService`                                            |
| `Features\Bootstrap\SharedDataContext`         | `App\Service\State\SharedStateService` + `App\Compatibility\SharedDataContextAdapter` |
| `Features\Bootstrap\Context\BaseContext`       | `App\Context\Base\BaseContext`                                                        |

### 7.3 Execution Order

1. Create the interfaces and base classes first
2. Create the service container configuration
3. Create the backward compatibility adapter
4. Update the autoloading configuration
5. Update the Behat configuration

---

## 8. Testing Strategy

### 8.1 Unit Tests

Create unit tests for:

- Service interfaces
- Base classes
- Backward compatibility adapter

### 8.2 Integration Tests

Create integration tests for:

- Service container configuration
- Service interactions

### 8.3 Verification Tests

Run existing Behat tests to verify that the new architecture works with existing code.

---

## 9. Rollback Plan

### 9.1 Rollback Triggers

- Critical functionality broken
- Significant performance degradation
- Incompatibility with existing tests

### 9.2 Rollback Process

1. Revert composer.json changes
2. Revert behat.yml changes
3. Remove the new directories
4. Run `composer dump-autoload`
5. Verify that the original code works
