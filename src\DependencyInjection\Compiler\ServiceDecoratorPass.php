<?php

namespace App\DependencyInjection\Compiler;

use App\Factory\ServiceDecoratorFactory;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

/**
 * Compiler pass to handle service decoration and tagging
 */
class ServiceDecoratorPass implements CompilerPassInterface
{
    private const SERVICE_TYPES = [
        'app.cacheable.configuration' => 'configuration',
        'app.cacheable.browser' => 'browser',
        'app.cacheable.api' => 'api',
        'app.cacheable.data' => 'data',
    ];

    public function process(ContainerBuilder $container): void
    {
        if (!$container->has(ServiceDecoratorFactory::class)) {
            return;
        }

        foreach (self::SERVICE_TYPES as $tag => $type) {
            $this->processTaggedServices($container, $tag, $type);
        }
    }

    private function processTaggedServices(ContainerBuilder $container, string $tag, string $type): void
    {
        $taggedServices = $container->findTaggedServiceIds($tag);
        foreach ($taggedServices as $id => $tags) {
            $definition = $container->getDefinition($id);

            // Skip if already decorated
            if ($definition->isAbstract() || $definition->isSynthetic()) {
                continue;
            }

            // Create decorated service
            $decoratedId = $id . '.inner';
            $container->setDefinition($decoratedId, $definition);

            // Create decorator using factory
            $container->register($id, get_class($definition->getClass()))
                ->setFactory([new Reference(ServiceDecoratorFactory::class), 'createCachedDecorator'])
                ->setArguments([
                    new Reference($decoratedId),
                    $type,
                    $id, // Use service ID as cache prefix
                ])
                ->setPublic($definition->isPublic());
        }
    }
} 