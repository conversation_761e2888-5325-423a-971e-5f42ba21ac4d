# Main service configuration
parameters:
  app.project_root: '%paths.base%'
  app.config_dir: '%paths.base%/config'
  app.fixtures_dir: '%paths.base%/features/fixtures'
  app.cache_dir: '%paths.base%/var/cache'
  app.logs_dir: '%paths.base%/var/logs'
  app.downloads_dir: '%paths.base%/downloads'
  app.screenshots_dir: '%paths.base%/screenshots'

imports:
  - { resource: 'parameters.yml' }
  - { resource: 'services/core.yml' }
  - { resource: 'services/contexts.yml' }
  - { resource: 'services/pages.yml' }
  - { resource: 'services/api.yml' }
  - { resource: 'services/app_factory.yml' }
  - { resource: 'services/critical.yml' }
  - { resource: 'services/browserstack.yml' }

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Path resolver service
  App\Service\Path\PathResolverInterface:
    alias: App\Service\Path\PathResolver
    public: true

  App\Service\Path\PathResolver:
    arguments:
      $projectRoot: '%app.project_root%'
    public: true

  # Parameter validator service
  App\Service\Configuration\ParameterValidatorInterface:
    alias: App\Service\Configuration\ParameterValidator
    public: true

  App\Service\Configuration\ParameterValidator:
    public: true