<?php

namespace App\Service\Configuration;

interface ConfigurationServiceInterface
{
    /**
     * Get a brand configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     */
    public function getBrandConfig(string $key);

    /**
     * Get an environment configuration value
     *
     * @param string $key Configuration key
     * @return mixed Configuration value
     */
    public function getEnvironmentConfig(string $key);

    /**
     * Get the current brand
     *
     * @return string Current brand
     */
    public function getCurrentBrand(): string;

    /**
     * Get the current environment
     *
     * @return string Current environment
     */
    public function getCurrentEnvironment(): string;

    /**
     * Set the current brand
     *
     * @param string $brand Brand to set
     * @return void
     */
    public function setBrand(string $brand): void;

    /**
     * Set the current environment
     *
     * @param string $environment Environment to set
     * @return void
     */
    public function setEnvironment(string $environment): void;
}
