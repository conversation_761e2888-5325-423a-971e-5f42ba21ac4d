[{"description": "Behat test automation framework for e-commerce with focus on product content, purchase flows and data-driven testing", "rules": [{"name": "Documentation Consultation", "actions": ["Start by reviewing docs/README.md for documentation structure and guidelines", "Consult relevant documentation files before making changes:", "- page_object_rules.md for page object patterns", "- context_rules.md for context implementation", "- step_definition_best_practices.md for step definitions", "- ai_test_framework_docs.md for architecture overview", "Verify changes align with documented patterns and best practices"]}, {"name": "Task Analysis and Planning", "actions": ["Break down tasks into subtasks, considering main objectives, challenges, and dependencies.", "Analyze existing codebase to identify relevant classes, methods, and modules.", "Propose a solution with a general action plan, outlining subtasks and reasoning."]}, {"name": "Implementation", "actions": ["Use first principles thinking, best practices, design patterns, and coding standards.", "Ensure code aligns with existing architecture and is maintainable.", "Include clear comments and documentation.", "Prioritize readability and security.", "Verify code functionality and compatibility with the existing codebase.", "In step definitions use regex syntax for step names. Use parameters for dynamic data. Example: /^I subscribe to product$/"]}, {"name": "Verification and Testing", "actions": ["Implement robust error handling and validation.", "Use shared data contexts for cross-step data sharing.", "Integrate new features seamlessly into existing test flows."]}, {"name": "Communication", "actions": ["Provide clear and precise instructions for changes, file by file.", "Be proactive in seeking clarification if needed."]}, {"name": "Test Data Management", "actions": ["Store all test data in YAML fixtures under features/fixtures/brands/{brand}/", "Use hierarchical product data structure with base info and extended content sections", "Maintain environment-specific configurations in config/brands/{brand}/{env}.yml", "Implement data validation in TestDataContext for required fields", "Cache loaded test data for performance optimization"]}, {"name": "Shared State Management", "actions": ["Use SharedDataContext singleton for cross-step data sharing", "Clean shared state before each scenario using @BeforeScenario hook", "Store dynamic test data (quantities, selections) for verification steps", "Implement getters/setters for atomic data access", "Document shared data dependencies in feature files"]}, {"name": "Page Object <PERSON>", "actions": ["Implement comprehensive page object methods for all UI interactions", "Add wait conditions for dynamic elements and AJAX updates", "Extract complex element interactions into helper methods", "Normalize data formats (prices, product names) for consistent comparison", "Document element selectors and their relationships"]}, {"name": "Price Verification", "actions": ["Verify one-time and subscription pricing separately", "Handle different quantity options (minimum, medium, maximum)", "Compare prices after applying discounts/coupons", "Validate price updates after quantity changes", "Document price calculation rules"]}, {"name": "Content Verification", "actions": ["Verify product basic info (name, subtitle, description)", "Check product badges and certifications", "Validate FAQ content and expandable sections", "Compare subscription benefits and trust badges", "Verify related products information"]}, {"name": "Purchase Flow Testing", "actions": ["Test different purchase types (one-time, subscription)", "Validate quantity changes and price updates", "Handle coupon applications and discounts", "Verify shipping methods and costs", "Test payment scenarios (valid, expired cards)"]}, {"name": "Erro<PERSON>", "actions": ["Add specific exceptions for missing/invalid test data", "Handle element not found scenarios gracefully", "Implement retry logic for flaky elements", "Add detailed error messages with context", "Log test execution details for debugging"]}, {"name": "Test Organization", "actions": ["Group related scenarios with descriptive tags", "Use Background for common setup steps", "Maintain consistent step definitions", "Document test dependencies and prerequisites", "Follow Given-When-Then pattern strictly"]}, {"name": "Documentation Maintenance", "actions": ["After significant changes, update relevant documentation files:", "- Update patterns and examples if introducing new approaches", "- Add cross-references between affected documentation files", "- Maintain documentation DRY principle - avoid duplication", "- Update version history in docs/README.md", "- Ensure all code examples follow current best practices", "Document any new:", "- Page object patterns in page_object_rules.md", "- Context patterns in context_rules.md", "- Step definition patterns in step_definition_best_practices.md", "- Framework changes in ai_test_framework_docs.md"]}]}]