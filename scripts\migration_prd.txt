<context>
# Overview  
This document defines the plan to migrate the MalabergTest automated test framework from the legacy architecture to the new modular architecture aligned with the BrowserStack + Behat Scaffold. The migration will adopt modern Symfony DI via the behat/symfony extension, unify service interfaces, decouple contexts, standardize page objects using the FriendsOfBehat PageObjectExtension, and streamline configuration and CI pipelines.

# Core Features  
- Dependency Injection Container Refactor  
  - What: Replace the deprecated Symfony2Extension with behat/symfony extension and centralize service definitions in YAML.  
  - Why: Achieve autowiring, autoconfigure, and full Symfony 6 support.  
  - How: Update `features/bootstrap/bootstrap.php`, `config/services/*.yml` and use the new `service_container` injection.  

- Service Layer Modularization  
  - What: Define core services (BrowserService, SharedStateService, ConfigurationService, TestDataService, CacheService and others).  
  - Why: Enforce single responsibility and loose coupling across contexts and page objects.  
  - How: Implement interfaces in `src/Service/*`, register them in `config/services/core.yml`.  

- Context Layer Decomposition  
  - What: Break down monolithic contexts into domain-specific contexts (ProductContext, CartContext, UpsellContext, etc.).  
  - Why: Improve maintainability and readability of step definitions.  
  - How: Relocate classes under `src/Context`, inject required services via constructor and tag as context services.  

- Page Object Standardization  
  - What: Migrate all UI interactions into page objects under `src/Page`, implement `BasePageInterface`.  
  - Why: Encapsulate element locators and actions for reuse and clarity.  
  - How: Refactor existing code in `src/Page/*`, update contexts to call page methods and implement `verifyPage()`.  

- Configuration and Profiles Simplification  
  - What: Consolidate `behat.yml` profiles for local, CI, and BrowserStack into a single file using `%env()%`.  
  - Why: Reduce configuration drift and duplication between environments.  
  
- CI/CD Pipeline Alignment  
  - What: Update GitLab CI to use a Docker-based test image (`project-behat-test:latest`) and new commands.  
  - Why: Speed up job startup, ensure environment reproducibility, and cache dependencies effectively.  

- Documentation and Training  
  - What: Revise existing docs (`01-overview.md` through `07-compatibility.md`) to describe the new architecture and guidelines.  
  - Why: Provide clear guidance for team adoption and future maintenance.  
</context>

<PRD>
# Technical Architecture  
- **Dependency Injection**: Use `behat/symfony` extension (v3+) for autowiring and autoconfigure. Bootstrap container in `features/bootstrap/bootstrap.php`.  
- **Service Layer**:  
  - `BrowserServiceInterface` → `BrowserService`  
  - `SharedStateServiceInterface` → `SharedStateService`  
  - `ConfigurationServiceInterface` → `ConfigurationService`  
  - `TestDataServiceInterface` → `TestDataService`  
  - `CacheServiceInterface` → `FilesystemCacheService`  
- **Context Layer**:  
  - Classes in `src/Context`, annotated with `context.service`.  
  - Inject services via constructor; remove legacy initializers and static calls.  
  - Reset shared state in `@BeforeScenario` hooks.  
- **Page Object Layer**:  
  - `BasePageInterface` and `BasePage` in `src/Page/Base`.  
  - Specialized page classes in `src/Page` implementing domain methods and `verifyPage()`.  
- **Configuration**:  
  - Service definitions in `config/services/*.yml`.  
  - `behat.yml` holds `default`, `ci`, and `browserstack` profiles using `%env()%`.  
- **CI/CD**:  
  - GitLab CI `.gitlab-ci.yml` uses Docker image `project-behat-test:latest`.  
  - Stages: `install`, `test:browserstack`, `report`; cache vendor and composer.  

# Development Roadmap  
1. Phase 1: Preparation  
   - Audit existing contexts, page objects, and services.  
   - Scaffold new `config/services` structure and sample YAMLs.  
   - Add `behat/symfony` to `composer.json`.  

2. Phase 2: DI Container Migration  
   - Remove `FriendsOfBehat\Symfony2Extension`.  
   - Configure `behat/symfony`; update bootstrap script.  
   - Validate container loading and service resolution.  

3. Phase 3: Service Layer Implementation  
   - Implement or update service interfaces in `src/Service`.  
   - Register services in `config/services/core.yml`.  
   - Write unit tests for core services (e.g., SharedStateService).  

4. Phase 4: Context Refactoring  
   - Inject services into contexts; tag services in YAML.  
   - Break large context files into granular contexts by domain.  

5. Phase 5: Page Object Refactoring  
   - Refactor UI logic into `src/Page` classes.  
   - Implement `BasePageInterface`; update contexts to use page methods.  

6. Phase 6: Configuration Cleanup  
   - Consolidate `behat.yml` profiles and remove legacy blocks.  
   - Update `.env.example`.  

7. Phase 7: CI Pipeline Update  
   - Modify `.gitlab-ci.yml` to use the Docker image.  
   - Verify caching, artifacts, and reports.  

8. Phase 8: Validation and Testing  
   - Execute full test suite locally and in CI.  
   - Fix flaky steps by adding explicit waits.  
   - Perform performance profiling and tune timeouts.  

9. Phase 9: Documentation and Handover  
   - Update `docs/` to reflect new architecture and migration steps.  
   - Host training sessions; finalize migration checklist.  

# Logical Dependency Chain  
1. Bootstrap & DI setup  
2. Core Service definitions  
3. Configuration & Test Data Service  
4. Shared State & PageFactory Service  
5. Context service refactoring  
6. Page object migration  
7. behat.yml & .env consolidation  
8. CI pipeline changes  
9. Documentation update  

# Risks and Mitigations  
- **Regression in Tests**: Run smoke tests after each phase; maintain a branch for migration.  
- **Learning Curve**: Provide code samples and training; document common patterns.  
- **Service Injection Errors**: Leverage autowire and validate container at bootstrap.  
- **Flaky Tests**: Standardize waits in page objects; capture screenshots on failures.  
- **Configuration Drift**: Keep `.env.example` and YAML under version control; review diffs.  

# Appendix  
- Base template: `scripts/example_prd.txt`.  
- Reference docs: `project_browserstack_behat/docs/01-07-*.md`.  
- Migration helper scripts: `scripts/backup_phase1.sh`, `scripts/restore_phase1.sh`.  
</PRD> 