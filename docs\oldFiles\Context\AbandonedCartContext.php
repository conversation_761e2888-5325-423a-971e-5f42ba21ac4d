<?php

namespace Features\Bootstrap\Context;

use ElementNotFoundException;
use Exception;
use Features\Bootstrap\Page\CartPage;
use Features\Bootstrap\Page\CheckoutPage;
use Features\Bootstrap\Page\HomePage;
use Features\Bootstrap\Page\ProductPage;
use App\Service\State\SharedStateServiceInterface;
use FriendsOfBehat\PageObjectExtension\Context\PageObjectContext;
use GuzzleHttp\Exception\GuzzleException;
use RuntimeException;

/**
 * Handles abandoned cart specific functionality
 */
class AbandonedCartContext extends PageObjectContext
{
    private ProductPage $productPage;
    private CartPage $cartPage;
    private CheckoutPage $checkoutPage;
    private HomePage $homePage;
    protected SharedStateServiceInterface $stateService;

    public function __construct(
        ProductPage $productPage,
        CartPage $cartPage,
        CheckoutPage $checkoutPage,
        HomePage $homePage
    )
    {
        parent::__construct();
        $this->productPage = $productPage;
        $this->cartPage = $cartPage;
        $this->checkoutPage = $checkoutPage;
        $this->homePage = $homePage;
        $this->sharedData = this->stateService;
    }

    /**
     * Gets the current product data or throws an exception
     *
     * @throws RuntimeException
     */
    private function getCurrentProductData(): array
    {
        $productData = $this->stateService->get('currentProduct');
        if (!$productData) {
            throw new RuntimeException('No product data loaded');
        }
        return $productData;
    }

    /**
     * Gets the user email or throws an exception
     *
     * @throws RuntimeException
     */
    private function getUserEmail(): string
    {
        $userEmail = $this->stateService->get('userEmail');
        if (!$userEmail) {
            throw new RuntimeException('No user email found in shared context');
        }
        return $userEmail;
    }

    /**
     * Gets the abandoned cart email or throws an exception
     *
     * @throws RuntimeException
     */
    private function getAbandonedCartEmail(): array
    {
        $email = $this->stateService->get('abandonedCartEmail');
        if (!$email) {
            throw new RuntimeException('No abandoned cart email found in shared context');
        }
        return $email;
    }

    /**
     * @Given /^I have an abandoned cart from "([^"]*)" hours ago$/
     * @throws RuntimeException
     */
    public function iHaveAbandonedCartFromHoursAgo(int $hours): void
    {
        try {
            $this->iHaveAnAbandonedCart();

            $orderId = $this->stateService->get('orderNumber');
            if (!$orderId) {
                throw new RuntimeException('No order number found in shared context');
            }

            $databaseContext = $this->contextManager->getContext(DatabaseContext::class);
            $databaseContext->updateOrderTimestamp($orderId, -$hours);
            
            $this->stateService->set('abandonedCartTime', time() - ($hours * 3600));
        } catch (Exception $e) {
            throw new RuntimeException(
                sprintf('Failed to create abandoned cart from %d hours ago: %s', $hours, $e->getMessage())
            );
        }
    }

    /**
     * @Given /^I have an abandoned cart$/
     * @throws RuntimeException
     */
    public function iHaveAnAbandonedCart(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            $navigationContext = $this->contextManager->getContext(NavigationContext::class);
            $cartContext = $this->contextManager->getContext(CartContext::class);
            $checkoutContext = $this->contextManager->getContext(CheckoutContext::class);

            // Navigate to product and add to cart
            $navigationContext->iAmOnTheProductPage();
            $this->productPage->selectQuantityOption('medium');
            $this->productPage->selectPurchaseType('One-Time Purchase');
            $this->productPage->addToCart();
            $this->waitForAjaxToComplete();

            // Proceed to checkout and abandon
            $cartContext->iProceedToCheckout();
            $checkoutContext->iFillInTheShippingInformationWith('default');
            $this->abandonCheckout();

            // Get and store order ID
            $databaseContext = $this->contextManager->getContext(DatabaseContext::class);
            $orderId = $databaseContext->getCurrentOrderId();
            $this->stateService->set('orderNumber', $orderId);
            $this->stateService->set('abandonedCartCreatedAt', time());

            // Wait for order status to update
            $this->waitForOrderStatusUpdate();
        } catch (Exception $e) {
            throw new RuntimeException('Failed to create abandoned cart: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I should receive an abandoned cart email with subject "([^"]*)"$/
     * @throws GuzzleException
     */
    public function iShouldReceiveAnAbandonedCartEmailWithSubject(string $subject): void
    {
        $userEmail = this->stateService->get('shippingInfo')['email'];
        if (!$userEmail) {
            throw new RuntimeException('No user email found in shared context');
        }

        // Wait for email with retries
        $maxRetries = 3;
        $email = null;
        while ($maxRetries > 0) {
            $email = $this->emailContext->getLatestOrderEmail($userEmail);
            if ($email && $email['subject'] === $subject) {
                break;
            }
            sleep(2);
            $maxRetries--;
        }

        if (!$email || $email['subject'] !== $subject) {
            throw new RuntimeException(
                sprintf('Expected email with subject "%s" not received', $subject)
            );
        }

        // Store email data for later steps
        this->stateService->set('abandonedCartEmail', $email);
    }

    /**
     * @When /^I do the abandon cart process for (\d+) times$/
     */
    public function iDoTheAbandonCartProcessForTimes(int $times): void
    {
        for ($i = 0; $i < $times; $i++) {
            // Add product to cart
            $this->productContext->addToCart();
            $this->waitForAjaxToComplete();

            // Proceed to checkout
            $this->cartContext->proceedToCheckout();
            $this->waitForAjaxToComplete();

            // Fill shipping info
            $userData = $this->testDataContext->getTestUser('default');
            $this->checkoutContext->fillInCheckoutForm($userData);
            $this->waitForAjaxToComplete();

            // Leave checkout
            $this->homePage->load();
            $this->waitForAjaxToComplete();

            // Wait a bit before next iteration
            if ($i < $times - 1) {
                sleep(2);
            }
        }
    }

    /**
     * @Then /^I should receive an abandoned cart email with a coupon code$/
     */
    public function iShouldReceiveAnAbandonedCartEmailWithACouponCode(): void
    {
        $userEmail = this->stateService->get('shippingInfo')['email'];
        if (!$userEmail) {
            throw new RuntimeException('No user email found in shared context');
        }

        // Wait for email with retries
        $maxRetries = 3;
        $email = null;
        while ($maxRetries > 0) {
            $email = $this->emailContext->getLatestOrderEmail($userEmail);
            if ($email && preg_match('/coupon code: ([A-Z0-9]+)/', $email['body'], $matches)) {
                break;
            }
            sleep(2);
            $maxRetries--;
        }

        if (!$email || !preg_match('/coupon code: ([A-Z0-9]+)/', $email['body'], $matches)) {
            throw new RuntimeException('Abandoned cart email with coupon code not received');
        }

        // Store email and coupon data for later steps
        this->stateService->setMultiple([
            'abandonedCartEmail' => $email,
            'couponCode' => $matches[1]
        ]);
    }


    /**
     * @When /^the abandoned cart cleanup job runs$/
     * @throws RuntimeException
     */
    public function theAbandonedCartCleanupJobRuns(): void
    {
        try {
            $databaseContext = $this->contextManager->getContext(DatabaseContext::class);
            $databaseContext->runCleanupJob();
            $this->stateService->set('cleanupJobRanAt', time());
        } catch (Exception $e) {
            throw new RuntimeException('Failed to run cleanup job: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I should receive an abandoned cart email$/
     * @throws RuntimeException
     */
    public function iShouldReceiveAnAbandonedCartEmail(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $emailContext = $this->contextManager->getContext(EmailContext::class);

            $email = $this->waitForAbandonedCartEmail($emailContext, $userEmail);
            if (!$email) {
                throw new RuntimeException('Abandoned cart email not received within timeout');
            }

            $this->stateService->set('abandonedCartEmail', $email);
            $this->stateService->set('emailReceivedAt', time());
        } catch (Exception $e) {
            throw new RuntimeException('Failed to verify abandoned cart email: ' . $e->getMessage());
        }
    }

    /**
     * @When /^I follow the recovery link in the email$/
     * @throws RuntimeException
     */
    public function iFollowRecoveryLinkInEmail(): void
    {
        try {
            $email = $this->getAbandonedCartEmail();
            $recoveryUrl = $this->extractRecoveryUrl($email);

            $this->homePage->getSession()->visit($recoveryUrl);
            $this->waitForAjaxToComplete();
            
            $this->stateService->set('recoveryAttemptedAt', time());
        } catch (Exception $e) {
            throw new RuntimeException('Failed to follow recovery link: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^my cart should be restored$/
     * @throws RuntimeException
     */
    public function myCartShouldBeRestored(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            if (!$this->cartPage->isProductDisplayed($productData['name'])) {
                throw new RuntimeException('Expected product not found in restored cart');
            }

            $quantity = $this->cartPage->getQuantity();
            if ($quantity !== 'medium') {
                throw new RuntimeException(
                    sprintf('Product quantity mismatch. Expected: medium, Found: %s', $quantity)
                );
            }

            $this->validateRestoredCart();
        } catch (ElementNotFoundException $e) {
            throw new RuntimeException('Failed to verify restored cart: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^the email should contain both subscription and one-time items$/
     * @throws RuntimeException
     */
    public function theEmailShouldContainBothSubscriptionAndOneTimeItems(): void
    {
        try {
            $email = $this->getAbandonedCartEmail();
            $this->validateEmailContent($email, [
                'Subscribe & Save' => 'subscription item',
                'One-Time Purchase' => 'one-time purchase item'
            ]);
        } catch (Exception $e) {
            throw new RuntimeException('Failed to verify email content: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^the email should mention the applied discount$/
     * @throws RuntimeException
     */
    public function theEmailShouldMentionTheAppliedDiscount(): void
    {
        try {
            $email = $this->getAbandonedCartEmail();
            $couponCode = $this->stateService->get('couponCode');
            if (!$couponCode) {
                throw new RuntimeException('No coupon code found in shared context');
            }

            if (!str_contains($email['body'], $couponCode)) {
                throw new RuntimeException(
                    sprintf('Email does not contain coupon code: %s', $couponCode)
                );
            }
        } catch (Exception $e) {
            throw new RuntimeException('Failed to verify discount in email: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I should receive a priority abandoned cart email within "([^"]*)" minutes$/
     * @throws RuntimeException
     */
    public function iShouldReceiveAPriorityAbandonedCartEmailWithinMinutes(int $minutes): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $emailContext = $this->contextManager->getContext(EmailContext::class);

            $email = $this->waitForPriorityEmail($emailContext, $userEmail, $minutes);
            if (!$email) {
                throw new RuntimeException(
                    sprintf('Priority abandoned cart email not received within %d minutes', $minutes)
                );
            }

            $this->stateService->set('priorityEmail', $email);
            $this->stateService->set('priorityEmailReceivedAt', time());
        } catch (Exception $e) {
            throw new RuntimeException('Failed to verify priority email: ' . $e->getMessage());
        }
    }

    /**
     * Waits for abandoned cart email with retries
     *
     * @throws RuntimeException
     */
    private function waitForAbandonedCartEmail($emailContext, string $userEmail, int $maxRetries = 3): ?array
    {
        while ($maxRetries > 0) {
            $email = $emailContext->getLatestOrderEmail($userEmail);
            if ($email && str_contains($email['subject'], 'You left something behind')) {
                return $email;
            }
            sleep(2);
            $maxRetries--;
        }
        return null;
    }

    /**
     * Waits for priority email with retries
     *
     * @throws RuntimeException
     */
    private function waitForPriorityEmail($emailContext, string $userEmail, int $minutes): ?array
    {
        $endTime = time() + ($minutes * 60);
        while (time() < $endTime) {
            $email = $emailContext->getLatestOrderEmail($userEmail);
            if ($email && str_contains($email['subject'], 'Priority: Complete Your High-Value Order')) {
                return $email;
            }
            sleep(20);
        }
        return null;
    }

    /**
     * Extracts recovery URL from email content
     *
     * @throws RuntimeException
     */
    private function extractRecoveryUrl(array $email): string
    {
        if (!preg_match('/<a[^>]*href="([^"]*)"[^>]*>.*?recover your cart.*?<\/a>/i', $email['body'], $matches)) {
            throw new RuntimeException('Recovery link not found in email');
        }
        return $matches[1];
    }

    /**
     * Validates restored cart state
     *
     * @throws RuntimeException
     */
    private function validateRestoredCart(): void
    {
        $cartContext = $this->contextManager->getContext(CartContext::class);
        $cartContext->validateCartState();

        $expectedItems = $this->stateService->get('abandonedCartItems');
        if ($expectedItems) {
            foreach ($expectedItems as $item) {
                if (!$this->cartPage->isProductDisplayed($item['name'])) {
                    throw new RuntimeException(
                        sprintf('Expected item "%s" not found in restored cart', $item['name'])
                    );
                }
            }
        }
    }

    /**
     * Validates email content against required phrases
     *
     * @throws RuntimeException
     */
    private function validateEmailContent(array $email, array $requiredPhrases): void
    {
        foreach ($requiredPhrases as $phrase => $description) {
            if (!str_contains($email['body'], $phrase)) {
                throw new RuntimeException(
                    sprintf('Email does not contain %s ("%s")', $description, $phrase)
                );
            }
        }
    }

    /**
     * Abandons the current checkout
     */
    private function abandonCheckout(): void
    {
        $this->stateService->set('checkoutAbandoned', true);
        $this->stateService->set('abandonedAt', time());
    }

    /**
     * Waits for order status to update
     */
    private function waitForOrderStatusUpdate(int $seconds = 2): void
    {
        sleep($seconds);
    }

    /**
     * Waits for all AJAX requests to complete
     *
     * @throws RuntimeException
     */
    private function waitForAjaxToComplete(int $timeout = 10000): void
    {
        try {
            $result = $this->getSession()->wait(
                $timeout,
                "return (typeof jQuery === 'undefined' || jQuery.active === 0);"
            );

            if (!$result) {
                throw new RuntimeException('Timeout waiting for AJAX requests to complete');
            }
        } catch (Exception $e) {
            throw new RuntimeException(
                sprintf('Error while waiting for AJAX: %s', $e->getMessage())
            );
        }
    }
} 