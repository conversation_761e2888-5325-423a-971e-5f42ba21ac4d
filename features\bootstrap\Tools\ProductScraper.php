<?php

namespace Features\Bootstrap\Tools;

use Symfony\Component\Yaml\Yaml;
use Symfony\Component\DomCrawler\Crawler;
use GuzzleHttp\Client;
use InvalidArgumentException;

class ProductScraper
{
    private Client $client;
    private string $baseUrl;
    private array $existingData;

    public function __construct(string $baseUrl)
    {
        $this->client = new Client([
            'verify' => false,
            'timeout' => 30,
            'headers' => [
                'User-Agent' => 'Aeons-Test-Automation/1.0'
            ]
        ]);
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * Scrapes product data from a given URL and updates the YAML file
     *
     * @param string $productUrl The product URL to scrape
     * @param string $brand The brand name (e.g., 'aeons')
     * @return array The scraped and processed product data
     */
    public function scrapeAndUpdate(string $productUrl, string $brand): array
    {
        $scrapedData = $this->scrapeProductData($productUrl);
        $this->validateScrapedData($scrapedData);
        $this->updateProductYaml($scrapedData, $brand);
        return $scrapedData;
    }

    /**
     * Scrapes product data from the given URL
     *
     * @param string $productUrl The product URL to scrape
     * @return array The scraped product data
     */
    private function scrapeProductData(string $productUrl): array
    {
        $response = $this->client->get($this->baseUrl . '/' . ltrim($productUrl, '/'));
        $html = (string) $response->getBody();
        $crawler = new Crawler($html);

        return [
            'name' => $this->extractText($crawler, '.content-grid .title'),
            'slug' => $this->extractSlugFromUrl($productUrl),
            'url_path' => ltrim($productUrl, '/'),
            'prices' => $this->extractPrices($crawler),
            'options' => $this->extractOptions($crawler),
            'meta' => $this->extractMeta($crawler),
            'content' => $this->extractContent($crawler),
            'funnel' => $this->extractFunnelData($crawler)
        ];
    }

    /**
     * Extracts prices from the product page
     */
    private function extractPrices(Crawler $crawler): array
    {
        $prices = ['one_time' => [], 'subscription' => []];
        
        // Extract one-time prices
        $crawler->filter('.purchase-option[data-variant-option-subscription="no"] .choose-item')
            ->each(function (Crawler $node) use (&$prices) {
                $quantity = $this->normalizeQuantityKey($node->filter('p')->first()->text());
                $priceText = $node->filter('.qty-price-per-item')->text();
                $prices['one_time'][$quantity] = $this->extractPriceValue($priceText);
            });

        // Extract subscription prices
        $crawler->filter('.purchase-option[data-variant-option-subscription="yes"] .choose-item')
            ->each(function (Crawler $node) use (&$prices) {
                $quantity = $this->normalizeQuantityKey($node->filter('p')->first()->text());
                $priceText = $node->filter('.qty-price-per-item')->text();
                $prices['subscription'][$quantity] = $this->extractPriceValue($priceText);
            });

        return $prices;
    }

    /**
     * Extracts product options (quantities, purchase types, etc.)
     */
    private function extractOptions(Crawler $crawler): array
    {
        return [
            'purchase_types' => $this->extractPurchaseTypes($crawler),
            'quantities' => $this->extractQuantities($crawler),
            'subscription_frequencies' => $this->extractFrequencies($crawler),
            'flavors' => $this->extractFlavors($crawler)
        ];
    }

    /**
     * Extracts purchase types (one-time, subscription)
     */
    private function extractPurchaseTypes(Crawler $crawler): array
    {
        $types = [];
        
        // Extract one-time purchase option
        $oneTime = $crawler->filter('.purchase-option[data-variant-option-subscription="no"] .product-variant-label-info');
        if ($oneTime->count() > 0) {
            $types['one_time'] = trim($oneTime->text());
        }
        
        // Extract subscription option
        $subscription = $crawler->filter('.purchase-option[data-variant-option-subscription="yes"] .product-variant-label-info');
        if ($subscription->count() > 0) {
            $types['subscription'] = trim($subscription->text());
        }
        
        return $types;
    }

    /**
     * Extracts quantity options
     */
    private function extractQuantities(Crawler $crawler): array
    {
        $quantities = [];
        
        $crawler->filter('.choose-item')->each(function (Crawler $node) use (&$quantities) {
            $fullName = trim($node->filter('p')->first()->text());
            $key = $this->normalizeQuantityKey($fullName);
            
            $quantities[$key] = [
                'fullName' => $fullName,
                'numberOfItems' => $this->extractNumberFromText($fullName),
                'savings' => $this->extractSavings($node)
            ];
        });
        
        return $quantities;
    }

    /**
     * Extracts subscription frequencies
     */
    private function extractFrequencies(Crawler $crawler): array
    {
        $frequencies = [];
        
        $crawler->filter('.subscription-frequency option')->each(function (Crawler $node) use (&$frequencies) {
            $value = trim($node->text());
            if ($value && $value !== 'Select frequency') {
                $frequencies[] = $value;
            }
        });
        
        return $frequencies;
    }

    /**
     * Extracts flavor options if available
     */
    private function extractFlavors(Crawler $crawler): array
    {
        $flavors = [];
        
        $crawler->filter('.flavor')->each(function (Crawler $node) use (&$flavors) {
            $code = $node->attr('data-option-value-code');
            if ($code) {
                $flavors[$code] = [
                    'name' => trim($node->filter('.flavor-name')->text()),
                    'description' => trim($node->filter('.flavor-description')->text()),
                    'code' => $code
                ];
            }
        });
        
        return $flavors;
    }

    /**
     * Extracts meta information about the product
     */
    private function extractMeta(Crawler $crawler): array
    {
        return [
            'category' => $this->extractText($crawler, '.breadcrumb-item:last-child'),
            'subtitle' => $this->extractText($crawler, '.main .note.color'),
            'reviews_count' => $this->extractReviewsCount($crawler),
            'breadcrumbs' => $this->extractBreadcrumbs($crawler)
        ];
    }

    /**
     * Extracts review count
     */
    private function extractReviewsCount(Crawler $crawler): int
    {
        try {
            $text = $crawler->filter('.reviews-count')->text();
            return (int) preg_replace('/[^0-9]/', '', $text);
        } catch (\InvalidArgumentException $e) {
            return 0;
        }
    }

    /**
     * Extracts breadcrumb navigation
     */
    private function extractBreadcrumbs(Crawler $crawler): array
    {
        $breadcrumbs = [];
        
        $crawler->filter('.breadcrumb-item')->each(function (Crawler $node) use (&$breadcrumbs) {
            $breadcrumbs[] = trim($node->text());
        });
        
        return $breadcrumbs;
    }

    /**
     * Extracts content information (description, badges, benefits, etc.)
     */
    private function extractContent(Crawler $crawler): array
    {
        return [
            'description' => $this->extractText($crawler, '.main .text'),
            'badges' => $this->extractBadges($crawler),
            'trust_badges' => $this->extractTrustBadges($crawler),
            'subscription_benefits' => $this->extractSubscriptionBenefits($crawler),
            'testimonial' => $this->extractTestimonial($crawler),
            'details' => $this->extractDetails($crawler),
            'features' => $this->extractFeatures($crawler),
            'certifications' => $this->extractCertifications($crawler),
            'faq' => $this->extractFAQ($crawler),
            'related_products' => $this->extractRelatedProducts($crawler)
        ];
    }

    /**
     * Extracts product badges
     */
    private function extractBadges(Crawler $crawler): array
    {
        $badges = [];
        
        $crawler->filter('.info-grid .info-item')->each(function (Crawler $node) use (&$badges) {
            $badges[] = [
                'name' => trim($node->filter('.info-title')->text()),
                'icon' => $node->filter('img')->attr('src')
            ];
        });
        
        return $badges;
    }

    /**
     * Extracts trust badges
     */
    private function extractTrustBadges(Crawler $crawler): array
    {
        $badges = [];
        
        $crawler->filter('.info-grid-w-bg .info-item')->each(function (Crawler $node) use (&$badges) {
            $badges[] = [
                'text' => trim($node->filter('.info-title')->text()),
                'icon' => $node->filter('img')->attr('src')
            ];
        });
        
        return $badges;
    }

    /**
     * Extracts subscription benefits
     */
    private function extractSubscriptionBenefits(Crawler $crawler): array
    {
        $benefits = [];
        
        $crawler->filter('.purchase-option[data-variant-option-subscription="yes"] ul li')
            ->each(function (Crawler $node) use (&$benefits) {
                $benefits[] = trim($node->text());
            });
        
        return $benefits;
    }

    /**
     * Extracts testimonial
     */
    private function extractTestimonial(Crawler $crawler): array
    {
        return [
            'quote' => $this->extractText($crawler, '.review-box .text-box .text'),
            'author' => $this->extractText($crawler, '.review-box .text-box .name-text'),
            'role' => $this->extractText($crawler, '.review-box .text-box .role')
        ];
    }

    /**
     * Extracts product details sections
     */
    private function extractDetails(Crawler $crawler): array
    {
        $details = [];
        
        $crawler->filter('.details-section')->each(function (Crawler $node) use (&$details) {
            $details[] = [
                'title' => trim($node->filter('.details-title')->text()),
                'content' => trim($node->filter('.details-content')->text())
            ];
        });
        
        return $details;
    }

    /**
     * Extracts product features
     */
    private function extractFeatures(Crawler $crawler): array
    {
        return [
            'title' => $this->extractText($crawler, '.features-section .title'),
            'subtitle' => $this->extractText($crawler, '.features-section .subtitle'),
            'benefits' => $this->extractFeatureBenefits($crawler)
        ];
    }

    /**
     * Extracts feature benefits
     */
    private function extractFeatureBenefits(Crawler $crawler): array
    {
        $benefits = [];
        
        $crawler->filter('.features-section .benefit-item')->each(function (Crawler $node) use (&$benefits) {
            $benefits[] = trim($node->text());
        });
        
        return $benefits;
    }

    /**
     * Extracts product certifications
     */
    private function extractCertifications(Crawler $crawler): array
    {
        $certifications = [];
        
        $crawler->filter('.certifications .certification-item')->each(function (Crawler $node) use (&$certifications) {
            $certifications[] = [
                'name' => trim($node->filter('.certification-name')->text()),
                'icon' => $node->filter('img')->attr('src')
            ];
        });
        
        return $certifications;
    }

    /**
     * Extracts FAQ sections
     */
    private function extractFAQ(Crawler $crawler): array
    {
        $faq = [];
        
        $crawler->filter('.accordion-item')->each(function (Crawler $node) use (&$faq) {
            $faq[] = [
                'question' => trim($node->filter('.accordion-button')->text()),
                'answer' => trim($node->filter('.accordion-body')->text())
            ];
        });
        
        return $faq;
    }

    /**
     * Extracts related products
     */
    private function extractRelatedProducts(Crawler $crawler): array
    {
        $products = [];
        
        $crawler->filter('.related-product')->each(function (Crawler $node) use (&$products) {
            $products[] = [
                'name' => trim($node->filter('.product-name')->text()),
                'subtitle' => trim($node->filter('.product-subtitle')->text()),
                'description' => trim($node->filter('.product-description')->text()),
                'url' => $node->filter('a')->attr('href')
            ];
        });
        
        return $products;
    }

    /**
     * Extracts funnel data
     */
    private function extractFunnelData(Crawler $crawler): array
    {
        return [
            'upsell_products' => $this->extractUpsellProducts($crawler),
            'restrictions' => $this->extractRestrictions($crawler)
        ];
    }

    /**
     * Extracts upsell products
     */
    private function extractUpsellProducts(Crawler $crawler): array
    {
        $upsells = [];
        
        $crawler->filter('.upsell-product')->each(function (Crawler $node) use (&$upsells) {
            $productKey = $this->generateProductKey(trim($node->filter('.product-name')->text()));
            
            $upsells[] = [
                'product' => $productKey,
                'message' => trim($node->filter('.upsell-message')->text()),
                'description' => trim($node->filter('.upsell-description')->text())
            ];
        });
        
        return $upsells;
    }

    /**
     * Extracts product restrictions
     */
    private function extractRestrictions(Crawler $crawler): array
    {
        $restrictions = [];
        
        // Extract storage instructions
        $storage = $crawler->filter('.storage-instructions');
        if ($storage->count() > 0) {
            $restrictions['storage'] = trim($storage->text());
        }
        
        // Extract warnings
        $warnings = [];
        $crawler->filter('.product-warning')->each(function (Crawler $node) use (&$warnings) {
            $warnings[] = trim($node->text());
        });
        
        if (!empty($warnings)) {
            $restrictions['warnings'] = $warnings;
        }
        
        // Extract age limit if present
        $ageLimit = $crawler->filter('.age-restriction');
        if ($ageLimit->count() > 0) {
            $restrictions['age_limit'] = (int) preg_replace('/[^0-9]/', '', $ageLimit->text());
        }
        
        return $restrictions;
    }

    /**
     * Updates the product YAML file with new data
     */
    private function updateProductYaml(array $scrapedData, string $brand): void
    {
        $yamlPath = sprintf('%s/features/bootstrap/fixtures/brands/%s/products.yml',
            dirname(__DIR__, 3),
            $brand
        );

        if (!file_exists($yamlPath)) {
            throw new \RuntimeException("Products YAML file not found: $yamlPath");
        }

        // Load existing YAML
        $existingData = Yaml::parseFile($yamlPath);
        
        // Find the product key by matching the slug
        $productKey = $this->findProductKey($existingData, $scrapedData['slug']);
        
        if ($productKey === null) {
            // New product - generate key from name
            $productKey = $this->generateProductKey($scrapedData['name']);
            $existingData[$productKey] = [];
        }

        // Merge new data with existing, preserving structure
        $existingData[$productKey] = $this->mergeProductData(
            $existingData[$productKey] ?? [],
            $scrapedData
        );

        // Save updated YAML
        file_put_contents($yamlPath, Yaml::dump($existingData, 6, 2));
    }

    /**
     * Validates the scraped data structure
     */
    private function validateScrapedData(array $data): void
    {
        $requiredFields = ['name', 'slug', 'url_path', 'prices', 'options'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new InvalidArgumentException("Missing required field: $field");
            }
        }

        // Validate prices structure
        if (!isset($data['prices']['one_time']) || !isset($data['prices']['subscription'])) {
            throw new InvalidArgumentException("Invalid prices structure");
        }

        // Validate options structure
        if (!isset($data['options']['purchase_types']) || !isset($data['options']['quantities'])) {
            throw new InvalidArgumentException("Invalid options structure");
        }
    }

    // Helper methods
    private function extractText(Crawler $crawler, string $selector, string $default = ''): string
    {
        try {
            return trim($crawler->filter($selector)->text());
        } catch (\InvalidArgumentException $e) {
            return $default;
        }
    }

    private function extractSlugFromUrl(string $url): string
    {
        return basename(parse_url($url, PHP_URL_PATH));
    }

    private function normalizeQuantityKey(string $text): string
    {
        if (preg_match('/1\s+(?:Bottle|Jar|Box)/i', $text)) {
            return 'minimum';
        } elseif (preg_match('/3\s+(?:Bottles|Jars|Boxes)/i', $text)) {
            return 'medium';
        } else {
            return 'maximum';
        }
    }

    private function extractPriceValue(string $text): float
    {
        return (float) preg_replace('/[^0-9.]/', '', $text);
    }

    private function extractNumberFromText(string $text): int
    {
        preg_match('/(\d+)/', $text, $matches);
        return isset($matches[1]) ? (int) $matches[1] : 0;
    }

    private function extractSavings(Crawler $node): ?string
    {
        try {
            $savingsText = $node->filter('.savings')->text();
            preg_match('/(\d+)%/', $savingsText, $matches);
            return isset($matches[1]) ? $matches[1] . '%' : null;
        } catch (\InvalidArgumentException $e) {
            return null;
        }
    }

    private function findProductKey(array $existingData, string $slug): ?string
    {
        foreach ($existingData as $key => $product) {
            if (isset($product['slug']) && $product['slug'] === $slug) {
                return $key;
            }
        }
        return null;
    }

    private function generateProductKey(string $name): string
    {
        return strtolower(preg_replace('/[^a-zA-Z0-9]+/', '_', $name));
    }

    private function mergeProductData(array $existing, array $new): array
    {
        // Preserve certain fields from existing data if they exist
        $preserveFields = ['discount_codes', 'funnel.restrictions'];
        
        foreach ($preserveFields as $field) {
            $parts = explode('.', $field);
            if (count($parts) === 1) {
                if (isset($existing[$field])) {
                    $new[$field] = $existing[$field];
                }
            } else {
                // Handle nested fields
                $current = &$new;
                $existingCurrent = &$existing;
                foreach ($parts as $part) {
                    if (!isset($current[$part])) {
                        $current[$part] = [];
                    }
                    if (isset($existingCurrent[$part])) {
                        $current[$part] = $existingCurrent[$part];
                    }
                    $current = &$current[$part];
                    $existingCurrent = &$existingCurrent[$part];
                }
            }
        }

        return $new;
    }
} 