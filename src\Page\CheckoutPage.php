<?php

namespace App\Page;

use App\Page\Base\BasePage;
use App\Page\Base\PaymentPageInterface;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;

/**
 * CheckoutPage handles actions on the checkout page.
 */
class CheckoutPage extends BasePage implements PaymentPageInterface
{
    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'SHIPPING_FORM' => '#shipping-address-form',
        'BILLING_FORM' => '#billing-address-form',
        'PAYMENT_FORM' => '#payment-form',
        'CONTINUE_BUTTON' => '.continue-button',
        'BACK_BUTTON' => '.back-button',
        'CHECKOUT_STEPS' => '.checkout-steps',
        'ACTIVE_STEP' => '.checkout-step.active',
        'ORDER_SUMMARY' => '.order-summary',
        'ERROR_MESSAGE' => '.error-message'
    ];
    /**
     * The path of the checkout page.
     *
     * @var string
     */
    protected string $path = '/checkout';
    /**
     * Test data service
     *
     * @var TestDataServiceInterface
     */
    private TestDataServiceInterface $dataService;

    /**
     * Constructor
     *
     * @param BrowserServiceInterface $browserService Browser service
     * @param TestDataServiceInterface $dataService Test data service
     * @param string|null $baseUrl Base URL (optional, defaults to environment variable)
     */
    public function __construct(
        BrowserServiceInterface  $browserService,
        TestDataServiceInterface $dataService,
        ?string                  $baseUrl = null
    )
    {
        parent::__construct($browserService, $baseUrl);
        $this->dataService = $dataService;
    }

    /**
     * Fill shipping information form
     *
     * @param array $userData User data with shipping information
     * @return void
     */
    public function fillShippingInformation(array $userData): void
    {
        $this->waitForElementVisible(self::SELECTORS['SHIPPING_FORM']);

        // Fill form fields
        $this->fillField('#first_name', $userData['firstName']);
        $this->fillField('#last_name', $userData['lastName']);
        $this->fillField('#email', $userData['email']);
        $this->fillField('#address', $userData['address']);
        $this->fillField('#city', $userData['city']);
        $this->selectOption('#state', $userData['state']);
        $this->fillField('#zip', $userData['zip']);
        $this->fillField('#phone', $userData['phone']);

        // Continue to next step
        $this->clickElement(self::SELECTORS['CONTINUE_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Fill billing information form
     *
     * @param array $userData User data with billing information
     * @param bool $sameAsShipping Whether billing is same as shipping
     * @return void
     */
    public function fillBillingInformation(array $userData, bool $sameAsShipping = true): void
    {
        $this->waitForElementVisible(self::SELECTORS['BILLING_FORM']);

        if ($sameAsShipping) {
            // Check the "Same as shipping" checkbox
            $this->clickElement('#same_as_shipping');
        } else {
            // Fill form fields
            $this->fillField('#billing_first_name', $userData['firstName']);
            $this->fillField('#billing_last_name', $userData['lastName']);
            $this->fillField('#billing_address', $userData['address']);
            $this->fillField('#billing_city', $userData['city']);
            $this->selectOption('#billing_state', $userData['state']);
            $this->fillField('#billing_zip', $userData['zip']);
        }

        // Continue to next step
        $this->clickElement(self::SELECTORS['CONTINUE_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Fill payment information form
     *
     * @param array $paymentData Payment data
     * @return void
     */
    public function fillPaymentInformation(array $paymentData): void
    {
        $this->waitForElementVisible(self::SELECTORS['PAYMENT_FORM']);

        // Fill credit card information
        $this->fillField('#card_number', $paymentData['cardNumber']);
        $this->fillField('#card_expiry', $paymentData['expiryDate']);
        $this->fillField('#card_cvv', $paymentData['cvv']);

        // Continue to next step
        $this->clickElement(self::SELECTORS['CONTINUE_BUTTON']);
        $this->waitForAjaxToComplete();
    }

    /**
     * Place the order
     *
     * @return void
     */
    public function placeOrder(): void
    {
        $this->waitForElementVisible('#place-order-button');
        $this->clickElement('#place-order-button');
        $this->waitForPageToLoad();
    }

    /**
     * Get the current checkout step
     *
     * @return string Step name
     */
    public function getCurrentStep(): string
    {
        $activeStep = $this->browserService->findElement(self::SELECTORS['ACTIVE_STEP']);
        return $activeStep->getText();
    }

    /**
     * Check if there are validation errors
     *
     * @return bool
     */
    public function hasValidationErrors(): bool
    {
        return $this->elementExists(self::SELECTORS['ERROR_MESSAGE']);
    }

    /**
     * Get validation error messages
     *
     * @return array List of error messages
     */
    public function getValidationErrors(): array
    {
        $errors = [];
        $errorElements = $this->browserService->findElements(self::SELECTORS['ERROR_MESSAGE']);

        foreach ($errorElements as $element) {
            $errors[] = $element->getText();
        }

        return $errors;
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible(self::SELECTORS['CHECKOUT_STEPS']);
    }

    /**
     * Check if the shipping form is visible
     *
     * @return bool True if shipping form is visible
     */
    public function isShippingFormVisible(): bool
    {
        return $this->browserService->isElementVisible(self::SELECTORS['SHIPPING_FORM']);
    }

    /**
     * Complete the PayPal checkout process
     *
     * @return void
     */
    public function completePayPalCheckout()
    {
        // Select PayPal payment option
        $this->waitForElementVisible('#paypal-option');
        $this->clickElement('#paypal-option');

        // Click the PayPal button
        $this->waitForElementVisible('#paypal-button');
        $this->clickElement('#paypal-button');

        // Wait for redirect to PayPal
        $this->waitForPageToLoad();
    }

    /**
     * Handle 3D Secure authentication
     *
     * @return void
     */
    public function handle3DSecureAuthentication()
    {
        // Switch to 3DS iframe
        $iframe = $this->browserService->findElement('#secure-3d-iframe');
        $this->browserService->getSession()->switchToIFrame($iframe->getAttribute('name'));

        // Complete authentication
        $this->waitForElementVisible('#complete-authentication-button');
        $this->clickElement('#complete-authentication-button');

        // Switch back to main content
        $this->browserService->getSession()->switchToIFrame(null);
        $this->waitForPageToLoad();
    }

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // TODO: Implement actual logic to count subscription items
        // For now, return a dummy value
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // TODO: Implement actual logic to count one-time purchase items
        // For now, return a dummy value
        return 0;
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // TODO: Implement actual logic to get subscription frequencies
        // For now, return an empty array
        return [];
    }

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array
    {
        // TODO: Implement actual logic to get cart items
        // For now, return an empty array
        return [];
    }
}
