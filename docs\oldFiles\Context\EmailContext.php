<?php

namespace Features\Bootstrap\Context;

use Behat\Behat\Hook\Scope\AfterScenarioScope;
use Features\Bootstrap\Tools\EmailVerificationTool;
use RuntimeException;
use Throwable;

/**
 * Manages email-related step definitions and verification
 *
 * This context handles:
 * - Abandoned cart email verification
 * - Order confirmation email verification
 * - Email link interactions
 * - Email content validation
 */
class EmailContext extends BaseContext
{
    private EmailVerificationTool $emailTool;
    private const MAX_RETRIES = 3;
    private const RETRY_DELAY = 2;
    private const DEFAULT_TIMEFRAME = 3600;
    private const MAILTRAP_API_URL = 'https://mailtrap.io/api/v1';
    private const INBOX_MESSAGES_ENDPOINT = '/inboxes/%s/messages';
    private const MESSAGE_ENDPOINT = '/inboxes/%s/messages/%s';

    /**
     * @param EmailVerificationTool $emailTool Tool for email verification
     */
    public function __construct(EmailVerificationTool $emailTool)
    {
        parent::__construct();
        $this->emailTool = $emailTool;
        $this->logInfo('EmailContext initialized');
    }

    /**
     * @Then /^I should receive an abandoned cart email within "([^"]*)" hour$/
     * @throws RuntimeException
     * @sets email.recovery
     */
    public function iShouldReceiveAbandonedCartEmailWithinHour(string $hours): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: 'Complete your purchase',
                timeframeSeconds: intval($hours) * 3600
            );

            $this->stateService->set('email.recovery', $email);
            $this->stateService->set('email.last_check_time', time());

            $this->logInfo(sprintf('Received abandoned cart email for user %s within %s hour(s)', $userEmail, $hours));
        } catch (Throwable $e) {
            $this->logError('Failed to check for abandoned cart email', $e);
            throw new RuntimeException(
                sprintf('Failed to check for abandoned cart email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I should receive "([^"]*)" abandoned cart emails$/
     * @throws RuntimeException
     */
    public function iShouldReceiveAbandonedCartEmails(int $count): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $emails = $this->findEmails(
                toEmail: $userEmail,
                subject: 'Complete your purchase'
            );

            $actualCount = count($emails);
            if ($actualCount !== $count) {
                throw new RuntimeException(
                    sprintf('Expected %d abandoned cart emails, but found %d', $count, $actualCount)
                );
            }

            $this->logInfo(sprintf('Verified %d abandoned cart emails for user %s', $count, $userEmail));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to check abandoned cart emails count (expected: %d)', $count), $e);
            throw new RuntimeException(
                sprintf('Failed to check abandoned cart emails count: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I should not receive any more recovery emails$/
     * @throws RuntimeException
     */
    public function iShouldNotReceiveAnyMoreRecoveryEmails(): void
    {
        try {
            $lastEmailTime = $this->stateService->get('email.last_check_time');
            if (!$lastEmailTime) {
                throw new RuntimeException('Last email check time not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $emails = $this->findEmails(
                toEmail: $userEmail,
                subject: 'Complete your purchase',
                timeframeSeconds: time() - $lastEmailTime
            );

            if (!empty($emails)) {
                throw new RuntimeException(
                    sprintf('Found %d unexpected recovery emails after cart expiry', count($emails))
                );
            }

            $this->logInfo(sprintf('Verified no additional recovery emails for user %s after last check', $userEmail));
        } catch (Throwable $e) {
            $this->logError('Failed to check for new recovery emails', $e);
            throw new RuntimeException(
                sprintf('Failed to check for new recovery emails: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I click the recovery link in the email$/
     * @throws RuntimeException
     */
    public function iClickTheRecoveryLinkInTheEmail(): void
    {
        try {
            $email = $this->stateService->get('email.recovery');
            if (!$email) {
                throw new RuntimeException('No recovery email found in shared context');
            }

            $links = $this->emailTool->extractEmailLinks(
                emailId: $email['id'],
                linkPattern: '/.*recover.*cart.*/i'
            );

            if (empty($links)) {
                throw new RuntimeException('Recovery link not found in email');
            }

            // Get session using context manager
            if ($this->contextManager === null) {
                throw new RuntimeException('Context manager not available');
            }

            $navigationContext = $this->contextManager->getContext(NavigationContext::class);
            $navigationContext->visitUrl($links[0]['url']);

            $this->logInfo('Clicked recovery link in email');
        } catch (Throwable $e) {
            $this->logError('Failed to process recovery email link', $e);
            throw new RuntimeException(
                sprintf('Failed to process recovery email link: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the order confirmation email$/
     * @throws RuntimeException
     */
    public function iVerifyTheOrderConfirmationEmail(): void
    {
        try {
            $orderNumber = $this->stateService->get('order.number');
            if (!$orderNumber) {
                throw new RuntimeException('Order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: sprintf('Order Confirmation #%s', $orderNumber)
            );

            $this->verifyOrderConfirmationEmailContent($email);
            $this->logInfo(sprintf('Verified order confirmation email for order %s', $orderNumber));
        } catch (Throwable $e) {
            $this->logError('Failed to verify order confirmation email', $e);
            throw new RuntimeException(
                sprintf('Failed to verify order confirmation email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I should receive an abandoned cart email with subject "([^"]*)"$/
     * @throws RuntimeException
     */
    public function iShouldReceiveAnAbandonedCartEmailWithSubject(string $subject): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(toEmail: $userEmail, subject: $subject);
            $this->stateService->set('email.abandoned_cart', $email);
            $this->logInfo(sprintf('Received abandoned cart email with subject "%s"', $subject));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to check for abandoned cart email with subject "%s"', $subject), $e);
            throw new RuntimeException(
                sprintf('Failed to check for abandoned cart email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I should receive an abandoned cart email with a coupon code$/
     * @throws RuntimeException
     * @sets email.coupon_code
     */
    public function iShouldReceiveAnAbandonedCartEmailWithACouponCode(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: 'Special offer inside'
            );

            if (!preg_match('/[A-Z0-9]{6,}/', $email['content'], $matches)) {
                throw new RuntimeException('Coupon code not found in email content');
            }

            // Save the coupon code
            $this->stateService->set('email.coupon_code', $matches[0]);
            $this->logInfo(sprintf('Found coupon code %s in abandoned cart email', $matches[0]));
        } catch (Throwable $e) {
            $this->logError('Failed to check for abandoned cart email with coupon', $e);
            throw new RuntimeException(
                sprintf('Failed to check for abandoned cart email with coupon: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I should not see an abandoned cart email$/
     * @throws RuntimeException
     */
    public function iShouldNotSeeAnAbandonedCartEmail(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $emails = $this->findEmails(
                toEmail: $userEmail,
                subject: 'Complete your purchase'
            );

            if (!empty($emails)) {
                throw new RuntimeException('Unexpected abandoned cart email found');
            }

            $this->logInfo('Verified no abandoned cart emails were sent');
        } catch (Throwable $e) {
            $this->logError('Failed to check for absence of abandoned cart email', $e);
            throw new RuntimeException(
                sprintf('Failed to check for absence of abandoned cart email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I should see that (\d+) emails was sent for each time$/
     * @throws RuntimeException
     */
    public function iShouldSeeThatEmailsWasSentForEachTime(int $expectedCount): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $emails = $this->waitForEmails(
                toEmail: $userEmail,
                expectedCount: $expectedCount
            );

            if (count($emails) !== $expectedCount) {
                throw new RuntimeException(
                    sprintf('Expected %d abandoned cart emails, but found %d', $expectedCount, count($emails))
                );
            }

            $this->logInfo(sprintf('Verified %d emails were sent', $expectedCount));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify email count (expected: %d)', $expectedCount), $e);
            throw new RuntimeException(
                sprintf('Failed to verify email count: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I follow the "([^"]*)" link in the email$/
     * @throws RuntimeException
     */
    public function iFollowTheLinkInTheEmail(string $linkText): void
    {
        try {
            $email = $this->stateService->get('email.abandoned_cart');
            if (!$email) {
                throw new RuntimeException('No abandoned cart email found in shared context');
            }

            if (!preg_match('/<a[^>]*href="([^"]*)"[^>]*>' . preg_quote($linkText, '/') . '<\/a>/', $email['body'], $matches)) {
                throw new RuntimeException(sprintf('Link with text "%s" not found in email', $linkText));
            }

            // Get session using context manager
            if ($this->contextManager === null) {
                throw new RuntimeException('Context manager not available');
            }

            $navigationContext = $this->contextManager->getContext(NavigationContext::class);
            $navigationContext->visitUrl($matches[1]);

            $this->logInfo(sprintf('Followed "%s" link in email', $linkText));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to follow email link with text "%s"', $linkText), $e);
            throw new RuntimeException(
                sprintf('Failed to follow email link: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Cleans up email cache between scenarios
     *
     * @AfterScenario
     */
    public function cleanupEmailCache(AfterScenarioScope $scope): void
    {
        try {
            $this->emailTool->clearCache();
            $this->logInfo('Email cache cleared');
        } catch (Throwable $e) {
            $this->logError('Failed to clear email cache', $e);
        }
    }

    // Helper Methods

    /**
     * Gets the user email from shared data
     *
     * @return string User email
     * @throws RuntimeException When user email is not found
     */
    private function getUserEmail(): string
    {
        $userEmail = $this->stateService->get('user.email');
        if (!$userEmail) {
            throw new RuntimeException('User email not found in shared context');
        }
        return $userEmail;
    }

    /**
     * Verifies the content of an order confirmation email
     *
     * @param array $email Email data
     * @throws RuntimeException When verification fails
     */
    private function verifyOrderConfirmationEmailContent(array $email): void
    {
        try {
            $orderData = $this->stateService->get('order.data');
            if (!$orderData) {
                throw new RuntimeException('Order data not found in shared context');
            }

            $this->assertEmailContains($email, $orderData['orderNumber'], 'Order number');
            $this->assertEmailContains($email, $orderData['total'], 'Order total');

            foreach ($orderData['products'] as $product) {
                $this->assertEmailContains($email, $product['name'], sprintf('Product "%s"', $product['name']));
            }
        } catch (Throwable $e) {
            $this->logError('Failed to verify order confirmation email content', $e);
            throw new RuntimeException(
                sprintf('Failed to verify order confirmation email content: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Waits for an email matching the criteria
     *
     * @param string $toEmail Target email address
     * @param string $subject Email subject
     * @param int $timeframeSeconds Timeframe in seconds
     * @return array Email data
     * @throws RuntimeException When email is not found
     */
    public function waitForEmail(
        string $toEmail,
        string $subject,
        int    $timeframeSeconds = self::DEFAULT_TIMEFRAME
    ): array
    {
        try {
            $criteria = [
                'to_email' => $toEmail,
                'subject' => $subject
            ];

            $email = $this->emailTool->verifyEmailExists(
                criteria: $criteria,
                timeframeSeconds: $timeframeSeconds,
                maxAttempts: self::MAX_RETRIES,
                delayBetweenAttempts: self::RETRY_DELAY
            );

            if (!$email) {
                throw new RuntimeException(
                    sprintf('Email with subject "%s" not found within %d seconds', $subject, $timeframeSeconds)
                );
            }

            return $email;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to wait for email with subject "%s"', $subject), $e);
            throw new RuntimeException(
                sprintf('Failed to wait for email with subject "%s": %s', $subject, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Finds emails matching the criteria
     *
     * @param string $toEmail Target email address
     * @param string $subject Email subject
     * @param int $timeframeSeconds Timeframe in seconds
     * @return array Matching emails
     * @throws RuntimeException When search fails
     */
    private function findEmails(
        string $toEmail,
        string $subject,
        int    $timeframeSeconds = self::DEFAULT_TIMEFRAME
    ): array
    {
        try {
            $criteria = [
                'to_email' => $toEmail,
                'subject' => $subject
            ];

            return $this->emailTool->findEmails(
                criteria: $criteria,
                timeframeSeconds: $timeframeSeconds
            );
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to find emails with subject "%s"', $subject), $e);
            throw new RuntimeException(
                sprintf('Failed to find emails with subject "%s": %s', $subject, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Waits for a specific number of emails
     *
     * @param string $toEmail Target email address
     * @param int $expectedCount Expected number of emails
     * @param int $timeframeSeconds Timeframe in seconds
     * @return array Found emails
     * @throws RuntimeException When expected count is not reached
     */
    private function waitForEmails(
        string $toEmail,
        int    $expectedCount,
        int    $timeframeSeconds = self::DEFAULT_TIMEFRAME
    ): array
    {
        try {
            $attempts = self::MAX_RETRIES;
            while ($attempts > 0) {
                $emails = $this->findEmails($toEmail, '', $timeframeSeconds);
                if (count($emails) === $expectedCount) {
                    return $emails;
                }
                sleep(self::RETRY_DELAY);
                $attempts--;
            }
            return [];
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to wait for %d emails', $expectedCount), $e);
            throw new RuntimeException(
                sprintf('Failed to wait for %d emails: %s', $expectedCount, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Asserts that email content contains expected text
     *
     * @param array $email Email data
     * @param string $expected Expected text
     * @param string $description Description for error message
     * @throws RuntimeException When expected text is not found
     */
    private function assertEmailContains(array $email, string $expected, string $description): void
    {
        if (!str_contains($email['content'], $expected)) {
            $this->logError(sprintf('%s not found in email content', $description));
            throw new RuntimeException(sprintf('%s not found in email content', $description));
        }
    }

    /**
     * @Then /^I verify the welcome email contains account credentials$/
     * @throws RuntimeException
     */
    public function iVerifyTheWelcomeEmailContainsAccountCredentials(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: 'Welcome To Dr. Sister Skincare!'
            );

            if (!str_contains($email['content'], 'login')) {
                throw new RuntimeException('Welcome email does not contain login information');
            }

            if (!str_contains($email['content'], 'password')) {
                throw new RuntimeException('Welcome email does not contain password information');
            }

            $this->logInfo('Verified welcome email contains account credentials');
        } catch (Throwable $e) {
            $this->logError('Failed to verify welcome email', $e);
            throw new RuntimeException(
                sprintf('Failed to verify welcome email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the subscription confirmation email$/
     * @throws RuntimeException
     */
    public function iVerifyTheSubscriptionConfirmationEmail(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: 'Your subscription has been created'
            );

            $orderData = $this->stateService->get('order.data');
            if (!$orderData) {
                throw new RuntimeException('Order data not found in shared context');
            }

            $this->assertEmailContains($email, $orderData['products'][0]['name'], 'Product name');
            $this->assertEmailContains($email, 'Ships Every 30 Days', 'Subscription frequency');
            $this->assertEmailContains($email, $orderData['total'], 'Subscription total');

            $this->logInfo('Verified subscription confirmation email');
        } catch (Throwable $e) {
            $this->logError('Failed to verify subscription confirmation email', $e);
            throw new RuntimeException(
                sprintf('Failed to verify subscription confirmation email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the order confirmation email for initial product only$/
     * @throws RuntimeException
     */
    public function iVerifyTheOrderConfirmationEmailForInitialProductOnly(): void
    {
        try {
            $orderNumber = $this->stateService->get('order.number');
            if (!$orderNumber) {
                throw new RuntimeException('Order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: sprintf('Order Confirmation #%s', $orderNumber)
            );

            $this->assertEmailContains($email, 'Dark Spot Vanish', 'Initial product');
            $this->assertEmailContains($email, '£91.95', 'Order total');

            // Ensure upsell product is NOT in the email
            if (str_contains($email['content'], 'Relax + Restore')) {
                throw new RuntimeException('Upsell product found in email when it should not be there');
            }

            $this->logInfo('Verified order confirmation email contains only initial product');
        } catch (Throwable $e) {
            $this->logError('Failed to verify order confirmation email for initial product only', $e);
            throw new RuntimeException(
                sprintf('Failed to verify order confirmation email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the order confirmation email for the renewal order$/
     * @throws RuntimeException
     */
    public function iVerifyTheOrderConfirmationEmailForTheRenewalOrder(): void
    {
        try {
            $newOrderNumber = $this->stateService->get('order.renewal_number');
            if (!$newOrderNumber) {
                throw new RuntimeException('New order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: sprintf('Order Confirmation #%s', $newOrderNumber)
            );

            $this->assertEmailContains($email, 'Younger You Skin Cream', 'Product name');
            $this->assertEmailContains($email, '£75.00', 'Order total');

            $this->logInfo('Verified order confirmation email for renewal order');
        } catch (Throwable $e) {
            $this->logError('Failed to verify renewal order confirmation email', $e);
            throw new RuntimeException(
                sprintf('Failed to verify renewal order confirmation email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I verify the order confirmation email contains all purchased items$/
     * @throws RuntimeException
     */
    public function iVerifyTheOrderConfirmationEmailContainsAllPurchasedItems(): void
    {
        try {
            $orderNumber = $this->stateService->get('order.number');
            if (!$orderNumber) {
                throw new RuntimeException('Order number not found in shared context');
            }

            $userEmail = $this->getUserEmail();
            $email = $this->waitForEmail(
                toEmail: $userEmail,
                subject: sprintf('Order Confirmation #%s', $orderNumber)
            );

            $this->assertEmailContains($email, 'Dark Spot Vanish', 'Initial product');
            $this->assertEmailContains($email, 'Relax + Restore', 'Upsell product');
            $this->assertEmailContains($email, '£101.95', 'Order total');

            $this->logInfo('Verified order confirmation email contains all purchased items');
        } catch (Throwable $e) {
            $this->logError('Failed to verify order confirmation email with all items', $e);
            throw new RuntimeException(
                sprintf('Failed to verify order confirmation email with all items: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^the abandoned cart email contains the correct product information$/
     * @throws RuntimeException
     */
    public function theAbandonedCartEmailContainsTheCorrectProductInformation(): void
    {
        try {
            $userEmail = $this->getUserEmail();
            $email = $this->stateService->get('email.recovery');
            if (!$email) {
                throw new RuntimeException('No recovery email found in shared context');
            }

            $this->assertEmailContains($email, 'Dark Spot Vanish', 'Product name');
            $this->assertEmailContains($email, '£91.95', 'Order total');

            // Check for recovery link
            $links = $this->emailTool->extractEmailLinks(
                emailId: $email['id'],
                linkPattern: '/.*complete.*purchase.*/i'
            );

            if (empty($links)) {
                throw new RuntimeException('Recovery link not found in abandoned cart email');
            }

            $this->logInfo('Verified abandoned cart email contains correct product information');
        } catch (Throwable $e) {
            $this->logError('Failed to verify abandoned cart email content', $e);
            throw new RuntimeException(
                sprintf('Failed to verify abandoned cart email content: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }
}
