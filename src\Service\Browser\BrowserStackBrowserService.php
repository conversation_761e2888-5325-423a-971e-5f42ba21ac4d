<?php

namespace App\Service\Browser;

use App\Service\AbstractService;
use App\Service\Browser\BrowserStack\BrowserStackCapabilitiesBuilder;
use App\Service\Browser\BrowserStack\BrowserStackSessionFactory;
use App\Service\Browser\BrowserStack\BrowserStackStatusReporter;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Session;
use Psr\Log\LoggerInterface;
use RuntimeException;

/**
 * BrowserStack implementation of the BrowserService
 */
class BrowserStackBrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    private int $defaultTimeout = 30;
    private BrowserStackStatusReporter $statusReporter;
    private BrowserStackSessionFactory $sessionFactory;

    /**
     * Constructor
     *
     * @param string $screenshotsDir Screenshots directory path
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        string           $screenshotsDir,
        ?LoggerInterface $logger = null
    )
    {
        parent::__construct($logger);

        $this->screenshotsDir = $screenshotsDir;
        $this->statusReporter = new BrowserStackStatusReporter($logger);
        $this->sessionFactory = new BrowserStackSessionFactory($logger);

        // Create screenshots directory if it doesn't exist
        if (!is_dir($this->screenshotsDir) && !mkdir($this->screenshotsDir, 0777, true)) {
            $this->logError(sprintf("Failed to create screenshots directory: %s", $this->screenshotsDir));
        }

        // Initialize BrowserStack session
        $this->initializeSession();

        $this->logInfo("Initialized BrowserStackBrowserService");
    }

    /**
     * Initialize BrowserStack session
     */
    private function initializeSession(): void
    {
        $this->logInfo("Initializing BrowserStack session");

        // Create capabilities builder
        $capabilitiesBuilder = new BrowserStackCapabilitiesBuilder($this->logger);

        // Load capabilities from environment
        $capabilities = $capabilitiesBuilder
            ->loadFromEnvironment()
            ->build();

        // Create session
        $this->session = $this->sessionFactory->createSession($capabilities);

        $this->logInfo("BrowserStack session initialized successfully");
    }

    /**
     * {@inheritdoc}
     */
    public function getSession(): Session
    {
        return $this->session;
    }

    /**
     * {@inheritdoc}
     */
    public function visit(string $url): void
    {
        $this->logInfo("Visiting URL: {$url}");
        $startTime = microtime(true);
        $maxRetries = 3;

        for ($retry = 0; $retry < $maxRetries; $retry++) {
            try {
                // Check if session is active and start it if needed
                if (!$this->session->isStarted() || !$this->isSessionActive()) {
                    $this->logInfo("Session not active, starting new session...");

                    // Stop existing session if needed
                    if ($this->session->isStarted()) {
                        try {
                            $this->session->stop();
                            $this->logInfo("Stopped existing session");
                        } catch (\Throwable $e) {
                            $this->logWarning("Could not stop existing session: " . $e->getMessage());
                        }
                    }

                    // Start session
                    $this->session->start();
                    $this->logInfo("Session started successfully");
                }

                // Execute the navigation
                $this->logInfo("Navigating to URL: {$url} (attempt " . ($retry + 1) . "/{$maxRetries})");
                $this->session->visit($url);

                // Wait for page to fully load
                $this->waitForPageToLoad();

                $totalElapsedTime = round((microtime(true) - $startTime) * 1000);
                $this->logInfo("Total visit operation completed in {$totalElapsedTime}ms");

                // Navigation succeeded, exit retry loop
                return;

            } catch (\Exception $e) {
                $this->logError("Exception while visiting {$url} (attempt " . ($retry + 1) . "/{$maxRetries}): " . $e->getMessage());
                $this->takeScreenshot('visit_error');

                // Only retry if we haven't reached max retries
                if ($retry < $maxRetries - 1) {
                    $delaySeconds = 5 * ($retry + 1);
                    $this->logInfo("Waiting {$delaySeconds}s before retry");
                    sleep($delaySeconds);
                } else {
                    // Max retries reached, propagate the exception
                    throw new RuntimeException(
                        sprintf('Failed to visit URL after %d attempts: %s', $maxRetries, $url),
                        0,
                        $e
                    );
                }
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function isSessionActive(): bool
    {
        try {
            // First check if session is started
            if (!$this->session->isStarted()) {
                return false;
            }

            // Try to execute a simple JavaScript command to verify connectivity
            $jsResult = $this->executeScript('return true;');
            return $jsResult === true;
        } catch (\Exception $e) {
            $this->logError("Session is not active: " . $e->getMessage());
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function executeScript(string $script)
    {
        $this->logInfo(sprintf("Executing JavaScript: %s", substr($script, 0, 50) . (strlen($script) > 50 ? '...' : '')));

        try {
            return $this->session->evaluateScript($script);
        } catch (\Exception $e) {
            $this->logError("Error executing JavaScript: " . $e->getMessage());
            throw new RuntimeException("Error executing JavaScript", 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function waitForPageToLoad(int $timeout = 60): void
    {
        $this->logInfo(sprintf("Waiting for page to load (timeout: %d seconds)", $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                // Check document ready state
                $readyState = $this->session->evaluateScript('return document.readyState;');

                if ($readyState === 'complete') {
                    // Also check for jQuery if it's present
                    $jQueryReady = $this->session->evaluateScript(
                        'return typeof jQuery === "undefined" || jQuery.active === 0;'
                    );

                    if ($jQueryReady) {
                        $this->logInfo(sprintf(
                            "Page loaded successfully after %.1f seconds",
                            microtime(true) - $startTime
                        ));
                        return;
                    }
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
                $this->logDebug("Exception while checking page load status: " . $e->getMessage());
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        // If we reach here, the page didn't load within timeout
        $this->logWarning(sprintf(
            "Timeout waiting for page to load after %.1f seconds",
            microtime(true) - $startTime
        ));

        // Take a screenshot to help with debugging
        $this->takeScreenshot('page_load_timeout');
    }

    /**
     * {@inheritdoc}
     */
    public function takeScreenshot(string $name = null): string
    {
        $this->logInfo(sprintf("Taking screenshot: %s", $name ?: 'unnamed'));

        try {
            if (!$this->session->isStarted()) {
                $this->logError("Cannot take screenshot: session not started");
                throw new RuntimeException("Cannot take screenshot: session not started");
            }

            $filename = $this->createScreenshotFilename($name);
            $filepath = $this->screenshotsDir . '/' . $filename;

            $screenshot = $this->session->getDriver()->getScreenshot();
            file_put_contents($filepath, $screenshot);

            $this->logInfo(sprintf("Screenshot saved: %s", $filepath));

            return $filepath;
        } catch (\Exception $e) {
            $this->logError("Failed to take screenshot: " . $e->getMessage());
            throw new RuntimeException("Failed to take screenshot", 0, $e);
        }
    }

    /**
     * Create a screenshot filename
     *
     * @param string|null $name Custom name
     * @return string Generated filename
     */
    private function createScreenshotFilename(?string $name = null): string
    {
        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid()), 0, 6);

        if ($name) {
            return sprintf('%s_%s_%s.png', $timestamp, $name, $random);
        }

        return sprintf('%s_screenshot_%s.png', $timestamp, $random);
    }

    /**
     * {@inheritdoc}
     */
    public function waitForElement(string $selector, int $timeout = 30): void
    {
        $this->logInfo(sprintf("Waiting for element: %s (timeout: %d seconds)", $selector, $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $element = $this->session->getPage()->find('css', $selector);
                if ($element && $element->isVisible()) {
                    $this->logInfo(sprintf("Element found: %s", $selector));
                    return;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logError(sprintf("Timeout waiting for element: %s", $selector));
        $this->takeScreenshot('element_timeout');
        throw new RuntimeException(sprintf("Timeout waiting for element: %s", $selector));
    }

    /**
     * {@inheritdoc}
     */
    public function findElements(string $selector): array
    {
        $this->logInfo(sprintf("Finding elements: %s", $selector));

        try {
            return $this->session->getPage()->findAll('css', $selector);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error finding elements: %s", $selector), $e);
            return [];
        }
    }

    /**
     * {@inheritdoc}
     */
    public function waitForElementVisible(string $selector, int $timeout = 30): bool
    {
        $this->logInfo(sprintf("Waiting for element to be visible: %s (timeout: %d seconds)", $selector, $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $element = $this->session->getPage()->find('css', $selector);
                if ($element && $element->isVisible()) {
                    $this->logInfo(sprintf("Element visible: %s", $selector));
                    return true;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logInfo(sprintf("Element not visible within timeout: %s", $selector));
        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function scrollToElement(string $selector): void
    {
        $this->logInfo(sprintf("Scrolling to element: %s", $selector));

        try {
            $script = sprintf(
                "const element = document.querySelector('%s'); " .
                "if (element) { element.scrollIntoView({behavior: 'smooth', block: 'center'}); return true; } " .
                "return false;",
                addslashes($selector)
            );

            $result = $this->executeScript($script);

            if (!$result) {
                $this->logWarning(sprintf("Element not found for scrolling: %s", $selector));
            }
        } catch (\Exception $e) {
            $this->logError(sprintf("Error scrolling to element: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error scrolling to element: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function clickElement(string $selector): void
    {
        $this->logInfo(sprintf("Clicking element: %s", $selector));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Element not found for clicking: %s", $selector));
                throw new RuntimeException(sprintf("Element not found for clicking: %s", $selector));
            }

            $element->click();
        } catch (\Exception $e) {
            $this->logError(sprintf("Error clicking element: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error clicking element: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function findElement(string $selector, int $timeout = 10): ?NodeElement
    {
        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $element = $this->session->getPage()->find('css', $selector);
                if ($element && $element->isVisible()) {
                    return $element;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function fillField(string $selector, string $value): void
    {
        $this->logInfo(sprintf("Filling field: %s", $selector));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Field not found for filling: %s", $selector));
                throw new RuntimeException(sprintf("Field not found for filling: %s", $selector));
            }

            $element->setValue($value);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error filling field: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error filling field: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function selectOption(string $selector, string $value): void
    {
        $this->logInfo(sprintf("Selecting option: %s = %s", $selector, $value));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Select field not found: %s", $selector));
                throw new RuntimeException(sprintf("Select field not found: %s", $selector));
            }

            $element->selectOption($value);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error selecting option: %s = %s", $selector, $value), $e);
            throw new RuntimeException(sprintf("Error selecting option: %s = %s", $selector, $value), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getElementText(string $selector): string
    {
        $this->logInfo(sprintf("Getting text from element: %s", $selector));

        try {
            $element = $this->findElement($selector);

            if (!$element) {
                $this->logError(sprintf("Element not found for getting text: %s", $selector));
                throw new RuntimeException(sprintf("Element not found for getting text: %s", $selector));
            }

            return $element->getText();
        } catch (\Exception $e) {
            $this->logError(sprintf("Error getting text from element: %s", $selector), $e);
            throw new RuntimeException(sprintf("Error getting text from element: %s", $selector), 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function isElementVisible(string $selector): bool
    {
        $this->logInfo(sprintf("Checking if element is visible: %s", $selector));

        try {
            $element = $this->session->getPage()->find('css', $selector);
            return $element && $element->isVisible();
        } catch (\Exception $e) {
            $this->logError(sprintf("Error checking element visibility: %s", $selector), $e);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function waitForDocumentReady(int $timeout = 30): void
    {
        $this->logInfo(sprintf("Waiting for document to be ready (timeout: %d seconds)", $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            $readyState = $this->session->evaluateScript('return document.readyState;');

            if ($readyState === 'complete') {
                $this->logInfo("Document is ready");
                return;
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logError("Timeout waiting for document to be ready");
        $this->takeScreenshot('document_ready_timeout');
        throw new RuntimeException("Timeout waiting for document to be ready");
    }

    /**
     * {@inheritdoc}
     */
    public function waitForAjaxToComplete(int $timeout = 30): void
    {
        $this->logInfo(sprintf("Waiting for AJAX requests to complete (timeout: %d seconds)", $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            // Check for jQuery AJAX requests
            $jQueryActive = $this->session->evaluateScript(
                'return (typeof jQuery !== "undefined") ? jQuery.active : 0;'
            );

            if ($jQueryActive === 0) {
                $this->logInfo("All AJAX requests completed");
                return;
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logWarning("Timeout waiting for AJAX requests to complete");
    }

    /**
     * {@inheritdoc}
     */
    public function elementExists(string $selector): bool
    {
        $this->logInfo(sprintf("Checking if element exists: %s", $selector));

        try {
            $element = $this->session->getPage()->find('css', $selector);
            return $element !== null;
        } catch (\Exception $e) {
            $this->logError(sprintf("Error checking if element exists: %s", $selector), $e);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getDriverType(): string
    {
        return 'BrowserStackDriver';
    }

    /**
     * {@inheritdoc}
     */
    public function navigateBack(): void
    {
        $this->logInfo("Navigating back in browser history");

        try {
            $this->session->back();
        } catch (\Exception $e) {
            $this->logError("Error navigating back in browser", $e);
            throw new RuntimeException("Error navigating back in browser", 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function isBrowserStackSession(): bool
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function getPageTitle(): string
    {
        try {
            return $this->session->getPage()->find('css', 'title')?->getText() ??
                $this->executeScript('return document.title;') ?? '';
        } catch (\Exception $e) {
            $this->logError("Error getting page title", $e);
            return '';
        }
    }

    /**
     * {@inheritdoc}
     */
    public function wait(int $seconds): void
    {
        $this->logInfo(sprintf("Waiting for %d seconds", $seconds));
        sleep($seconds);
    }

    /**
     * {@inheritdoc}
     */
    public function waitForUrlContains(string $text, int $timeout = 30): bool
    {
        $this->logInfo(sprintf("Waiting for URL to contain: %s (timeout: %d seconds)", $text, $timeout));

        $startTime = microtime(true);
        $endTime = $startTime + $timeout;

        do {
            try {
                $currentUrl = $this->getCurrentUrl();
                if (strpos($currentUrl, $text) !== false) {
                    $this->logInfo(sprintf("URL contains '%s': %s", $text, $currentUrl));
                    return true;
                }
            } catch (\Exception $e) {
                // Ignore exceptions during polling
            }

            usleep(100000); // 100ms
        } while (microtime(true) < $endTime);

        $this->logInfo(sprintf("Timeout waiting for URL to contain: %s", $text));
        return false;
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentUrl(): string
    {
        try {
            return $this->session->getCurrentUrl();
        } catch (\Exception $e) {
            $this->logError("Error getting current URL", $e);
            throw new RuntimeException("Error getting current URL", 0, $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function hasContent(string $text): bool
    {
        $this->logInfo(sprintf("Checking if page contains text: %s", $text));

        try {
            return $this->session->getPage()->hasContent($text);
        } catch (\Exception $e) {
            $this->logError(sprintf("Error checking if page contains text: %s", $text), $e);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function waitForRedirect(int $timeout = 60, int $maxRedirects = 5, int $stabilityDuration = 2): array
    {
        $this->logInfo(sprintf(
            "Waiting for redirect chain to complete (timeout: %d seconds, max redirects: %d, stability: %d seconds)",
            $timeout,
            $maxRedirects,
            $stabilityDuration
        ));

        $startTime = microtime(true);
        $overallEndTime = $startTime + $timeout;

        // Track the redirect chain
        $redirectChain = [];
        $visitedUrls = [];
        $redirectCount = 0;
        $stableUrlStartTime = null;

        try {
            // Get initial URL
            $originalUrl = $this->getCurrentUrl();
            $redirectChain[] = [
                'url' => $originalUrl,
                'timestamp' => microtime(true),
                'index' => 0
            ];
            $visitedUrls[$originalUrl] = true;
            $currentUrl = $originalUrl;

            // Monitor for URL changes
            while (microtime(true) < $overallEndTime) {
                try {
                    $newUrl = $this->getCurrentUrl();

                    // URL has changed
                    if ($newUrl !== $currentUrl) {
                        $redirectCount++;

                        // Record the new URL in the chain
                        $redirectChain[] = [
                            'url' => $newUrl,
                            'timestamp' => microtime(true),
                            'index' => count($redirectChain)
                        ];

                        // Update tracking variables
                        $visitedUrls[$newUrl] = true;
                        $currentUrl = $newUrl;
                        $stableUrlStartTime = microtime(true);

                        // Check if max redirects reached
                        if ($redirectCount >= $maxRedirects) {
                            break;
                        }
                    } // URL is stable
                    else {
                        // If this is the first time we're seeing stability, set the start time
                        if ($stableUrlStartTime === null) {
                            $stableUrlStartTime = microtime(true);
                        }

                        // Check if URL has been stable for long enough
                        $stableDuration = microtime(true) - $stableUrlStartTime;
                        if ($stableDuration >= $stabilityDuration) {
                            break;
                        }
                    }

                    // Small pause between checks
                    usleep(100000); // 100ms

                } catch (\Exception $e) {
                    // Ignore exceptions during URL polling
                }
            }

            $totalTime = microtime(true) - $startTime;

            return [
                'original_url' => $originalUrl,
                'final_url' => $currentUrl,
                'redirect_count' => $redirectCount,
                'chain' => $redirectChain,
                'total_time' => $totalTime,
                'timeout_reached' => (microtime(true) >= $overallEndTime)
            ];

        } catch (\Exception $e) {
            $this->logError("Error waiting for redirect chain: " . $e->getMessage());
            return [
                'original_url' => $originalUrl ?? 'unknown',
                'final_url' => $currentUrl ?? 'unknown',
                'redirect_count' => $redirectCount,
                'chain' => $redirectChain,
                'error' => $e->getMessage(),
                'total_time' => microtime(true) - $startTime
            ];
        }
    }

    /**
     * Mark test as passed in BrowserStack
     *
     * @param string|null $reason Reason for passing
     * @return bool Whether the status was reported successfully
     */
    public function markTestPassed(?string $reason = null): bool
    {
        return $this->statusReporter->markTestPassed($this->session, $reason);
    }

    /**
     * Mark test as failed in BrowserStack
     *
     * @param string|null $reason Reason for failing
     * @return bool Whether the status was reported successfully
     */
    public function markTestFailed(?string $reason = null): bool
    {
        return $this->statusReporter->markTestFailed($this->session, $reason);
    }

    /**
     * Destructor
     */
    public function __destruct()
    {
        // Stop session if it's started
        if (isset($this->session) && $this->session->isStarted()) {
            try {
                $this->session->stop();
                $this->logInfo("Session stopped in destructor");
            } catch (\Exception $e) {
                $this->logWarning("Error stopping session in destructor: " . $e->getMessage());
            }
        }

        // Stop BrowserStack Local if it's running
        if (isset($this->sessionFactory)) {
            $this->sessionFactory->stopLocalTesting();
        }
    }
}
