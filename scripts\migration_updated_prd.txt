<context>
# Overview  
This document defines the plan to migrate the MalabergTest automated test framework from the legacy architecture to the new modular architecture aligned with the BrowserStack + Behat Scaffold. The migration will adopt modern Symfony DI via the behat/symfony extension, unify service interfaces, decouple contexts, standardize page objects using the FriendsOfBehat PageObjectExtension, and streamline configuration and CI pipelines.

# Core Features  
- Dependency Injection Container Refactor  
  - What: Replace the deprecated Symfony2Extension with behat/symfony extension and centralize service definitions in YAML.  
  - Why: Achieve autowiring, autoconfigure, and full Symfony 6 support.  
  - How: Update `features/bootstrap/bootstrap.php`, `config/services/*.yml` and use the new `service_container` injection.  

- Service Layer Modularization  
  - What: Define core services (BrowserService, SharedStateService, ConfigurationService, TestDataService, CacheService and others).  
  - Why: Enforce single responsibility and loose coupling across contexts and page objects.  
  - How: Implement interfaces in `src/Service/*`, register them in `config/services/core.yml`.  

- Context Layer Decomposition  
  - What: Break down monolithic contexts into domain-specific contexts (ProductContext, CartContext, UpsellContext, etc.).  
  - Why: Improve maintainability and readability of step definitions.  
  - How: Relocate classes under `src/Context`, inject required services via constructor and tag as context services.  

- Page Object Standardization  
  - What: Migrate all UI interactions into page objects under `src/Page`, implement `BasePageInterface`.  
  - Why: Encapsulate element locators and actions for reuse and clarity.  
  - How: Refactor existing code in `src/Page/*`, update contexts to call page methods and implement `verifyPage()`.  

- Configuration and Profiles Simplification  
  - What: Consolidate `behat.yml` profiles for local, CI, and BrowserStack into a single file using `%env()%`.  
  - Why: Reduce configuration drift and duplication between environments.  
  
- CI/CD Pipeline Alignment  
  - What: Update GitLab CI to use a Docker-based test image (`project-behat-test:latest`) and new commands.  
  - Why: Speed up job startup, ensure environment reproducibility, and cache dependencies effectively.  

- Documentation and Training  
  - What: Revise existing docs (`01-overview.md` through `07-compatibility.md`) to describe the new architecture and guidelines.  
  - Why: Provide clear guidance for team adoption and future maintenance.  
</context>

# Technical Architecture Migration Plan

## Current State Analysis
The MalabergTest project already has several components that align with the target architecture:

- **BrowserStack Integration**: Already configured in `behat.yml` with profiles for different environments
- **Service Layer**: Core services like `BrowserServiceInterface`, `SharedStateServiceInterface`, etc. already implemented
- **Page Objects**: Basic page object structure with `BasePage` and `BasePageInterface` already in place
- **Context Structure**: Contexts are organized in a hierarchical structure with base classes

## Target Architecture
- **Dependency Injection**: Use `behat/symfony` extension (v3+) for autowiring and autoconfigure. Bootstrap container in `features/bootstrap/bootstrap.php`.  
- **Service Layer**:  
  - `BrowserServiceInterface` → `BrowserStackBrowserService` (already implemented)
  - `SharedStateServiceInterface` → `SharedStateService` (already implemented)
  - `ConfigurationServiceInterface` → `ConfigurationService` (already implemented)
  - `TestDataServiceInterface` → `TestDataService` (already implemented)
  - `CacheServiceInterface` → `FilesystemCacheService` (already implemented)
- **Context Layer**:  
  - Classes in `src/Context`, annotated with `context.service`.  
  - Inject services via constructor; remove legacy initializers and static calls.  
  - Reset shared state in `@BeforeScenario` hooks.  
- **Page Object Layer**:  
  - `BasePageInterface` and `BasePage` in `src/Page/Base` (already implemented).  
  - Specialized page classes in `src/Page` implementing domain methods and `verifyPage()`.  
- **Configuration**:  
  - Service definitions in `config/services/*.yml` (already implemented).  
  - `behat.yml` holds `default`, `ci`, and `browserstack` profiles using `%env()%` (already implemented).  
- **CI/CD**:  
  - GitLab CI `.gitlab-ci.yml` uses Docker image `project-behat-test:latest`.  
  - Stages: `install`, `test:browserstack`, `report`; cache vendor and composer.  

## Development Roadmap  

1. Phase 1: Dependency Injection Refinement
   - Update `composer.json` to ensure all required packages are at compatible versions
   - Refine `behat/symfony` extension configuration
   - Update bootstrap script to use the new extension
   - Validate container loading and service resolution

2. Phase 2: Service Layer Optimization
   - Review and optimize existing service interfaces
   - Ensure all services are properly registered in `config/services/core.yml`
   - Implement any missing service methods required by the new architecture
   - Ensure BrowserStack integration is fully optimized

3. Phase 3: Context Refinement
   - Review existing contexts and ensure they follow the new architecture
   - Inject services into contexts via constructor
   - Remove any legacy initializers or static calls
   - Ensure all contexts are properly tagged as services

4. Phase 4: Page Object Standardization
   - Review existing page objects and ensure they implement `BasePageInterface`
   - Implement `verifyPage()` method in all page objects
   - Update contexts to use page objects consistently
   - Ensure page objects are properly registered as services

5. Phase 5: Configuration Cleanup
   - Review and optimize `behat.yml` profiles
   - Ensure all environment variables are properly documented
   - Update `.env.example` with all required variables

6. Phase 6: CI Pipeline Update
   - Create Docker image for test execution
   - Update `.gitlab-ci.yml` to use the Docker image
   - Configure caching, artifacts, and reports

7. Phase 7: Documentation Update
   - Create comprehensive documentation for the new architecture
   - Document all services, contexts, and page objects
   - Provide examples of how to use the framework

## Logical Dependency Chain
1. Composer dependencies and package compatibility
2. Bootstrap & DI setup
3. Service layer optimization
4. Context refinement
5. Page object standardization
6. Configuration cleanup
7. CI pipeline update
8. Documentation

## Risks and Mitigations
- **Regression in Tests**: Run smoke tests after each phase; maintain a branch for migration.
- **Learning Curve**: Provide code samples and training; document common patterns.
- **Service Injection Errors**: Leverage autowire and validate container at bootstrap.
- **Flaky Tests**: Standardize waits in page objects; capture screenshots on failures.
- **Configuration Drift**: Keep `.env.example` and YAML under version control; review diffs.

## Specific Implementation Tasks

### 1. Composer Dependencies Update
```json
{
  "require": {
    "php": "^8.2",
    "behat/symfony-extension": "^3.0",
    "friends-of-behat/mink-extension": "^2.7",
    "friends-of-behat/page-object-extension": "^0.3.2",
    "browserstack/browserstack-local": "^1.1",
    "symfony/dependency-injection": "^6.4",
    "symfony/config": "^6.4",
    "symfony/yaml": "^6.4"
  }
}
```

### 2. Bootstrap Script Update
```php
// features/bootstrap/bootstrap.php
require_once __DIR__ . '/../../vendor/autoload.php';

// Load environment variables
$dotenv = new \Symfony\Component\Dotenv\Dotenv();
$dotenv->load(__DIR__ . '/../../.env');

// Set up container parameters
$projectRoot = dirname(dirname(__DIR__));
putenv("APP_PROJECT_ROOT={$projectRoot}");
```

### 3. Behat Configuration Update
```yaml
# behat.yml
default:
  extensions:
    Behat\SymfonyExtension:
      kernel:
        bootstrap: features/bootstrap/bootstrap.php
        class: App\Kernel
        env: test
        debug: true
    FriendsOfBehat\PageObjectExtension:
      namespaces:
        page: App\Page
        element: App\Page\Element
```

### 4. Service Registration
```yaml
# config/services/core.yml
services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: true

  App\Service\Browser\BrowserServiceInterface:
    alias: App\Service\Browser\BrowserStackBrowserService

  App\Service\Browser\BrowserStackBrowserService:
    arguments:
      $screenshotsDir: '%app.screenshots_dir%'
      $logger: '@logger'
```

### 5. Context Registration
```yaml
# config/services/contexts.yml
services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: true
    tags: ['context.service']

  App\Context\:
    resource: '../../src/Context/*'
    exclude: '../../src/Context/{Base}/*'
```

### 6. Page Object Registration
```yaml
# config/services/pages.yml
services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: true

  App\Page\:
    resource: '../../src/Page/*'
    exclude: '../../src/Page/{Base,Element}/*'
    tags: ['page.service']
```

## Appendix
- Reference architecture: `project_browserstack_behat/`
- Current implementation: MalabergTest codebase
