<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Behat\Behat\Tester\Exception\PendingException;
use Behat\Step\Then;
use Behat\Step\When;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Main feature context for test execution
 */
class FeatureContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param ContainerInterface $container Service container
     * @param BrowserServiceInterface $browserService Browser service
     * @param SharedStateServiceInterface $stateService Shared state service
     */
    public function __construct(
        ContainerInterface      $container,
        BrowserServiceInterface $browserService,
        SharedStateServiceInterface $stateService
    )
    {
        parent::__construct($container);
        $this->browserService = $browserService;
        $this->stateService = $stateService;

        $this->logInfo("FeatureContext initialized with injected services");
    }

    /**
     * Create a mock browser service for testing
     *
     * @return BrowserServiceInterface
     */
    private function createMockBrowserService(): BrowserServiceInterface
    {
        return new class implements BrowserServiceInterface {
            public function elementExists(string $selector): bool
            {
                return true;
            }

            public function wait(int $seconds): void
            { /* do nothing */
            }

            public function isSessionActive(): bool
            {
                return true;
            }

            public function getDriverType(): string
            {
                return 'mock';
            }

            public function hasContent(string $text): bool
            {
                return true;
            }

            public function navigateBack(): void
            { /* do nothing */
            }

            public function getPageTitle(): string
            {
                return 'Mock Page Title';
            }

            public function waitForUrlContains(string $text, int $timeout = 30): bool
            {
                return true;
            }

            public function isBrowserStackSession(): bool
            {
                return false;
            }

            public function findElement(string $selector): ?\Behat\Mink\Element\NodeElement
            {
                return null;
            }

            public function getCurrentUrl(): string
            {
                return 'https://example.com';
            }

            public function visit(string $url): void
            { /* do nothing */
            }

            public function getSession(): \Behat\Mink\Session
            {
                throw new \RuntimeException('Not implemented');
            }

            public function waitForElement(string $selector, int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForPageToLoad(int $timeout = 30): void
            { /* do nothing */
            }

            public function takeScreenshot(string $name = null): string
            {
                return '/path/to/screenshot.png';
            }

            public function fillField(string $field, string $value): void
            { /* do nothing */
            }

            public function selectOption(string $select, string $option): void
            { /* do nothing */
            }

            public function executeScript(string $script)
            {
                return null;
            }

            public function findElements(string $selector): array
            {
                return [];
            }

            public function waitForElementVisible(string $selector, int $timeout = 30): bool
            {
                return true;
            }

            public function scrollToElement(string $selector): void
            { /* do nothing */
            }

            public function clickElement(string $selector): void
            { /* do nothing */
            }

            public function getElementText(string $selector): string
            {
                return 'Mock Text';
            }

            public function isElementVisible(string $selector): bool
            {
                return true;
            }

            public function waitForDocumentReady(int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForAjaxToComplete(int $timeout = 30): void
            { /* do nothing */
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        $environment = $scope->getEnvironment();

        // Store environment in shared state for potential use by other contexts
        $this->stateService->set('behat.environment', $environment, 'scenario');

        $this->logInfo("Gathered contexts in BeforeScenario hook");
    }

    /**
     * @Given I am on the homepage
     */
    public function iAmOnTheHomepage(): void
    {
        try {
            $baseUrl = $this->getConfigService()->getEnvironmentConfig('base_url');
            $this->browserService->visit($baseUrl);
            $this->stateService->set('page.current', 'homepage');
            $this->logInfo("Navigated to homepage");
        } catch (\Throwable $e) {
            $this->logError("Failed to navigate to homepage", $e);
            throw $e;
        }
    }

    /**
     * @When I take a screenshot named :name
     */
    public function iTakeAScreenshotNamed(string $name): void
    {
        try {
            $path = $this->browserService->takeScreenshot($name);
            $this->stateService->set('screenshot.last', $path);
            $this->logInfo("Took screenshot: $path");
        } catch (\Throwable $e) {
            $this->logError("Failed to take screenshot", $e);
            throw $e;
        }
    }

    /**
     * @Then I should see :text
     */
    public function iShouldSee(string $text): void
    {
        try {
            $page = $this->browserService->getSession()->getPage();
            $found = $page->hasContent($text);

            if (!$found) {
                $this->browserService->takeScreenshot('text_not_found_' . md5($text));
                throw new \RuntimeException("Text '$text' not found on page");
            }

            $this->logInfo("Found text: $text");
        } catch (\Throwable $e) {
            $this->logError("Failed to find text: $text", $e);
            throw $e;
        }
    }

    /**
     * @When I click on :selector
     */
    public function iClickOn(string $selector): void
    {
        try {
            $page = $this->browserService->getSession()->getPage();
            $element = $page->find('css', $selector);

            if (!$element) {
                $this->browserService->takeScreenshot('element_not_found_' . md5($selector));
                throw new \RuntimeException("Element with selector '$selector' not found");
            }

            $element->click();
            $this->browserService->waitForPageToLoad();
            $this->logInfo("Clicked on element: $selector");
        } catch (\Throwable $e) {
            $this->logError("Failed to click on element: $selector", $e);
            throw $e;
        }
    }

    /**
     * @When I wait for :seconds second(s)
     */
    public function iWaitForSeconds(int $seconds): void
    {
        $this->logInfo("Waiting for $seconds seconds");
        sleep($seconds);
    }

    /**
     * @When I wait for element :selector
     */
    public function iWaitForElement(string $selector): void
    {
        try {
            $this->browserService->waitForElement($selector);
            $this->logInfo("Element found: $selector");
        } catch (\Throwable $e) {
            $this->logError("Failed to find element: $selector", $e);
            throw $e;
        }
    }

    /**
     * @Then I should remain on the checkout page
     */
    public function iShouldRemainOnTheCheckoutPage(): void
    {
        try {
            $currentUrl = $this->browserService->getCurrentUrl();

            // Check if the URL contains checkout or if the page has checkout elements
            $isCheckoutPage = strpos($currentUrl, 'checkout') !== false ||
                $this->browserService->elementExists('#checkout-form');

            if (!$isCheckoutPage) {
                $this->browserService->takeScreenshot('not_on_checkout_page');
                throw new \RuntimeException("Not on checkout page. Current URL: $currentUrl");
            }

            $this->logInfo("Verified user remains on checkout page");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify checkout page", $e);
            throw $e;
        }
    }

    /**
     * @When I navigate back in browser
     */
    public function iNavigateBackInBrowser(): void
    {
        try {
            // Store current URL before navigating back
            $currentUrl = $this->browserService->getCurrentUrl();
            $this->stateService->set('navigation.previous_url', $currentUrl);

            // Navigate back in browser history
            $this->browserService->navigateBack();

            // Wait for page to load after navigation
            $this->browserService->waitForPageToLoad();

            $newUrl = $this->browserService->getCurrentUrl();
            $this->logInfo("Navigated back in browser from '$currentUrl' to '$newUrl'");
        } catch (\Throwable $e) {
            $this->logError("Failed to navigate back in browser", $e);
            throw $e;
        }
    }
}
