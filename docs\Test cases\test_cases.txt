Feature: E-commerce Checkout and Post-Purchase Flows

  Background: User Environment Setup
    Given the user is using a web browser in Incognito mode
    And the user has access to the store front-end at "dstest.info"
    And the user has access to the admin panel at "dstest.info/admin"
    And the user has access to the Mailtrap inbox for "staging-sites"
    And the user has access to a terminal/SSH session for running backend commands
    And the user has access to a PayPal sandbox account ("<EMAIL>")
    And the user has access to test credit card details

  Scenario: Successful One-Time Purchase using PayPal
    Given the user navigates to the product page "dstest.info/products/younger-you-skin-cream"
    When the user selects the "One-Time Purchase" option (£100.00)
    And the user clicks the "Add To Bag" button
    And the user navigates to the shopping cart page
    And the user verifies the cart contains "Younger You Skin Cream" (One-time Purchase) with a subtotal of £100.00 and shipping of £2.95
    And the user clicks the "CHECKOUT" button
    And the user fills in the customer information section with:
      | Field         | Value                                     |
      | Email         | <EMAIL>       |
      | First name    | Karolis                                   |
      | Last name     | Skripka                                   |
      | Phone number  | +***********                              |
      | Address line 1| 83 Ducie Street, aptx, aptx               |
      | Address line 2| aptx                                      |
      | City          | Manchester                                |
      | Postcode      | M1 2JQ                                    |
      | County        | Manchester                                |
      | Country       | United Kingdom                            |
    And the user ensures "Same as billing address" is selected for Shipping Address
    And the user scrolls down (clicks "Continue to shipping" implicitly)
    And the user selects the "Domestic tracked" shipping method (£2.95)
    And the user selects the "PayPal" payment method
    And the user clicks the "Complete Purchase" button
    And the user waits for the order processing to complete
    And the user is redirected to the PayPal sandbox login page
    And the user enters the PayPal email "<EMAIL>" and clicks "Next"
    And the user enters the PayPal password and clicks "Log In"
    And the user reviews the PayPal payment details (£102.95 GBP)
    And the user clicks the "Continue to Review Order" button
    And the user waits for PayPal processing
    And the user is redirected back to the store's "Congratulations! Your Order Is Complete" page
    Then the order confirmation page displays the purchased item "Younger You Skin Cream" (One-time Purchase) and the total amount £102.95
    And the user navigates to the Mailtrap inbox
    And the user finds an email for "<EMAIL>" with subject containing "Order #********** Is Confirmed"
    And the order confirmation email details match the purchase (item, total £102.95)
    And the user finds an email for "<EMAIL>" with subject containing "Welcome To Dr. Sister Skincare!"
    And the welcome email contains account login credentials (email and password)

  Scenario: Successful Subscription Purchase using Credit Card
    Given the user navigates to the product page "dstest.info/products/younger-you-skin-cream"
    When the user selects the "Subscribe & Save" option (£75.00) with frequency "Ships Every 30 Days"
    And the user clicks the "Add To Bag" button
    And the user navigates to the shopping cart page
    And the user verifies the cart contains "Younger You Skin Cream" (Subscribe & Save, every 30 days) with a total of £75.00 and free shipping
    And the user clicks the "CHECKOUT" button
    And the user fills in the customer information section with:
      | Field         | Value                                     |
      | Email         | <EMAIL>    |
      | First name    | Karolis                                   |
      | Last name     | Skripka                                   |
      | Phone number  | +***********                              |
      | Address line 1| 83 Ducie Street, aptx, aptx               |
      | Address line 2| aptx                                      |
      | City          | Manchester                                |
      | Postcode      | M1 2JQ                                    |
      | County        | Manchester                                |
      | Country       | United Kingdom                            |
    And the user ensures "Same as billing address" is selected for Shipping Address
    And the user scrolls down (clicks "Continue to shipping" implicitly)
    And the user verifies the "Domestic tracked" shipping method is selected and shows "FREE"
    And the user selects the "Debit or Credit Card" payment method
    And the user enters the following credit card details:
      | Field       | Value            |
      | Card number | **************** |
      | Exp. mm/yy  | 04 / 25          |
      | CVC         | 424              |
    And the user clicks the "Complete Purchase" button
    And the user waits for the order processing to complete
    And the user is redirected to the store's "Congratulations! Your Order Is Complete" page
    Then the order confirmation page displays the purchased item "Younger You Skin Cream" (Subscribe & Save) and the total amount £75.00
    And the user navigates to the Mailtrap inbox
    And the user finds an email for "<EMAIL>" with subject containing "Order #********** Is Confirmed"
    And the order confirmation email details match the purchase (item, total £75.00)
    And the user finds an email for "<EMAIL>" with subject containing "Your subscription has been created"
    And the subscription creation email confirms the product, frequency (30 days), next order date, and total (£75.00)
    And the user finds an email for "<EMAIL>" with subject containing "Welcome To Dr. Sister Skincare!"
    And the welcome email contains account login credentials (email and password)

  Scenario: Successfully purchase initial product and receive upsell via sales funnel using Credit Card (with frontend upsell page failure)
    Given the user is logged into the admin panel at "dstest.info/admin"
    And the user navigates to the "Sales funnel items" page under "Configuration"
    And a sales funnel item with code "demo-dsv-1" exists and is marked as an initial product
    When the user right-clicks on the "demo-dsv-1" code and opens the link in a new incognito window, navigating to "dstest.info/specials/start/demo-dsv-1"
    And the checkout page loads displaying the initial product "Dark Spot Vanish"
    And the user fills in the customer information section with:
      | Field         | Value                                           |
      | Email         | <EMAIL>     |
      | First name    | Karolis                                         |
      | Last name     | Skripka                                         |
      | Phone number  | +***********                                    |
      | Address line 1| 83 Ducie Street, aptx, aptx                     |
      | Address line 2| aptx                                            |
      | City          | Manchester                                      |
      | Postcode      | M1 2JQ                                          |
      | County        | Manchester                                      |
      | Country       | United Kingdom                                  |
    And the user ensures "Same as billing address" is selected for Shipping Address
    And the user clicks the "Continue to shipping" button
    And the user selects the "Domestic tracked" shipping method (£2.95)
    And the user selects the "Debit or Credit Card" payment method
    And the user enters the following credit card details:
      | Field       | Value            |
      | Card number | **************** |
      | Exp. mm/yy  | 04 / 27          |
      | CVC         | 424              |
    And the user clicks the "Complete Purchase" button
    And the user waits for the order processing to complete
    # Note: The video shows the redirect to the upsell page fails (ERR_CONNECTION_TIMED_OUT)
    And the user navigates back to the admin panel tab
    And the user copies the "Link to close the order" URL ("https://dstest.info/checkout/complete") from the "Sales funnel items" page
    And the user pastes the copied URL into the incognito window's address bar and navigates to it
    Then the "Congratulations! Your Order Is Complete" page is displayed
    And the "Your Order Details" section shows both the initial product "Dark Spot Vanish" (£89.00) and the upsell product "Relax + Restore" (£10.00)
    And the final Order total is £101.95 (£89 + £10 + £2.95 shipping)
    And the user navigates to the Mailtrap inbox
    And the user finds an email for "<EMAIL>" with subject containing "Order #********** Is Confirmed"
    And the order confirmation email details match the purchase including both items and the total £101.95
    And the user finds an email for "<EMAIL>" with subject containing "Welcome To Dr. Sister Skincare!"
    And the welcome email contains account login credentials

  Scenario: Force complete a sales funnel order using backend command after frontend upsell page failure
    Given the user is logged into the admin panel at "dstest.info/admin"
    And the user navigates to the "Sales funnel items" page under "Configuration"
    And a sales funnel item with code "demo-dsv-1" exists and is marked as an initial product
    When the user right-clicks on the "demo-dsv-1" code and opens the link in a new incognito window
    And the checkout page loads displaying the initial product "Dark Spot Vanish"
    And the user fills in the customer information section with email "<EMAIL>" and other valid details
    And the user clicks "Continue to shipping"
    And the user selects the "Domestic tracked" shipping method (£2.95)
    And the user selects the "Debit or Credit Card" payment method
    And the user enters valid credit card details (e.g., 4242..., 02/42, 424)
    And the user clicks the "Complete Purchase" button
    And the user waits for order processing
    # Note: The video shows the redirect to the upsell page fails (ERR_CONNECTION_TIMED_OUT)
    And the user switches to the terminal/SSH window
    And the user executes the command `.../bin/console app:sales-funnel:complete-payments DSS --last-updated-before=-1second -e prod -v`
    And the command output indicates "Order number #0000003091 created, and payment state marked as: paid"
    And the user navigates to the "Orders" page in the admin panel
    Then the order list shows order #0000003091 for "<EMAIL>"
    And the order status is "New", Payment status is "Paid", Shipping status is "Ready"
    And the order total is £91.95 (initial product + shipping, no upsell added)
    And the user navigates to the Mailtrap inbox
    And the user finds an email for "<EMAIL>" with subject containing "Order #0000003091 Is Confirmed"
    And the order confirmation email details match the purchase (initial item only, total £91.95)
    And the user finds an email for "<EMAIL>" with subject containing "Welcome To Dr. Sister Skincare!"

  Scenario: Abandon cart during PayPal checkout in a sales funnel and trigger abandoned cart email via backend command
    Given the user is logged into the admin panel at "dstest.info/admin"
    And the user navigates to the "Sales funnel items" page under "Configuration"
    And a sales funnel item with code "demo-dsv-1" exists and is marked as an initial product
    And an abandoned cart reminder process/script is available
    When the user right-clicks on the "demo-dsv-1" code and opens the link in a new incognito window
    And the checkout page loads displaying the initial product "Dark Spot Vanish"
    And the user fills in the customer information section with email "<EMAIL>" and other valid details
    And the user clicks "Continue to shipping"
    And the user selects the "Domestic tracked" shipping method (£2.95)
    And the user selects the "PayPal" payment method
    And the user clicks the "Complete Purchase" button
    And the user waits for order processing
    And the user is redirected to the PayPal sandbox login page
    And the user *does not* complete the PayPal payment, effectively abandoning the cart
    And the user switches to the terminal/SSH window
    And the user executes the command `.../bin/console app:abandon-resta:call-center DSS --last-updated-before=-1minute -e prod -v`
    And the command output indicates processing abandoned cart order (e.g., ID 3332)
    And the user navigates to the "Orders" page in the admin panel
    Then the order list shows order #0000003092 for "<EMAIL>"
    And the order status, payment status, and shipping status are all marked as "Cancelled"
    And the user navigates to the Mailtrap inbox
    And the user finds an email for "<EMAIL>" with subject "You left something behind | Dr. Sister Skincare"
    And the abandoned cart email displays the "Dark Spot Vanish" product and a total of £91.95, with a link/button to complete the purchase

  Scenario: Manually trigger a subscription renewal using backend command
    Given a customer with email "<EMAIL>" has an active subscription order #********** for "Younger You Skin Cream" (£75.00)
    And the initial subscription order #********** has been fulfilled and shipped
    And the user is logged into the admin panel at "dstest.info/admin"
    And a subscription renewal process/script is available
    When the user navigates to the order details page for order #**********
    And the user verifies the "Scheduled renewal" date is set
    # Optional/Debugging Step shown in video: User updates the subscription frequency directly in the database (sets frequency to 0 days for order id 3328)
    # When the user executes the SQL `UPDATE 'dstest'.'sylius_order' SET 'subscription_frequency_in_days' = '0' WHERE ('id' = '3328')`
    And the user switches to the terminal/SSH window
    And the user executes the command `.../bin/console app:reorder DSS --last-ordered-before=-1second -e prod -v`
    And the command output indicates "Order number #0000003094 created (from order number #**********)"
    And the user navigates to the "Orders" page in the admin panel
    Then a new order #0000003094 exists for the customer "<EMAIL>"
    And the new order #0000003094 shows the product "Younger You Skin Cream" (Subscribe & Save) with a total of £75.00
    And the new order details indicate it was "Created from subscription: #**********"
    # Observation: The video shows the 'Subscription?' flag on the new order is 'No', which might be unexpected.
    And the user navigates to the Mailtrap inbox
    And the user finds an email for "<EMAIL>" with subject containing "Order #0000003094 Is Confirmed"
    And the order confirmation email details match the renewal purchase (item, total £75.00)
    # Observation: The video shows the 'Scheduled renewal' date on the original order #3089 didn't update immediately after the script ran. This might be a separate issue or timing related.