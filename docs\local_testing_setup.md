# [DEPRECATED] Local Browser Testing Setup Guide

> **IMPORTANT**: This guide is deprecated. The framework now uses BrowserStack exclusively for browser automation.
> Please refer to the [BrowserStack Testing Setup Guide](browserstack_testing_setup.md) for the current setup
> instructions.

## Overview

This guide outlines how to set up and configure the test framework for local browser testing without BrowserStack
integration. The framework previously used Selenium WebDriver with local browser drivers for browser automation, but
this approach is no longer supported.

## Version: 1.0
**Last Updated**: 2025-03-27

## Prerequisites

### Required Software
- PHP 8.1 or higher
- Composer
- Java Runtime Environment (JRE) for Selenium
- Chrome browser

### Browser Drivers
- **ChromeDriver** (for Chrome)
- **GeckoDriver** (for Firefox)
- **Selenium Server** (optional)

## Installation Steps

### 1. ChromeDriver Setup

#### Automatic Setup (Recommended)
```bash
# Run the automatic ChromeDriver setup script
php bin/setup-webdriver.php
```

The script will:
- Detect the latest stable ChromeDriver version
- Download the appropriate version for your OS
- Extract it to `tools/webdriver` directory
- Display configuration information

#### Manual Setup (Alternative)
1. **Download ChromeDriver**:
   - Visit [ChromeDriver Downloads](https://chromedriver.chromium.org/downloads)
   - Select the version that matches your Chrome browser version
   - Download the appropriate file for your operating system

2. **Installation**:
    - **Windows**:
     - Extract the ZIP file
     - Add the extracted directory to your system PATH
     - Verify installation: `chromedriver --version`

   - **macOS/Linux**:
     - Extract the ZIP file
     - Move to a directory in your PATH: `sudo mv chromedriver /usr/local/bin/`
     - Make executable: `chmod +x /usr/local/bin/chromedriver`
     - Verify installation: `chromedriver --version`

### 2. GeckoDriver Setup (Optional)

1. **Download GeckoDriver**:
   - Visit [GeckoDriver Releases](https://github.com/mozilla/geckodriver/releases)
   - Download the appropriate version for your operating system

2. **Installation**:
   - **Windows**:
     - Extract the ZIP file
     - Add the extracted directory to your system PATH
     - Verify installation: `geckodriver --version`

   - **macOS/Linux**:
     - Extract the ZIP file
     - Move to a directory in your PATH: `sudo mv geckodriver /usr/local/bin/`
     - Make executable: `chmod +x /usr/local/bin/geckodriver`
     - Verify installation: `geckodriver --version`

### 3. Selenium Server Setup (Optional)

1. **Download Selenium Server**:
   - Visit [Selenium Downloads](https://www.selenium.dev/downloads/)
   - Download Selenium Server Standalone JAR file

2. **Running Selenium Server**:
   - Open a terminal/command prompt
   - Navigate to the directory containing the downloaded JAR file
   - Run: `java -jar selenium-server-standalone-x.xx.x.jar`
   - Server will start on port 4444
   - Verify by accessing `http://localhost:4444/wd/hub` in a browser

## Configuration

### Environment Variables

Examine existing  `.env` file in your project root to make sure that it contains the following variables:

```
# Test Configuration
TEST_BASE_URL=https://aeonstest.info
BROWSER_NAME=chrome
WEBDRIVER_HOST=http://localhost:4444/wd/hub
BUILD_NUMBER=local_build

# Mailtrap Configuration
MAILTRAP_ACCOUNT_ID=your_account_id
MAILTRAP_TOKEN=your_token
MAILTRAP_AEONS_INBOX_ID=your_inbox_id

# SSH Configuration
SSH_HOST=your_host
SSH_USER=your_user
SSH_KEY=your_private_key

# Database Configuration
DB_HOST=your_db_host
DB_PORT=3306
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
```

## Running Tests

### Basic Test Execution

```bash
# Run all tests
vendor/bin/behat

# Run specific feature
vendor/bin/behat features/purchase.feature

# Run tests with specific tag
vendor/bin/behat --tags=@smoke_one_time

# Run with specific profile
vendor/bin/behat --profile=chrome
```

### Using the Test Runner Script

```bash
# Run all products for a brand
./bin/run-tests --brand=aeons --env=stage --all-products

# Run specific product tests
./bin/run-tests --brand=aeons --env=stage --product=golden_harvest

# Run tests with specific tags
./bin/run-tests --brand=aeons --env=stage --tags=@smoke_one_time
```

## Troubleshooting

### Common Issues

1. **WebDriver Connection Errors**:
   - Ensure the WebDriver is running
   - Verify the correct WebDriver version for your browser
   - Check the WEBDRIVER_HOST environment variable
   - If using the standalone setup, run: `php bin/setup-webdriver.php`

2. **Browser Not Found**:
   - Ensure the browser is installed
   - Verify the BROWSER_NAME environment variable
   - Check path to browser executable

3. **Permission Issues**:
   - Ensure WebDriver executables have proper permissions
   - Run with appropriate privileges

4. **Port Conflicts**:
   - Check for conflicting services on port 4444
   - Change Selenium Server port with: `java -jar selenium-server-standalone-x.xx.x.jar -port 4445`

### Known Changes from BrowserStack Integration

The following components have been removed from the framework:

- BrowserStack context classes
  - `BrowserStackContext.php`
  - `BrowserStackLogsContext.php`
- BrowserStack tools
  - `verify_browserstack.php`
  - `test_browserstack_connection.php`

If you encounter any references to these components in your code, they should be updated to use the local WebDriver setup.

## Advanced Configuration

### Multiple Browser Profiles

You can define multiple browser profiles in your `behat.yml`:

```yaml
default:
  # Default configuration

chrome:
  extensions:
    Behat\MinkExtension:
      browser_name: chrome

firefox:
  extensions:
    Behat\MinkExtension:
      browser_name: firefox
```

### Headless Mode

For headless browser testing, modify your `behat.yml`:

```yaml
default:
  extensions:
    Behat\MinkExtension:
      sessions:
        selenium2:
          selenium2:
            capabilities:
              extra_capabilities:
                chromeOptions:
                  args:
                    - "--headless"
                    - "--disable-gpu"
                    - "--window-size=1920,1080"
```

## Reference Documentation

- [Behat Documentation](https://docs.behat.org/)
- [Mink Extension Documentation](https://github.com/FriendsOfBehat/MinkExtension)
- [Selenium Documentation](https://www.selenium.dev/documentation/en/)
- [ChromeDriver Documentation](https://chromedriver.chromium.org/)
- [GeckoDriver Documentation](https://github.com/mozilla/geckodriver)
