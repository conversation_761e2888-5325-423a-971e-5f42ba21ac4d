<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Data\TestDataServiceInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for product-related functionality
 */
class ProductContext extends BaseContext
{
    private PageFactoryInterface $pageFactory;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param PageFactoryInterface|null $pageFactory Page factory service
     * @param TestDataServiceInterface|null $dataService Test data service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface          $container = null,
        ?PageFactoryInterface        $pageFactory = null,
        ?TestDataServiceInterface    $dataService = null,
        ?SharedStateServiceInterface $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
            $this->dataService = $dataService ?? $container->get(TestDataServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
            $this->dataService = $dataService ?? $this->createMockDataService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $this->logInfo("ProductContext initialized");
    }

    /**
     * Create a mock page factory for testing
     *
     * @return PageFactoryInterface
     */
    private function createMockPageFactory(): PageFactoryInterface
    {
        return new class implements PageFactoryInterface {
            public function createPage(string $pageClass, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot create pages');
            }

            public function getPage(string $pageName, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot get pages');
            }

            public function hasPage(string $pageName): bool
            {
                return false;
            }
        };
    }

    /**
     * Create a mock test data service for testing
     *
     * @return TestDataServiceInterface
     */
    private function createMockDataService(): TestDataServiceInterface
    {
        return new class implements TestDataServiceInterface {
            private array $testData = [];

            public function loadTestData(string $brand, string $type, ?string $key = null): array
            {
                return [];
            }

            public function getTestData(string $type, ?string $key = null): array
            {
                return [];
            }

            public function getRandomTestData(string $type): array
            {
                return [];
            }

            public function validateTestData(string $type, array $data): bool
            {
                return true;
            }

            public function registerData(string $key, array $data): void
            {
                $this->testData[$key] = $data;
            }

            public function getData(string $key)
            {
                return $this->testData[$key] ?? null;
            }

            public function hasData(string $key): bool
            {
                return isset($this->testData[$key]);
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @Given I am viewing the product :productIdentifier
     * @Given I am on the :productIdentifier product page
     * @Given I am on the product page
     */
    public function iAmViewingTheProduct(string $productIdentifier = null): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');

            // If no identifier is provided, use the current product data
            if ($productIdentifier === null) {
                $productData = $this->stateService->get('currentProduct');
                if (!$productData) {
                    throw new \Exception('No product data loaded');
                }

                $productPage->loadWithName($productData['name'], $productData);
                $this->stateService->set('product.name', $productData['name']);
                $this->stateService->set('product.current', $productData['slug'] ?? '');
            } // Determine if identifier is a slug or name
            else if (strpos($productIdentifier, '-') !== false) {
                // Likely a slug
                $productPage->open(['slug' => $productIdentifier]);
                $this->stateService->set('product.current', $productIdentifier);
            } else {
                // Likely a product name
                $productPage->loadWithName($productIdentifier);
                $productSlug = $this->getSlugFromName($productIdentifier);
                $this->stateService->set('product.current', $productSlug);
                $this->stateService->set('product.name', $productIdentifier);
            }

            $this->stateService->set('page.current', 'product');
            $this->logInfo("Navigated to product page: " . ($productIdentifier ?? 'current product'));
        } catch (\Throwable $e) {
            $this->logError("Failed to navigate to product page: " . ($productIdentifier ?? 'current product'), $e);
            throw $e;
        }
    }

    /**
     * @When I select flavor :flavor
     * @When I select the flavor :flavor
     */
    public function iSelectFlavor(string $flavor): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $productPage->selectFlavor($flavor);

            $this->stateService->set('product.selected_flavor', $flavor);
            $this->logInfo("Selected flavor: $flavor");
        } catch (\Throwable $e) {
            $this->logError("Failed to select flavor: $flavor", $e);
            throw $e;
        }
    }

    /**
     * @When I select quantity :quantity
     * @When I select the :quantity quantity option
     * @When I select :quantity quantity
     */
    public function iSelectQuantity($quantity): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $productPage->selectQuantity($quantity);

            $this->stateService->set('product.selected_quantity', $quantity);
            $this->logInfo("Selected quantity: $quantity");
        } catch (\Throwable $e) {
            $this->logError("Failed to select quantity: $quantity", $e);
            throw $e;
        }
    }

    /**
     * @When I select purchase type :purchaseType
     * @When I select :purchaseType purchase
     * @When I select the :purchaseType purchase type
     */
    public function iSelectPurchaseType(string $purchaseType): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');

            // Map purchase type to internal value if needed
            $purchaseTypeMap = [
                'one-time' => 'one_time',
                'one time' => 'one_time',
                'One-Time Purchase' => 'one_time',
                'subscribe' => 'subscription',
                'Subscribe & Save' => 'subscription',
                'subscription' => 'subscription'
            ];

            $internalType = $purchaseTypeMap[strtolower($purchaseType)] ?? $purchaseType;
            $productPage->selectPurchaseType($purchaseType);

            $this->stateService->set('product.purchase_type', $internalType);
            $this->logInfo("Selected purchase type: $purchaseType");
        } catch (\Throwable $e) {
            $this->logError("Failed to select purchase type: $purchaseType", $e);
            throw $e;
        }
    }

    /**
     * @When I set the supply to :frequency
     */
    public function iSetTheSupplyTo(string $frequency): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $days = $productPage->selectSupplyDuration($frequency);

            // Store both display format and actual days
            $this->stateService->set('product.subscription_frequency_days', $days);
            $this->stateService->set('product.subscription_frequency_display', $frequency);

            $this->logInfo(sprintf('Set supply duration to: %s (%d days)', $frequency, $days));
        } catch (\Throwable $e) {
            $this->logError("Failed to set supply duration: $frequency", $e);
            throw $e;
        }
    }

    /**
     * @When I subscribe to product
     */
    public function iSubscribeToProduct(): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $productPage->selectPurchaseType('Subscribe & Save');

            $this->stateService->set('product.purchase_type', 'subscription');
            $this->logInfo('Selected subscription for product');
        } catch (\Throwable $e) {
            $this->logError('Failed to subscribe to product', $e);
            throw $e;
        }
    }

    /**
     * @When I add the product to cart
     * @When I click add to cart
     */
    public function iAddTheProductToCart(): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $productPage->addToCart();

            // Get current product data
            $productSlug = $this->stateService->get('product.current');
            $flavor = $this->stateService->get('product.selected_flavor');
            $quantity = $this->stateService->get('product.selected_quantity', 'scenario') ?? 1;
            $purchaseType = $this->stateService->get('product.purchase_type', 'scenario') ?? 'one_time';

            // Store cart item in shared state
            $cartItem = [
                'product' => $productSlug,
                'flavor' => $flavor,
                'quantity' => $quantity,
                'purchase_type' => $purchaseType
            ];

            // Get existing cart items or initialize empty array
            $cartItems = $this->stateService->get('cart.items', 'scenario') ?? [];
            $cartItems[] = $cartItem;

            // Update cart in shared state
            $this->stateService->set('cart.items', $cartItems);
            $this->stateService->set('cart.last_added', $cartItem);

            $this->logInfo("Added product to cart: " . json_encode($cartItem));
        } catch (\Throwable $e) {
            $this->logError("Failed to add product to cart", $e);
            throw $e;
        }
    }

    /**
     * @Then Expected sum of products should be calculated correctly
     */
    public function expectedSumOfProductsShouldBeCalculatedCorrectly(): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');

            // Get current product data
            $productData = $this->stateService->get('currentProduct');
            if (!$productData) {
                throw new \RuntimeException('No product data loaded');
            }

            // Get purchase type and quantity
            $purchaseType = $this->stateService->get('product.purchase_type') ?? 'one_time';
            $quantity = $this->stateService->get('product.selected_quantity');

            // Calculate expected price
            $expectedPrice = $this->calculateExpectedPrice($productData, $purchaseType, $quantity);

            // Get actual price from page
            $actualPrice = $productPage->getPrice();

            // Normalize prices for comparison
            $normalizedExpected = $this->normalizePriceForComparison($expectedPrice);
            $normalizedActual = $this->normalizePriceForComparison($actualPrice);

            if ($normalizedExpected !== $normalizedActual) {
                throw new \RuntimeException(
                    sprintf('Price mismatch. Expected: %s, Actual: %s', $expectedPrice, $actualPrice)
                );
            }

            $this->logInfo(sprintf('Verified price is correct: %s', $actualPrice));
        } catch (\Throwable $e) {
            $this->logError('Failed to verify product sum calculation', $e);
            throw $e;
        }
    }

    /**
     * @Then I verify product content matches configuration
     */
    public function iVerifyProductContentMatchesConfiguration(): void
    {
        try {
            $productData = $this->stateService->get('currentProduct');
            if (!$productData) {
                throw new \RuntimeException('No product data loaded');
            }

            $productPage = $this->pageFactory->getPage('ProductPage');
            $errors = [];

            // Verify product name
            if (!$productPage->hasProductName($productData['name'])) {
                $errors[] = sprintf('Product name mismatch. Expected: %s', $productData['name']);
            }

            // Verify product description
            if (isset($productData['content']['description'])) {
                if (!$productPage->hasContentText($productData['content']['description'])) {
                    $errors[] = 'Product description not found';
                }
            }

            // Verify benefits
            if (isset($productData['content']['benefits'])) {
                foreach ($productData['content']['benefits'] as $benefit) {
                    if (!$productPage->hasContentText($benefit)) {
                        $errors[] = sprintf('Benefit not found: %s', $benefit);
                    }
                }
            }

            if (!empty($errors)) {
                throw new \RuntimeException("Product content validation failed:\n" . implode("\n", $errors));
            }

            $this->logInfo('Product content verified successfully');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify product content', $e);
            throw $e;
        }
    }

    /**
     * @Then I should see the product details for :productSlug
     */
    public function iShouldSeeTheProductDetailsFor(string $productSlug): void
    {
        try {
            // Load product data
            $brand = $this->getConfigService()->getCurrentBrand();
            $productData = $this->dataService->loadTestData($brand, 'products', $productSlug);

            // Verify product details are displayed
            $productPage = $this->pageFactory->getPage('ProductPage');
            $productName = $productData['name'];

            if (!$productPage->hasProductName($productName)) {
                throw new \RuntimeException("Product name '$productName' not found on page");
            }

            // Store product data in shared state
            $this->stateService->set('product.data', $productData);

            $this->logInfo("Verified product details for: $productSlug");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify product details for: $productSlug", $e);
            throw $e;
        }
    }

    /**
     * @Then The selected flavor should be :expectedFlavor
     */
    public function theSelectedFlavorShouldBe(string $expectedFlavor): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $actualFlavor = $productPage->getSelectedFlavor();

            // If product doesn't have flavors, skip the check
            if ($actualFlavor === null && !$productPage->hasFlavorOptions()) {
                return;
            }

            if ($actualFlavor !== $expectedFlavor) {
                throw new \RuntimeException(
                    sprintf('Expected flavor "%s", but got "%s"', $expectedFlavor, $actualFlavor)
                );
            }

            $this->logInfo("Verified selected flavor is: $expectedFlavor");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify selected flavor: $expectedFlavor", $e);
            throw $e;
        }
    }

    /**
     * @Then the purchase option :expectedOption should be selected
     */
    public function verifySelectedPurchaseOption(string $expectedOption): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $selectedOption = $productPage->getSelectedPurchaseOption();

            if (stripos($selectedOption, $expectedOption) === false) {
                throw new \RuntimeException(
                    sprintf('Expected purchase option "%s" but got "%s"', $expectedOption, $selectedOption)
                );
            }

            $this->logInfo("Verified selected purchase option is: $expectedOption");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify selected purchase option: $expectedOption", $e);
            throw $e;
        }
    }

    /**
     * Calculate expected price based on product data and selections
     */
    private function calculateExpectedPrice(array $productData, string $purchaseType, $quantity): float
    {
        // Default to medium if no specific quantity selected
        if (!$quantity || !is_string($quantity)) {
            $quantity = 'medium';
        }

        // Map numeric quantity to size keys
        if (is_numeric($quantity)) {
            $quantityMap = [
                1 => 'minimum',
                3 => 'medium',
                6 => 'maximum'
            ];
            $quantity = $quantityMap[$quantity] ?? 'medium';
        }

        // Get price from product data
        $priceKey = $purchaseType === 'subscription' ? 'subscription' : 'one_time';

        if (!isset($productData['prices'][$priceKey][$quantity])) {
            throw new \RuntimeException(
                sprintf('Price not found for %s-%s', $priceKey, $quantity)
            );
        }

        return (float)$productData['prices'][$priceKey][$quantity];
    }

    /**
     * Normalize price for comparison
     */
    private function normalizePriceForComparison($price): float
    {
        if (is_string($price)) {
            // Remove currency symbols and formatting
            $price = preg_replace('/[^0-9.]/', '', $price);
        }

        return (float)$price;
    }

    /**
     * Convert product name to slug
     */
    private function getSlugFromName(string $productName): string
    {
        $mapping = [
            'Total Harmony' => 'aeons-total-harmony',
            'Ancient Roots' => 'aeons-ancient-roots',
            'Sunrise Spark' => 'aeons-sunrise-spark',
            'Sunset Soothe' => 'aeons-sunset-soothe',
            'Nature\'s Gift' => 'aeons-natures-gift',
            'Golden Harvest' => 'aeons-golden-harvest'
            // Add other products as needed
        ];

        return $mapping[$productName] ?? strtolower(str_replace(' ', '-', $productName));
    }
}
