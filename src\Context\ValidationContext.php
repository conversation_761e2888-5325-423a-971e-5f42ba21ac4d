<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Data\TestDataServiceInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for validation-related functionality
 */
class ValidationContext extends BaseContext
{
    private PageFactoryInterface $pageFactory;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;
    private ValidationServiceInterface $validationService;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param PageFactoryInterface|null $pageFactory Page factory service
     * @param TestDataServiceInterface|null $dataService Test data service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     * @param ValidationServiceInterface|null $validationService Validation service
     */
    public function __construct(
        ?ContainerInterface          $container = null,
        ?PageFactoryInterface        $pageFactory = null,
        ?TestDataServiceInterface    $dataService = null,
        ?SharedStateServiceInterface $stateService = null,
        ?ValidationServiceInterface  $validationService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->pageFactory = $pageFactory ?? $container->get(PageFactoryInterface::class);
            $this->dataService = $dataService ?? $container->get(TestDataServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
            $this->validationService = $validationService ?? $container->get(ValidationServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->pageFactory = $pageFactory ?? $this->createMockPageFactory();
            $this->dataService = $dataService ?? $this->createMockDataService();
            $this->stateService = $stateService ?? $this->createMockStateService();
            $this->validationService = $validationService ?? $this->createMockValidationService();
        }

        $this->logInfo("ValidationContext initialized");
    }

    /**
     * Create a mock page factory for testing
     *
     * @return PageFactoryInterface
     */
    private function createMockPageFactory(): PageFactoryInterface
    {
        return new class implements PageFactoryInterface {
            public function createPage(string $pageClass, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot create pages');
            }

            public function getPage(string $pageName, array $parameters = []): \App\Page\Base\BasePageInterface
            {
                throw new \RuntimeException('Mock page factory cannot get pages');
            }

            public function hasPage(string $pageName): bool
            {
                return false;
            }
        };
    }

    /**
     * Create a mock test data service for testing
     *
     * @return TestDataServiceInterface
     */
    private function createMockDataService(): TestDataServiceInterface
    {
        return new class implements TestDataServiceInterface {
            private array $testData = [];

            public function loadTestData(string $brand, string $type, ?string $key = null): array
            {
                return [];
            }

            public function getTestData(string $type, ?string $key = null): array
            {
                return [];
            }

            public function getRandomTestData(string $type): array
            {
                return [];
            }

            public function validateTestData(string $type, array $data): bool
            {
                return true;
            }

            public function registerData(string $key, array $data): void
            {
                $this->testData[$key] = $data;
            }

            public function getData(string $key)
            {
                return $this->testData[$key] ?? null;
            }

            public function hasData(string $key): bool
            {
                return isset($this->testData[$key]);
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * Create a mock validation service for testing
     *
     * @return ValidationServiceInterface
     */
    private function createMockValidationService(): ValidationServiceInterface
    {
        return new class implements ValidationServiceInterface {
            public function validateSchema(array $data, string $schema): void
            {
                // Do nothing in mock
            }

            public function validateProductData(array $data): void
            {
                // Do nothing in mock
            }

            public function validateUserData(array $data): void
            {
                // Do nothing in mock
            }

            public function validateShippingData(array $data): void
            {
                // Do nothing in mock
            }
        };
    }

    /**
     * @Then I verify product instructions contain all warnings
     */
    public function iVerifyProductInstructionsContainAllWarnings(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            if (!isset($productData['warnings'])) {
                throw new \RuntimeException('Product warnings data not found');
            }

            $productPage = $this->pageFactory->getPage('ProductPage');
            $instructionsText = $productPage->getInstructionsText();

            foreach ($productData['warnings'] as $warning) {
                if (strpos($instructionsText, $warning) === false) {
                    throw new \RuntimeException(
                        sprintf('Warning not found in instructions: %s', $warning)
                    );
                }
            }

            $this->logInfo('Verified all product warnings are present in instructions');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify product warnings', $e);
            throw $e;
        }
    }

    /**
     * Get current product data from shared state
     *
     * @return array Product data
     * @throws \RuntimeException When product data is not found
     */
    private function getCurrentProductData(): array
    {
        $productData = $this->stateService->get('product.data');

        if (!$productData) {
            throw new \RuntimeException('No product data found in shared state');
        }

        return $productData;
    }

    /**
     * @Then I verify the cart contains the correct product details
     */
    public function iVerifyTheCartContainsTheCorrectProductDetails(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            $selectedQuantity = $this->stateService->get('product.selected_quantity');
            if (!$selectedQuantity) {
                throw new \RuntimeException('Selected quantity data not found');
            }

            $selectedPurchaseOption = $this->stateService->get('product.purchase_type');
            if (!$selectedPurchaseOption) {
                throw new \RuntimeException('Selected purchase option not found');
            }

            $cartPage = $this->pageFactory->getPage('CartPage');

            // Verify product details
            $cartPage->verifyProductName($productData['name']);
            $cartPage->verifyProductQuantity($selectedQuantity);

            // Verify purchase option
            $expectedPurchaseText = $selectedPurchaseOption === 'subscription' ? 'Subscription' : 'One-time purchase';
            $cartPage->verifyPurchaseType($expectedPurchaseText);

            $this->logInfo('Verified cart contains correct product details');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify cart product details', $e);
            throw $e;
        }
    }

    /**
     * @Then I validate the order total matches expected value
     */
    public function iValidateTheOrderTotalMatchesExpectedValue(): void
    {
        try {
            $expectedTotal = $this->stateService->get('cart.expected_total');
            if (!$expectedTotal) {
                throw new \RuntimeException('Expected total not found in shared data');
            }

            $checkoutPage = $this->pageFactory->getPage('CheckoutPage');
            $actualTotal = $checkoutPage->getOrderTotal();

            if ($expectedTotal != $actualTotal) {
                throw new \RuntimeException(
                    sprintf('Expected order total %s but got %s', $expectedTotal, $actualTotal)
                );
            }

            $this->logInfo(sprintf('Validated order total matches expected value: %s', $actualTotal));
        } catch (\Throwable $e) {
            $this->logError('Failed to validate order total', $e);
            throw $e;
        }
    }

    /**
     * @Then I verify product content matches :contentKey
     */
    public function iVerifyProductContentMatches(string $contentKey): void
    {
        try {
            $productData = $this->getCurrentProductData();

            if (!isset($productData['content'][$contentKey])) {
                throw new \RuntimeException(sprintf('Content key "%s" not found in product data', $contentKey));
            }

            $expectedContent = $productData['content'][$contentKey];
            $productPage = $this->pageFactory->getPage('ProductPage');
            $actualContent = $productPage->getContentText($contentKey);

            if ($expectedContent !== $actualContent) {
                throw new \RuntimeException(
                    sprintf('Product %s does not match. Expected: "%s", Got: "%s"',
                        $contentKey,
                        $expectedContent,
                        $actualContent
                    )
                );
            }

            $this->logInfo(sprintf('Verified product content "%s" matches expected value', $contentKey));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to verify product content "%s"', $contentKey), $e);
            throw $e;
        }
    }

    /**
     * @Then I verify product badges are present:
     */
    public function iVerifyProductBadgesArePresent(array $badges): void
    {
        try {
            $productData = $this->getCurrentProductData();
            $expectedBadges = $productData['content']['badges'] ?? [];

            if ($badges !== $expectedBadges) {
                throw new \RuntimeException('Product badges do not match expected values');
            }

            $productPage = $this->pageFactory->getPage('ProductPage');
            $actualBadges = $productPage->getBadges();

            if (count($actualBadges) !== count($expectedBadges)) {
                throw new \RuntimeException(
                    sprintf('Expected %d badges, found %d', count($expectedBadges), count($actualBadges))
                );
            }

            foreach ($expectedBadges as $badge) {
                if (!in_array($badge, $actualBadges)) {
                    throw new \RuntimeException(sprintf('Badge "%s" not found on page', $badge));
                }
            }

            $this->logInfo('Verified all product badges are present');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify product badges', $e);
            throw $e;
        }
    }

    /**
     * @Then I verify product images are loaded
     */
    public function iVerifyProductImagesAreLoaded(): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $imageResults = $productPage->verifyImagesLoaded();

            $this->logInfo(sprintf('Verified %d product images are loaded', count($imageResults)));
        } catch (\Throwable $e) {
            $this->logError('Failed to verify product images', $e);
            throw $e;
        }
    }

    /**
     * @Then I verify the URL is :expectedUrl
     */
    public function iVerifyTheUrlIs(string $expectedUrl): void
    {
        try {
            $basePage = $this->pageFactory->getPage('BasePage');
            $actualUrl = $basePage->getCurrentUrl();

            if ($expectedUrl !== $actualUrl) {
                throw new \RuntimeException(
                    sprintf('Expected URL "%s", but found "%s"', $expectedUrl, $actualUrl)
                );
            }

            $this->logInfo(sprintf('Verified current URL is: %s', $expectedUrl));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to verify URL is: %s', $expectedUrl), $e);
            throw $e;
        }
    }

    /**
     * @Then I verify Google Tag Manager is present
     */
    public function iVerifyGoogleTagManagerIsPresent(): void
    {
        try {
            $basePage = $this->pageFactory->getPage('BasePage');
            $result = $basePage->verifyGoogleTagManager();

            $this->logInfo(sprintf('Verified Google Tag Manager is present (type: %s)', $result['type']));
        } catch (\Throwable $e) {
            $this->logError('Failed to verify Google Tag Manager presence', $e);
            throw $e;
        }
    }

    /**
     * @Then I verify the sum of products is calculated correctly
     */
    public function iVerifyTheSumOfProductsIsCalculatedCorrectly(): void
    {
        try {
            $productPage = $this->pageFactory->getPage('ProductPage');
            $productData = $this->getCurrentProductData();

            // Get product details from page
            $displayedTotal = $productPage->getProductTotal();
            $selectedSizeOption = $productPage->getSelectedSize();
            $quantity = $productPage->getSelectedQuantity();
            $pricingMode = $productPage->getSelectedPurchaseType();

            // Calculate expected total
            $expectedTotal = $this->calculateExpectedTotal($selectedSizeOption, $quantity, $pricingMode);

            // Compare totals
            if ($displayedTotal !== $expectedTotal) {
                throw new \RuntimeException(
                    sprintf('Displayed total %s does not match expected total %s', $displayedTotal, $expectedTotal)
                );
            }

            $this->logInfo(sprintf('Verified product total %s is calculated correctly', $displayedTotal));
        } catch (\Throwable $e) {
            $this->logError('Failed to verify product total calculation', $e);
            throw $e;
        }
    }

    /**
     * Calculate expected total based on product options
     *
     * @param string $sizeOption Selected size option
     * @param int $quantity Selected quantity
     * @param string $mode Selected pricing mode
     * @return string Expected total price
     */
    private function calculateExpectedTotal(string $sizeOption, int $quantity, string $mode): string
    {
        // Get product data from shared state
        $productData = $this->getCurrentProductData();

        // Map size option to price key
        $sizeKey = match (true) {
            strpos($sizeOption, '1') !== false => 'minimum',
            strpos($sizeOption, '3') !== false => 'medium',
            strpos($sizeOption, '6') !== false => 'maximum',
            default => 'minimum',
        };

        // Determine price type based on mode
        $priceType = strpos(strtolower($mode), 'subscription') !== false ? 'subscription' : 'one_time';

        // Get unit price
        $unitPrice = $productData['prices'][$priceType][$sizeKey] ?? 0;

        // Calculate total
        $total = $unitPrice * $quantity;

        return number_format($total, 2);
    }

    /**
     * @Then I verify the order details are correct
     */
    public function iVerifyTheOrderDetailsAreCorrect(): void
    {
        try {
            $shippingInfo = $this->stateService->get('checkout.shipping_address');

            if (empty($shippingInfo)) {
                throw new \RuntimeException('Shipping information not found in shared data');
            }

            $confirmationPage = $this->pageFactory->getPage('ConfirmationPage');
            $displayedAddress = $this->cleanupAddressText($confirmationPage->getShippingAddress());

            // Create expected address in the same format as displayed
            $expectedAddress = $this->formatExpectedAddress($shippingInfo);
            $expectedAddress = $this->cleanupAddressText($expectedAddress);

            if ($displayedAddress !== $expectedAddress) {
                throw new \RuntimeException(
                    sprintf(
                        'Shipping address mismatch. Expected: "%s", Got: "%s"',
                        $expectedAddress,
                        $displayedAddress
                    )
                );
            }

            $this->logInfo('Verified order details are correct');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify order details', $e);
            throw $e;
        }
    }

    /**
     * Clean up address text for comparison
     *
     * @param string $address Address text
     * @return string Cleaned address
     */
    private function cleanupAddressText(string $address): string
    {
        // Remove any HTML
        $address = strip_tags($address);

        // Normalize line endings
        $address = str_replace(["\r\n", "\r"], "\n", $address);

        // Remove multiple spaces
        $address = preg_replace('/\s+/', ' ', $address);

        // Clean up each line
        $lines = array_map(function ($line) {
            return trim($line);
        }, explode("\n", $address));

        // Remove empty lines
        $lines = array_filter($lines);

        // Rejoin with consistent line endings
        return implode("\n", $lines);
    }

    /**
     * Format expected address from shipping info
     *
     * @param array $shippingInfo Shipping information
     * @return string Formatted address
     */
    private function formatExpectedAddress(array $shippingInfo): string
    {
        return implode("\n", [
            "{$shippingInfo['first_name']} {$shippingInfo['last_name']}",
            $shippingInfo['phone'] ?? '',
            $shippingInfo['address1'],
            $shippingInfo['address2'] ?? '',
            "{$shippingInfo['city']}, {$shippingInfo['postcode']}",
            strtoupper($this->mapCountryCodeToName($shippingInfo['country']))
        ]);
    }

    /**
     * Map country code to country name
     *
     * @param string $countryCode ISO country code
     * @return string Country name
     */
    private function mapCountryCodeToName(string $countryCode): string
    {
        $countryMap = [
            'US' => 'United States',
            'GB' => 'United Kingdom',
            'CA' => 'Canada',
            'AU' => 'Australia',
            'DE' => 'Germany',
            'FR' => 'France',
            // Add more as needed
        ];

        return $countryMap[strtoupper($countryCode)] ?? $countryCode;
    }

    /**
     * @Then /^I see the order number$/
     */
    public function iSeeTheOrderNumber(): void
    {
        try {
            $confirmationPage = $this->pageFactory->getPage('ConfirmationPage');
            $orderNumber = $confirmationPage->getOrderNumber();

            if (empty($orderNumber)) {
                throw new \RuntimeException("Order number is not displayed");
            }

            $this->stateService->set('order.number', $orderNumber);
            $this->logInfo("Order number found: $orderNumber");
        } catch (\Throwable $e) {
            $this->logError("Failed to find order number", $e);
            throw new \RuntimeException("Failed to find order number: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Then /^I should see the order processing page$/
     */
    public function iShouldSeeTheOrderProcessingPage(): void
    {
        try {
            $confirmationPage = $this->pageFactory->getPage('ConfirmationPage');

            if (!$confirmationPage->isProcessingPageDisplayed()) {
                throw new \RuntimeException("Order processing page is not displayed");
            }

            $this->stateService->set('order.processing_displayed', true);
            $this->logInfo("Order processing page is displayed");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify order processing page", $e);
            throw new \RuntimeException("Failed to verify order processing page: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Then /^I verify the shipping cost$/
     */
    public function iVerifyTheShippingCost(): void
    {
        try {
            $expectedCost = $this->stateService->get('shipping.expected_cost');

            if (empty($expectedCost)) {
                // If no expected cost in state, get it from the test data
                $shippingData = $this->stateService->get('shipping.data');
                if (!empty($shippingData) && isset($shippingData['cost'])) {
                    $expectedCost = $shippingData['cost'];
                } else {
                    throw new \RuntimeException("Expected shipping cost not found in shared state");
                }
            }

            $checkoutPage = $this->pageFactory->getPage('CheckoutPage');
            $actualCost = $checkoutPage->getShippingCost();

            if ($expectedCost != $actualCost) {
                throw new \RuntimeException("Expected shipping cost $expectedCost, but found $actualCost");
            }

            $this->stateService->set('shipping.cost', $actualCost);
            $this->logInfo("Verified shipping cost: $actualCost");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify shipping cost", $e);
            throw new \RuntimeException("Failed to verify shipping cost: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Then /^I validate both initial and upsell products are in the order$/
     */
    public function iValidateBothInitialAndUpsellProductsAreInTheOrder(): void
    {
        try {
            $funnelData = $this->stateService->get('funnel.current_funnel');

            if (empty($funnelData)) {
                throw new \RuntimeException("Funnel data not found in shared state");
            }

            $initialProduct = $funnelData['entry']['product'] ?? null;
            $upsellProduct = $funnelData['upsell']['product'] ?? null;

            if (!$initialProduct || !$upsellProduct) {
                throw new \RuntimeException("Initial product or upsell product not specified in funnel data");
            }

            $confirmationPage = $this->pageFactory->getPage('ConfirmationPage');

            if (!$confirmationPage->hasProductInOrder($initialProduct)) {
                throw new \RuntimeException("Initial product '$initialProduct' not found in order");
            }

            if (!$confirmationPage->hasProductInOrder($upsellProduct)) {
                throw new \RuntimeException("Upsell product '$upsellProduct' not found in order");
            }

            $this->logInfo("Validated both initial and upsell products are in the order: $initialProduct and $upsellProduct");
        } catch (\Throwable $e) {
            $this->logError("Failed to validate both products in order", $e);
            throw new \RuntimeException("Failed to validate both products in order: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Then /^I validate all dietary restriction warnings are displayed$/
     */
    public function iValidateAllDietaryRestrictionWarningsAreDisplayed(): void
    {
        try {
            $productData = $this->getCurrentProductData();

            if (!isset($productData['funnel']['restrictions']['dietary']) ||
                !is_array($productData['funnel']['restrictions']['dietary'])) {
                throw new \RuntimeException("Dietary restriction data not found in product data");
            }

            $dietaryRestrictions = $productData['funnel']['restrictions']['dietary'];

            $upsellPage = $this->pageFactory->getPage('UpsellPage');

            foreach ($dietaryRestrictions as $warning) {
                if (!$upsellPage->hasRestrictionWarning($warning)) {
                    throw new \RuntimeException("Dietary restriction warning not found: $warning");
                }
            }

            $this->logInfo("Validated all dietary restriction warnings are displayed");
        } catch (\Throwable $e) {
            $this->logError("Failed to validate dietary restriction warnings", $e);
            throw new \RuntimeException("Failed to validate dietary restriction warnings: " . $e->getMessage(), 0, $e);
        }
    }

    // Removed duplicate step definition for 'I verify only one upsell product is in the order'
    // This step is now handled by SalesFunnelContext::iVerifyOnlyOneUpsellProductIsInTheOrder()

    /**
     * @When /^I verify shipping cost is updated to "([^"]*)"$/
     * @Then /^I verify the shipping cost is "([^"]*)"$/
     */
    public function iVerifyTheShippingCostIs(string $expectedCost): void
    {
        try {
            $confirmationPage = $this->pageFactory->getPage('ConfirmationPage');
            $shippingInfo = $confirmationPage->getShippingInfo();

            if (!isset($shippingInfo['cost'])) {
                throw new \RuntimeException("Shipping cost not found on confirmation page");
            }

            $actualCost = $shippingInfo['cost'];

            if ($expectedCost !== $actualCost) {
                throw new \RuntimeException("Expected shipping cost $expectedCost, but found $actualCost");
            }

            $this->stateService->set('shipping.cost', $actualCost);
            $this->logInfo("Verified shipping cost is $actualCost");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify shipping cost", $e);
            throw new \RuntimeException("Failed to verify shipping cost: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * @Then /^I should be redirected back to the merchant site after validation$/
     */
    public function iShouldBeRedirectedBackToTheMerchantSiteAfterValidation(): void
    {
        try {
            $baseUrl = $this->getConfigService()->getBaseUrl();
            $browserService = $this->getBrowserService();

            if (!$browserService->waitForUrlContains($baseUrl, 10)) {
                throw new \RuntimeException("Not redirected back to merchant site within timeout");
            }

            $this->logInfo("Verified redirection back to merchant site after validation");
        } catch (\Throwable $e) {
            $this->logError("Failed to verify redirection to merchant site after validation", $e);
            throw new \RuntimeException("Failed to verify redirection to merchant site after validation: " . $e->getMessage(), 0, $e);
        }
    }
}
