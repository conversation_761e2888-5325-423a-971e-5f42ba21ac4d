### **1\. Executive Summary**

- **Product Overview**:  
  This project focuses on testing a CRM-like e-commerce platform based on **Sylius** (a PHP-based framework built on
  Symfony). The testing framework incorporates **Behat** for BDD-style tests, **MinkExtension** for UI interactions, and
  **BrowserStack** for cross-browser and cross-device compatibility testing. The testing framework now incorporates
  robust data sharing mechanisms through SharedDataContext, with enhanced product content verification and quantity
  management features.

- **Objectives**:

    - **Backend Testing**: Validate critical workflows such as adding products to the cart, checkout flow, and order
      processing by executing predefined **Sylius commands**.
    - **UI Testing**: Ensure UI functions correctly across various devices (both mobile and desktop) through *
      *BrowserStack**.
    - **Content Verification**: Validate product information, FAQs, and dynamic content updates
    - **State Management**: Ensure proper data sharing between test steps and contexts
    - **Quantity Management**: Verify correct price updates with quantity changes
    - **Data Integrity**: Enhanced validation and caching of test data

- **Target Audience**:

    - **End Customers** using the webstore for shopping.
    - **Admin Users** managing products and processing orders.
- **Tech Stack**:
    - **Languages/Tools**: PHP/Behat, Mink, BrowserStack, Sylius for backend commands
    - **CI/CD**: GitLab CI/CD with parallel test execution
    - **Test Data Management**: YAML-based fixtures per brand
    - **Cross-browser Testing**: BrowserStack integration with self-healing capabilities
    - **State Management**: SharedDataContext singleton pattern
    - **Data Validation**: Enhanced TestDataContext with caching
    - **Price Verification**: Dedicated price comparison utilities
    - **Data Caching**: Implemented in TestDataContext for performance
    - **Content Verification**: Structured validation for product content
    - **State Management**: Enhanced SharedDataContext with cleanup hooks

___

### **2\. Product Vision**

- **Long-Term Vision**:  
  Build scalable automated test coverage that adapts to future feature expansions (e.g., API testing, mobile app
  testing), ensuring stability and consistency across various environments.

- **Alignment with Strategy**:  
  Provide comprehensive test coverage for both backend (business logic) and frontend (UI and UX) elements across
  devices, reducing risks during product updates and deployments.

- **Architecture Vision**:
    - Maintain singleton pattern for shared state
    - Implement robust data validation
    - Support dynamic content verification
    - Enable flexible quantity management

- **Test Coverage Vision**:
    - Comprehensive product content verification
    - Dynamic price updates validation
    - FAQ and product details validation
    - Cross-step data sharing

___

### **3\. Target Persona**

- **End User Personas**:

    - **Shoppers**: People purchasing products via the webstore.
    - **Admin Users**: Managing products and orders, ensuring smooth internal operations.
- **Pain Points**:

    - Ensuring a consistent checkout experience across all devices and browsers.
    - Verifying backend workflows (like order management) and ensuring they align with the UI state.

___

### **4\. Problem Statement**

The project aims to address the challenge of ensuring that **backend workflows** function correctly and consistently,
while also ensuring the **UI behaves properly** across mobile and desktop devices. The platform must be tested across
multiple environments to eliminate discrepancies.

___

### **5\. Product Goals and Success Metrics**

- **Goals**:

    - Automate core e-commerce workflows like product purchases, cart updates, and checkout processes.
    - Perform cross-browser and cross-device testing to verify consistent functionality across platforms.
- **Success Metrics (KPIs)**:

    - **Bug Detection Rate** across different devices and browsers.
    - **Test Execution Time**: Optimize tests to run efficiently across all targeted platforms.

___

### **6\. Features and Requirements**

#### **6.1 Core Features**

- **Multi-Brand Support**:
    - Separate configurations and test data for each brand:
        - Aeons Labs (aeons)
        - Dr Sister Skincare (dss)
        - Origins Diet (odd)
        - Your Pet Nutrition (ypn)
        - Apex Laboratories (apex)
    - Environment-specific configurations (staging/production)
    - Brand-specific product catalogs and pricing

- **Test Data Management**:
    - YAML-based fixtures organized by brand
    - Separate test data for different environments
    - Reusable test user profiles and payment methods
    - Product-specific configurations including variants and pricing

- **Purchase Workflows**:
    - One-time purchase flow with various payment methods
    - Subscription-based purchases with supply duration options
    - Coupon code application and validation
    - Payment method validation including expired card scenarios
    - Google Tag Manager integration verification
    - Driver type verification


- **Automated Testing** of:
    - Shopping cart workflows (e.g., adding/removing products, updating quantities).
    - Checkout process (e.g., filling customer details, confirming order completion).
    - Order management workflows, using both backend Sylius commands and UI validation.
    - Cross-device compatibility testing using **BrowserStack**.
        - Multiple purchase types (one-time, subscription)
    - Various quantity selections
    - Payment processing with different card scenarios
    - Shipping and billing information handling
    - Order confirmation validation
    - Analytics integration (Google Tag Manager)

- **Enhanced Data Management**:
    - Cached test data loading for performance
    - Normalized product slugs handling
    - Robust data validation in TestDataContext
    - Singleton pattern for shared state

- **Product Content Verification**:
    - FAQ content validation
    - Product badges and certifications
    - Subscription benefits verification
    - Price updates with quantity changes

- **Price Management**:
    - Dynamic price updates with quantity changes
    - Price normalization for consistent comparison
    - Support for different quantity options
    - Subscription vs one-time purchase pricing

- **Enhanced Product Verification**:
    - FAQ content validation
    - Product badges verification
    - Subscription benefits validation
    - Trust badges verification
    - Dynamic price updates

- **Data Management**:
    - Cached test data loading
    - Product slug normalization
    - Enhanced error handling
    - Structured data validation

- **Price Management**:
    - Dynamic quantity updates
    - Price recalculation verification
    - Subscription vs one-time pricing
    - Bundle pricing validation

#### **6.2 Nice-to-Have Features**

- **Visual Regression Testing**: Using **Percy** to catch UI inconsistencies across platforms and devices.
- **AI-Based Visual Validation**: Leveraging **OpenAI's API** for intelligent layout and visual validation in complex
  scenarios.

#### **6.3 Updated Features**

- **Data Management and Sharing**:
    - Implemented `TestDataContext` for managing brand-specific test data
    - Enhanced `SharedDataContext` for cross-context data sharing
    - Brand-specific configuration management
    - Environment-specific test data handling
- **Data Sharing Across Contexts**:
    - Implemented a **SharedDataContext** to facilitate data sharing between different Behat context classes. This
      ensures that data (like memorized product details, shipping information, order numbers) can be accessed and
      verified across multiple steps and contexts during the test execution.

___

### **7\. User Flow and Interaction**

- **Purchase Flows**:
    1. One-Time Purchase:
        - Select product and quantity
        - Add to cart
        - Optional coupon application
        - Enter shipping/billing details
        - Process payment
        - Verify order confirmation

    2. Subscription Purchase:
        - Select subscription option
        - Choose supply duration
        - Set quantity
        - Complete checkout process
        - Verify subscription details

    3. Payment Validation:
        - Verify successful payments with valid cards
        - Validate error handling for expired cards
        - Ensure proper error messaging

- **Test User Flow**:
    - Begin on the homepage, navigate to product pages, add products to the cart, proceed to checkout, and complete
      purchases.
    - Validate customer-facing interactions, such as adding products to the cart and completing orders.
    - Validate admin workflows, including order processing and product management.
- **Sylius Backend Workflow**:
    - Execute predefined Sylius commands in the backend for validating data consistency (e.g., checking orders,
      validating shipping info).

___

### **8\. Constraints and Assumptions**

- **Constraints**:

    - Testing across multiple platforms (mobile and desktop) may require optimization for execution time and stability.
    - Some mobile-specific gestures (e.g., swipe, pinch) may need separate handling.

- **Assumptions**:

    - BrowserStack offers accurate device/browser emulation, providing reliable cross-browser/device testing.
    - Sylius backend commands can be seamlessly triggered and validated within the test automation framework.

___

### **9\. Dependencies**

- **External Dependencies**:

    - **BrowserStack** for cross-browser and mobile testing.
    - **Sylius** for backend command execution, triggered through the testing framework.
- **Internal Dependencies**:
    - **Friends of Behat** extentions: MinkExtension, MinkContext, SymfonyExtension. Page Object Model.
    - **PHP-based Sylius commands** are triggered using Python subprocess or an alternative method within the testing
      suite for backend validation.

___

### **10\. Milestones and Timeline**

- **Milestones**:
    1. **Initial Setup**: Setting up the base test automation framework using Behat, Mink, and BrowserStack.
    2. **Integration of Sylius Commands**: Trigger backend commands to validate workflows programmatically.
    3. **Expand into Visual Testing**: Future expansion includes visual testing using Percy and AI-based automation
       using the OpenAI API.

___

### **11\. Risks and Mitigation Strategies**

- **Risks**:

    - **Cross-Browser Discrepancies**: UI behavior may differ across browsers.
    - **Sylius Backend Integration Issues**: API or command execution issues might cause validation problems.
- **Mitigation Strategies**:

    - Use **BrowserStack** to perform thorough cross-browser testing, identifying and addressing discrepancies.
    - Implement robust logging and error reporting for Sylius command execution to catch backend issues early.

___

### **12\. Success Criteria and Acceptance Tests**

- **Acceptance Tests**:
    - All core workflows (cart, checkout, order processing) are tested and validated across all supported browsers and
      mobile devices.
    - Backend workflows executed via Sylius commands function correctly and reflect in the UI.

___

### **13\. Future Considerations**

- **API Test Coverage**: Expand test coverage to include API testing for Sylius backend services.
- **Visual Regression Testing**: Integrate **Percy** to ensure visual consistency across platforms, catching layout
  issues automatically.
- **AI Integration**: Incorporate **OpenAI API** for intelligent layout comparison and validation in complex UI
  scenarios.

___

### **14\. Codebase Description**

#### **Project Structure**

project_root/
├── .gitlab-ci.yml # GitLab CI/CD configuration
├── behat.yml # Base Behat configuration
├── composer.json # Project dependencies
├── bin/
│ └── run-brand-tests.sh # Test execution script
├── config/
│ ├── brands/ # Brand-specific configurations
│ │ ├── aeons/
│ │ ├── dss/
│ │ ├── odd/
│ │ ├── ypn/
│ │ └── apex/
│ └── common/ # Shared configurations
├── features/
│ ├── bootstrap/
│ │ └── Context/ # Behat contexts
│ ├── fixtures/ # Test data fixtures
│ │ ├── brands/ # Brand-specific fixtures
│ │ └── common/ # Shared fixtures
│ └── .feature # Feature files
└── src/
└── Page/ # Page Objects

#### **Core Classes and Structure**

- **TestDataContext**:
    - Manages brand-specific test data
    - Loads appropriate fixtures based on current brand/environment
    - Provides methods for accessing test data across contexts

- **BrandContext**:
    - Handles brand-specific configurations
    - Manages brand-specific URLs and settings
    - Provides brand-specific helper methods

- **BasePage.php**:  
  The foundational page object class providing common methods for interacting with web elements. It includes methods
  like `open()`, `findElement()`, `clickElement()`, `enterText()`, and `waitForElementVisible()`. This class is
  essential for encapsulating generic interactions across different pages.

- **HomePage.php**:  
  Manages actions on the homepage, such as navigating to the shop by clicking the "Shop Now" button and verifying that
  the homepage loads correctly.


- **ProductPage.php**:  
  Handles interactions on the product detail page, including:

    - Selecting product variants such as size and purchase options (one-time purchase or subscription).
    - Setting product quantities.
    - Adding products to the cart.
    - Interacting with UI components like the FAQ accordion to verify its functionality.

- **CartPage.php**:  
  Manages the shopping cart functionality, including:

    - Verifying cart contents and product details.
    - Updating product quantities.
    - Applying coupon codes and verifying discounts.
    - Calculating totals, including taxes and discounts.
    - Proceeding to the checkout page.
    - Validating error messages and ensuring proper cart behavior.
- **CheckoutPage.php**:  
  Handles the entire checkout process, including:

    - Filling in shipping and billing information.
    - Selecting shipping methods.
    - Entering payment details.
    - Completing the purchase and verifying order confirmation.
    - Validating each step to ensure the checkout process functions correctly.
- **FeatureContext.php**:  
  Implements global step definitions and serves as the primary context for Behat tests. It coordinates interactions
  between different page objects and defines high-level test steps that are common across multiple scenarios.

#### **Behat Context Classes**

- **CartPageContext.php**:  
  Contains step definitions specific to cart functionality, such as:

    - Adding or removing items from the cart.
    - Applying and verifying coupon codes.
    - Updating item quantities.
    - Verifying cart totals and summaries.
- **CheckoutPageContext.php**:  
  Defines step definitions related to the checkout process, including:

    - Filling out customer information forms.
    - Selecting shipping and payment methods.
    - Verifying that the checkout steps progress correctly.
    - Ensuring order completion and confirmation messages.
- **ProductPageContext.php**:  
  Manages step definitions for interactions on the product page, such as:

    - Selecting product options and variants.
    - Interacting with UI elements.
    - Adding products to the cart from the product page.

- **ConfirmationPageContext.php**:
    - **New Context Class Added**:
        - Manages the order confirmation page steps, including:
            - Waiting for the confirmation page to load.
            - Memorizing the order number.
            - Verifying the order details are correct.

- **SharedDataContext.php**:
    - Provides a mechanism to share data between different context classes.
    - Stores shared data such as memorized product details, shipping information, and order numbers.
    - Ensures that data is accessible across different steps and contexts during the test execution.
    - Singleton pattern implementation
    - Cross-step data sharing
        - Automatic cleanup between scenarios
        - Methods: set(), get(), setMultiple(), getAll()
- **ProductPage**:
    - Enhanced price verification
    - FAQ interaction methods
    - Product content validation
    - Quantity management
    - Methods:
        - getProductName()
        - getProductSubtitle()
        - getProductDescription()
        - getProductBadges()
        - getFAQs()
        - getPricing()
        - expandFAQQuestion()

- **TestDataContext**:
    - Cache implementation
    - Product data validation
        - Normalized slug handling
        - Enhanced error handling

#### **Feature Files**

- **features/purchase.feature**:  
  Contains Gherkin scenarios that outline key user flows, including:
    - **Verify FAQ Accordion Functionality**: Checks that the FAQ accordion on the product page expands and collapses as
      expected.

___

### **15\. Test Coverage Mapping**

To ensure comprehensive test coverage, the following matrix maps PRD features to their corresponding automated tests:

| PRD Feature                                   | Automated Test Scenario                                      | Implemented In                 |
|-----------------------------------------------|--------------------------------------------------------------|--------------------------------|
| Shopping Cart Workflows                       | Scenario: Successful Purchase Flow                           | purchase.feature               |
|                                               | Scenario: Add Product to Cart with Subscription              | purchase.feature               |
|                                               | Steps in CartPageContext.php                                 |                                |
| Checkout Process                              | Scenario: Successful Purchase Flow                           | purchase.feature               |
|                                               | Steps in CheckoutPageContext.php                             |                                |
| Applying Coupon Codes                         | Scenario: Verify Cart Functionality with Coupon              | purchase.feature               |
|                                               | Steps in CartPageContext.php                                 |                                |
| UI Interactions (FAQ Accordion Functionality) | Scenario: Verify FAQ Accordion Functionality                 | purchase.feature               |
|                                               | Methods in ProductPage.php                                   |                                |
| Verifying Order Totals and Discounts          | Steps: "Then the order total should be calculated correctly" | CartPageContext.php            |
| Cross-Browser and Cross-Device Testing        | Configurations in behat.yml for BrowserStack integration     |                                |
| Backend Workflows via Sylius Commands         | To be implemented                                            | Planned in future enhancements |
| Data Sharing Across Contexts                  | Implemented via `SharedDataContext`                          | Shared across contexts         |
| Order Confirmation Verification               | Scenario: Successful One-Time Purchase with Normal Card      | `purchase.feature`             |
|                                               | Steps in `ConfirmationPageContext.php`                       |

| PRD Feature              | Automated Test Scenario                                                       | Implemented In   |
|--------------------------|-------------------------------------------------------------------------------|------------------|
| One-Time Purchase Flow   | Scenario: Successful one-time purchase with normal card                       | purchase.feature |
| Coupon Code Application  | Scenario: Successful one-time purchase with normal card and coupon            | purchase.feature |
| Payment Validation       | Scenario: Unsuccessful one-time purchase with expired card                    | purchase.feature |
| Subscription Purchase    | Scenario: Successful subscription purchase with normal card and 60 day supply | purchase.feature |
| Google Tag Manager       | Steps: "Then I verify Google Tag Manager is present"                          | All scenarios    |
| Driver Type Verification | Steps: "And I check the driver type"                                          | All scenarios    |
|

### 15. Test Coverage Mapping

[Add new test scenarios:]

| PRD Feature         | Automated Test Scenario                   | Implemented In        |
|---------------------|-------------------------------------------|-----------------------|
| Quantity Management | Scenario: Change product quantity         | purchase.feature      |
| Price Updates       | Steps: Verify price updates with quantity | ProductPage.php       |
| FAQ Validation      | Steps: Verify FAQ content                 | ProductPage.php       |
| Product Content     | Steps: Verify product content matches     | FeatureContext.php    |
| Data Sharing        | SharedDataContext implementation          | SharedDataContext.php |

### **16\. Sylius Backend Integration Enhancements**

To align the codebase with the PRD's objective of integrating Sylius backend commands:

#### **Implement Sylius Command Execution**:

- **Develop Methods for Command Execution**:  
  Use PHP's `exec()` or Symfony's `Process` component to execute Sylius backend commands from within the tests. These
  methods can be encapsulated within utility classes or integrated into existing context classes.

- **Create Backend Validation Steps**:  
  Write step definitions that trigger backend commands and verify their outcomes. Steps could include actions like
  resetting the database, generating test data, or verifying backend processes.

- **Ensure Backend and Frontend Alignment**:  
  After executing backend commands, use frontend tests to verify that the changes are reflected in the UI. This ensures
  data consistency between the backend and frontend systems.

### **17\. Project Architecture Overview**

- **Scenario Organization**:
    - Background steps optimization
    - Common setup extraction
    - Enhanced readability
    - Consistent step definitions

- **Data Management**:
    - Structured product fixtures
    - Environment-specific configs
    - Brand-specific settings
    - Enhanced error handling

- **Verification Patterns**:
    - Content validation helpers
    - Price verification utilities
    - Dynamic content checks
    - State management helpers

- **Multi-Brand Architecture**:
    - Each brand has its own configuration and test data
    - Shared components for common functionality
    - Environment-specific settings (staging/production)

- **Test Data Management**:
    - YAML fixtures organized by brand
    - Separate configurations for different environments
    - Centralized test data access through TestDataContext

- **CI/CD Integration**:
    - GitLab CI/CD pipeline with parallel execution
    - Separate jobs for each brand
    - Automatic test execution on merge requests
    - Test report generation and artifact storage


- **Shared Data Management**:
    - `SharedDataContext` to handle data sharing across contexts.
    - This allows for memorized data (e.g., product details, shipping info, order numbers) to be accessible throughout
      the test scenarios.
    - The `SharedDataContext` is injected into each context class constructor, ensuring consistent access to shared
      data.
    - Methods like `set()`, `get()`, `setMultiple()`, and `getAll()` are used to manipulate shared data.

- **Page Object Model (POM)**:
    - The codebase follows the Page Object Model pattern, where each page is represented by a class encapsulating its
      elements and interactions.

- **State Management Architecture**:
    - SharedDataContext singleton pattern
    - Automatic state cleanup between scenarios
    - Cross-context data sharing
    - Cached test data loading

- **Content Verification Framework**:
    - Structured product content validation
    - Dynamic price verification
    - FAQ content management
    - Product badges verification

- **Data Normalization Layer**:
    - Product slug normalization
    - Price format standardization
    - Content comparison utilities

- **PHP Behat contexts sharing mechanism**:
    - Each page has its own context class, and the `FeatureContext` is used to share step definitions between contexts.
      Feature context inherit other Mink Extention contexts. Other page contexts inject FeatureContext into their
      constructors, using PHP code like this:
  ```
  $this->featureContext = $environment->getContext('Features\Bootstrap\FeatureContext'); 
  ```
- **PHP Behat page object initialization**:
    - Page classes are initialized
- **PHP Behat behat.yml configuration**:
    - The `behat.yml` file is configured to:
      -
- **Use of Friends of Behat Mink extension**:
    - Detailed use of Page object extention:
    - Detailed use of MinkExtension:
      -- use of Browserstack webdriver in "Freinds of Behat" MinkExtension:
    - Detailed use of SymfonyExtension:

- **Reusability and Maintainability**:
    - By separating concerns into specific context and page classes, the codebase is more maintainable and scalable.

___

### **18\. Instructions for AI Coder Agents**

When adding new features or pages, follow these additional guidelines:

#### Adding Support for a New Brand

1. **Create Brand Configuration**:
   ```bash
   config/brands/new-brand/
   ├── prod.yml
   └── stage.yml
   ```

2. **Create Product Fixtures for new brand**:
   ```bash
   features/fixtures/brands/new-brand/
   ├── products.yml
   ├── test_data.yml
   └── discounts.yml
   ```

3. **Update CI/CD Configuration**:
    - Add new job in `.gitlab-ci.yml` for the brand
    - Configure brand-specific variables if needed

To maintain consistency and adhere to the project's architecture and coding standards, AI coder agents should follow
these guidelines when adding new features or pages:

#### Adding a New Page

0. **Create a new fixture file with product information in features/fixtures/brands/new-brand/products.yml**
1. **Create a New Page Class**:
    - In the `src/Page/` directory, create a new class representing the page (e.g., `NewPage.php`).
    - Extend `BasePage` and implement methods corresponding to the page's functionalities, reuse existing methods from
      `BasePage` when possible.
    - Add docblocks to the class and methods.
    - Use meaningful method and variable names.
    - If shared data is needed, ensure the page class's methods return the data to be stored in the `SharedDataContext`
      in the Context class (see Context Classes section).

2. **Add step definitions in FeatureContext.php**:
    - In the `Features/Bootstrap/Context/FeatureContext.php` file, add step definitions specific to the new page.
    - Inject `SharedDataContext` if data sharing is needed.

3. **Update Feature Files**:
    - Add new scenarios or steps to existing feature files, or create a new feature file under `Features/`.
    - Ensure that the steps correspond to the step definitions in your context class.

4. **Use SharedDataContext for Data Sharing**:
    - If you need to share data across contexts (e.g., memorizing information), use the `SharedDataContext`.
    - Set and get data using methods provided by `SharedDataContext`.

5. **Follow Coding Standards**:
    - Adhere to PSR-12 coding standards for PHP.
    - Write clear and informative comments.
    - Use meaningful method and variable names.
    - Add extensive docblocks, with explanations for all parameters, examples, and return values.

#### General Coding Guidelines

- **Reusability**: Implement reusable methods in page classes.
- **Error Handling**: Include appropriate exception handling and provide clear error messages.
- **Documentation**: Include docblocks for classes and methods, with explanations for all parameters, examples, and
  return values.

___

### **19\. Additional Instructions**

- **Brand-Specific Considerations**:
    - Always test against the correct environment
    - Use brand-specific test data from fixtures
    - Verify brand-specific UI elements and workflows

- **CI/CD Pipeline**:
    - Run tests against staging before production
    - Use appropriate tags for test selection
    - Monitor BrowserStack integration status

- **Test Data Management**:
    - Keep fixtures up to date with product changes
    - Maintain separate test users per environment
    - Document any brand-specific test requirements

- **Element Selectors Management**: Maintain a consistent approach for element selectors.
- **Test Data Management**: Use data tables in Gherkin steps for input data when appropriate.
- **Logging and Reporting**: Implement logging within tests for better debugging.
- **Continuous Integration (CI) Considerations**: Ensure tests can be integrated into CI pipelines.
- **Performance Optimization**: Avoid unnecessary waits or sleeps; use proper synchronization methods.
- **BrowserStack Context**:
    - Ensure that BrowserStack capabilities are correctly set up in the `behat.yml` file for each scenario.
    - Use the `browser_stack` session in your contexts to interact with BrowserStack.

### Additional Instructions for AI Agent

#### 1. State Management Patterns

- **SharedDataContext Usage**:
  ```php
  // CORRECT: Use getInstance() for singleton pattern
  SharedDataContext::getInstance()->set('key', $value);
  
  // INCORRECT: Don't create new instances
  $context = new SharedDataContext();
  ```

- **Cleanup Handling**:
  ```php
  // CORRECT: Use @BeforeScenario in SharedDataContext
  /** @BeforeScenario */
  public function cleanup(): void {
      $this->data = [];
  }
  
  // INCORRECT: Don't duplicate cleanup in FeatureContext
  public function cleanupSharedContext(): void {
      // Don't do this - cleanup is already handled
  }
  ```

#### 2. Data Validation and Caching

```php
class TestDataContext {
    // CORRECT: Implement caching
    private array $cache = [];
    
    public function getProductData(string $productSlug): array {
        if (isset($this->cache['products'][$productSlug])) {
            return $this->cache['products'][$productSlug];
        }
        // Load and cache data...
    }
    
    // CORRECT: Validate required fields
    private function validateProductData(array $data): void {
        $requiredFields = ['name', 'slug', 'prices', 'options'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new RuntimeException("Missing required field: $field");
            }
        }
    }
}
```

#### 3. Product Content Structure

```yaml
product_name:
  # Base info
  name: "Product Name"
  slug: "product-slug"

  # Extended content
  meta:
    category: "Category"
    subtitle: "Subtitle"

  content:
    description: "Description"
    badges: [ ]
    trust_badges: [ ]
    subscription_benefits: [ ]
    faq: [ ]
```

#### 4. Error Handling Best Practices

```php
// CORRECT: Specific exceptions with context
throw new RuntimeException(sprintf(
    'Product "%s" not found in test data for brand %s',
    $productSlug,
    $this->brand
));

// CORRECT: Validate configuration
protected function loadEnvironmentConfig(): void {
    if (empty($this->config['brand']) || empty($this->config['brand']['url'])) {
        throw new RuntimeException(
            sprintf('Invalid environment configuration in %s', $configFile)
        );
    }
}
```

#### 5. Page Object Pattern Updates

```php
class ProductPage {
    // CORRECT: Comprehensive getters for all content
    public function getProductName(): string
    public function getProductSubtitle(): string
    public function getProductDescription(): string
    public function getProductBadges(): array
    public function getPricing(): array
    
    // CORRECT: Structured data returns
    public function getPricing(): array {
        return [
            'one_time' => [
                'options' => [],
                'selected' => []
            ],
            'subscription' => [
                'options' => [],
                'selected' => []
            ]
        ];
    }
}
```

#### 6. Feature File Organization

```gherkin
# CORRECT: Use Background for common setup
Feature: Product Purchase

  Background:
    Given I load brand configuration
    And I load product data
    And I am on the product page
    Then I verify Google Tag Manager is present

  # CORRECT: Group related scenarios with tags
  @quantity
  Scenario: Change product quantity
    When I select "One-Time Purchase"
    And I set the quantity to "medium"
```

#### Key Lessons:

1. **State Management**:
    - Always use singleton pattern for SharedDataContext
    - Avoid duplicate cleanup mechanisms
    - Use proper initialization in constructors

2. **Data Handling**:
    - Implement caching for frequently accessed data
    - Validate data structure before use
    - Use specific exceptions with context

3. **Content Verification**:
    - Structure product content hierarchically
    - Implement comprehensive getters
    - Normalize data for comparison

4. **Test Organization**:
    - Move common setup to Background
    - Use descriptive tags for scenarios
    - Group related scenarios together

5. **Error Prevention**:
    - Validate configuration early
    - Use type hints and return types
    - Document method behavior

6. **Code Reusability**:
    - Extract common functionality to base classes
    - Use helper methods for complex operations
    - Share context through proper mechanisms

Remember: These patterns ensure maintainable, reliable tests that properly validate product content and behavior while
maintaining clean state between scenarios.
