@subscription @admin
Feature: Subscription Reorder Command
  As a system administrator
  I want to test the app:reorder command
  So that I can ensure subscription orders are renewed correctly

  Background:
    Given I load brand configuration
    And I load product data


  @smoke
  Scenario: Verify subscription renewal for basic subscription order
    Given I have a subscription order with following items:
      | product       | quantity | purchase_type |
      | total_harmony | medium   | subscription  |
    When I run the admin command "app:reorder"
    Then the command should be successful
    And a new order should be created with same subscription items
    And the new order should have same delivery address
    And the new order should have same payment method

  @regression @subscription_renewal
  Scenario: Verify subscription renewal with mixed items
    Given I have a subscription order with following items:
      | product       | quantity | purchase_type |
      | total_harmony | medium   | subscription  |
      | ancient_roots | minimum  | one_time      |
    When I run the admin command "app:reorder"
    Then the command should be successful
    And a new order should be created with subscription items only
    And the new order should not contain one-time items
    And the new order should have same delivery address
    And the new order should have same payment method