<?php

namespace App\Service\Browser\BrowserStack;

use Behat\Mink\Driver\Selenium2Driver;
use Behat\Mink\Session;
use BrowserStack\Local;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;
use RuntimeException;

/**
 * Factory for creating BrowserStack sessions
 */
class BrowserStackSessionFactory
{
    private LoggerInterface $logger;
    private ?Local $bsLocal = null;

    /**
     * Constructor
     *
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(?LoggerInterface $logger = null)
    {
        $this->logger = $logger ?? new NullLogger();
    }

    /**
     * Create a BrowserStack session
     *
     * @param array $capabilities BrowserStack capabilities
     * @return Session
     */
    public function createSession(array $capabilities): Session
    {
        $this->logger->info('Creating BrowserStack session');

        // Get BrowserStack credentials from environment
        $username = getenv('BROWSERSTACK_USERNAME');
        $accessKey = getenv('BROWSERSTACK_ACCESS_KEY');

        if (!$username || !$accessKey) {
            throw new RuntimeException('BrowserStack credentials not set. Please set BROWSERSTACK_USERNAME and BROWSERSTACK_ACCESS_KEY environment variables.');
        }

        // Start BrowserStack Local if needed
        if (isset($capabilities['browserstack-local']) && $capabilities['browserstack-local'] === 'true') {
            $this->startLocalTesting($accessKey);
        }

        // Create WebDriver URL
        $wdHost = sprintf(
            'https://%s:%<EMAIL>/wd/hub',
            $username,
            $accessKey
        );

        // Create Selenium2Driver with BrowserStack capabilities
        $driver = new Selenium2Driver(
            $capabilities['browserName'] ?? 'chrome',
            $capabilities,
            $wdHost
        );

        // Create and return session
        $session = new Session($driver);

        $this->logger->info('BrowserStack session created successfully');

        return $session;
    }

    /**
     * Start BrowserStack Local testing
     *
     * @param string $accessKey BrowserStack access key
     * @return void
     */
    private function startLocalTesting(string $accessKey): void
    {
        $this->logger->info('Starting BrowserStack Local');

        // Check if Local is already running
        if ($this->bsLocal !== null && $this->bsLocal->isRunning()) {
            $this->logger->info('BrowserStack Local is already running');
            return;
        }

        // Create Local instance
        $this->bsLocal = new Local();

        // Configure Local
        $localConfig = [
            'key' => $accessKey,
            'verbose' => true,
            'force' => true,
            'onlyAutomate' => true,
        ];

        // Start Local
        try {
            $this->bsLocal->start($localConfig);
            $this->logger->info('BrowserStack Local started successfully');
        } catch (\Exception $e) {
            $this->logger->error('Failed to start BrowserStack Local: ' . $e->getMessage());
            throw new RuntimeException('Failed to start BrowserStack Local: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Destructor
     */
    public function __destruct()
    {
        $this->stopLocalTesting();
    }

    /**
     * Stop BrowserStack Local testing
     *
     * @return void
     */
    public function stopLocalTesting(): void
    {
        if ($this->bsLocal !== null && $this->bsLocal->isRunning()) {
            $this->logger->info('Stopping BrowserStack Local');
            $this->bsLocal->stop();
            $this->logger->info('BrowserStack Local stopped successfully');
        }
    }
}
