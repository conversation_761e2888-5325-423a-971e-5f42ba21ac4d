<?php

$host = '**************';
$port = 22;

echo "Testing DNS resolution for {$host}:\n";
$ip = gethostbyname($host);
echo "IP Address: " . $ip . "\n\n";

echo "Testing SSH connection:\n";
$connection = @fsockopen($host, $port, $errno, $errstr, 10);

if (!$connection) {
    echo "Connection failed: $errstr (error $errno)\n";
} else {
    echo "Successfully connected to $host:$port\n";
    fclose($connection);
}