# Phase 2: Core Services Implementation

## Service-Oriented Test Architecture Migration

**Version:** 1.0  
**Last Updated:** 2025-04-17  
**Author:** AI Assistant

## Table of Contents

1. [Overview](#1-overview)
2. [Current Implementation Analysis](#2-current-implementation-analysis)
3. [Configuration Service](#3-configuration-service)
4. [Test Data Service](#4-test-data-service)
5. [Shared State Service](#5-shared-state-service)
6. [Validation Service](#6-validation-service)
7. [Browser Service](#7-browser-service)
8. [Service Integration](#8-service-integration)
9. [Implementation Plan](#9-implementation-plan)
10. [Testing Strategy](#10-testing-strategy)
11. [Rollback Plan](#11-rollback-plan)

---

## 1. Overview

Phase 2 focuses on implementing the core services that will replace the existing singleton-based components with
properly designed service classes. This phase builds upon the foundation established in Phase 1 and provides the
essential services that will be used throughout the test framework.

### 1.1 Objectives

- Implement the five core services defined in Phase 1
- Ensure backward compatibility with existing code
- Provide comprehensive unit tests for each service
- Document service usage patterns

### 1.2 Timeline

- Estimated duration: 2 weeks
- Dependencies: Phase 1 (Foundation Setup)

---

## 2. Current Implementation Analysis

Before implementing the new services, we need to understand the current implementation to ensure proper migration and
backward compatibility.

### 2.1 ConfigurationManager Analysis

The current `ConfigurationManager` handles:

- Loading brand-specific configurations
- Loading environment-specific configurations
- Providing access to configuration values
- Managing current brand and environment settings

Key issues:

- Direct file system access without abstraction
- Limited caching of configuration values
- No support for configuration overrides
- Inconsistent error handling

### 2.2 TestDataRegistry Analysis

The current `TestDataRegistry` handles:

- Loading test data from fixture files
- Registering runtime test data
- Providing access to test data
- Basic validation of test data structure

Key issues:

- Direct dependency on ConfigurationManager
- No separation between data loading and validation
- Limited caching strategy
- Inconsistent error handling

### 2.3 SharedDataContext Analysis

The current `SharedDataContext` handles:

- Storing and retrieving shared data between contexts
- Managing funnel-specific data
- Providing singleton access to shared state

Key issues:

- Singleton pattern creates tight coupling
- No scoping of data (scenario, feature, global)
- No event-based cleanup
- No type safety or validation

### 2.4 DataValidator Analysis

The current `DataValidator` handles:

- Validating product data structure
- Validating user data structure
- Validating shipping data structure

Key issues:

- No schema-based validation
- Hard-coded validation rules
- Limited error reporting
- No extensibility for new data types

### 2.5 Browser Interaction Analysis

The current implementation handles browser interactions through:

- Direct Mink Session usage in contexts
- Page objects with direct Mink dependencies
- No centralized screenshot or logging capabilities

Key issues:

- Inconsistent browser interaction patterns
- Duplicated wait and element finding logic
- No centralized error handling for browser interactions
- Limited support for different browser drivers

---

## 3. Configuration Service

### 3.1 Implementation Details

The `ConfigurationService` will replace the current `ConfigurationManager` with a more robust and flexible
implementation.

#### 3.1.1 Class Structure

```php
namespace App\Service\Configuration;

use App\Service\AbstractService;
use Symfony\Component\Yaml\Yaml;
use Symfony\Component\Yaml\Exception\ParseException;

class ConfigurationService extends AbstractService implements ConfigurationServiceInterface
{
    private string $configDir;
    private string $currentBrand;
    private string $currentEnvironment;
    private array $brandConfigs = [];
    private array $environmentConfigs = [];
    
    public function __construct(
        string $configDir,
        ?string $brand = null,
        ?string $environment = null,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->configDir = $configDir;
        $this->currentBrand = $brand ?? getenv('TEST_BRAND') ?? 'aeons';
        $this->currentEnvironment = $environment ?? getenv('TEST_ENV') ?? 'stage';
        
        $this->loadBrandConfig($this->currentBrand);
        $this->loadEnvironmentConfig($this->currentBrand, $this->currentEnvironment);
    }
    
    // Interface implementation methods
    
    private function loadBrandConfig(string $brand): void
    {
        // Implementation details
    }
    
    private function loadEnvironmentConfig(string $brand, string $environment): void
    {
        // Implementation details
    }
}
```

#### 3.1.2 Key Features

- **Configuration Caching**: Cache configurations to avoid repeated file system access
- **Environment Variable Support**: Support for environment variable overrides
- **Hierarchical Configuration**: Support for brand, environment, and global configurations
- **Robust Error Handling**: Comprehensive error handling with detailed logging
- **Configuration Validation**: Validate configuration structure during loading

#### 3.1.3 Backward Compatibility

To ensure backward compatibility with existing code:

1. Register the service with the same ID as the old class
2. Implement all methods from the old class
3. Maintain the same method signatures and return types

### 3.2 Unit Tests

Create comprehensive unit tests for the `ConfigurationService`:

1. Test loading valid configurations
2. Test handling invalid configurations
3. Test environment variable overrides
4. Test brand and environment switching
5. Test configuration inheritance

---

## 4. Test Data Service

### 4.1 Implementation Details

The `TestDataService` will replace the current `TestDataRegistry` with a more robust and flexible implementation.

#### 4.1.1 Class Structure

```php
namespace App\Service\Data;

use App\Service\AbstractService;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Symfony\Component\Yaml\Yaml;
use Symfony\Component\Yaml\Exception\ParseException;

class TestDataService extends AbstractService implements TestDataServiceInterface
{
    private string $fixturesDir;
    private array $testData = [];
    private array $cache = [];
    private ValidationServiceInterface $validator;
    private ConfigurationServiceInterface $configService;
    
    public function __construct(
        string $fixturesDir,
        ValidationServiceInterface $validator,
        ConfigurationServiceInterface $configService,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->fixturesDir = $fixturesDir;
        $this->validator = $validator;
        $this->configService = $configService;
    }
    
    // Interface implementation methods
    
    private function loadDataFile(string $path): array
    {
        // Implementation details
    }
    
    private function validateData(array $data, string $type): void
    {
        // Implementation details
    }
}
```

#### 4.1.2 Key Features

- **Data Caching**: Cache test data to avoid repeated file system access
- **Validation Integration**: Integrate with ValidationService for data validation
- **Multiple Data Formats**: Support for YAML, JSON, and PHP array formats
- **Brand-Specific Data**: Support for brand-specific test data
- **Runtime Data Registration**: Support for registering runtime test data

#### 4.1.3 Backward Compatibility

To ensure backward compatibility with existing code:

1. Register the service with the same ID as the old class
2. Implement all methods from the old class
3. Maintain the same method signatures and return types

### 4.2 Unit Tests

Create comprehensive unit tests for the `TestDataService`:

1. Test loading valid test data
2. Test handling invalid test data
3. Test data registration and retrieval
4. Test brand-specific data loading
5. Test data validation integration

---

## 5. Shared State Service

### 5.1 Implementation Details

The `SharedStateService` will replace the current `SharedDataContext` singleton with a proper service implementation.

#### 5.1.1 Class Structure

```php
namespace App\Service\State;

use App\Service\AbstractService;
use Behat\Behat\EventDispatcher\Event\ScenarioTested;
use Behat\Behat\EventDispatcher\Event\FeatureTested;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class SharedStateService extends AbstractService implements SharedStateServiceInterface, EventSubscriberInterface
{
    private array $scenarioState = [];
    private array $featureState = [];
    private array $globalState = [];
    
    public static function getSubscribedEvents()
    {
        return [
            ScenarioTested::AFTER => ['resetScenarioState'],
            FeatureTested::AFTER => ['resetFeatureState'],
        ];
    }
    
    // Interface implementation methods
    
    public function resetScenarioState(): void
    {
        $this->scenarioState = [];
    }
    
    public function resetFeatureState(): void
    {
        $this->featureState = [];
    }
}
```

#### 5.1.2 Key Features

- **Scoped State**: Support for scenario, feature, and global state scopes
- **Event-Based Cleanup**: Automatic cleanup of state based on Behat events
- **Type Safety**: Optional type safety for stored values
- **Namespaced Keys**: Support for namespaced keys to avoid collisions
- **State Snapshots**: Support for creating and restoring state snapshots

#### 5.1.3 Backward Compatibility

To ensure backward compatibility with existing code:

1. Create a `SharedDataContextAdapter` that uses the new service
2. Implement the singleton pattern in the adapter
3. Forward all method calls to the service

### 5.2 Unit Tests

Create comprehensive unit tests for the `SharedStateService`:

1. Test setting and getting values in different scopes
2. Test state reset functionality
3. Test event-based cleanup
4. Test namespaced keys
5. Test state snapshots

---

## 6. Validation Service

### 6.1 Implementation Details

The `ValidationService` will replace the current `DataValidator` with a more robust and flexible implementation.

#### 6.1.1 Class Structure

```php
namespace App\Service\Validation;

use App\Service\AbstractService;
use RuntimeException;

class ValidationService extends AbstractService implements ValidationServiceInterface
{
    private array $schemas = [];
    
    public function __construct(string $schemasDir = null, ?LoggerInterface $logger = null)
    {
        parent::__construct($logger);
        if ($schemasDir && is_dir($schemasDir)) {
            $this->loadSchemas($schemasDir);
        }
    }
    
    // Interface implementation methods
    
    private function loadSchemas(string $schemasDir): void
    {
        // Implementation details
    }
    
    private function validateAgainstSchema(array $data, array $schema): array
    {
        // Implementation details
        return $errors;
    }
}
```

#### 6.1.2 Key Features

- **Schema-Based Validation**: Support for JSON Schema validation
- **Custom Validation Rules**: Support for custom validation rules
- **Detailed Error Reporting**: Comprehensive error reporting with path information
- **Schema Inheritance**: Support for schema inheritance and composition
- **Schema Caching**: Cache schemas to avoid repeated file system access

#### 6.1.3 Backward Compatibility

To ensure backward compatibility with existing code:

1. Register the service with the same ID as the old class
2. Implement all methods from the old class
3. Maintain the same method signatures and return types

### 6.2 Unit Tests

Create comprehensive unit tests for the `ValidationService`:

1. Test validating against valid schemas
2. Test handling invalid schemas
3. Test custom validation rules
4. Test schema inheritance
5. Test error reporting

---

## 7. Browser Service

### 7.1 Implementation Details

The `BrowserService` will provide a centralized service for browser interactions.

#### 7.1.1 Class Structure

```php
namespace App\Service\Browser;

use App\Service\AbstractService;
use Behat\Mink\Session;
use Behat\Mink\Driver\Selenium2Driver;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\Mink\Exception\DriverException;

class BrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    
    public function __construct(
        Session $session,
        string $screenshotsDir,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->session = $session;
        $this->screenshotsDir = $screenshotsDir;
    }
    
    // Interface implementation methods
    
    private function findElement(string $selector, int $timeout = 10): ?NodeElement
    {
        // Implementation details
    }
    
    private function createScreenshotFilename(?string $name = null): string
    {
        // Implementation details
    }
}
```

#### 7.1.2 Key Features

- **Session Management**: Centralized management of Mink session
- **Element Finding**: Robust element finding with timeouts and retries
- **Screenshot Capabilities**: Comprehensive screenshot capabilities with naming and organization
- **JavaScript Execution**: Safe JavaScript execution with error handling
- **Wait Functions**: Comprehensive wait functions for various conditions

#### 7.1.3 Backward Compatibility

Since this is a new service without a direct predecessor, backward compatibility is less of a concern. However, we
should ensure that:

1. The service can be used alongside existing page objects
2. The service provides methods that match common patterns in the codebase

### 7.2 Unit Tests

Create comprehensive unit tests for the `BrowserService`:

1. Test session management
2. Test element finding
3. Test screenshot capabilities
4. Test JavaScript execution
5. Test wait functions

---

## 8. Service Integration

### 8.1 Service Dependencies

The services have the following dependencies:

1. `ConfigurationService`: No dependencies on other services
2. `TestDataService`: Depends on `ConfigurationService` and `ValidationService`
3. `SharedStateService`: No dependencies on other services
4. `ValidationService`: No dependencies on other services
5. `BrowserService`: No dependencies on other services

### 8.2 Integration Points

The services integrate with each other at the following points:

1. `TestDataService` uses `ConfigurationService` to determine brand-specific data paths
2. `TestDataService` uses `ValidationService` to validate loaded data
3. Contexts will use multiple services together to implement step definitions

### 8.3 Service Container Configuration

Update the service container configuration to reflect the dependencies:

```yaml
# config/services/core.yml
services:
  # Configuration Service
  App\Service\Configuration\ConfigurationServiceInterface:
    alias: App\Service\Configuration\ConfigurationService

  App\Service\Configuration\ConfigurationService:
    arguments:
      $configDir: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
    public: true

  # Validation Service
  App\Service\Validation\ValidationServiceInterface:
    alias: App\Service\Validation\ValidationService

  App\Service\Validation\ValidationService:
    arguments:
      $schemasDir: '%app.config_dir%/schemas'
    public: true

  # Test Data Service
  App\Service\Data\TestDataServiceInterface:
    alias: App\Service\Data\TestDataService

  App\Service\Data\TestDataService:
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
      $validator: '@App\Service\Validation\ValidationServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
    public: true

  # Shared State Service
  App\Service\State\SharedStateServiceInterface:
    alias: App\Service\State\SharedStateService

  App\Service\State\SharedStateService:
    public: true
    tags:
      - { name: kernel.event_subscriber }

  # Browser Service
  App\Service\Browser\BrowserServiceInterface:
    alias: App\Service\Browser\BrowserService

  App\Service\Browser\BrowserService:
    arguments:
      $session: '@behat.mink.default_session'
      $screenshotsDir: '%app.project_root%/screenshots'
    public: true
```

---

## 9. Implementation Plan

### 9.1 Implementation Order

Implement the services in the following order to respect dependencies:

1. `ConfigurationService`: No dependencies on other services
2. `ValidationService`: No dependencies on other services
3. `SharedStateService`: No dependencies on other services
4. `TestDataService`: Depends on `ConfigurationService` and `ValidationService`
5. `BrowserService`: No dependencies on other services

### 9.2 Step-by-Step Implementation

#### 9.2.1 Configuration Service

1. Create `src/Service/Configuration/ConfigurationService.php`
2. Implement the interface methods
3. Add caching and error handling
4. Create unit tests
5. Update service container configuration

#### 9.2.2 Validation Service

1. Create `src/Service/Validation/ValidationService.php`
2. Implement the interface methods
3. Add schema loading and validation
4. Create unit tests
5. Update service container configuration

#### 9.2.3 Shared State Service

1. Create `src/Service/State/SharedStateService.php`
2. Implement the interface methods
3. Add event subscribers for cleanup
4. Create unit tests
5. Update service container configuration
6. Create `src/Compatibility/SharedDataContextAdapter.php` for backward compatibility

#### 9.2.4 Test Data Service

1. Create `src/Service/Data/TestDataService.php`
2. Implement the interface methods
3. Add integration with `ConfigurationService` and `ValidationService`
4. Create unit tests
5. Update service container configuration

#### 9.2.5 Browser Service

1. Create `src/Service/Browser/BrowserService.php`
2. Implement the interface methods
3. Add screenshot and logging capabilities
4. Create unit tests
5. Update service container configuration

### 9.3 Detailed Task Breakdown

#### Week 1

| Day | Tasks                                                               |
|-----|---------------------------------------------------------------------|
| 1   | Analyze current implementation, create detailed implementation plan |
| 2   | Implement `ConfigurationService` and unit tests                     |
| 3   | Implement `ValidationService` and unit tests                        |
| 4   | Implement `SharedStateService` and unit tests                       |
| 5   | Create backward compatibility adapters, integration tests           |

#### Week 2

| Day | Tasks                                                     |
|-----|-----------------------------------------------------------|
| 1   | Implement `TestDataService` and unit tests                |
| 2   | Implement `BrowserService` and unit tests                 |
| 3   | Update service container configuration, integration tests |
| 4   | Create documentation and usage examples                   |
| 5   | Final testing and bug fixes                               |

---

## 10. Testing Strategy

### 10.1 Unit Testing

Create unit tests for each service to verify:

1. Core functionality works as expected
2. Edge cases are handled properly
3. Error conditions are handled gracefully
4. Backward compatibility is maintained

### 10.2 Integration Testing

Create integration tests to verify:

1. Services work together correctly
2. Dependencies are properly resolved
3. Service container configuration is correct
4. Backward compatibility adapters work correctly

### 10.3 Functional Testing

Run existing Behat tests with the new services to verify:

1. Existing functionality continues to work
2. Performance is maintained or improved
3. No regressions are introduced

### 10.4 Test Coverage

Aim for high test coverage:

1. 90%+ line coverage for core service methods
2. 100% coverage for interface methods
3. 80%+ coverage for helper methods
4. 70%+ coverage for backward compatibility adapters

---

## 11. Rollback Plan

### 11.1 Rollback Triggers

Consider rolling back if:

1. Critical functionality is broken
2. Performance is significantly degraded
3. Backward compatibility is broken
4. Integration with existing code fails

### 11.2 Rollback Process

1. Revert service container configuration to use original classes
2. Remove new service implementations
3. Verify original functionality works
4. Document issues for future resolution

### 11.3 Partial Rollback

Consider partial rollback if only specific services are problematic:

1. Identify problematic services
2. Revert only those services to original implementation
3. Keep working services in place
4. Document issues for future resolution
