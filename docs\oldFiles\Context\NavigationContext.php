<?php

namespace Features\Bootstrap\Context;

use Behat\Mink\Session;
use ElementNotFoundException;
use Features\Bootstrap\ContextManager;
use Features\Bootstrap\Page\HomePage;
use Features\Bootstrap\Page\ProductPage;
use PHPUnit\Framework\Assert;
use RuntimeException;
use Throwable;

/**
 * Handles browser navigation and verification
 */
class NavigationContext extends BaseContext
{
    private HomePage $homePage;
    private ProductPage $productPage;
    private Session $session;
    private BrandContext $brandContext;

    /**
     * @param HomePage $homePage Home page object
     * @param ProductPage $productPage Product page object
     * @param Session $session Mink session
     */
    public function __construct(
        HomePage    $homePage,
        ProductPage $productPage,
        Session     $session
    )
    {
        parent::__construct();
        $this->homePage = $homePage;
        $this->productPage = $productPage;
        $this->session = $session;
        $this->logInfo('NavigationContext initialized');
    }

    public function setContextManager(ContextManager|\Features\Bootstrap\Core\ContextManager $contextManager): void
    {
        parent::setContextManager($contextManager);
        $this->brandContext = $this->contextManager->getContext(BrandContext::class);
    }

    /**
     * Gets the current product data or throws an exception
     *
     * @return array Product data
     * @throws RuntimeException When no product data is available
     */
    private function getCurrentProductData(): array
    {
        $productData = $this->stateService->get('product.data');
        if (!$productData) {
            $this->logError('No product data loaded');
            throw new RuntimeException('No product data loaded');
        }
        return $productData;
    }

    /**
     * @Given /^I am on the home page$/
     * @throws RuntimeException
     */
    public function iAmOnTheHomePage(): void
    {
        try {
            $this->homePage->open();
            $this->waitForAjaxToComplete();
            $this->logInfo('Navigated to home page');
        } catch (Throwable $e) {
            $this->logError('Failed to open home page', $e);
            throw new RuntimeException(
                sprintf('Failed to open home page: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I am on the product page$/
     * @throws RuntimeException
     */
    public function iAmOnTheProductPage(): void
    {
        try {
            $productData = $this->getCurrentProductData();
            if (!isset($productData['name'])) {
                throw new RuntimeException('Product name not found in product data');
            }

            $this->productPage->loadWithName($productData['name'], $productData);
            $this->waitForAjaxToComplete();
            $this->logInfo(sprintf('Navigated to product page for "%s"', $productData['name']));
        } catch (ElementNotFoundException $e) {
            $this->logError('Failed to load product page', $e);
            throw new RuntimeException(
                sprintf('Failed to load product page: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (Throwable $e) {
            $this->logError('Failed to navigate to product page', $e);
            throw $e;
        }
    }

    /**
     * @Then /^I verify the URL is "([^"]*)"$/
     * @throws RuntimeException
     */
    public function iVerifyTheUrlIs(string $expectedUrl): void
    {
        try {
            $actualUrl = $this->session->getCurrentUrl();
            Assert::assertEquals(
                $expectedUrl, 
                $actualUrl, 
                sprintf("Expected URL '%s', but found '%s'", $expectedUrl, $actualUrl)
            );
            $this->logInfo(sprintf('Verified URL is "%s"', $expectedUrl));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to verify URL (expected: "%s")', $expectedUrl), $e);
            throw new RuntimeException(
                sprintf('Failed to verify URL: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I navigate back in browser$/
     * @throws RuntimeException
     */
    public function iNavigateBackInBrowser(): void
    {
        try {
            $this->session->back();
            $this->waitForAjaxToComplete();
            $this->logInfo('Navigated back in browser');
        } catch (Throwable $e) {
            $this->logError('Failed to navigate back', $e);
            throw new RuntimeException(
                sprintf('Failed to navigate back: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then /^I should be redirected to my cart$/
     * @throws RuntimeException
     */
    public function iShouldBeRedirectedToMyCart(): void
    {
        try {
            $this->waitForUrlContains('/cart');
            $this->waitForAjaxToComplete();
            $this->logInfo('Verified redirection to cart page');
        } catch (Throwable $e) {
            $this->logError('Failed to verify cart redirection', $e);
            throw new RuntimeException(
                sprintf('Failed to verify cart redirection: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Allows visiting a specific URL
     *
     * @param string $url URL to visit
     * @throws RuntimeException When navigation fails
     */
    public function visitUrl(string $url): void
    {
        try {
            $this->session->visit($url);
            $this->waitForAjaxToComplete();
            $this->logInfo(sprintf('Visited URL: %s', $url));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to visit URL: %s', $url), $e);
            throw new RuntimeException(
                sprintf('Failed to visit URL: %s - %s', $url, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Waits for URL to contain specific text
     *
     * @param string $text Text that should be in the URL
     * @param int $timeout Timeout in milliseconds
     * @throws RuntimeException When timeout occurs
     */
    protected function waitForUrlContains(string $text, int $timeout = 10000): void
    {
        try {
            $result = $this->session->wait(
                $timeout,
                sprintf("return window.location.href.includes('%s');", $text)
            );

            if (!$result) {
                throw new RuntimeException(
                    sprintf('Timeout waiting for URL to contain "%s"', $text)
                );
            }
            $this->logInfo(sprintf('URL now contains "%s"', $text));
        } catch (Throwable $e) {
            $this->logError(sprintf('Error while waiting for URL to contain "%s"', $text), $e);
            throw new RuntimeException(
                sprintf('Error while waiting for URL: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Waits for all AJAX requests to complete
     *
     * @param int $timeout Timeout in milliseconds
     * @throws RuntimeException When timeout occurs
     */
    protected function waitForAjaxToComplete(int $timeout = 10000): void
    {
        try {
            $result = $this->session->wait(
                $timeout,
                "return (typeof jQuery === 'undefined' || jQuery.active === 0);"
            );

            if (!$result) {
                throw new RuntimeException('Timeout waiting for AJAX requests to complete');
            }
        } catch (Throwable $e) {
            $this->logError('Error while waiting for AJAX', $e);
            throw new RuntimeException(
                sprintf('Error while waiting for AJAX: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Gets the base URL for the application
     *
     * @return string Base URL
     * @throws RuntimeException When base URL is not available
     */
    protected function getBaseUrl(): string
    {
        try {
            if ($this->contextManager !== null) {
                $brandContext = $this->contextManager->getContext(BrandContext::class);
                return $brandContext->getBaseUrl();
            }

            $baseUrl = getenv('TEST_BASE_URL');
            if (!$baseUrl) {
                throw new RuntimeException('TEST_BASE_URL not set');
            }

            return $baseUrl;
        } catch (Throwable $e) {
            $this->logError('Failed to get base URL', $e);
            throw new RuntimeException(
                sprintf('Failed to get base URL: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I am logged into the admin panel$/
     * @throws RuntimeException
     */
    public function iAmLoggedIntoTheAdminPanel(): void
    {
        try {
            $adminUrl = $this->getBaseUrl() . '/admin';
            $this->visitPath($adminUrl);

            // Check if we need to log in
            if ($this->session->getPage()->hasContent('Username') &&
                $this->session->getPage()->hasContent('Password')) {

                $adminUsername = getenv('ADMIN_USERNAME') ?: 'admin';
                $adminPassword = getenv('ADMIN_PASSWORD') ?: 'admin';

                $this->session->getPage()->fillField('Username', $adminUsername);
                $this->session->getPage()->fillField('Password', $adminPassword);
                $this->session->getPage()->pressButton('Login');
            }

            $this->waitForAjaxToComplete();

            // Verify we're logged in
            if (!$this->session->getPage()->hasContent('Dashboard')) {
                throw new RuntimeException('Failed to log in to admin panel');
            }

            $this->logInfo('Successfully logged into admin panel');
        } catch (Throwable $e) {
            $this->logError('Failed to log in to admin panel', $e);
            throw new RuntimeException(
                sprintf('Failed to log in to admin panel: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I navigate to the sales funnel configuration page$/
     * @throws RuntimeException
     */
    public function iNavigateToTheSalesFunnelConfigurationPage(): void
    {
        try {
            $adminUrl = $this->getBaseUrl() . '/admin/configuration/sales-funnel-items';
            $this->visitPath($adminUrl);
            $this->waitForAjaxToComplete();

            // Verify we're on the sales funnel page
            if (!$this->session->getPage()->hasContent('Sales funnel items')) {
                throw new RuntimeException('Failed to navigate to sales funnel configuration page');
            }

            $this->logInfo('Navigated to sales funnel configuration page');
        } catch (Throwable $e) {
            $this->logError('Failed to navigate to sales funnel configuration page', $e);
            throw new RuntimeException(
                sprintf('Failed to navigate to sales funnel configuration page: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I obtain the "([^"]*)" sales funnel URL$/
     * @throws RuntimeException
     * @sets funnel.url
     */
    public function iObtainTheSalesFunnelUrl(string $funnelCode): void
    {
        try {
            // Wait for the table to be visible
            $this->session->wait(5000, "return !!document.querySelector('table.table')");

            // Find the row with the funnel code
            $page = $this->session->getPage();
            $rows = $page->findAll('css', 'table.table tbody tr');

            $funnelUrl = null;
            foreach ($rows as $row) {
                if (strpos($row->getText(), $funnelCode) !== false) {
                    // Found the row, now get the URL
                    $link = $row->find('css', 'a[href*="specials/start"]');
                    if ($link) {
                        $funnelUrl = $link->getAttribute('href');
                        break;
                    }
                }
            }

            if (!$funnelUrl) {
                // If not found directly, simulate constructing the URL
                $funnelUrl = $this->getBaseUrl() . '/specials/start/' . $funnelCode;
            }

            $this->stateService->set('funnel.url', $funnelUrl);
            $this->logInfo(sprintf('Obtained sales funnel URL for code "%s": %s', $funnelCode, $funnelUrl));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to obtain sales funnel URL for code "%s"', $funnelCode), $e);
            throw new RuntimeException(
                sprintf('Failed to obtain sales funnel URL: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I navigate to the sales funnel URL$/
     * @throws RuntimeException
     */
    public function iNavigateToTheSalesFunnelUrl(): void
    {
        try {
            $funnelUrl = $this->stateService->get('funnel.url');
            if (!$funnelUrl) {
                throw new RuntimeException('Sales funnel URL not found in shared context');
            }

            $this->visitPath($funnelUrl);
            $this->waitForAjaxToComplete();
            $this->logInfo('Navigated to sales funnel URL');
        } catch (Throwable $e) {
            $this->logError('Failed to navigate to sales funnel URL', $e);
            throw new RuntimeException(
                sprintf('Failed to navigate to sales funnel URL: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given /^I am on the product page "([^"]*)"$/
     * @throws RuntimeException
     */
    public function iAmOnTheProductPageWithSlug(string $productSlug): void
    {
        try {
            $productUrl = $this->getBaseUrl() . '/products/' . $productSlug;
            $this->visitPath($productUrl);
            $this->waitForAjaxToComplete();
            $this->logInfo(sprintf('Navigated to product page with slug "%s"', $productSlug));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to navigate to product page with slug "%s"', $productSlug), $e);
            throw new RuntimeException(
                sprintf('Failed to navigate to product page with slug "%s": %s', $productSlug, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I manually navigate to the order completion page due to upsell page failure$/
     * @throws RuntimeException
     */
    public function iManuallyNavigateToTheOrderCompletionPageDueToUpsellPageFailure(): void
    {
        try {
            $completionUrl = $this->getBaseUrl() . '/checkout/complete';
            $this->visitPath($completionUrl);
            $this->waitForAjaxToComplete();

            // Verify we're on the order completion page
            if (!$this->session->getPage()->hasContent('Congratulations! Your Order Is Complete')) {
                throw new RuntimeException('Failed to navigate to order completion page');
            }

            $this->logInfo('Manually navigated to order completion page');
        } catch (Throwable $e) {
            $this->logError('Failed to navigate to order completion page', $e);
            throw new RuntimeException(
                sprintf('Failed to navigate to order completion page: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I abandon the PayPal checkout$/
     * @throws RuntimeException
     */
    public function iAbandonThePayPalCheckout(): void
    {
        try {
            // Simply navigate away from the PayPal page to abandon the checkout
            $this->session->back();
            $this->waitForAjaxToComplete();
            $this->logInfo('Abandoned PayPal checkout');
        } catch (Throwable $e) {
            $this->logError('Failed to abandon PayPal checkout', $e);
            throw new RuntimeException(
                sprintf('Failed to abandon PayPal checkout: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Helper function to visit a path
     *
     * @param string $path Path to visit
     * @throws RuntimeException When navigation fails
     */
    private function visitPath(string $path): void
    {
        try {
            $this->session->visit($path);
            $this->logInfo(sprintf('Visited path: %s', $path));
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to visit path: %s', $path), $e);
            throw new RuntimeException(
                sprintf('Failed to visit path "%s": %s', $path, $e->getMessage()),
                0,
                $e
            );
        }
    }
}