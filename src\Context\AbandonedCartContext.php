<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for abandoned cart functionality
 */
class AbandonedCartContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;

    // References to other contexts
    private ?CartContext $cartContext = null;
    private ?CheckoutContext $checkoutContext = null;
    private ?ProductContext $productContext = null;
    private ?EmailContext $emailContext = null;
    private ?DatabaseContext $databaseContext = null;

    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     * @param BrowserServiceInterface|null $browserService Browser service
     * @param TestDataServiceInterface|null $dataService Test data service
     * @param SharedStateServiceInterface|null $stateService Shared state service
     */
    public function __construct(
        ?ContainerInterface          $container = null,
        ?BrowserServiceInterface     $browserService = null,
        ?TestDataServiceInterface    $dataService = null,
        ?SharedStateServiceInterface $stateService = null
    )
    {
        parent::__construct($container);

        // Get services from container if not provided
        if ($container !== null) {
            $this->browserService = $browserService ?? $container->get(BrowserServiceInterface::class);
            $this->dataService = $dataService ?? $container->get(TestDataServiceInterface::class);
            $this->stateService = $stateService ?? $container->get(SharedStateServiceInterface::class);
        } else {
            // Create mock services if container is not available
            $this->browserService = $browserService ?? $this->createMockBrowserService();
            $this->dataService = $dataService ?? $this->createMockDataService();
            $this->stateService = $stateService ?? $this->createMockStateService();
        }

        $this->logInfo("AbandonedCartContext initialized");
    }

    /**
     * Create a mock browser service for testing
     *
     * @return BrowserServiceInterface
     */
    private function createMockBrowserService(): BrowserServiceInterface
    {
        return new class implements BrowserServiceInterface {
            public function elementExists(string $selector): bool
            {
                return true;
            }

            public function wait(int $seconds): void
            { /* do nothing */
            }

            public function isSessionActive(): bool
            {
                return true;
            }

            public function getDriverType(): string
            {
                return 'mock';
            }

            public function hasContent(string $text): bool
            {
                return true;
            }

            public function navigateBack(): void
            { /* do nothing */
            }

            public function getPageTitle(): string
            {
                return 'Mock Page Title';
            }

            public function waitForUrlContains(string $text, int $timeout = 30): bool
            {
                return true;
            }

            public function isBrowserStackSession(): bool
            {
                return false;
            }

            public function findElement(string $selector): ?\Behat\Mink\Element\NodeElement
            {
                return null;
            }

            public function getCurrentUrl(): string
            {
                return 'https://example.com';
            }

            public function visit(string $url): void
            { /* do nothing */
            }

            public function getSession(): \Behat\Mink\Session
            {
                throw new \RuntimeException('Not implemented');
            }

            public function waitForElement(string $selector, int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForPageToLoad(int $timeout = 30): void
            { /* do nothing */
            }

            public function takeScreenshot(string $name = null): string
            {
                return '/path/to/screenshot.png';
            }

            public function fillField(string $field, string $value): void
            { /* do nothing */
            }

            public function selectOption(string $select, string $option): void
            { /* do nothing */
            }

            public function executeScript(string $script)
            {
                return null;
            }

            public function findElements(string $selector): array
            {
                return [];
            }

            public function waitForElementVisible(string $selector, int $timeout = 30): bool
            {
                return true;
            }

            public function scrollToElement(string $selector): void
            { /* do nothing */
            }

            public function clickElement(string $selector): void
            { /* do nothing */
            }

            public function getElementText(string $selector): string
            {
                return 'Mock Text';
            }

            public function isElementVisible(string $selector): bool
            {
                return true;
            }

            public function waitForDocumentReady(int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForAjaxToComplete(int $timeout = 30): void
            { /* do nothing */
            }

            public function clearCookies(): void
            { /* do nothing */
            }

            public function waitForPageLoad(): void
            { /* do nothing */
            }
        };
    }

    /**
     * Create a mock test data service for testing
     *
     * @return TestDataServiceInterface
     */
    private function createMockDataService(): TestDataServiceInterface
    {
        return new class implements TestDataServiceInterface {
            private array $testData = [];

            public function loadTestData(string $brand, string $type, ?string $key = null): array
            {
                return [];
            }

            public function getTestData(string $type, ?string $key = null): array
            {
                return [];
            }

            public function getRandomTestData(string $type): array
            {
                return [];
            }

            public function validateTestData(string $type, array $data): bool
            {
                return true;
            }

            public function registerData(string $key, array $data): void
            {
                $this->testData[$key] = $data;
            }

            public function getData(string $key)
            {
                return $this->testData[$key] ?? null;
            }

            public function hasData(string $key): bool
            {
                return isset($this->testData[$key]);
            }
        };
    }

    /**
     * Create a mock shared state service for testing
     *
     * @return SharedStateServiceInterface
     */
    private function createMockStateService(): SharedStateServiceInterface
    {
        return new class implements SharedStateServiceInterface {
            private array $state = [];

            public function set(string $key, $value, string $scope = 'scenario'): void
            {
                $this->state[$scope][$key] = $value;
            }

            public function get(string $key, string $scope = 'scenario')
            {
                return $this->state[$scope][$key] ?? null;
            }

            public function has(string $key, string $scope = 'scenario'): bool
            {
                return isset($this->state[$scope][$key]);
            }

            public function getAll(string $scope = 'scenario'): array
            {
                return $this->state[$scope] ?? [];
            }

            public function reset(string $scope = 'scenario'): void
            {
                $this->state[$scope] = [];
            }
        };
    }

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        $environment = $scope->getEnvironment();

        // Get related contexts if available
        if ($environment->hasContextClass(CartContext::class)) {
            $this->cartContext = $environment->getContext(CartContext::class);
        }

        if ($environment->hasContextClass(CheckoutContext::class)) {
            $this->checkoutContext = $environment->getContext(CheckoutContext::class);
        }

        if ($environment->hasContextClass(ProductContext::class)) {
            $this->productContext = $environment->getContext(ProductContext::class);
        }

        if ($environment->hasContextClass(EmailContext::class)) {
            $this->emailContext = $environment->getContext(EmailContext::class);
        }

        if ($environment->hasContextClass(DatabaseContext::class)) {
            $this->databaseContext = $environment->getContext(DatabaseContext::class);
        }

        $this->logInfo("Gathered contexts in BeforeScenario hook");
    }

    /**
     * @Given I have an abandoned cart from :hours hours ago
     */
    public function iHaveAbandonedCartFromHoursAgo(int $hours): void
    {
        try {
            $this->iHaveAnAbandonedCart();

            $orderId = $this->stateService->get('order.number');
            if (!$orderId) {
                throw new \RuntimeException('No order number found in shared state');
            }

            $this->ensureContext('database');
            $this->databaseContext->updateOrderTimestamp($orderId, -$hours);

            $this->stateService->set('cart.abandoned_time', time() - ($hours * 3600));
            $this->logInfo(sprintf('Created abandoned cart from %d hours ago', $hours));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to create abandoned cart from %d hours ago', $hours), $e);
            throw new \RuntimeException(
                sprintf('Failed to create abandoned cart from %d hours ago: %s', $hours, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Given I have an abandoned cart
     */
    public function iHaveAnAbandonedCart(): void
    {
        try {
            $this->ensureContext('product');
            $this->ensureContext('cart');
            $this->ensureContext('checkout');

            // Navigate to product and add to cart
            $productSlug = $this->stateService->get('product.key');
            if (!$productSlug) {
                // Use a default product if none is specified
                $productSlug = 'golden_harvest';
                $this->stateService->set('product.key', $productSlug);
            }

            $this->productContext->iAmViewingTheProduct($productSlug);
            $this->productContext->iSelectQuantity(2);
            $this->productContext->iSelectPurchaseType('one_time');
            $this->productContext->iAddTheProductToCart();

            // Proceed to checkout and abandon
            $this->cartContext->iProceedToCheckout();
            $this->checkoutContext->iFillInTheShippingInformationWith('default');
            $this->abandonCheckout();

            // Get and store order ID if database context is available
            if ($this->databaseContext) {
                $orderId = $this->databaseContext->getCurrentOrderId();
                $this->stateService->set('order.number', $orderId);
            }

            $this->stateService->set('cart.abandoned_at', time());
            $this->logInfo('Created abandoned cart');
        } catch (\Throwable $e) {
            $this->logError('Failed to create abandoned cart', $e);
            throw new \RuntimeException(
                sprintf('Failed to create abandoned cart: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Ensure a specific context is available
     *
     * @param string $contextType Context type to check
     * @throws \RuntimeException When required context is not available
     */
    private function ensureContext(string $contextType): void
    {
        switch ($contextType) {
            case 'cart':
                if (!$this->cartContext) {
                    throw new \RuntimeException('CartContext not available');
                }
                break;

            case 'checkout':
                if (!$this->checkoutContext) {
                    throw new \RuntimeException('CheckoutContext not available');
                }
                break;

            case 'product':
                if (!$this->productContext) {
                    throw new \RuntimeException('ProductContext not available');
                }
                break;

            case 'email':
                if (!$this->emailContext) {
                    throw new \RuntimeException('EmailContext not available');
                }
                break;

            case 'database':
                if (!$this->databaseContext) {
                    throw new \RuntimeException('DatabaseContext not available');
                }
                break;

            default:
                throw new \RuntimeException(sprintf('Unknown context type: %s', $contextType));
        }
    }

    /**
     * Abandons the current checkout
     */
    private function abandonCheckout(): void
    {
        try {
            // Navigate away from checkout to simulate abandonment
            $baseUrl = $this->getConfigService()->getEnvironmentConfig('base_url');
            $this->browserService->visit($baseUrl);

            $this->stateService->set('cart.abandoned', true);
            $this->stateService->set('cart.abandoned_at', time());

            $this->logInfo('Abandoned checkout');
        } catch (\Throwable $e) {
            $this->logError('Failed to abandon checkout', $e);
            throw new \RuntimeException(
                sprintf('Failed to abandon checkout: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When the abandoned cart cleanup job runs
     */
    public function theAbandonedCartCleanupJobRuns(): void
    {
        try {
            $this->ensureContext('database');
            $this->databaseContext->runCleanupJob();
            $this->stateService->set('cart.cleanup_job_ran_at', time());
            $this->logInfo('Ran abandoned cart cleanup job');
        } catch (\Throwable $e) {
            $this->logError('Failed to run cleanup job', $e);
            throw new \RuntimeException(
                sprintf('Failed to run cleanup job: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should see my cart with the abandoned items
     */
    public function iShouldSeeMyCartWithTheAbandonedItems(): void
    {
        try {
            $this->ensureContext('cart');

            // Get the expected number of items
            $cartItems = $this->stateService->get('cart.items') ?? [];
            $expectedCount = count($cartItems);

            if ($expectedCount === 0) {
                throw new \RuntimeException('No abandoned cart items found in shared state');
            }

            // Verify the cart contains the expected number of items
            $this->cartContext->iShouldSeeItemsInTheCart($expectedCount);

            $this->logInfo(sprintf('Verified cart contains %d abandoned items', $expectedCount));
        } catch (\Throwable $e) {
            $this->logError('Failed to verify abandoned cart items', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify abandoned cart items: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When I do the abandon cart process for :times times
     */
    public function iDoTheAbandonCartProcessForTimes(int $times): void
    {
        try {
            $this->ensureContext('product');
            $this->ensureContext('cart');
            $this->ensureContext('checkout');

            for ($i = 0; $i < $times; $i++) {
                // Add product to cart
                $this->productContext->iAddTheProductToCart();

                // Proceed to checkout
                $this->cartContext->iProceedToCheckout();

                // Fill shipping info
                $this->checkoutContext->iFillInTheShippingInformationWith('default');

                // Leave checkout
                $baseUrl = $this->getConfigService()->getEnvironmentConfig('base_url');
                $this->browserService->visit($baseUrl);

                // Wait a bit before next iteration
                if ($i < $times - 1) {
                    sleep(2);
                }
            }

            $this->stateService->set('cart.abandoned_count', $times);
            $this->stateService->set('cart.last_abandoned_at', time());

            $this->logInfo(sprintf('Abandoned cart process completed %d times', $times));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to complete abandon cart process %d times', $times), $e);
            throw new \RuntimeException(
                sprintf('Failed to complete abandon cart process %d times: %s', $times, $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @Then I should receive a priority abandoned cart email within :minutes minutes
     */
    public function iShouldReceiveAPriorityAbandonedCartEmailWithinMinutes(int $minutes): void
    {
        try {
            $this->ensureContext('email');

            $userEmail = $this->getUserEmail();
            $email = $this->waitForPriorityEmail($userEmail, $minutes);

            if (!$email) {
                throw new \RuntimeException(
                    sprintf('Priority abandoned cart email not received within %d minutes', $minutes)
                );
            }

            $this->stateService->set('email.priority', $email);
            $this->stateService->set('email.priority_received_at', time());

            $this->logInfo('Received priority abandoned cart email');
        } catch (\Throwable $e) {
            $this->logError('Failed to verify priority abandoned cart email', $e);
            throw new \RuntimeException(
                sprintf('Failed to verify priority abandoned cart email: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Get user email from shared state
     *
     * @return string User email
     * @throws \RuntimeException When user email is not found
     */
    private function getUserEmail(): string
    {
        $userData = $this->stateService->get('user.data');

        if (!$userData || !isset($userData['email'])) {
            throw new \RuntimeException('User email not found in shared state');
        }

        return $userData['email'];
    }

    /**
     * Wait for priority email
     *
     * @param string $userEmail User email
     * @param int $minutes Minutes to wait
     * @return array|null Email data or null if not found
     */
    private function waitForPriorityEmail(string $userEmail, int $minutes): ?array
    {
        $startTime = time();
        $endTime = $startTime + ($minutes * 60);

        while (time() < $endTime) {
            try {
                $email = $this->emailContext->waitForEmail(
                    $userEmail,
                    'Priority: Complete your purchase',
                    60
                );

                if ($email) {
                    return $email;
                }

                sleep(10);
            } catch (\Throwable $e) {
                $this->logError('Error while waiting for priority email', $e);
                sleep(10);
            }
        }

        return null;
    }

    /**
     * @Then I should see that cart was cancelled :count times with different order ids
     */
    public function iShouldSeeThatCartWasCancelledTimesWithDifferentOrderIds(int $expectedCount): void
    {
        try {
            $this->ensureContext('database');

            // Get test user email
            $userEmail = $this->getUserEmail();

            $cancelledCount = $this->databaseContext->getCancelledOrderCount($userEmail);
            if ($cancelledCount !== $expectedCount) {
                throw new \RuntimeException(
                    sprintf('Expected %d cancelled orders, but found %d', $expectedCount, $cancelledCount)
                );
            }

            // Verify orders have different IDs
            $orders = $this->databaseContext->getOrdersByEmail($userEmail, 'cancelled');
            $orderIds = array_column($orders, 'number');
            $uniqueOrderIds = array_unique($orderIds);

            if (count($uniqueOrderIds) !== $expectedCount) {
                throw new \RuntimeException(
                    sprintf('Expected %d unique order IDs, but found %d', $expectedCount, count($uniqueOrderIds))
                );
            }

            $this->logInfo(sprintf('Verified %d cancelled orders with unique IDs', $expectedCount));
        } catch (\Throwable $e) {
            $this->logError(sprintf('Failed to verify %d cancelled orders', $expectedCount), $e);
            throw new \RuntimeException(
                sprintf('Failed to verify %d cancelled orders: %s', $expectedCount, $e->getMessage()),
                0,
                $e
            );
        }
    }
}
