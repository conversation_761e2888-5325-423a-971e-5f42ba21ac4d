# Container Integration Fix Plan

This document outlines a comprehensive plan to fix the container integration issues in the test framework, ensuring that
contexts receive proper service dependencies through Symfony's container.

## Problem Statement

Currently, Behat contexts like `CartContext` are falling back to mock services because:

1. The Symfony container is not properly integrated with Behat's context initialization
2. The container is not available globally when contexts are instantiated
3. There's no consistent approach to dependency injection across contexts

## Solution Overview

The solution involves three phases:

1. **Immediate Fix**: Register the container globally and force real services
2. **Service Definition Refactoring**: Define contexts as explicit services with proper dependency injection
3. **Context Constructor Refactoring**: Update context constructors to rely on injected dependencies

## Phase 1: Immediate Fix (Completed)

### Files Modified

1. **bin/run-tests.php**
    - Added global container registration: `$GLOBALS['service_container'] = $container;`
    - Set environment variable: `putenv("FORCE_REAL_SERVICES=true");`

2. **.env**
    - Added `FORCE_REAL_SERVICES=true`

## Phase 2: Service Definition Refactoring (1-2 weeks)

### Files to Modify

1. **composer.json**
   ```json
   "require-dev": {
     "friends-of-behat/service-container-extension": "^1.1"
   }
   ```

2. **behat.yml**
   ```yaml
   default:
     extensions:
       FriendsOfBehat\ServiceContainerExtension:
         imports:
           - config/services.yml
   ```

3. **config/services/contexts.yml**
    - Add explicit service definitions for each context:
   ```yaml
   services:
     behat.context.cart:
       class: App\Context\CartContext
       public: true
       arguments:
         $container: '@service_container'
         $pageFactory: '@App\Service\Page\PageFactoryInterface'
         $stateService: '@App\Service\State\SharedStateServiceInterface'
       tags: [ 'context.service' ]

     behat.context.feature:
       class: App\Context\FeatureContext
       public: true
       arguments:
         $container: '@service_container'
         $browserService: '@App\Service\Browser\BrowserServiceInterface'
         $stateService: '@App\Service\State\SharedStateServiceInterface'
       tags: [ 'context.service' ]

     # Add similar definitions for other contexts
   ```

4. **config/services.yml**
    - Ensure it imports contexts.yml:
   ```yaml
   imports:
     - { resource: 'services/contexts.yml' }
   ```

## Phase 3: Context Constructor Refactoring (2-3 weeks)

### Files to Modify

1. **src/Context/CartContext.php**
    - Update constructor to accept specific dependencies
    - Gradually remove fallback logic

2. **src/Context/Base/ServiceAwareContext.php**
    - Simplify to rely on injected container
    - Remove fallback container building logic

3. **src/Context/Base/BaseContext.php**
    - Update to properly handle service access
    - Improve error handling for missing services

4. **All other context classes**
    - Update constructors to follow the same pattern
    - Remove duplicate mock service creation methods
    - Add proper type hints and null checks

## Phase 4: Test Runner Optimization (1-2 weeks)

### Files to Modify

1. **bin/run-tests.php**
    - Improve container caching
    - Add command-line options for better control
    - Implement proper error handling and reporting

2. **bin/verify-container.php** (New file)
    - Create a utility script to verify container configuration
    - Check that all required services are registered
    - Validate service wiring

3. **config/services/optimization.yml**
    - Add performance optimizations for services
    - Configure caching for frequently used services

## Phase 5: Documentation and Standards (1 week)

### Files to Create/Modify

1. **docs/Architecture_migration/context_service_pattern.md** (New file)
    - Document the new context service pattern
    - Provide examples of proper context definition

2. **docs/Architecture_migration/dependency_injection_guide.md** (New file)
    - Create a guide for dependency injection in contexts
    - Show before/after examples

3. **docs/Architecture_migration/migration_checklist.md** (New file)
    - Provide a checklist for migrating existing contexts
    - Include verification steps

## Detailed Implementation Plan

### Week 1: Setup and Initial Refactoring

#### Day 1-2: Environment Setup

- Install `friends-of-behat/service-container-extension`
- Configure behat.yml with the extension
- Update composer.json with required dependencies

#### Day 3-4: Core Context Services

- Define service definitions for core contexts:
    - FeatureContext
    - BrandContext
    - ProductContext
    - CartContext

#### Day 5: Testing and Verification

- Run tests to verify service definitions
- Fix any issues with service wiring
- Document progress and findings

### Week 2: Complete Service Definitions

#### Day 1-3: Additional Context Services

- Define service definitions for remaining contexts:
    - CheckoutContext
    - PaymentContext
    - ValidationContext
    - EmailContext
    - SalesFunnelContext
    - UpsellContext
    - AbandonedCartContext
    - DatabaseContext
    - SSHContext
    - AdminCommandContext
    - TestDataContext

#### Day 4-5: Integration Testing

- Run comprehensive tests across all contexts
- Verify service injection is working correctly
- Document any issues or edge cases

### Week 3-4: Context Constructor Refactoring

#### Week 3: Core Contexts

- Refactor constructors for core contexts:
    - Update parameter types and null handling
    - Remove fallback logic where possible
    - Add better error reporting

#### Week 4: Remaining Contexts

- Apply the same refactoring to remaining contexts
- Ensure backward compatibility during transition
- Add unit tests for context initialization

### Week 5: Test Runner and Documentation

#### Day 1-3: Test Runner Optimization

- Improve container caching in run-tests.php
- Add verification utilities
- Optimize service configuration

#### Day 4-5: Documentation

- Create comprehensive documentation
- Update README and migration guides
- Provide examples and best practices

## File Inventory

Here's a complete list of files that will be affected by this plan:

### Configuration Files

- behat.yml
- composer.json
- config/services.yml
- config/services/contexts.yml
- config/services/core.yml
- config/services/optimization.yml
- .env

### Script Files

- bin/run-tests.php
- bin/verify-container.php (new)

### Context Base Classes

- src/Context/Base/ServiceAwareContext.php
- src/Context/Base/BaseContext.php

### Context Implementation Classes

- src/Context/FeatureContext.php
- src/Context/BrandContext.php
- src/Context/ProductContext.php
- src/Context/CartContext.php
- src/Context/CheckoutContext.php
- src/Context/PaymentContext.php
- src/Context/ValidationContext.php
- src/Context/EmailContext.php
- src/Context/SalesFunnelContext.php
- src/Context/UpsellContext.php
- src/Context/AbandonedCartContext.php
- src/Context/DatabaseContext.php
- src/Context/SSHContext.php
- src/Context/AdminCommandContext.php
- src/Context/TestDataContext.php

### Documentation Files

- docs/Architecture_migration/0704_next_fix_container.md (this file)
- docs/Architecture_migration/context_service_pattern.md (new)
- docs/Architecture_migration/dependency_injection_guide.md (new)
- docs/Architecture_migration/migration_checklist.md (new)

## Success Criteria

The implementation will be considered successful when:

1. All contexts are properly defined as services in the container
2. Contexts receive their dependencies through constructor injection
3. No context falls back to creating mock services
4. Tests run successfully with real services
5. The codebase follows consistent dependency injection patterns
6. Documentation is updated to reflect the new architecture

## Risks and Mitigations

### Risk: Breaking existing tests during refactoring

**Mitigation**: Implement changes gradually, maintain backward compatibility, and run tests after each change.

### Risk: Performance degradation with service container

**Mitigation**: Implement proper caching and optimization in the container configuration.

### Risk: Inconsistent implementation across the team

**Mitigation**: Provide clear documentation, examples, and code reviews to ensure consistency.

## Conclusion

This plan provides a comprehensive approach to fixing the container integration issues in the test framework. By
following this plan, we will establish a more maintainable, reliable, and efficient testing infrastructure that properly
leverages Symfony's dependency injection capabilities.