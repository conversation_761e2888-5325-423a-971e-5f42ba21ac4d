<?php
// Simple site connectivity test
$site = getenv('TEST_BASE_URL') ?: 'https://aeonstest.info';
echo "Testing connectivity to: {$site}\n";

$ch = curl_init($site);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HEADER => true,
    CURLOPT_NOBODY => false
]);

echo "Connecting...\n";
$startTime = microtime(true);
$response = curl_exec($ch);
$duration = round((microtime(true) - $startTime) * 1000);

if ($response === false) {
    echo "ERROR: " . curl_error($ch) . " (code: " . curl_errno($ch) . ")\n";
} else {
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);

    echo "Success: Received HTTP {$statusCode} in {$duration}ms\n";
    echo "Response headers:\n{$headers}\n";
}
curl_close($ch);

// Test Selenium connection
$seleniumUrl = getenv('WEBDRIVER_URL') ?: 'http://selenium_chrome:4444/wd/hub';
echo "\nTesting connectivity to Selenium: {$seleniumUrl}\n";

$ch = curl_init(str_replace('/wd/hub', '/status', $seleniumUrl));
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_TIMEOUT => 30
]);

echo "Connecting to Selenium...\n";
$startTime = microtime(true);
$response = curl_exec($ch);
$duration = round((microtime(true) - $startTime) * 1000);

if ($response === false) {
    echo "ERROR: " . curl_error($ch) . " (code: " . curl_errno($ch) . ")\n";
} else {
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    echo "Success: Received HTTP {$statusCode} in {$duration}ms\n";

    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "Selenium status: " . ($data['value']['ready'] ? "Ready" : "Not ready") . "\n";

        if (isset($data['value']['message'])) {
            echo "Selenium message: {$data['value']['message']}\n";
        }
    } else {
        echo "Invalid JSON response\n";
    }
}
curl_close($ch); 