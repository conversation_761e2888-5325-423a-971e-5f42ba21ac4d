<?php

namespace Features\Bootstrap\Helper;

use PDO;
use RuntimeException;

class DatabaseHelper
{
    private array $config;
    private ?SSHTunnel $tunnel = null;
    private ?PDO $connection = null;

    public function __construct(array $config)
    {
        $this->validateConfig($config);
        $this->config = $config;
    }

    private function validateConfig(array $config): void
    {
        // Validate SSH config
        if (!empty($config['ssh'])) {
            if (empty($config['ssh']['host'])) {
                throw new RuntimeException('SSH host is required');
            }
            if (empty($config['ssh']['user'])) {
                throw new RuntimeException('SSH user is required');
            }
            if (empty($config['ssh']['private_key']) && empty($config['ssh']['password'])) {
                throw new RuntimeException('Either SSH private key or password is required');
            }
        }

        // Validate DB config
        if (empty($config['db']['host'])) {
            throw new RuntimeException('Database host is required');
        }
        if (empty($config['db']['port'])) {
            throw new RuntimeException('Database port is required');
        }
        if (empty($config['db']['name'])) {
            throw new RuntimeException('Database name is required');
        }
        if (empty($config['db']['user'])) {
            throw new RuntimeException('Database user is required');
        }
        if (!isset($config['db']['password'])) {
            throw new RuntimeException('Database password is required');
        }
    }

    public function connect(): void
    {
        try {
            // If SSH config is provided, establish SSH tunnel first
            if (!empty($this->config['ssh'])) {
                echo "Setting up SSH tunnel...\n";
                $this->tunnel = new SSHTunnel($this->config);
                $this->tunnel->connect();
                $host = '127.0.0.1';
                $port = $this->tunnel->getLocalPort();
            } else {
                $host = $this->config['db']['host'];
                $port = $this->config['db']['port'];
            }

            echo "Connecting to MySQL...\n";
            echo "Host: {$host}:{$port}\n";
            echo "Database: {$this->config['db']['name']}\n";
            echo "User: {$this->config['db']['user']}\n";

            // Create DSN
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=utf8mb4',
                $host,
                $port,
                $this->config['db']['name']
            );

            // Set PDO options
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            // Create PDO connection
            $this->connection = new PDO(
                $dsn,
                $this->config['db']['user'],
                $this->config['db']['password'],
                $options
            );

            echo "Database connection established successfully.\n";

        } catch (\Exception $e) {
            throw new RuntimeException('Database connection failed: ' . $e->getMessage());
        }
    }

    public function query(string $sql, array $params = []): array
    {
        if (!$this->connection) {
            throw new RuntimeException('Not connected to database');
        }

        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (\Exception $e) {
            throw new RuntimeException('Query failed: ' . $e->getMessage());
        }
    }

    public function disconnect(): void
    {
        $this->connection = null;
        if ($this->tunnel) {
            unset($this->tunnel);
        }
    }

    public function __destruct()
    {
        $this->disconnect();
    }
} 