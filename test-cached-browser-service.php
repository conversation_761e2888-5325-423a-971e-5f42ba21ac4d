<?php

require __DIR__ . '/vendor/autoload.php';

use App\Service\Browser\BrowserServiceInterface;
use App\Service\Browser\CachedBrowserService;
use App\Service\Browser\BrowserService;
use App\Service\Cache\CacheServiceInterface;
use App\Service\Cache\FilesystemCacheService;

// Create a simple test harness
class TestHarness
{
    public function run()
    {
        echo "Testing CachedBrowserService implementation...\n";

        // Create a mock inner browser service
        $innerService = $this->createMockBrowserService();

        // Create a real cache service
        $cacheService = new FilesystemCacheService(__DIR__ . '/var/cache/test');

        // Create the cached browser service
        $cachedService = new CachedBrowserService($innerService, $cacheService);

        // Test all required methods
        $this->testAllMethods($cachedService);

        echo "All tests completed successfully!\n";
    }

    private function createMockBrowserService()
    {
        return new class implements BrowserServiceInterface {
            public function elementExists(string $selector): bool
            {
                return true;
            }

            public function wait(int $seconds): void
            { /* do nothing */
            }

            public function isSessionActive(): bool
            {
                return true;
            }

            public function getDriverType(): string
            {
                return 'mock';
            }

            public function hasContent(string $text): bool
            {
                return true;
            }

            public function navigateBack(): void
            { /* do nothing */
            }

            public function getPageTitle(): string
            {
                return 'Mock Page Title';
            }

            public function waitForUrlContains(string $text, int $timeout = 30): bool
            {
                return true;
            }

            public function isBrowserStackSession(): bool
            {
                return false;
            }

            public function findElement(string $selector): ?\Behat\Mink\Element\NodeElement
            {
                return null;
            }

            public function getCurrentUrl(): string
            {
                return 'https://example.com';
            }

            public function visit(string $url): void
            { /* do nothing */
            }

            public function getSession(): \Behat\Mink\Session
            {
                throw new \RuntimeException('Not implemented');
            }

            public function waitForElement(string $selector, int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForPageToLoad(int $timeout = 30): void
            { /* do nothing */
            }

            public function takeScreenshot(string $name = null): string
            {
                return '/path/to/screenshot.png';
            }

            public function fillField(string $field, string $value): void
            { /* do nothing */
            }

            public function selectOption(string $select, string $option): void
            { /* do nothing */
            }

            public function executeScript(string $script)
            {
                return null;
            }

            public function findElements(string $selector): array
            {
                return [];
            }

            public function waitForElementVisible(string $selector, int $timeout = 30): bool
            {
                return true;
            }

            public function scrollToElement(string $selector): void
            { /* do nothing */
            }

            public function clickElement(string $selector): void
            { /* do nothing */
            }

            public function getElementText(string $selector): string
            {
                return 'Mock Text';
            }

            public function isElementVisible(string $selector): bool
            {
                return true;
            }

            public function waitForDocumentReady(int $timeout = 30): void
            { /* do nothing */
            }

            public function waitForAjaxToComplete(int $timeout = 30): void
            { /* do nothing */
            }
        };
    }

    private function testAllMethods(CachedBrowserService $service)
    {
        // Test each method to ensure it exists and can be called
        echo "Testing elementExists()... ";
        $service->elementExists('.test-selector');
        echo "OK\n";

        echo "Testing wait()... ";
        $service->wait(1);
        echo "OK\n";

        echo "Testing isSessionActive()... ";
        $service->isSessionActive();
        echo "OK\n";

        echo "Testing getDriverType()... ";
        $service->getDriverType();
        echo "OK\n";

        echo "Testing hasContent()... ";
        $service->hasContent('test content');
        echo "OK\n";

        echo "Testing navigateBack()... ";
        $service->navigateBack();
        echo "OK\n";

        echo "Testing getPageTitle()... ";
        $service->getPageTitle();
        echo "OK\n";

        echo "Testing waitForUrlContains()... ";
        $service->waitForUrlContains('example');
        echo "OK\n";

        echo "Testing isBrowserStackSession()... ";
        $service->isBrowserStackSession();
        echo "OK\n";

        echo "Testing findElement()... ";
        $service->findElement('.test-selector');
        echo "OK\n";

        echo "Testing getCurrentUrl()... ";
        $service->getCurrentUrl();
        echo "OK\n";
    }
}

// Run the test
$testHarness = new TestHarness();
$testHarness->run();
