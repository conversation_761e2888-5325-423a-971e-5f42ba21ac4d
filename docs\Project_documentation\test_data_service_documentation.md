# Test Data Service Documentation

## Overview

The Test Data Service is a key component of the Malaberg test automation framework. It provides a mechanism for loading and accessing test data from YAML files. This document covers the Test Data Service interface and implementation.

## TestDataServiceInterface

The `TestDataServiceInterface` defines the contract for test data services.

```php
interface TestDataServiceInterface
{
    /**
     * Load test data from a file
     *
     * @param string $brand Brand name
     * @param string $type Data type (e.g., 'products', 'users')
     * @param string|null $key Specific data key to load
     * @return array Test data
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array;

    /**
     * Get test data for a specific brand, type, and key
     *
     * @param string $brand Brand name
     * @param string $type Data type (e.g., 'products', 'users')
     * @param string $key Data key
     * @return array Test data
     */
    public function getTestData(string $brand, string $type, string $key): array;

    /**
     * Check if test data exists for a specific brand, type, and key
     *
     * @param string $brand Brand name
     * @param string $type Data type (e.g., 'products', 'users')
     * @param string $key Data key
     * @return bool
     */
    public function hasTestData(string $brand, string $type, string $key): bool;
}
```

## TestDataService Implementation

The `TestDataService` class implements the `TestDataServiceInterface` and provides the actual test data functionality.

```php
class TestDataService implements TestDataServiceInterface
{
    /**
     * Configuration service
     *
     * @var ConfigurationServiceInterface
     */
    private ConfigurationServiceInterface $configService;

    /**
     * Test data cache
     *
     * @var array
     */
    private array $dataCache = [];

    /**
     * Constructor
     *
     * @param ConfigurationServiceInterface $configService Configuration service
     */
    public function __construct(ConfigurationServiceInterface $configService)
    {
        $this->configService = $configService;
    }

    /**
     * {@inheritdoc}
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        $cacheKey = sprintf('%s.%s', $brand, $type);

        // Check if data is already cached
        if (!isset($this->dataCache[$cacheKey])) {
            // Load data from file
            $filePath = sprintf('%s/features/fixtures/brands/%s/%s.yml', $this->configService->getProjectRoot(), $brand, $type);
            
            if (!file_exists($filePath)) {
                throw new FileNotFoundException(sprintf('Test data file not found: %s', $filePath));
            }

            $data = Yaml::parseFile($filePath);
            $this->dataCache[$cacheKey] = $data;
        }

        // Return specific key or all data
        if ($key !== null) {
            if (!isset($this->dataCache[$cacheKey][$key])) {
                throw new InvalidArgumentException(sprintf('Test data key not found: %s', $key));
            }
            return $this->dataCache[$cacheKey][$key];
        }

        return $this->dataCache[$cacheKey];
    }

    /**
     * {@inheritdoc}
     */
    public function getTestData(string $brand, string $type, string $key): array
    {
        return $this->loadTestData($brand, $type, $key);
    }

    /**
     * {@inheritdoc}
     */
    public function hasTestData(string $brand, string $type, string $key): bool
    {
        try {
            $this->getTestData($brand, $type, $key);
            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }
}
```

## Test Data Structure

Test data is stored in YAML files in the `features/fixtures/brands/{brand}` directory. Each file represents a specific type of data, such as products or users.

Example product data:

```yaml
# features/fixtures/brands/aeons/products.yml
total_harmony:
  name: "Total Harmony"
  slug: "aeons-total-harmony"
  prices:
    one_time:
      minimum: 29.95
      medium: 39.95
      maximum: 49.95
    subscription:
      minimum: 24.95
      medium: 34.95
      maximum: 44.95
  options:
    purchase_types:
      one_time: "One-Time Purchase"
      subscription: "Subscribe & Save"
    quantities:
      minimum:
        fullName: "1 Jar"
        numberOfItems: 1
      medium:
        fullName: "3 Jars"
        numberOfItems: 3
      maximum:
        fullName: "6 Jars"
        numberOfItems: 6
    flavors:
      - "Original"
      - "Citrus"
      - "Berry"
```

Example user data:

```yaml
# features/fixtures/brands/aeons/users.yml
default:
  first_name: "John"
  last_name: "Doe"
  email: "<EMAIL>"
  phone: "************"
  address:
    street: "123 Main St"
    city: "Anytown"
    state: "CA"
    zip: "12345"
    country: "US"
  payment:
    card_number: "****************"
    expiry_month: "12"
    expiry_year: "2025"
    cvv: "123"
```

## Usage in Contexts

The Test Data Service is used by contexts to load and access test data.

```php
class ProductContext extends BaseContext
{
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;

    public function __construct(
        TestDataServiceInterface $dataService,
        SharedStateServiceInterface $stateService
    ) {
        $this->dataService = $dataService;
        $this->stateService = $stateService;
    }

    /**
     * @Given I am viewing the product :productName
     */
    public function iAmViewingTheProduct(string $productName): void
    {
        // Get the current brand from configuration
        $brand = $this->getConfigService()->getCurrentBrand();
        
        // Load product data
        $productData = $this->dataService->getTestData($brand, 'products', $productName);
        
        // Store product data in shared state
        $this->stateService->set('currentProduct', $productData);
        
        // Navigate to the product page
        $productPage = $this->pageFactory->getPage('ProductPage');
        $productPage->loadWithName($productName, $productData);
    }
}
```

## Best Practices for Test Data

1. **Organize by Brand**: Organize test data by brand to support multi-brand testing.
2. **Use Descriptive Keys**: Use descriptive keys that clearly indicate what the data represents.
3. **Keep Data Minimal**: Keep test data minimal and focused on the specific test requirements.
4. **Use Realistic Data**: Use realistic data that reflects actual user data.
5. **Avoid Hardcoding**: Avoid hardcoding test data in tests. Instead, load it from YAML files.
6. **Cache Data**: Cache test data to improve performance.
7. **Handle Errors**: Handle errors gracefully when test data is not found.
8. **Document Data Structure**: Document the structure of test data files to make them easier to maintain.
