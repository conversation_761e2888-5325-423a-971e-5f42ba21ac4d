<?php

namespace App\Service\Decorator;

use App\Service\Cache\CacheServiceInterface;
use Psr\Log\LoggerInterface;

/**
 * Abstract base class for service decorators with caching functionality
 */
abstract class AbstractServiceDecorator
{
    protected object $decoratedService;
    protected CacheServiceInterface $cacheService;
    protected LoggerInterface $logger;
    protected string $cachePrefix;
    protected int $cacheLifetime;

    public function __construct(
        object                $decoratedService,
        CacheServiceInterface $cacheService,
        LoggerInterface       $logger,
        string                $cachePrefix,
        int                   $cacheLifetime
    )
    {
        $this->decoratedService = $decoratedService;
        $this->cacheService = $cacheService;
        $this->logger = $logger;
        $this->cachePrefix = $cachePrefix;
        $this->cacheLifetime = $cacheLifetime;
    }

    /**
     * Get cached value or compute and cache it
     */
    protected function getCachedValue(string $key, callable $callback, ?int $lifetime = null): mixed
    {
        $cacheKey = $this->buildCacheKey($key);
        $value = $this->cacheService->get($cacheKey);

        if ($value !== null) {
            $this->logger->debug('Cache hit for key: {key}', ['key' => $cacheKey]);
            return $value;
        }

        $value = $callback();
        $this->cacheService->set($cacheKey, $value, $lifetime ?? $this->cacheLifetime);
        $this->logger->debug('Cache miss for key: {key}', ['key' => $cacheKey]);

        return $value;
    }

    /**
     * Build a cache key with prefix
     */
    protected function buildCacheKey(string $key): string
    {
        return sprintf('%s.%s', $this->cachePrefix, $key);
    }

    /**
     * Clear cache for a specific key
     */
    protected function clearCache(string $key): void
    {
        $cacheKey = $this->buildCacheKey($key);
        $this->cacheService->delete($cacheKey);
        $this->logger->debug('Cleared cache for key: {key}', ['key' => $cacheKey]);
    }

    /**
     * Clear all cache entries with this decorator's prefix
     */
    protected function clearAllCache(): void
    {
        $pattern = sprintf('%s.*', $this->cachePrefix);
        $this->cacheService->deletePattern($pattern);
        $this->logger->debug('Cleared all cache for prefix: {prefix}', ['prefix' => $this->cachePrefix]);
    }
} 