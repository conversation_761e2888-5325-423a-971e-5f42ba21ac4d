#!/usr/bin/env php
<?php

require_once __DIR__ . '/../../../vendor/autoload.php';

use Features\Bootstrap\Tools\SimpleProductScraper;
use Features\Bootstrap\Tools\SelectorUpdater;
use Symfony\Component\Yaml\Yaml;

// Parse command line arguments
$options = getopt('', ['brand:', 'base-url:', 'product:', 'update-selectors::']);

if (!isset($options['brand']) || !isset($options['base-url']) || !isset($options['product'])) {
    echo "Usage: php update-product-data.php --brand=BRAND --base-url=BASE_URL --product=PRODUCT_URL [--update-selectors]\n";
    echo "\n";
    echo "Options:\n";
    echo "  --brand            Brand name (e.g., 'aeons')\n";
    echo "  --base-url         Base URL of the website\n";
    echo "  --product          Product URL path\n";
    echo "  --update-selectors Optional: Also update page object selectors\n";
    exit(1);
}

try {
    // Initialize tools
    $scraper = new SimpleProductScraper($options['base-url']);
    
    // Scrape and update product data
    echo "Scraping product data from {$options['base-url']}/{$options['product']}...\n";
    $scrapedData = $scraper->scrapeAndUpdate($options['product'], $options['brand']);
    
    echo "Product data updated successfully.\n";
    echo "Scraped data:\n";
    echo Yaml::dump($scrapedData, 4, 2);
    
    // Update selectors if requested
    if (isset($options['update-selectors'])) {
        $updater = new SelectorUpdater($options['base-url']);
        $pageObjectPath = __DIR__ . '/../Page/ProductPage.php';
        
        echo "\nUpdating page object selectors...\n";
        $changes = $updater->scanAndUpdate($options['product'], $pageObjectPath);
        
        if (empty($changes)) {
            echo "No selector changes needed.\n";
        } else {
            echo "Selector changes made:\n";
            foreach ($changes as $type => $change) {
                echo "  $type:\n";
                echo "    Old: " . ($change['old'] ?? 'none') . "\n";
                echo "    New: " . $change['new'] . "\n";
            }
        }
    }
    
    echo "\nDone!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
} 