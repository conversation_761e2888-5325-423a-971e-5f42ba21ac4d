<?php

require_once __DIR__ . '/vendor/autoload.php';

echo "Checking PageObjectExtension configuration...\n\n";

// Check if the class exists
$extensionClass = 'FriendsOfBehat\PageObjectExtension\ServiceContainer\PageObjectExtension';
if (class_exists($extensionClass)) {
    echo "✅ Class {$extensionClass} exists.\n";

    $reflection = new ReflectionClass($extensionClass);
    echo "   Location: " . $reflection->getFileName() . "\n";
} else {
    echo "❌ Class {$extensionClass} does not exist.\n";

    // Check if the file exists
    $possiblePaths = [
        __DIR__ . '/vendor/friends-of-behat/page-object-extension/src/ServiceContainer/PageObjectExtension.php',
        __DIR__ . '/vendor/friends-of-behat/page-object-extension/ServiceContainer/PageObjectExtension.php'
    ];

    foreach ($possiblePaths as $path) {
        echo "   Checking: {$path} - " . (file_exists($path) ? "exists" : "not found") . "\n";
    }
}

// Check if the interface exists
$extensionInterface = 'Behat\Testwork\ServiceContainer\Extension';
if (interface_exists($extensionInterface)) {
    echo "✅ Interface {$extensionInterface} exists.\n";
} else {
    echo "❌ Interface {$extensionInterface} does not exist.\n";
}

// Check the file structure in the vendor directory
$pageObjectDir = __DIR__ . '/vendor/friends-of-behat/page-object-extension';
if (is_dir($pageObjectDir)) {
    echo "\nDirectory structure of page-object-extension:\n";
    $files = scandir($pageObjectDir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo "   {$file}\n";

            if (is_dir($pageObjectDir . '/' . $file)) {
                $subFiles = scandir($pageObjectDir . '/' . $file);
                foreach ($subFiles as $subFile) {
                    if ($subFile != '.' && $subFile != '..') {
                        echo "     - {$subFile}\n";
                    }
                }
            }
        }
    }
} else {
    echo "\n❌ Directory {$pageObjectDir} does not exist.\n";
}

echo "\nBehat version information:\n";
$behatClass = new ReflectionClass('Behat\Behat\ApplicationFactory');
echo "Behat location: " . $behatClass->getFileName() . "\n";
