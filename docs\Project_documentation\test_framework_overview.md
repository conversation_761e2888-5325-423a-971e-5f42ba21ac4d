# Test Framework Overview

## Introduction

The Malaberg test automation framework is a Behat-based framework for automated testing of web applications. It follows the Page Object pattern to provide a clean separation between test logic and page interaction details. This document provides an overview of the framework architecture and key components.

## Architecture

The framework follows a layered architecture:

1. **Feature Layer**: Gherkin feature files that describe the behavior of the application in a human-readable format.
2. **Step Definition Layer**: PHP classes that implement the steps defined in the feature files.
3. **Page Object Layer**: PHP classes that encapsulate the interaction with web pages.
4. **Service Layer**: PHP classes that provide common functionality and services to the other layers.

### Feature Layer

Feature files are written in Gherkin syntax and describe the behavior of the application from a user's perspective. They are stored in the `features` directory.

Example:

```gherkin
Feature: Product Purchase
  As a customer
  I want to purchase a product
  So that I can receive it at my home

  Scenario: Add product to cart
    Given I am on the homepage
    When I navigate to the product page for "Total Harmony"
    And I select quantity "3"
    And I select purchase type "one_time"
    And I add the product to cart
    Then I should see 1 item in my cart
    And the cart total should be "$39.95"
```

### Step Definition Layer

Step definitions are PHP classes that implement the steps defined in the feature files. They are stored in the `src/Context` directory.

Example:

```php
/**
 * @When I navigate to the product page for :productName
 */
public function iNavigateToTheProductPageFor(string $productName): void
{
    $productPage = $this->pageFactory->getPage('ProductPage');
    $productPage->loadWithName($productName);
}
```

### Page Object Layer

Page objects are PHP classes that encapsulate the interaction with web pages. They are stored in the `src/Page` directory.

Example:

```php
class ProductPage extends BasePage
{
    protected string $path = '/product/{slug}';

    public function loadWithName(string $productName): void
    {
        $slug = $this->getSlugForProduct($productName);
        $this->open(['slug' => $slug]);
    }
}
```

### Service Layer

Services are PHP classes that provide common functionality to the other layers. They are stored in the `src/Service` directory.

Example:

```php
class BrowserStackBrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    private BrowserStackStatusReporter $statusReporter;

    public function visit(string $url): void
    {
        $this->logger->debug("Visiting URL: {$url}");
        $this->session->visit($url);
        $this->waitForPageToLoad();
    }

    public function markTestPassed(?string $reason = null): bool
    {
        return $this->statusReporter->markTestPassed($this->session, $reason);
    }
}
```

## Key Components

### Page Objects

Page objects encapsulate the interaction with web pages, providing a higher-level API for tests to use. They abstract away the details of the HTML structure and browser interactions.

Key page objects:

- `BasePage`: Base class for all page objects
- `HomePage`: Represents the home page
- `ProductPage`: Represents the product detail page
- `CartPage`: Represents the shopping cart page
- `CheckoutPage`: Represents the checkout page

### Contexts

Contexts are step definition classes that implement the steps defined in the feature files. They use page objects to interact with the application.

Key contexts:

- `FeatureContext`: Main context class
- `ProductContext`: Handles product-related steps
- `CartContext`: Handles cart-related steps
- `CheckoutContext`: Handles checkout-related steps
- `AbandonedCartContext`: Handles abandoned cart scenarios

### Services

Services provide common functionality to the other layers. They are injected into contexts and page objects as needed.

Key services:

- `BrowserStackBrowserService`: Provides methods for interacting with the browser using BrowserStack
- `BrowserStackCapabilitiesBuilder`: Builds capabilities for BrowserStack sessions
- `BrowserStackSessionFactory`: Creates BrowserStack sessions
- `BrowserStackStatusReporter`: Reports test status to BrowserStack
- `ConfigurationService`: Provides access to configuration settings
- `DataService`: Provides access to test data
- `PageFactory`: Creates page objects
- `SharedStateService`: Manages shared state between steps
- `ValidationService`: Validates data

## Test Data Management

Test data is managed through YAML files stored in the `features/fixtures` directory. The `DataService` provides methods for loading and accessing this data.

Example:

```yaml
# features/fixtures/brands/aeons/products.yml
total_harmony:
  name: "Total Harmony"
  slug: "aeons-total-harmony"
  prices:
    one_time:
      minimum: 29.95
      medium: 39.95
      maximum: 49.95
    subscription:
      minimum: 24.95
      medium: 34.95
      maximum: 44.95
```

## State Management

Shared state between steps is managed through the `SharedStateService`. This service provides methods for setting and getting values in different scopes (scenario, feature, or global).

Example:

```php
// Set a value in the scenario scope
$this->stateService->set('product.selected_quantity', 3);

// Get a value from the scenario scope
$quantity = $this->stateService->get('product.selected_quantity');
```

## Configuration Management

Configuration settings are managed through YAML files stored in the `config` directory. The `ConfigurationService` provides methods for accessing these settings.

Example:

```yaml
# config/brands/aeons/stage.yml
base_url: "https://aeonstest.info"
admin_url: "https://aeonstest.info/admin"
api_url: "https://aeonstest.info/api"
```

## Running Tests

Tests are run using the BrowserStack integration script. The `behat.yml` file in the project root directory configures
Behat with the BrowserStack profile.

Example:

```powershell
# Run all tests with BrowserStack
.\run-browserstack-tests.ps1

# Run a specific feature with BrowserStack
.\run-browserstack-tests.ps1 -Feature "features/purchase.feature"

# Run tests with a specific tag with BrowserStack
.\run-browserstack-tests.ps1 -Tags "@cart"

# Run tests in dry-run mode with BrowserStack
.\run-browserstack-tests.ps1 -DryRun
```

You can also run tests directly with Behat using the BrowserStack profile:

```powershell
# Run all tests with BrowserStack profile
vendor/bin/behat --profile=browserstack

# Run a specific feature with BrowserStack profile
vendor/bin/behat --profile=browserstack features/purchase.feature

# Run tests with a specific tag with BrowserStack profile
vendor/bin/behat --profile=browserstack --tags=@cart
```

## Extending the Framework

The framework is designed to be extensible. New page objects, contexts, and services can be added as needed.

### Adding a New Page Object

1. Create a new class in the `src/Page` directory
2. Extend the `BasePage` class
3. Implement the `verifyPage()` method
4. Add methods for interacting with the page

### Adding a New Context

1. Create a new class in the `src/Context` directory
2. Extend the `BaseContext` class
3. Implement the necessary step methods
4. Register the context in the `behat.yml` file

### Adding a New Service

1. Create a new interface in the `src/Service` directory
2. Create a new class that implements the interface
3. Register the service in the `config/services.yml` file

## Best Practices

1. **Single Responsibility**: Each class should have a single responsibility.
2. **Encapsulation**: Page objects should encapsulate the details of the page structure.
3. **Descriptive Step Names**: Step names should be descriptive and follow a consistent pattern.
4. **Error Handling**: All methods should handle errors gracefully and provide meaningful error messages.
5. **State Management**: Use the shared state service to manage state between steps.
6. **Service Injection**: Use dependency injection to access services.
7. **Documentation**: All classes and methods should be well-documented.
