# 02 Architecture

This section explains how the scaffold wires contexts, page objects, and services using Symfony DI and Mink.

## DI Container

- Defined in `features/bootstrap/bootstrap.php`:
    - Loads `.env` and sets parameters (`app.project_root`).
    - Registers core services:
        - `App\Service\State\SharedStateService`
        - `App\Service\Browser\MinkBrowserService`
    - Aliases interfaces:
        - `SharedStateServiceInterface` → `SharedStateService`
        - `BrowserServiceInterface` → `MinkBrowserService`
    - Exposes container via `$GLOBALS['service_container']` for Behat contexts.

## Contexts

- Listed under `behat.yml` in the `contexts:` section.
- Constructed via Symfony2Extension, with dependencies injected from the container.
- Context classes reside in `src/Context/` (you can drop your existing PHP files here).

## Page Objects

- Implemented via FriendsOfBehat PageObjectExtension.
- Classes placed in `src/Page/` and named like `HomePage`, `CartPage`, etc.
- Registered automatically by the extension; injected into contexts as needed.

## Service Adapters

- `MinkBrowserService` (in `src/Service/Browser/`): wraps `Behat\Mink\Session` calls to implement
  `BrowserServiceInterface` methods.
- `SharedStateService` (in `src/Service/State/`): application state across steps.

## Bootstrap Flow

1. Behat starts and loads `features/bootstrap/bootstrap.php` via Symfony2Extension.
2. Dotenv loads environment variables from `.env`.
3. ContainerBuilder builds services and sets `$GLOBALS['service_container']`.
4. Symfony2Extension constructs each Context, injecting service dependencies.
5. FriendsOfBehat PageObjectExtension registers page objects and injects them when contexts request them.
6. MinkExtension instantiates a Mink Session (local or remote) and passes it to `MinkBrowserService`.
7. Tests execute with full DI, page objects, and configured Mink driver.

Next: see how to configure environment and profiles in [03 Configuration](03-configuration.md). 