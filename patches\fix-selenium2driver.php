<?php

/**
 * This script fixes all method signatures in the Selenium2Driver that have type hints
 * which are incompatible with the CoreDriver interface.
 */

$driverFile = __DIR__ . '/../vendor/behat/mink-selenium2-driver/src/Selenium2Driver.php';

if (!file_exists($driverFile)) {
    echo "Error: " . $driverFile . " does not exist." . PHP_EOL;
    exit(1);
}

$content = file_get_contents($driverFile);

// List of methods to fix
$methodsToFix = [
    'switchToWindow',
    'switchToIFrame',
    'findElementXpaths',
    'getTagName',
    'getText',
    'getHtml',
    'getOuterHtml',
    'getAttribute',
    'getValue',
    'setValue',
    'check',
    'uncheck',
    'isChecked',
    'selectOption',
    'isSelected',
    'click',
    'doubleClick',
    'rightClick',
    'attachFile',
    'isVisible',
    'mouseOver',
    'focus',
    'blur',
    'keyPress',
    'keyDown',
    'keyUp',
    'dragTo',
    'executeScript',
    'evaluateScript',
    'wait',
    'resizeWindow',
    'maximizeWindow',
    'submitForm',
    'visit'
];

$count = 0;

foreach ($methodsToFix as $method) {
    // Remove string type hints from method parameters
    $pattern = '/public function ' . $method . '\((?:\??string|\??int|\??bool|\??array)[\s$][^)]*\)/';
    preg_match($pattern, $content, $matches);

    if (!empty($matches)) {
        $originalSignature = $matches[0];

        // Get method parameters without type hints
        $originalParams = substr($originalSignature, strlen('public function ' . $method . '('), -1);
        $newParams = preg_replace('/\??string\s+|\??int\s+|\??bool\s+|\??array\s+/', '', $originalParams);

        // Replace original signature with new one
        $newSignature = 'public function ' . $method . '(' . $newParams . ')';
        $content = str_replace($originalSignature, $newSignature, $content);

        echo "Fixed method: " . $method . PHP_EOL;
        $count++;
    }
}

if ($count > 0) {
    file_put_contents($driverFile, $content);
    echo "Fixed $count method signatures in Selenium2Driver.php" . PHP_EOL;
} else {
    echo "No method signatures requiring fixes were found." . PHP_EOL;
} 