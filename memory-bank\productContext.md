# Product Context

## Project Overview

The E-commerce Test Automation Framework is a comprehensive testing solution designed for testing e-commerce applications with a focus on product content, purchase flows, and data-driven testing.

## Core Technologies

- **PHP/Behat**: Core testing framework for BDD-style tests
  - Version: PHP ^8.1, Behat ^3.12
  - Purpose: Behavior-driven development and test execution

- **MinkExtension**: Web testing extension for Behat
  - Purpose: Browser interaction and UI testing
  - Integration: Seamless integration with Behat contexts

- **Sylius**: PHP e-commerce framework
  - Base: Symfony framework
  - Purpose: Backend e-commerce functionality
  - Integration: Command-line interface for backend operations

## Key Features

- Behavior-driven development approach
- Page Object Model for UI interactions
- Data-driven testing with YAML fixtures
- Brand and environment-specific configurations
- Comprehensive test coverage for e-commerce flows
- Email verification and database validation

## Architecture

The project has recently migrated to a service-oriented architecture that provides:

- Clear separation of concerns
- Dependency injection
- Interface-based programming
- Improved testability and maintainability

The new architecture consists of three primary layers:
1. **Service Layer**: Core services that encapsulate business logic and technical operations
2. **Context Layer**: Behat contexts that map Gherkin steps to service calls
3. **Page Object Layer**: Page objects that represent UI elements and interactions