# Performance Optimization Guide

## Overview

This document provides guidance on optimizing the performance of the Malaberg test automation framework. It covers techniques for reducing test execution time, minimizing resource usage, and improving overall efficiency.

## Performance Bottlenecks

Common performance bottlenecks in test automation include:

1. **Browser Interactions**: Browser interactions are typically the slowest part of test execution.
2. **Page Load Times**: Waiting for pages to load can significantly impact test execution time.
3. **AJAX Requests**: Waiting for AJAX requests to complete can add significant overhead.
4. **Service Initialization**: Creating and initializing services can be expensive.
5. **Test Data Management**: Loading and processing test data can be time-consuming.
6. **Logging and Screenshots**: Excessive logging and taking screenshots can slow down tests.

## Optimization Techniques

### 1. Service Optimization

#### Lazy Loading

Lazy loading services can significantly reduce initialization time by only creating services when they are actually needed:

```yaml
# config/services/optimization.yml
services:
  App\Service\Data\TestDataService:
    lazy: true
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
      $validator: '@App\Service\Validation\ValidationServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
      $logger: '@logger'
    public: true

  App\Service\Validation\ValidationService:
    lazy: true
    arguments:
      $schemasDir: '%app.project_root%/config/schemas'
      $logger: '@logger'
    public: true
```

#### Service Caching

Caching service results can avoid redundant operations:

```php
class CachedTestDataService implements TestDataServiceInterface
{
    private TestDataServiceInterface $innerService;
    private CacheServiceInterface $cacheService;
    private array $cache = [];

    public function __construct(
        TestDataServiceInterface $innerService,
        CacheServiceInterface $cacheService
    ) {
        $this->innerService = $innerService;
        $this->cacheService = $cacheService;
    }

    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        $cacheKey = sprintf('%s.%s.%s', $brand, $type, $key ?? 'all');

        // Check in-memory cache first
        if (isset($this->cache[$cacheKey])) {
            return $this->cache[$cacheKey];
        }

        // Check persistent cache
        if ($this->cacheService->has($cacheKey)) {
            $data = $this->cacheService->get($cacheKey);
            $this->cache[$cacheKey] = $data;
            return $data;
        }

        // Load data from inner service
        $data = $this->innerService->loadTestData($brand, $type, $key);

        // Cache the result
        $this->cache[$cacheKey] = $data;
        $this->cacheService->set($cacheKey, $data, 3600); // Cache for 1 hour

        return $data;
    }

    // Implement other methods...
}
```

#### Optimized Page Factory

Using an optimized page factory that caches page instances can avoid creating the same page multiple times:

```php
class OptimizedPageFactory extends AbstractService implements PageFactoryInterface
{
    private ContainerInterface $container;
    private CacheServiceInterface $cacheService;
    private array $pageInstances = [];

    public function __construct(
        ContainerInterface $container,
        CacheServiceInterface $cacheService,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->container = $container;
        $this->cacheService = $cacheService;
    }

    public function getPage(string $pageName, array $parameters = []): BasePageInterface
    {
        // Convert page name to class name (e.g., 'HomePage' to 'App\Page\HomePage')
        $pageClass = $this->resolvePageClass($pageName);

        return $this->createPage($pageClass, $parameters);
    }

    public function createPage(string $pageClass, array $parameters = []): BasePageInterface
    {
        // Generate a cache key based on the class name and parameters
        $cacheKey = $this->generateCacheKey($pageClass, $parameters);

        // Check if we already have an instance of this page
        if (isset($this->pageInstances[$cacheKey])) {
            $this->logInfo(sprintf("Returning cached page instance for %s", $pageClass));
            return $this->pageInstances[$cacheKey];
        }

        // Create a new instance
        $this->logInfo(sprintf("Creating new page instance for %s", $pageClass));
        $page = $this->container->get($pageClass);

        // Cache the instance
        $this->pageInstances[$cacheKey] = $page;

        return $page;
    }

    private function generateCacheKey(string $pageClass, array $parameters = []): string
    {
        return $pageClass . ':' . md5(serialize($parameters));
    }

    // Implement other methods...
}
```

### 2. Browser Optimization

#### Headless Mode

Running tests in headless mode can significantly improve performance:

```yaml
# config/services/core.yml
services:
  mink.session_factory:
    class: App\Service\Browser\SessionFactory
    arguments:
      $driverName: 'chrome'
      $driverOptions:
        api_url: 'http://localhost:9222'
        download_behavior: 'allow'
        download_path: '%app.project_root%/downloads'
        socket_timeout: 60
        headless: true
    public: true
```

#### Connection Pooling

Reusing browser connections can avoid the overhead of starting a new browser for each test:

```yaml
# config/services/optimization.yml
services:
  browser.connection_pool:
    class: App\Service\Browser\ConnectionPool
    arguments:
      $maxConnections: 5
      $sessionFactory: '@mink.session_factory'
    public: true

  App\Service\Browser\BrowserService:
    arguments:
      $connectionPool: '@browser.connection_pool'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: true
```

#### Optimized Waiting

Using optimized waiting strategies can reduce unnecessary waiting time:

```php
public function waitForElementVisible(string $selector, int $timeout = 30): bool
{
    $startTime = microtime(true);
    $endTime = $startTime + $timeout;
    $interval = 0.1; // Start with a short interval

    while (microtime(true) < $endTime) {
        if ($this->isElementVisible($selector)) {
            return true;
        }

        // Exponential backoff
        usleep($interval * 1000000);
        $interval = min($interval * 1.5, 1.0); // Increase interval, but cap at 1 second
    }

    return false;
}
```

### 3. Test Data Optimization

#### Data Caching

Caching test data can avoid loading the same data multiple times:

```php
public function loadTestData(string $brand, string $type, ?string $key = null): array
{
    $cacheKey = sprintf('test_data.%s.%s.%s', $brand, $type, $key ?? 'all');

    // Check if data is already cached
    if ($this->cacheService->has($cacheKey)) {
        return $this->cacheService->get($cacheKey);
    }

    // Load data from file
    $filePath = sprintf('%s/features/fixtures/brands/%s/%s.yml', $this->configService->getProjectRoot(), $brand, $type);
    
    if (!file_exists($filePath)) {
        throw new FileNotFoundException(sprintf('Test data file not found: %s', $filePath));
    }

    $data = Yaml::parseFile($filePath);

    // Cache the data
    $this->cacheService->set($cacheKey, $data, 3600); // Cache for 1 hour

    // Return specific key or all data
    if ($key !== null) {
        if (!isset($data[$key])) {
            throw new InvalidArgumentException(sprintf('Test data key not found: %s', $key));
        }
        return $data[$key];
    }

    return $data;
}
```

#### Minimal Data Sets

Using minimal data sets can reduce the amount of data that needs to be loaded and processed:

```yaml
# features/fixtures/brands/aeons/products.yml
total_harmony:
  name: "Total Harmony"
  slug: "aeons-total-harmony"
  prices:
    one_time:
      minimum: 29.95
      medium: 39.95
      maximum: 49.95
    subscription:
      minimum: 24.95
      medium: 34.95
      maximum: 44.95
  # Only include fields that are actually used in tests
```

#### Data Preloading

Preloading commonly used data can avoid loading it during test execution:

```php
class PreloadedTestDataService implements TestDataServiceInterface
{
    private TestDataServiceInterface $innerService;
    private array $preloadedData = [];

    public function __construct(TestDataServiceInterface $innerService)
    {
        $this->innerService = $innerService;
        $this->preloadCommonData();
    }

    private function preloadCommonData(): void
    {
        // Preload commonly used data
        $this->preloadedData['aeons.products'] = $this->innerService->loadTestData('aeons', 'products');
        $this->preloadedData['aeons.users'] = $this->innerService->loadTestData('aeons', 'users');
    }

    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        $cacheKey = sprintf('%s.%s', $brand, $type);

        // Check if data is preloaded
        if (isset($this->preloadedData[$cacheKey])) {
            $data = $this->preloadedData[$cacheKey];

            // Return specific key or all data
            if ($key !== null) {
                if (!isset($data[$key])) {
                    throw new InvalidArgumentException(sprintf('Test data key not found: %s', $key));
                }
                return $data[$key];
            }

            return $data;
        }

        // Load data from inner service
        return $this->innerService->loadTestData($brand, $type, $key);
    }

    // Implement other methods...
}
```

### 4. Parallel Execution

#### Parallel Test Execution

Running tests in parallel can significantly reduce overall execution time:

```yaml
# behat.yml
default:
  # ...
  extensions:
    Behat\MinkExtension:
      # ...
    Liuggio\Behat\ParallelExtension:
      process_count: 4
```

#### Test Isolation

Ensuring tests are isolated can avoid interference between parallel tests:

```php
/**
 * @BeforeScenario
 */
public function resetState(): void
{
    // Reset shared state
    $this->stateService->reset('scenario');
    $this->stateService->reset('feature');

    // Reset browser state
    $this->browserService->getSession()->reset();
}
```

#### Resource Management

Managing resources carefully can avoid contention between parallel tests:

```php
class ResourceManager
{
    private static array $resources = [];
    private static array $locks = [];

    public static function acquireResource(string $resourceName): bool
    {
        if (isset(self::$locks[$resourceName])) {
            return false;
        }

        self::$locks[$resourceName] = true;
        return true;
    }

    public static function releaseResource(string $resourceName): void
    {
        unset(self::$locks[$resourceName]);
    }

    public static function withResource(string $resourceName, callable $callback)
    {
        if (!self::acquireResource($resourceName)) {
            throw new \RuntimeException(sprintf('Resource "%s" is already in use', $resourceName));
        }

        try {
            return $callback();
        } finally {
            self::releaseResource($resourceName);
        }
    }
}
```

### 5. Logging Optimization

#### Selective Logging

Using selective logging can reduce the overhead of logging:

```php
public function logInfo(string $message, array $context = []): void
{
    // Only log if log level is INFO or lower
    if ($this->logLevel <= LogLevel::INFO) {
        parent::logInfo($message, $context);
    }
}
```

#### Buffered Logging

Using buffered logging can reduce I/O operations:

```php
class BufferedLogger implements LoggerInterface
{
    private LoggerInterface $innerLogger;
    private array $buffer = [];
    private int $bufferSize;
    private int $currentSize = 0;

    public function __construct(LoggerInterface $innerLogger, int $bufferSize = 100)
    {
        $this->innerLogger = $innerLogger;
        $this->bufferSize = $bufferSize;
    }

    public function info(string $message, array $context = []): void
    {
        $this->buffer[] = ['level' => 'info', 'message' => $message, 'context' => $context];
        $this->currentSize++;

        if ($this->currentSize >= $this->bufferSize) {
            $this->flush();
        }
    }

    public function flush(): void
    {
        foreach ($this->buffer as $entry) {
            $level = $entry['level'];
            $this->innerLogger->$level($entry['message'], $entry['context']);
        }

        $this->buffer = [];
        $this->currentSize = 0;
    }

    // Implement other methods...
}
```

#### Conditional Screenshots

Taking screenshots only when needed can reduce overhead:

```php
public function takeScreenshot(?string $name = null): string
{
    // Only take screenshots in debug mode or when tests fail
    if ($this->isDebugMode || $this->testFailed) {
        return parent::takeScreenshot($name);
    }

    return '';
}
```

### 6. Code Optimization

#### Optimized Selectors

Using optimized selectors can improve element location performance:

```php
// Avoid complex selectors
// Bad
$this->browserService->findElement('div.container div.row div.col-md-6 div.product-card h2.product-title');

// Good
$this->browserService->findElement('.product-title');
```

#### Reduced DOM Traversal

Reducing DOM traversal can improve performance:

```php
// Avoid multiple DOM traversals
// Bad
$productName = $this->browserService->getElementText('.product-title');
$productPrice = $this->browserService->getElementText('.product-price');
$productDescription = $this->browserService->getElementText('.product-description');

// Good
$productCard = $this->browserService->findElement('.product-card');
$productName = $productCard->find('css', '.product-title')->getText();
$productPrice = $productCard->find('css', '.product-price')->getText();
$productDescription = $productCard->find('css', '.product-description')->getText();
```

#### Optimized Waiting

Using optimized waiting strategies can reduce unnecessary waiting time:

```php
// Avoid fixed waits
// Bad
$this->browserService->wait(5);

// Good
$this->browserService->waitForElementVisible('.product-title');
```

## Performance Monitoring

### 1. Test Execution Time

Monitoring test execution time can help identify slow tests:

```php
/**
 * @BeforeScenario
 */
public function startTimer(): void
{
    $this->startTime = microtime(true);
}

/**
 * @AfterScenario
 */
public function logExecutionTime(): void
{
    $endTime = microtime(true);
    $executionTime = $endTime - $this->startTime;
    $this->logInfo(sprintf('Scenario execution time: %.2f seconds', $executionTime));
}
```

### 2. Resource Usage

Monitoring resource usage can help identify resource-intensive operations:

```php
/**
 * @AfterScenario
 */
public function logResourceUsage(): void
{
    $memoryUsage = memory_get_peak_usage(true) / 1024 / 1024;
    $this->logInfo(sprintf('Peak memory usage: %.2f MB', $memoryUsage));
}
```

### 3. Service Usage

Monitoring service usage can help identify frequently used services:

```php
class ServiceUsageMonitor
{
    private static array $usageCounts = [];

    public static function recordUsage(string $serviceId): void
    {
        if (!isset(self::$usageCounts[$serviceId])) {
            self::$usageCounts[$serviceId] = 0;
        }

        self::$usageCounts[$serviceId]++;
    }

    public static function getUsageCounts(): array
    {
        return self::$usageCounts;
    }
}
```

## Performance Testing

### 1. Load Testing

Load testing can help identify performance issues under load:

```php
/**
 * @Given I run :count concurrent users
 */
public function iRunConcurrentUsers(int $count): void
{
    $processes = [];

    for ($i = 0; $i < $count; $i++) {
        $processes[] = new Process(['vendor/bin/behat', 'features/purchase.feature']);
    }

    foreach ($processes as $process) {
        $process->start();
    }

    foreach ($processes as $process) {
        $process->wait();
    }
}
```

### 2. Performance Benchmarks

Creating performance benchmarks can help track performance over time:

```php
/**
 * @Then the page should load within :seconds seconds
 */
public function thePageShouldLoadWithinSeconds(float $seconds): void
{
    $startTime = microtime(true);
    $this->browserService->waitForPageToLoad();
    $endTime = microtime(true);
    $loadTime = $endTime - $startTime;

    if ($loadTime > $seconds) {
        throw new \RuntimeException(sprintf('Page load time (%.2f seconds) exceeds the limit (%.2f seconds)', $loadTime, $seconds));
    }
}
```

### 3. Performance Regression Testing

Performance regression testing can help identify performance regressions:

```php
/**
 * @Then the performance should not degrade by more than :percent percent
 */
public function thePerformanceShouldNotDegradeByMoreThanPercent(float $percent): void
{
    $baselineTime = $this->getBaselineExecutionTime();
    $currentTime = $this->getCurrentExecutionTime();
    $degradation = ($currentTime - $baselineTime) / $baselineTime * 100;

    if ($degradation > $percent) {
        throw new \RuntimeException(sprintf('Performance degradation (%.2f%%) exceeds the limit (%.2f%%)', $degradation, $percent));
    }
}
```

## Caching Strategies

### Cache Service Implementation

The framework provides a robust caching system that can significantly improve test execution performance:

1. **Available Cache Adapters**
    - Array (in-memory, fastest but volatile)
    - Filesystem (persistent, good for local development)
    - Redis (distributed, ideal for CI/CD environments)

2. **Configurable Cache Lifetimes**
   ```yaml
   lifetimes:
     configuration: 3600  # Cache brand configs for 1 hour
     browser: 1800       # Cache browser sessions for 30 minutes
     api: 300           # Cache API responses for 5 minutes
     data: 600          # Cache test data for 10 minutes
   ```

3. **Cache Key Patterns**
    - Organized by service type
    - Support for pattern-based invalidation
    - Namespace isolation for parallel test runs

### Performance Benefits

1. **Configuration Caching**
    - Brand configuration cached between scenarios
    - Environment settings cached during test suite
    - Reduced filesystem I/O operations

2. **Browser Session Caching**
    - WebDriver session reuse
    - Cached authentication states
    - Preserved browser state between scenarios

3. **API Response Caching**
    - Reduced external API calls
    - Faster test data setup
    - Consistent test environment

4. **Test Data Caching**
    - Preloaded fixture data
    - Cached database queries
    - Shared state optimization

### Implementation Examples

1. **Caching Browser Sessions**

```php
public function getBrowserSession(string $sessionId): ?Session
{
    return $this->cache->get("browser.session.$sessionId") ?? $this->createNewSession();
}
```

2. **Caching API Responses**

```php
public function getApiResponse(string $endpoint): array
{
    $cacheKey = "api.response." . md5($endpoint);
    return $this->cache->get($cacheKey) ?? $this->makeApiCall($endpoint);
}
```

3. **Caching Test Data**

```php
public function getTestData(string $brand, string $type): array
{
    $cacheKey = "test_data.$brand.$type";
    return $this->cache->get($cacheKey) ?? $this->loadTestData($brand, $type);
}
```

### Cache Monitoring and Optimization

1. **Using LoggingCacheDecorator**
    - Monitor cache hit/miss ratios
    - Track cache operation timing
    - Identify cache invalidation patterns

```php
// Example log output
[debug] Cache hit for key: browser.session.12345
[debug] Cache miss for key: api.response.products
[debug] Setting cache for key: test_data.brand1.users
```

2. **Cache Warmup Strategies**
    - Pre-cache common configurations
    - Warm browser session cache
    - Preload test data fixtures

```php
public function warmupCache(): void
{
    foreach ($this->getBrands() as $brand) {
        $this->cache->set(
            "config.brand.$brand",
            $this->loadBrandConfig($brand),
            3600
        );
    }
}
```

3. **Cache Cleanup**
    - Automatic TTL-based expiration
    - Pattern-based cache clearing
    - Explicit cache invalidation

```php
// Clear all browser sessions
$this->cache->deletePattern('browser.session.*');

// Clear specific brand cache
$this->cache->delete("config.brand.$brand");
```

### Best Practices for Performance

1. **Cache Key Design**
    - Use hierarchical keys
    - Include version/timestamp in keys
    - Implement key prefixing

2. **Lifetime Optimization**
    - Match TTL to data volatility
    - Use shorter TTLs for volatile data
    - Implement cache versioning

3. **Memory Management**
    - Monitor cache size
    - Implement cache size limits
    - Use appropriate serialization

4. **Parallel Test Execution**
    - Namespace cache keys
    - Use process-safe cache adapters
    - Implement cache isolation

### Measuring Impact

1. **Performance Metrics**
    - Test execution time
    - Cache hit ratio
    - Resource utilization

2. **Monitoring Tools**
    - Cache statistics
    - Performance profiling
    - Resource monitoring

3. **Optimization Process**
    - Identify bottlenecks
    - Measure improvements
    - Iterate on solutions

## Conclusion

Optimizing the performance of the Malaberg test automation framework requires a multi-faceted approach. By addressing bottlenecks in service initialization, browser interactions, test data management, and code execution, you can significantly improve test execution time and resource usage. Regular performance monitoring and testing can help identify and address performance issues before they become significant problems.
