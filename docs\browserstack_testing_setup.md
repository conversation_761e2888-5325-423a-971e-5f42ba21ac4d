# BrowserStack Testing Setup Guide

## Overview

This guide outlines how to set up and configure the test framework for cloud-based browser testing using BrowserStack.
The framework now uses BrowserStack exclusively for browser automation, eliminating the need for local browser drivers
or Docker containers.

## Version: 1.0

**Last Updated**: 2025-03-27

## Prerequisites

### Required Software

- PHP 8.1 or higher
- Composer
- BrowserStack account with Automate access

## Installation Steps

### 1. BrowserStack Account Setup

1. **Create a BrowserStack Account**:
    - Visit [BrowserStack](https://www.browserstack.com/) and sign up for an account
    - Ensure you have access to the Automate product

2. **Get BrowserStack Credentials**:
    - Log in to your BrowserStack account
    - Navigate to Account > Settings
    - Note your Username and Access Key

### 2. Project Configuration

1. **Install Dependencies**:
   ```bash
   composer install
   ```

2. **Configure Environment Variables**:
    - Copy the provided `.env.browserstack` file to `.env`:
      ```bash
      cp .env.browserstack .env
      ```
    - Edit the `.env` file to update your BrowserStack credentials:
      ```
      BROWSERSTACK_USERNAME=your_username
      BROWSERSTACK_ACCESS_KEY=your_access_key
      ```

## Configuration

### Environment Variables

The `.env` file should contain the following variables:

```
# BrowserStack Configuration
BROWSERSTACK_USERNAME=your_username
BROWSERSTACK_ACCESS_KEY=your_access_key
BROWSERSTACK_URL=https://your_username:<EMAIL>/wd/hub
DEFAULT_SESSION=browserstack

# Browser Configuration
BROWSER_NAME=chrome
BROWSER_VERSION=latest
PLATFORM=Windows
PLATFORM_VERSION=11

# BrowserStack Options
BROWSERSTACK_DEBUG=true
BROWSERSTACK_LOCAL=false
BROWSERSTACK_CONSOLE_LOGS=verbose
BROWSERSTACK_NETWORK_LOGS=true

# Test Configuration
BUILD_NUMBER=local_build
TEST_BRAND=aeons
TEST_ENV=stage
TEST_BASE_URL=https://aeonstest.info

# Mailtrap Configuration
MAILTRAP_ACCOUNT_ID=your_account_id
MAILTRAP_TOKEN=your_token
MAILTRAP_AEONS_INBOX_ID=your_inbox_id

# SSH Configuration
SSH_HOST=your_host
SSH_USER=your_user
SSH_KEY=your_private_key

# Database Configuration
DB_HOST=your_db_host
DB_PORT=3306
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
```

### BrowserStack Local Testing (Optional)

If you need to test applications hosted on your local network or localhost:

1. **Enable Local Testing**:
    - Set `BROWSERSTACK_LOCAL=true` in your `.env` file

2. **Configure Local Testing**:
    - The BrowserStackSessionFactory will automatically start the BrowserStack Local binary when needed
    - No additional configuration is required

## Running Tests

### Using the BrowserStack Script

```powershell
# Run all tests
.\run-browserstack-tests.ps1

# Run specific feature
.\run-browserstack-tests.ps1 -Feature "features/purchase.feature"

# Run tests with specific tag
.\run-browserstack-tests.ps1 -Tags "@smoke_one_time"

# Run tests for specific brand
.\run-browserstack-tests.ps1 -Brand "aeons" -Environment "stage"

# Run tests for specific product
.\run-browserstack-tests.ps1 -Product "golden_harvest"

# Run in dry-run mode
.\run-browserstack-tests.ps1 -DryRun
```

### Using Behat Directly

```powershell
# Run all tests with BrowserStack profile
vendor/bin/behat --profile=browserstack

# Run specific feature with BrowserStack profile
vendor/bin/behat --profile=browserstack features/purchase.feature

# Run tests with specific tag with BrowserStack profile
vendor/bin/behat --profile=browserstack --tags=@smoke_one_time
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
    - Verify your BrowserStack username and access key
    - Check that the credentials are correctly set in your `.env` file
    - Ensure your BrowserStack subscription is active

2. **Session Creation Failures**:
    - Check that you have enough parallel sessions available in your BrowserStack plan
    - Verify that the requested browser/OS combination is available in your plan
    - Check the BrowserStack status page for any ongoing issues

3. **Test Status Not Reported**:
    - Ensure the BrowserStackContext is being used
    - Check that the test is running on BrowserStack
    - Verify that the BrowserStack session is active

4. **Local Testing Issues**:
    - Ensure your firewall allows the BrowserStack Local binary to connect
    - Check that the application is accessible on your local network
    - Verify that the correct port is being used

### Viewing Test Results

1. **BrowserStack Dashboard**:
    - Log in to your BrowserStack account
    - Navigate to Automate > Dashboard
    - View test results, screenshots, logs, and videos

2. **Local Screenshots**:
    - Screenshots are saved to the `screenshots` directory
    - Each screenshot is named with a timestamp and description

## Advanced Configuration

### Multiple Browser Configurations

You can test on different browsers and operating systems by modifying the BrowserStack capabilities in your `.env` file:

```
# For Chrome on Windows
BROWSER_NAME=chrome
BROWSER_VERSION=latest
PLATFORM=Windows
PLATFORM_VERSION=11

# For Firefox on macOS
BROWSER_NAME=firefox
BROWSER_VERSION=latest
PLATFORM=OS X
PLATFORM_VERSION=Monterey

# For Safari on iOS
BROWSER_NAME=safari
BROWSER_VERSION=latest
PLATFORM=iOS
PLATFORM_VERSION=15
```

### Custom Capabilities

You can add custom BrowserStack capabilities by modifying the `BrowserStackCapabilitiesBuilder` class or by setting
environment variables that are automatically loaded by the builder.

## Reference Documentation

- [BrowserStack Automate Documentation](https://www.browserstack.com/docs/automate/selenium)
- [BrowserStack Capabilities Generator](https://www.browserstack.com/automate/capabilities)
- [BrowserStack Local Testing](https://www.browserstack.com/docs/automate/selenium/getting-started/php/local-testing)
- [Behat Documentation](https://docs.behat.org/)
- [Mink Extension Documentation](https://github.com/FriendsOfBehat/MinkExtension)
