<?php

namespace Tests\Page;

use App\Page\CartPage;
use App\Service\Browser\BrowserServiceInterface;
use Behat\Mink\Element\NodeElement;
use PHPUnit\Framework\TestCase;

class CartPageTest extends TestCase
{
    private $browserService;
    private $cartPage;

    protected function setUp(): void
    {
        $this->browserService = $this->createMock(BrowserServiceInterface::class);
        $this->cartPage = new CartPage($this->browserService, 'https://test.example.com');
    }

    public function testGetUrl(): void
    {
        $this->assertEquals('https://test.example.com/cart', $this->cartPage->getUrl());
    }

    public function testIsEmpty(): void
    {
        $this->browserService->expects($this->once())
            ->method('elementExists')
            ->with('.cart-empty-message')
            ->willReturn(true);

        $this->assertTrue($this->cartPage->isEmpty());
    }

    public function testGetCartItems(): void
    {
        $itemElement1 = $this->createMock(NodeElement::class);
        $itemElement2 = $this->createMock(NodeElement::class);

        $nameElement1 = $this->createMock(NodeElement::class);
        $priceElement1 = $this->createMock(NodeElement::class);
        $quantityElement1 = $this->createMock(NodeElement::class);

        $nameElement2 = $this->createMock(NodeElement::class);
        $priceElement2 = $this->createMock(NodeElement::class);
        $quantityElement2 = $this->createMock(NodeElement::class);

        $itemElement1->expects($this->exactly(3))
            ->method('find')
            ->withConsecutive(
                ['css', '.item-name'],
                ['css', '.item-price'],
                ['css', '.item-quantity input']
            )
            ->willReturnOnConsecutiveCalls(
                $nameElement1,
                $priceElement1,
                $quantityElement1
            );

        $itemElement2->expects($this->exactly(3))
            ->method('find')
            ->withConsecutive(
                ['css', '.item-name'],
                ['css', '.item-price'],
                ['css', '.item-quantity input']
            )
            ->willReturnOnConsecutiveCalls(
                $nameElement2,
                $priceElement2,
                $quantityElement2
            );

        $nameElement1->expects($this->once())
            ->method('getText')
            ->willReturn('Product 1');

        $priceElement1->expects($this->once())
            ->method('getText')
            ->willReturn('$10.00');

        $quantityElement1->expects($this->once())
            ->method('getAttribute')
            ->with('value')
            ->willReturn('2');

        $nameElement2->expects($this->once())
            ->method('getText')
            ->willReturn('Product 2');

        $priceElement2->expects($this->once())
            ->method('getText')
            ->willReturn('$15.00');

        $quantityElement2->expects($this->once())
            ->method('getAttribute')
            ->with('value')
            ->willReturn('1');

        $this->browserService->expects($this->once())
            ->method('findElements')
            ->with('.cart-items .cart-item')
            ->willReturn([$itemElement1, $itemElement2]);

        $expected = [
            [
                'name' => 'Product 1',
                'price' => '$10.00',
                'quantity' => 2
            ],
            [
                'name' => 'Product 2',
                'price' => '$15.00',
                'quantity' => 1
            ]
        ];

        $this->assertEquals($expected, $this->cartPage->getCartItems());
    }

    public function testGetCartTotal(): void
    {
        $this->browserService->expects($this->once())
            ->method('getElementText')
            ->with('.cart-summary .cart-total')
            ->willReturn('$25.00');

        $this->assertEquals('$25.00', $this->cartPage->getCartTotal());
    }

    public function testProceedToCheckoutWithEmptyCart(): void
    {
        $this->browserService->expects($this->once())
            ->method('elementExists')
            ->with('.cart-empty-message')
            ->willReturn(true);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Cannot proceed to checkout with empty cart');

        $this->cartPage->proceedToCheckout();
    }

    public function testProceedToCheckout(): void
    {
        $this->browserService->expects($this->once())
            ->method('elementExists')
            ->with('.cart-empty-message')
            ->willReturn(false);

        $this->browserService->expects($this->once())
            ->method('clickElement')
            ->with('.checkout-button');

        $this->browserService->expects($this->once())
            ->method('waitForDocumentReady');

        $this->browserService->expects($this->once())
            ->method('waitForElementVisible')
            ->with('.checkout-steps')
            ->willReturn(true);

        $this->cartPage->proceedToCheckout();
    }

    public function testFindCartItemByName(): void
    {
        $itemElement1 = $this->createMock(NodeElement::class);
        $itemElement2 = $this->createMock(NodeElement::class);

        $nameElement1 = $this->createMock(NodeElement::class);
        $nameElement2 = $this->createMock(NodeElement::class);

        $itemElement1->expects($this->once())
            ->method('find')
            ->with('css', '.item-name')
            ->willReturn($nameElement1);

        $itemElement2->expects($this->once())
            ->method('find')
            ->with('css', '.item-name')
            ->willReturn($nameElement2);

        $nameElement1->expects($this->once())
            ->method('getText')
            ->willReturn('Product 1');

        $nameElement2->expects($this->once())
            ->method('getText')
            ->willReturn('Product 2');

        $this->browserService->expects($this->once())
            ->method('findElements')
            ->with('.cart-items .cart-item')
            ->willReturn([$itemElement1, $itemElement2]);

        $method = new \ReflectionMethod(CartPage::class, 'findCartItemByName');
        $method->setAccessible(true);

        $result = $method->invoke($this->cartPage, 'Product 2');
        $this->assertSame($itemElement2, $result);
    }

    public function testUpdateItemQuantity(): void
    {
        $itemElement = $this->createMock(NodeElement::class);
        $nameElement = $this->createMock(NodeElement::class);
        $quantityElement = $this->createMock(NodeElement::class);

        $itemElement->expects($this->exactly(2))
            ->method('find')
            ->withConsecutive(
                ['css', '.item-name'],
                ['css', '.item-quantity input']
            )
            ->willReturnOnConsecutiveCalls(
                $nameElement,
                $quantityElement
            );

        $nameElement->expects($this->once())
            ->method('getText')
            ->willReturn('Product 1');

        $quantityElement->expects($this->once())
            ->method('setValue')
            ->with('3');

        $this->browserService->expects($this->once())
            ->method('findElements')
            ->with('.cart-items .cart-item')
            ->willReturn([$itemElement]);

        $this->browserService->expects($this->once())
            ->method('clickElement')
            ->with('.update-cart-button');

        $this->browserService->expects($this->once())
            ->method('waitForAjaxToComplete');

        $this->cartPage->updateItemQuantity('Product 1', 3);
    }

    public function testUpdateItemQuantityProductNotFound(): void
    {
        $itemElement = $this->createMock(NodeElement::class);
        $nameElement = $this->createMock(NodeElement::class);

        $itemElement->expects($this->once())
            ->method('find')
            ->with('css', '.item-name')
            ->willReturn($nameElement);

        $nameElement->expects($this->once())
            ->method('getText')
            ->willReturn('Product 1');

        $this->browserService->expects($this->once())
            ->method('findElements')
            ->with('.cart-items .cart-item')
            ->willReturn([$itemElement]);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Product "Product 2" not found in cart');

        $this->cartPage->updateItemQuantity('Product 2', 3);
    }

    public function testRemoveItem(): void
    {
        $itemElement = $this->createMock(NodeElement::class);
        $nameElement = $this->createMock(NodeElement::class);
        $removeButton = $this->createMock(NodeElement::class);

        $itemElement->expects($this->exactly(2))
            ->method('find')
            ->withConsecutive(
                ['css', '.item-name'],
                ['css', '.remove-item-button']
            )
            ->willReturnOnConsecutiveCalls(
                $nameElement,
                $removeButton
            );

        $nameElement->expects($this->once())
            ->method('getText')
            ->willReturn('Product 1');

        $removeButton->expects($this->once())
            ->method('click');

        $this->browserService->expects($this->once())
            ->method('findElements')
            ->with('.cart-items .cart-item')
            ->willReturn([$itemElement]);

        $this->browserService->expects($this->once())
            ->method('waitForAjaxToComplete');

        $this->cartPage->removeItem('Product 1');
    }

    public function testRemoveItemProductNotFound(): void
    {
        $itemElement = $this->createMock(NodeElement::class);
        $nameElement = $this->createMock(NodeElement::class);

        $itemElement->expects($this->once())
            ->method('find')
            ->with('css', '.item-name')
            ->willReturn($nameElement);

        $nameElement->expects($this->once())
            ->method('getText')
            ->willReturn('Product 1');

        $this->browserService->expects($this->once())
            ->method('findElements')
            ->with('.cart-items .cart-item')
            ->willReturn([$itemElement]);

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Product "Product 2" not found in cart');

        $this->cartPage->removeItem('Product 2');
    }

    public function testVerifyPage(): void
    {
        $this->browserService->expects($this->once())
            ->method('waitForElementVisible')
            ->with('.cart-summary')
            ->willReturn(true);

        $method = new \ReflectionMethod(CartPage::class, 'verifyPage');
        $method->setAccessible(true);

        $method->invoke($this->cartPage);
    }
}
