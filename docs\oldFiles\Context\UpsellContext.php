<?php

namespace Features\Bootstrap\Context;

use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Features\Bootstrap\Page\UpsellPage;
use App\Service\State\SharedStateServiceInterface;

/**
 * UpsellContext handles upsell-related test steps.
 */
class UpsellContext extends BaseContext
{
    private ?UpsellPage $upsellPage = null;
    private ?EmailContext $emailContext = null;

    public function __construct(?UpsellPage $upsellPage = null)
    {
        parent::__construct();
        $this->upsellPage = $upsellPage ?? $this->createDefaultUpsellPage();
        $this->sharedData = this->stateService;
    }

    private function createDefaultUpsellPage(): UpsellPage
    {
        // Create a default UpsellPage with base URL for dry run
        return new UpsellPage("https://aeonstest.info");
    }

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        $environment = $scope->getEnvironment();

        // Get other contexts we'll need if they're available
        if ($environment->hasContextClass('Features\Bootstrap\Context\EmailContext')) {
            $this->emailContext = $environment->getContext('Features\Bootstrap\Context\EmailContext');
        }
    }

    /**
     * @When /^I accept the upsell offer$/
     */
    public function iAcceptTheUpsellOffer(): void
    {
        $this->upsellPage->acceptUpsell();
        $this->stateService->set('upsellAccepted', true);
    }

    /**
     * @When /^I decline the upsell offer$/
     */
    public function iDeclineTheUpsellOffer(): void
    {
        $this->upsellPage->declineUpsell();
        $this->stateService->set('upsellDeclined', true);
    }

    /**
     * @Then /^I should see the upsell message$/
     */
    public function iShouldSeeTheUpsellMessage(): void
    {
        $this->upsellPage->waitForPageToLoad();
    }

    /**
     * @When /^I accept the first upsell offer$/
     */
    public function iAcceptTheFirstUpsellOffer(): void
    {
        $this->iAcceptTheUpsellOffer();
        $this->stateService->set('firstUpsellAccepted', true);
    }

    /**
     * @When /^I accept the second upsell offer$/
     */
    public function iAcceptTheSecondUpsellOffer(): void
    {
        $this->iAcceptTheUpsellOffer();
        $this->stateService->set('secondUpsellAccepted', true);
    }

    /**
     * @When /^I decline the first upsell offer$/
     */
    public function iDeclineTheFirstUpsellOffer(): void
    {
        $this->iDeclineTheUpsellOffer();
        $this->stateService->set('firstUpsellDeclined', true);
    }

    /**
     * @When /^I decline the second upsell offer$/
     */
    public function iDeclineTheSecondUpsellOffer(): void
    {
        $this->iDeclineTheUpsellOffer();
        $this->stateService->set('secondUpsellDeclined', true);
    }

    /**
     * @When /^I click the accept button multiple times$/
     */
    public function iClickTheAcceptButtonMultipleTimes(): void
    {
        $this->upsellPage->clickAcceptMultipleTimes(3);
    }

    /**
     * @Then /^I verify dietary restriction warnings are displayed$/
     */
    public function iVerifyDietaryRestrictionWarningsAreDisplayed(): void
    {
        $funnel = $this->stateService->get('currentFunnel');

        if (!isset($funnel['restrictions']) || !isset($funnel['restrictions']['dietary'])) {
            throw new \RuntimeException('No dietary restrictions defined for this funnel');
        }

        $warnings = $funnel['restrictions']['dietary'];

        foreach ($warnings as $warning) {
            if (!$this->upsellPage->hasRestrictionWarning($warning)) {
                throw new \RuntimeException(
                    sprintf('Expected dietary restriction warning "%s" not found', $warning)
                );
            }
        }
    }

    /**
     * @Then /^I should be redirected to the (first |second |)upsell page$/
     */
    public function iShouldBeRedirectedToTheUpsellPage($position = ''): void
    {
        try {
            $this->upsellPage->waitForPageToLoad();

            // For multi-step funnels, verify we're on the correct upsell step
            if ($position) {
                $funnelData = $this->stateService->get('currentFunnel');
                $expectedStep = trim($position) === 'first' ? 1 : 2;

                // Store current upsell step in shared data
                $this->stateService->set('currentUpsellStep', $expectedStep);

                // Verify the correct upsell product is shown
                $expectedProduct = isset($funnelData['upsells'])
                    ? $funnelData['upsells'][$expectedStep - 1]['name']
                    : $funnelData['upsell_product']['name'];

                $productName = $this->upsellPage->getUpsellProductName();

                if (strpos($productName, $expectedProduct) === false) {
                    throw new \RuntimeException(
                        sprintf('Expected upsell product "%s" not found in "%s"', $expectedProduct, $productName)
                    );
                }
            }

            $this->logStep("Verified redirect to " . ($position ? $position : '') . "upsell page");
        } catch (\Exception $e) {
            throw new \RuntimeException(
                sprintf('Failed to verify redirect to upsell page: %s', $e->getMessage())
            );
        }
    }

    /**
     * @Then /^I verify only one upsell product is in the order$/
     */
    public function iVerifyOnlyOneUpsellProductIsInTheOrder(): void
    {
        // This step needs to wait for confirmation page to load and verify product count
        // Will be implemented in SalesFunnelContext with access to ConfirmationPage
    }

    /**
     * Helper method to log test steps with timestamps
     */
    private function logStep(string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        error_log("[$timestamp] [UpsellContext] $message");
    }
}
