# Import service definitions
imports:
    - { resource: 'services/optimization.yml' }
  - { resource: 'services/core.yml' }
  - { resource: 'services/compatibility.yml' }
  - { resource: 'services/contexts.yml' }
  - { resource: 'services/pages.yml' }

parameters:
  # Global parameters
  paths.base: '%kernel.project_dir%'
  app.project_root: '%kernel.project_dir%'
  app.config_dir: '%app.project_root%/config'
  app.fixtures_dir: '%app.project_root%/features/fixtures'
  env(TEST_BRAND): 'aeons' # Default brand
  env(TEST_ENV): 'stage' # Default environment
  env(SSH_HOST): ''
  env(SSH_USER): ''

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false
    bind:
      $projectRoot: '%app.project_root%'
      $configDir: '%app.config_dir%'
      $fixturesDir: '%app.fixtures_dir%'

  # Auto-register services
  App\Service\:
    resource: '../src/Service/*'
    exclude: '../src/Service/AbstractService.php'

  # Legacy services - will be migrated in later phases
  Features\Bootstrap\Core\ConfigurationManager:
    public: true
    arguments:
      $configPath: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'

  Features\Bootstrap\Core\TestDataRegistry:
    public: true
    arguments:
      $validator: '@Features\Bootstrap\Core\DataValidator'
      $fixturesPath: '%app.fixtures_dir%'
      $configManager: '@Features\Bootstrap\Core\ConfigurationManager'

  Features\Bootstrap\Core\DataValidator:
    public: true

  # Shared Data Context (as a regular, shared service - default behavior)
  Features\Bootstrap\SharedDataContext:
    public: true

  # Context Manager (as a regular service, avoid if possible)
  Features\Bootstrap\ContextManager:
    public: true
    arguments: [ '@service_container' ]

  # Auto-configure Page Objects
  Features\Bootstrap\Page\:
    resource: '../features/bootstrap/Page/*'
    public: true

  # Auto-configure Contexts
  Features\Bootstrap\Context\:
    resource: '../features/bootstrap/Context/*'
    public: true
    arguments:
      $sharedData: '@Features\Bootstrap\SharedDataContext'
    tags: [ 'context.service' ]
    exclude:
      - '../features/bootstrap/Context/BaseContext.php'
      - '../features/bootstrap/Context/Migration/*'

  # Explicit overrides for contexts that need specific arguments
  Features\Bootstrap\Context\SSHContext:
    public: true
    arguments:
      $sharedData: '@Features\Bootstrap\SharedDataContext'
      $host: '%env(SSH_HOST)%'
      $username: '%env(SSH_USER)%'
    tags: [ 'context.service' ]

  Features\Bootstrap\Context\TestDataContext:
    public: true
    arguments:
      $sharedData: '@Features\Bootstrap\SharedDataContext'
      $configManager: '@Features\Bootstrap\Core\ConfigurationManager'
      $testDataRegistry: '@Features\Bootstrap\Core\TestDataRegistry'
    tags: [ 'context.service' ]

  # Remove explicit definitions for contexts if covered by resource loading & autowiring
  # Example: Remove NavigationContext, ProductContext, CartContext etc. specific definitions
  # UNLESS they require specific, non-autowireable arguments or setter calls
  # Remove ALL `calls:` blocks for `setContextManager` - contexts should get dependencies via constructor

  # Keep Mink services if still needed and not provided by MinkExtension itself
  # Behat\Mink\Mink:
  #   class: Behat\Mink\Mink
  #   public: true
  #
  # Behat\Mink\Session:
  #   class: Behat\Mink\Session
  #   public: true
  #   factory: [ '@Behat\Mink\Mink', 'getSession' ]
  #   arguments: [ 'local' ] # Session name might vary

