services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: true
  # Configuration Service
  App\Service\Configuration\ConfigurationServiceInterface:
    alias: App\Service\Configuration\ConfigurationService
    public: true

  App\Service\Configuration\ConfigurationService:
    arguments:
      $configDir: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
    public: true

  # Test Data Service
  App\Service\Data\TestDataServiceInterface:
    alias: App\Service\Data\TestDataService
    public: true

  App\Service\Data\TestDataService:
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
      $validator: '@App\Service\Validation\ValidationServiceInterface'
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
      $logger: '@logger'
    public: true

  # Shared State Service
  App\Service\State\SharedStateServiceInterface:
    alias: App\Service\State\SharedStateService
    public: true

  App\Service\State\SharedStateService:
    public: true
    tags:
      - { name: kernel.event_subscriber }

  # Validation Service
  App\Service\Validation\ValidationServiceInterface:
    alias: App\Service\Validation\ValidationService
    public: true

  App\Service\Validation\ValidationService:
    arguments:
      $schemasDir: '%app.project_root%/config/schemas'
      $logger: '@logger'
    public: true

  # Browser Service (base implementation)
  App\Service\Browser\BrowserService:
    arguments:
      $session: '@mink.session'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: true

  # Note: The Browser Service Interface is defined in optimization.yml

  # Logger Service
  logger:
    class: Psr\Log\NullLogger
    public: true

  # Cache Service
  App\Service\Cache\CacheServiceInterface:
    alias: App\Service\Cache\FilesystemCacheService
    public: true

  App\Service\Cache\FilesystemCacheService:
    arguments:
      $cacheDir: '%app.cache_dir%/browser'
    public: true

  mink.session:
    class: Behat\Mink\Session
    factory: [ '@mink.session_factory', 'createSession' ]
    public: true

  mink.session_factory:
    class: App\Service\Browser\SessionFactory
    arguments:
      $driverName: 'chrome'
      $driverOptions:
        api_url: '%env(BROWSERSTACK_URL)%'
        download_behavior: 'allow'
        download_path: '%app.project_root%/downloads'
        socket_timeout: 60
    public: true

  # Test Runner Service
  App\Service\TestRunner\TestRunnerServiceInterface:
    alias: App\Service\TestRunner\TestRunnerService
    public: true

  App\Service\TestRunner\TestRunnerService:
    arguments:
      $configService: '@App\Service\Configuration\ConfigurationServiceInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $reportingService: '@App\Service\Reporting\ReportingServiceInterface'
      $envService: '@App\Service\Environment\EnvironmentServiceInterface'
      $projectRoot: '%app.project_root%'
      $logger: '@logger'
    public: true

  # Reporting Service
  App\Service\Reporting\ReportingServiceInterface:
    alias: App\Service\Reporting\ReportingService

  App\Service\Reporting\ReportingService:
    arguments:
      $projectRoot: '%app.project_root%'
    public: true

  # Environment Service
  App\Service\Environment\EnvironmentServiceInterface:
    alias: App\Service\Environment\EnvironmentService
    public: true

  App\Service\Environment\EnvironmentService:
    public: true
