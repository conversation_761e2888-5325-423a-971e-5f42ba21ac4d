<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Exception\ElementNotFoundException;

/**
 * Stripe3DSPage handles actions on the Stripe 3D Secure authentication page.
 */
class Stripe3DSPage extends BasePage
{
    /**
     * The path of the Stripe 3DS page.
     *
     * @var string
     */
    protected $path = '/stripe/3ds';

    /**
     * @var array
     */
    private const SELECTORS = [
        'COMPLETE_BUTTON' => 'button[data-testid="hosted-3ds2-complete"]',
        'FAIL_BUTTON' => 'button[data-testid="hosted-3ds2-fail"]',
        '3DS_IFRAME' => 'iframe[name*="privateStripeFrame"]'
    ];

    /**
     * Verifies that we're on the expected page.
     *
     * @throws ElementNotFoundException If required elements are not found
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        try {
            $this->waitForElementVisible(self::SELECTORS['3DS_IFRAME']);
        } catch (ElementNotFoundException $e) {
            error_log("[Stripe3DSPage] Failed to verify page: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Gets the URL for the Stripe 3DS page.
     *
     * @return string The complete URL
     */
    public function getUrl(array $urlParameters = []): string
    {
        return $this->baseUrl . $this->path;
    }

    /**
     * Check if element exists on the page
     *
     * @param string $name
     * @param array $parameters
     * @return bool
     */
    protected function hasElement($name, array $parameters = []): bool
    {
        try {
            if (!isset(self::SELECTORS[$name])) {
                error_log("[Stripe3DSPage] Invalid selector name: $name");
                return false;
            }
            return $this->findElement(self::SELECTORS[$name]) !== null;
        } catch (ElementNotFoundException $e) {
            error_log("[Stripe3DSPage] Element not found: $name");
            return false;
        }
    }

    /**
     * Check if 3DS page is displayed
     *
     * @return bool
     */
    public function is3DSPageDisplayed(): bool
    {
        try {
            // Wait briefly for iframe to appear with proper error handling
            $this->getSession()->wait(2000, 
                "document.querySelector('iframe[name*=\"privateStripeFrame\"]') !== null"
            );
            $isDisplayed = $this->hasElement('3DS_IFRAME');
            error_log("[Stripe3DSPage] 3DS page display status: " . ($isDisplayed ? 'true' : 'false'));
            return $isDisplayed;
        } catch (\Exception $e) {
            error_log("[Stripe3DSPage] Error checking 3DS page display: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Switch to 3DS iframe with improved error handling and frame detection
     *
     * @throws \RuntimeException If the 3DS iframe cannot be found or accessed
     */
    public function switchTo3DSIframe(): void
    {
        try {
            error_log("[Stripe3DSPage] Attempting to switch to 3DS iframe");
            
            // Wait for iframe to be present
            $this->getSession()->wait(5000, 
                "document.querySelector('iframe[name*=\"privateStripeFrame\"]') !== null"
            );

            // Find the 3DS iframe
            $iframe = $this->findElement(self::SELECTORS['3DS_IFRAME']);
            if (!$iframe || !$iframe->isValid()) {
                throw new \RuntimeException('3DS iframe not found or not accessible');
            }

            // Switch to the iframe
            $this->getSession()->switchToIFrame($iframe->getAttribute('name'));
            
            // Verify we're in the correct frame
            $this->waitForElementVisible(self::SELECTORS['COMPLETE_BUTTON'], 15000);
            error_log("[Stripe3DSPage] Successfully switched to 3DS iframe");

        } catch (\Exception $e) {
            $error = sprintf('Failed to switch to 3DS iframe: %s', $e->getMessage());
            error_log("[Stripe3DSPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Click Complete button with improved error handling
     *
     * @throws \RuntimeException If the complete button cannot be clicked
     */
    public function clickCompleteButton(): void
    {
        try {
            error_log("[Stripe3DSPage] Attempting to click complete button");
            $this->switchTo3DSIframe();
            $this->clickElement(self::SELECTORS['COMPLETE_BUTTON']);
            $this->getSession()->switchToIFrame(null);
            error_log("[Stripe3DSPage] Successfully clicked complete button");
        } catch (\Exception $e) {
            $error = sprintf('Failed to click complete button: %s', $e->getMessage());
            error_log("[Stripe3DSPage] " . $error);
            throw new \RuntimeException($error);
        }
    }

    /**
     * Click Fail button with improved error handling
     *
     * @throws \RuntimeException If the fail button cannot be clicked
     */
    public function clickFailButton(): void
    {
        try {
            error_log("[Stripe3DSPage] Attempting to click fail button");
            $this->switchTo3DSIframe();
            $this->clickElement(self::SELECTORS['FAIL_BUTTON']);
            $this->getSession()->switchToIFrame(null);
            error_log("[Stripe3DSPage] Successfully clicked fail button");
        } catch (\Exception $e) {
            $error = sprintf('Failed to click fail button: %s', $e->getMessage());
            error_log("[Stripe3DSPage] " . $error);
            throw new \RuntimeException($error);
        }
    }
} 