Feature: Sales Funnel Error Recovery
  As an e-commerce store administrator
  I want to handle errors and recover abandoned sales funnel purchases
  So that I can maximize conversions despite technical issues

  Background:
    Given I load brand configuration
    And I load product data

  @funnel @error-recovery @high-priority
  Scenario: Force complete a sales funnel order after frontend upsell page failure
    Given I am logged into the admin panel
    And a sales funnel item with code "demo-dsv-1" exists as an initial product
    And I open the sales funnel in a new browser window
    When I fill in the checkout form with email "<EMAIL>"
    And I select the "Domestic tracked" shipping method
    And I enter "stripe_valid" payment details
    And I complete the purchase
    And the upsell page fails to load
    Then I run the sales funnel completion command
    And a new order should be created with status "Paid"
    And the order should contain only the initial product
    And I verify the order confirmation email was sent
    And I verify the welcome email contains account credentials

  @funnel @abandoned-cart @email
  Scenario: Abandoned cart during PayPal checkout in a sales funnel
    Given I am logged into the admin panel
    And a sales funnel item with code "demo-dsv-1" exists as an initial product
    And I open the sales funnel in a new browser window
    When I fill in the checkout form with email "<EMAIL>"
    And I select the "Domestic tracked" shipping method
    And I select the "PayPal" payment method
    And I complete the purchase
    And I am redirected to the PayPal login page
    And I close the browser without completing payment
    Then I run the abandoned cart command
    And the order status should be "Cancelled"
    And I verify an abandoned cart email was sent
    And the abandoned cart email contains a recovery link

  @funnel @abandoned-cart @recovery
  Scenario: Recover abandoned funnel purchase through recovery link
    Given I have an abandoned funnel purchase for "demo-dsv-1"
    And an abandoned cart email has been sent
    When I click the recovery link in the email
    Then I should be redirected to the funnel checkout page
    And my shipping information should be pre-filled
    When I complete the purchase
    And I accept the upsell offer
    Then a new order should be created with status "Paid"
    And the order should contain both initial and upsell products
    And I verify the order confirmation email was sent

  @funnel @auto-recovery
  Scenario: Automatic completion of sales funnel order after timeout
    Given I am logged into the admin panel
    And a sales funnel item with code "demo-dsv-1" exists as an initial product
    And I open the sales funnel in a new browser window
    When I fill in the checkout form with email "<EMAIL>"
    And I select the "Domestic tracked" shipping method
    And I enter "stripe_valid" payment details
    And I complete the purchase
    And I wait for the automatic order completion timeout
    Then a new order should be created with status "Paid"
    And the order should contain only the initial product
    And I verify the order confirmation email was sent

  @funnel @error-handling @session
  Scenario: Maintain funnel session across page reloads
    Given I am on the sales funnel page "total-harmony-funnel"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I refresh the browser
    Then my shipping information should still be present
    When I complete the purchase
    And I accept the upsell offer
    Then I wait for the order confirmation page to load
    And I verify the order details are correct