param (
    [string]$Feature = "features/salesFunnel.feature",
    [string]$Tags = "@high-priority",
    [string]$Brand = "aeons",
    [string]$Environment = "stage",
    [string]$BaseUrl = "https://aeonstest.info",
    [string]$Product = "",
    [switch]$DryRun = $false,
    [switch]$Debug = $false,
    [switch]$Help = $false
)

# Show help if requested
if ($Help) {
    Write-Host "Malaberg Test Framework - BrowserStack Test Runner" -ForegroundColor Cyan
    Write-Host "Usage: .\run-browserstack-tests.ps1 [OPTIONS]" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Feature PATH        Feature file to run (default: features/salesFunnel.feature)" -ForegroundColor White
    Write-Host "  -Tags TAGS           Behat tags to run (default: @high-priority)" -ForegroundColor White
    Write-Host "  -Brand BRAND         Brand to test (default: aeons)" -ForegroundColor White
    Write-Host "  -Environment ENV     Environment to test (default: stage)" -ForegroundColor White
    Write-Host "  -BaseUrl URL         Base URL for tests (default: https://aeonstest.info)" -ForegroundColor White
    Write-Host "  -Product PRODUCT     Specific product to test (optional)" -ForegroundColor White
    Write-Host "  -DryRun              Perform a dry run without executing tests" -ForegroundColor White
    Write-Host "  -Debug               Enable debug mode" -ForegroundColor White
    Write-Host "  -Help                Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\run-browserstack-tests.ps1 -Feature 'features/checkout.feature' -Tags '@regression'" -ForegroundColor White
    Write-Host "  .\run-browserstack-tests.ps1 -Brand 'aeons' -Environment 'dev' -BaseUrl 'https://dev.aeonstest.info'" -ForegroundColor White
    Write-Host "  .\run-browserstack-tests.ps1 -Product 'golden_harvest' -DryRun" -ForegroundColor White
    exit 0
}

Write-Host "Malaberg Test Framework - BrowserStack Test Runner" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

# Set environment variables
$env:TEST_BRAND = $Brand
$env:TEST_ENV = $Environment
$env:TEST_BASE_URL = $BaseUrl
$env:TEST_PRODUCT = $Product
$env:BUILD_NUMBER = "local_build_" + (Get-Date -Format "yyyyMMddHHmmss")

# Update environment variables loading to prefer .env.browserstack if it exists
$browserStackEnvFile = Join-Path $PSScriptRoot ".env.browserstack"
$browserStackExampleFile = Join-Path $PSScriptRoot "config\env.browserstack.example"
$defaultEnvFile = Join-Path $PSScriptRoot ".env"

# First try to load BrowserStack-specific env file
if (Test-Path $browserStackEnvFile) {
    Write-Host "Loading environment variables from .env.browserstack file..." -ForegroundColor Gray
    Get-Content $browserStackEnvFile | ForEach-Object {
        $line = $_.Trim()
        # Ignore comments and empty lines
        if ($line -and $line -notmatch '^\s*#') {
            $parts = $line -split '=', 2
            if ($parts.Length -eq 2) {
                $key = $parts[0].Trim()
                $value = $parts[1].Trim()
                # Remove surrounding quotes if present
                if (($value.StartsWith('"') -and $value.EndsWith('"')) -or ($value.StartsWith("'") -and $value.EndsWith("'"))) {
                    $value = $value.Substring(1, $value.Length - 2)
                }
                # Set environment variable for the current process
                [System.Environment]::SetEnvironmentVariable($key, $value, [System.EnvironmentVariableTarget]::Process)
                Write-Host "  Set: $key" -ForegroundColor DarkGray
            }
        }
    }
} elseif (Test-Path $browserStackExampleFile) {
    Write-Host "Loading environment variables from config\env.browserstack.example file..." -ForegroundColor Gray
    Write-Host "NOTE: This is using example values. Create a .env.browserstack file with your own values." -ForegroundColor Yellow
    Get-Content $browserStackExampleFile | ForEach-Object {
        $line = $_.Trim()
        # Ignore comments and empty lines
        if ($line -and $line -notmatch '^\s*#') {
            $parts = $line -split '=', 2
            if ($parts.Length -eq 2) {
                $key = $parts[0].Trim()
                $value = $parts[1].Trim()
                # Remove surrounding quotes if present
                if (($value.StartsWith('"') -and $value.EndsWith('"')) -or ($value.StartsWith("'") -and $value.EndsWith("'"))) {
                    $value = $value.Substring(1, $value.Length - 2)
                }
                # Set environment variable for the current process
                [System.Environment]::SetEnvironmentVariable($key, $value, [System.EnvironmentVariableTarget]::Process)
                Write-Host "  Set: $key" -ForegroundColor DarkGray
            }
        }
    }
}

# Then load default .env file, but don't override existing variables
if (Test-Path $defaultEnvFile) {
    Write-Host "Loading environment variables from .env file..." -ForegroundColor Gray
    Get-Content $defaultEnvFile | ForEach-Object {
        $line = $_.Trim()
        # Ignore comments and empty lines
        if ($line -and $line -notmatch '^\s*#') {
            $parts = $line -split '=', 2
            if ($parts.Length -eq 2) {
                $key = $parts[0].Trim()
                $value = $parts[1].Trim()
                
                # Only set if not already set by browserstack env file
                if (-not [System.Environment]::GetEnvironmentVariable($key)) {
                    # Remove surrounding quotes if present
                    if (($value.StartsWith('"') -and $value.EndsWith('"')) -or ($value.StartsWith("'") -and $value.EndsWith("'"))) {
                        $value = $value.Substring(1, $value.Length - 2)
                    }
                    # Set environment variable for the current process
                    [System.Environment]::SetEnvironmentVariable($key, $value, [System.EnvironmentVariableTarget]::Process)
                    Write-Host "  Set: $key" -ForegroundColor DarkGray
                }
            }
        }
    }
}

if (-not $env:BROWSERSTACK_USERNAME -or -not $env:BROWSERSTACK_ACCESS_KEY) {
    Write-Host "BrowserStack credentials not set. Please set BROWSERSTACK_USERNAME and BROWSERSTACK_ACCESS_KEY environment variables." -ForegroundColor Red
    Write-Host "You can set them temporarily for this session with:" -ForegroundColor Yellow
    Write-Host '$env:BROWSERSTACK_USERNAME = "your_username"' -ForegroundColor Yellow
    Write-Host '$env:BROWSERSTACK_ACCESS_KEY = "your_access_key"' -ForegroundColor Yellow
    exit 1
}

# Prepare dry run flag if needed
$dryRunFlag = ""
if ($DryRun) {
    $dryRunFlag = "--dry-run"
    Write-Host "Running in DRY RUN mode" -ForegroundColor Magenta
}

# Display test information
Write-Host "Running tests with:" -ForegroundColor Yellow
Write-Host "  Feature: $Feature" -ForegroundColor Yellow
Write-Host "  Tags: $Tags" -ForegroundColor Yellow
Write-Host "  Brand: $Brand" -ForegroundColor Yellow
Write-Host "  Environment: $Environment" -ForegroundColor Yellow
Write-Host "  Base URL: $BaseUrl" -ForegroundColor Yellow
if ($Product) {
    Write-Host "  Product: $Product" -ForegroundColor Yellow
}
Write-Host "  Mode: BrowserStack Cloud" -ForegroundColor Cyan

# Prepare product parameter if specified
$productParam = ""
if ($Product) {
    $productParam = "--product=$Product"
}

# Prepare debug mode if enabled
$debugMode = ""
if ($Debug) {
    $debugMode = "--debug"
    Write-Host "Debug mode enabled" -ForegroundColor Magenta
}

# Run the tests
Write-Host "Executing tests on BrowserStack..." -ForegroundColor Green

# Build the command
$behatCmd = "vendor/bin/behat --profile=browserstack"

if ($Feature) {
    $behatCmd += " $Feature"
}

if ($Tags) {
    $behatCmd += " --tags=$Tags"
}

if ($DryRun) {
    $behatCmd += " --dry-run"
}

if ($Debug) {
    $behatCmd += " --verbose"
}

# Execute the command
try {
    Invoke-Expression $behatCmd
} catch {
    Write-Host "Error executing tests: $_" -ForegroundColor Red
    $exitCode = 1
}

# Check exit code
$exitCode = $LASTEXITCODE
if ($exitCode -eq 0) {
    Write-Host "Tests completed successfully on BrowserStack!" -ForegroundColor Green
} else {
    Write-Host "Tests failed with exit code: $exitCode" -ForegroundColor Red
    Write-Host "Check BrowserStack dashboard for error details." -ForegroundColor Yellow
}

exit $exitCode
