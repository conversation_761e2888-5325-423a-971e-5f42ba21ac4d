<?php

namespace App\Service\Container;

use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Service to provide access to the container
 */
class ContainerProvider
{
    private ContainerInterface $container;

    /**
     * Constructor
     *
     * @param ContainerInterface $container
     */
    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        // Also set the container in the global scope
        $GLOBALS['service_container'] = $container;

        // Call the setServiceContainer function if it exists
        if (function_exists('setServiceContainer')) {
            setServiceContainer($container);
        }
    }

    /**
     * Get the container
     *
     * @return ContainerInterface
     */
    public function getContainer(): ContainerInterface
    {
        return $this->container;
    }
}
