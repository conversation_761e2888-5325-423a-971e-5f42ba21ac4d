<?php

namespace App\Service\Environment;

interface EnvironmentServiceInterface
{
    /**
     * Set an environment variable
     *
     * @param string $name Variable name
     * @param string $value Variable value
     * @return void
     */
    public function setVariable(string $name, string $value): void;

    /**
     * Get an environment variable
     *
     * @param string $name Variable name
     * @param string|null $default Default value
     * @return string|null Variable value
     */
    public function getVariable(string $name, string $default = null): ?string;

    /**
     * Check if an environment variable exists
     *
     * @param string $name Variable name
     * @return bool Whether the variable exists
     */
    public function hasVariable(string $name): bool;

    /**
     * Load environment variables from a file
     *
     * @param string $filePath Path to environment file
     * @return void
     */
    public function loadFromFile(string $filePath): void;

    /**
     * Get all environment variables
     *
     * @return array All environment variables
     */
    public function getAllVariables(): array;
}
