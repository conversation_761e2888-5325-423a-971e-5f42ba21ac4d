# Shared State Service Documentation

## Overview

The Shared State Service is a key component of the Malaberg test automation framework. It provides a mechanism for sharing state between different steps and contexts during test execution. This document covers the Shared State Service interface and implementation.

## SharedStateServiceInterface

The `SharedStateServiceInterface` defines the contract for shared state services.

```php
interface SharedStateServiceInterface
{
    /**
     * Set a value in the shared state
     *
     * @param string $key Key to set
     * @param mixed $value Value to set
     * @param string $scope Scope of the value (scenario, feature, or global)
     * @return void
     */
    public function set(string $key, $value, string $scope = 'scenario'): void;

    /**
     * Get a value from the shared state
     *
     * @param string $key Key to get
     * @param string $scope Scope to search in (scenario, feature, or global)
     * @param mixed $default Default value to return if key is not found
     * @return mixed
     */
    public function get(string $key, string $scope = 'scenario', $default = null);

    /**
     * Check if a key exists in the shared state
     *
     * @param string $key Key to check
     * @param string $scope Scope to search in (scenario, feature, or global)
     * @return bool
     */
    public function has(string $key, string $scope = 'scenario'): bool;

    /**
     * Remove a value from the shared state
     *
     * @param string $key Key to remove
     * @param string $scope Scope to remove from (scenario, feature, or global)
     * @return void
     */
    public function remove(string $key, string $scope = 'scenario'): void;

    /**
     * Reset the shared state for a specific scope
     *
     * @param string $scope Scope to reset (scenario, feature, or global)
     * @return void
     */
    public function reset(string $scope = 'scenario'): void;
}
```

## SharedStateService Implementation

The `SharedStateService` class implements the `SharedStateServiceInterface` and provides the actual shared state functionality.

```php
class SharedStateService implements SharedStateServiceInterface
{
    /**
     * Shared state data
     *
     * @var array
     */
    private array $state = [
        'scenario' => [],
        'feature' => [],
        'global' => []
    ];

    /**
     * {@inheritdoc}
     */
    public function set(string $key, $value, string $scope = 'scenario'): void
    {
        $this->validateScope($scope);
        $this->state[$scope][$key] = $value;
    }

    /**
     * {@inheritdoc}
     */
    public function get(string $key, string $scope = 'scenario', $default = null)
    {
        $this->validateScope($scope);
        return $this->has($key, $scope) ? $this->state[$scope][$key] : $default;
    }

    /**
     * {@inheritdoc}
     */
    public function has(string $key, string $scope = 'scenario'): bool
    {
        $this->validateScope($scope);
        return array_key_exists($key, $this->state[$scope]);
    }

    /**
     * {@inheritdoc}
     */
    public function remove(string $key, string $scope = 'scenario'): void
    {
        $this->validateScope($scope);
        if ($this->has($key, $scope)) {
            unset($this->state[$scope][$key]);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function reset(string $scope = 'scenario'): void
    {
        $this->validateScope($scope);
        $this->state[$scope] = [];
    }

    /**
     * Validate the scope
     *
     * @param string $scope Scope to validate
     * @throws InvalidArgumentException If scope is invalid
     */
    private function validateScope(string $scope): void
    {
        if (!in_array($scope, ['scenario', 'feature', 'global'])) {
            throw new InvalidArgumentException(
                sprintf('Invalid scope: %s. Valid scopes are: scenario, feature, global', $scope)
            );
        }
    }
}
```

## Usage in Contexts

The Shared State Service is used by contexts to share state between steps and between different contexts.

```php
class ProductContext extends BaseContext
{
    private SharedStateServiceInterface $stateService;

    public function __construct(SharedStateServiceInterface $stateService)
    {
        $this->stateService = $stateService;
    }

    /**
     * @When I select quantity :quantity
     */
    public function iSelectQuantity($quantity): void
    {
        $productPage = $this->pageFactory->getPage('ProductPage');
        $productPage->selectQuantity($quantity);
        
        // Store the selected quantity in the shared state
        $this->stateService->set('product.selected_quantity', $quantity);
    }
}

class CartContext extends BaseContext
{
    private SharedStateServiceInterface $stateService;

    public function __construct(SharedStateServiceInterface $stateService)
    {
        $this->stateService = $stateService;
    }

    /**
     * @Then the cart should contain :quantity items
     */
    public function theCartShouldContainItems(int $quantity): void
    {
        $cartPage = $this->pageFactory->getPage('CartPage');
        $itemCount = $cartPage->getItemCount();
        
        // Get the selected quantity from the shared state
        $selectedQuantity = $this->stateService->get('product.selected_quantity');
        
        if ($itemCount !== $quantity) {
            throw new \RuntimeException(
                sprintf('Expected %d items in cart, got %d', $quantity, $itemCount)
            );
        }
    }
}
```

## Scopes

The Shared State Service supports three scopes:

1. **Scenario**: State is shared within a single scenario. This is the default scope and is reset after each scenario.
2. **Feature**: State is shared within a single feature. This is reset after each feature.
3. **Global**: State is shared across all features. This is never automatically reset.

## Best Practices for Shared State

1. **Use Descriptive Keys**: Use descriptive keys that clearly indicate what the value represents. For example, `product.selected_quantity` instead of just `quantity`.
2. **Use Namespaces**: Use namespaces in keys to avoid conflicts. For example, `product.selected_quantity` and `cart.item_count`.
3. **Use the Appropriate Scope**: Use the appropriate scope for the data. Most data should be in the scenario scope, but some data might need to be shared across scenarios or features.
4. **Reset State**: Reset state when it's no longer needed to avoid leaking state between tests.
5. **Document State Dependencies**: Document which steps depend on which state values. This helps with understanding the flow of data between steps.
6. **Avoid Global State**: Avoid using the global scope unless absolutely necessary. Global state can lead to hard-to-debug issues.
7. **Use Type Hints**: Use type hints when getting values from the shared state to ensure the correct type is returned.
