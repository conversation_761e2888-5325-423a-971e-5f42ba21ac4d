<?php

namespace Features\Bootstrap;

use Behat\Behat\Tester\Exception\PendingException;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\MinkExtension\Context\MinkContext;
use Features\Bootstrap\Context\BrandContext;
use Features\Bootstrap\Context\TestDataContext;
use Features\Bootstrap\Page\CartPage;
use Features\Bootstrap\Page\CheckoutPage;
use Features\Bootstrap\Page\ConfirmationPage;
use Features\Bootstrap\Page\HomePage;
use Features\Bootstrap\Page\ProductPage;
use PHPUnit\Framework\Assert;
use RuntimeException;
use SensioLabs\Behat\PageObjectExtension\Context\PageObjectAware;
use SensioLabs\Behat\PageObjectExtension\PageObject\Factory as PageObjectFactory;

/**
 * Class FeatureContext
 * @package Features\Bootstrap
 */
class FeatureContext extends MinkContext implements PageObjectAware
{
    private SharedStateServiceInterface $stateService;
    private PageObjectFactory $pageObjectFactory;
    private BrandContext $brandContext;
    private TestDataContext $testDataContext;
    private ?string $baseUrl = null;
    private array $pageObjects = [];

    public function __construct(
        SharedDataContext $sharedData,
        PageObjectFactory $pageObjectFactory,
        BrandContext      $brandContext,
        TestDataContext   $testDataContext,
        ?string           $baseUrl = null
    )
    {
        $this->sharedData = $sharedData;
        $this->pageObjectFactory = $pageObjectFactory;
        $this->brandContext = $brandContext;
        $this->testDataContext = $testDataContext;
        $this->baseUrl = $baseUrl ?? $this->brandContext->getBaseUrl() ?? getenv('TEST_BASE_URL') ?? 'https://aeonstest.info';
    }

    /**
     * @BeforeScenario
     */
    public function initializePageObjects(): void
    {
        if ($this->getSession() && $this->pageObjectFactory) {
            $this->pageObjects = [];
            try {
                $this->pageObjects['home'] = $this->pageObjectFactory->createPage('HomePage');
                $this->pageObjects['product'] = $this->pageObjectFactory->createPage('ProductPage');
                $this->pageObjects['cart'] = $this->pageObjectFactory->createPage('CartPage');
                $this->pageObjects['checkout'] = $this->pageObjectFactory->createPage('CheckoutPage');
                $this->pageObjects['confirmation'] = $this->pageObjectFactory->createPage('ConfirmationPage');
                $this->pageObjects['upsell'] = $this->pageObjectFactory->createPage('UpsellPage');
                $this->pageObjects['paypal'] = $this->pageObjectFactory->createPage('PayPalPage');
                $this->pageObjects['stripe3ds'] = $this->pageObjectFactory->createPage('Stripe3DSPage');
            } catch (\Exception $e) {
                echo "Error initializing page objects: " . $e->getMessage() . "\n";
            }
        } else {
            error_log("Warning: Session or PageObjectFactory not available during @BeforeScenario initializePageObjects.");
        }
    }

    /**
     * @Then /^I check the driver type$/
     */
    public function iCheckTheDriverType(): void
    {
        $driver = $this->getSession()->getDriver();
        if ($driver instanceof \Behat\Mink\Driver\Selenium2Driver) {
            echo "Using Selenium2Driver\n";
        } elseif ($driver instanceof \DMore\ChromeDriver\ChromeDriver) {
            echo "Using ChromeDriver\n";
        } else {
            echo "Using an unknown driver: " . get_class($driver) . "\n";
        }
    }

    /**
     * @Given /^I load brand configuration$/
     */
    public function iLoadBrandConfiguration(): void
    {
        $brand = getenv('TEST_BRAND') ?? 'aeons';
        $environment = getenv('TEST_ENV') ?? 'stage';

        $this->brandContext->iAmUsingTheBrand($brand);
        $this->brandContext->iAmUsingTheEnvironment($environment);

        $this->stateService->set('currentBrand', $brand);
    }

    /**
     * @Given /^I load product data$/
     * @throws Exception
     */
    public function iLoadProductData(): void
    {
        $productName = getenv('TEST_PRODUCT') ?? 'Total Harmony';

        try {
            $productData = $this->testDataContext->getProductData($productName);
            $this->stateService->set('currentProduct', $productData);
            return;
        } catch (\Exception $e) {
            echo "Warning: Failed to load product data for '$productName' via TestDataContext. Using mock data instead. Error: " . $e->getMessage() . "\n";
        }

        $productData = [
            'name' => $productName,
            'slug' => strtolower(str_replace(' ', '-', $productName)),
            'prices' => [
                'one_time' => [
                    'minimum' => 29.95,
                    'medium' => 39.95,
                    'maximum' => 49.95
                ],
                'subscription' => [
                    'minimum' => 24.95,
                    'medium' => 34.95,
                    'maximum' => 44.95
                ]
            ],
            'options' => [
                'purchase_types' => [
                    'one_time' => 'One-Time Purchase',
                    'subscription' => 'Subscribe & Save'
                ],
                'quantities' => [
                    'minimum' => [
                        'fullName' => '1 Jar',
                        'numberOfItems' => 1
                    ],
                    'medium' => [
                        'fullName' => '3 Jars',
                        'numberOfItems' => 3
                    ],
                    'maximum' => [
                        'fullName' => '6 Jars',
                        'numberOfItems' => 6
                    ]
                ]
            ],
            'content' => [
                'description' => 'Product description goes here',
                'benefits' => ['Benefit 1', 'Benefit 2', 'Benefit 3']
            ]
        ];

        $this->stateService->set('currentProduct', $productData);
    }

    /**
     * @Then /^I wait for the order confirmation page to load$/
     * @throws ElementNotFoundException
     * @throws Exception
     */
    public function iWaitForTheOrderConfirmationPageToLoad(): void
    {
        try {
            $confirmationPage = $this->getConfirmationPage();
            $confirmationPage->waitForPageToLoad();
        } catch (\Exception $e) {
            throw new \Exception('Failed to load confirmation page: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I see the order number$/
     */
    public function iSeeTheOrderNumber(): void
    {
        $confirmationPage = $this->getConfirmationPage();
        $orderNumber = $confirmationPage->getOrderNumber();
        if (empty($orderNumber)) {
            throw new \Exception("Order number is not displayed.");
        }
        $this->stateService->set('orderNumber', $orderNumber);
    }

    /**
     * @Then /^I should see the order processing page$/
     */
    public function iShouldSeeTheOrderProcessingPage()
    {
        throw new \Behat\Behat\Tester\Exception\PendingException();
    }

    /**
     * @return HomePage
     */
    public function getHomePage(): HomePage
    {
        return $this->pageObjects['home'] ?? $this->pageObjectFactory->createPage('HomePage');
    }

    /**
     * @return ProductPage
     */
    public function getProductPage(): ProductPage
    {
        return $this->pageObjects['product'] ?? $this->pageObjectFactory->createPage('ProductPage');
    }

    /**
     * @return CartPage
     */
    public function getCartPage(): CartPage
    {
        return $this->pageObjects['cart'] ?? $this->pageObjectFactory->createPage('CartPage');
    }

    /**
     * @return CheckoutPage
     */
    public function getCheckoutPage(): CheckoutPage
    {
        return $this->pageObjects['checkout'] ?? $this->pageObjectFactory->createPage('CheckoutPage');
    }

    /**
     * @return ConfirmationPage
     */
    public function getConfirmationPage(): ConfirmationPage
    {
        return $this->pageObjects['confirmation'] ?? $this->pageObjectFactory->createPage('ConfirmationPage');
    }

    /**
     * @Given /^The flavor on confirmation page should be "([^"]*)"$/
     */
    public function theFlavorOnConfirmationPageShouldBe($arg1)
    {
        throw new PendingException();
    }

    /**
     * Maps country codes to their full names as displayed on the confirmation page
     * @param string $countryCode ISO country code
     * @return string Full country name
     */
    private function mapCountryCodeToName(string $countryCode): string
    {
        $countryMap = [
            'GB' => 'UNITED KINGDOM',
            'US' => 'UNITED STATES',
            'IL' => 'ISRAEL',
            // Add more mappings as needed
        ];

        return $countryMap[$countryCode] ?? strtoupper($countryCode);
    }

    /**
     * @Then /^I verify the order confirmation email$/
     */
    public function iVerifyTheOrderConfirmationEmail(): void
    {
        echo "Placeholder: Verification of order confirmation email would happen here.\n";
    }

    /**
     * @Given /^I am on the sales funnel page "([^"]*)"$/
     */
    public function iAmOnSalesFunnelPage(string $funnelId): void
    {
        $funnelConfig = [
            'entry' => [
                'url' => 'f/' . $funnelId,
                'product' => 'Total Harmony'
            ],
            'upsell' => [
                'url' => 'f/' . $funnelId . '/upsell',
                'product' => 'Ancient Roots',
                'quantity' => 'minimum'
            ]
        ];

        $this->stateService->set("funnel.current_funnel", $funnelConfig);

        $baseUrl = $this->baseUrl ?? $this->brandContext->getBaseUrl();
        $funnelUrl = sprintf('%s/f/%s', $baseUrl, $funnelId);
        
        $this->getSession()->visit($funnelUrl);
    }

    /**
     * @Given /^I verify the funnel product details$/
     */
    public function iVerifyTheFunnelProductDetails(): void
    {
        echo "Placeholder: Verifying funnel product details.\n";
    }

    /**
     * @When /^I fill in the shipping information with "([^"]*)" user data$/
     */
    public function iFillInTheShippingInformationWith(string $userType): void
    {
        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => '123 Test St',
            'city' => 'Testville',
            'postcode' => 'TE12 3ST',
            'country' => 'GB'
        ];

        $this->stateService->set('shippingInfo', $userData);
    }

    /**
     * @When /^I use the same address for billing$/
     */
    public function iUseTheSameAddressForBilling(): void
    {
        $shippingInfo = $this->stateService->get('shippingInfo');
        $this->stateService->set('billingInfo', $shippingInfo);
    }

    /**
     * @Then /^The shipping method "([^"]*)" should be selected$/
     */
    public function theShippingMethodShouldBeSelected(string $shippingMethod): void
    {
        $this->stateService->set('shippingMethod', $shippingMethod);
    }

    /**
     * @Then /^I verify the shipping cost$/
     */
    public function iVerifyTheShippingCost(): void
    {
        $this->stateService->set('shippingCost', '£2.95');
    }

    /**
     * @When /^I enter "([^"]*)" payment details$/
     */
    public function iEnterPaymentDetails(string $paymentType): void
    {
        $paymentData = [
            'type' => $paymentType,
            'cardNumber' => '****************',
            'cardExpiry' => '12/25',
            'cardCvc' => '123'
        ];

        $this->stateService->set('paymentDetails', $paymentData);
    }

    /**
     * @When /^I complete the purchase$/
     */
    public function iCompletePurchase(): void
    {
        echo "Placeholder: Completing purchase.\n";
    }

    /**
     * @Then /^I should be redirected to the upsell page$/
     */
    public function iShouldBeRedirectedToTheUpsellPage(): void
    {
        $funnel = $this->stateService->get("funnel.current_funnel");
        $baseUrl = $this->baseUrl ?? $this->brandContext->getBaseUrl();
        $upsellUrl = sprintf('%s/%s', $baseUrl, $funnel['upsell']['url']);
        echo "Placeholder: Verifying redirect to upsell page ($upsellUrl).\n";
    }

    /**
     * @When /^I accept the upsell offer$/
     */
    public function iAcceptTheUpsellOffer(): void
    {
        $this->stateService->set('upsellAccepted', true);
        echo "Placeholder: Accepting upsell offer.\n";
    }

    /**
     * @Then /^I verify the order details are correct$/
     */
    public function iVerifyTheOrderDetailsAreCorrect(): void
    {
        echo "Placeholder: Verifying order details.\n";
    }

    /**
     * @When /^I verify shipping cost is updated to "([^"]*)"$/
     */
    public function iVerifyShippingCost(string $expectedCost): void
    {
        $confirmationPage = $this->getConfirmationPage();
        $actualCost = $confirmationPage->getShippingInfo()['cost'];
        Assert::assertEquals(
            $expectedCost,
            $actualCost,
            "Shipping cost not updated correctly"
        );
    }

    /**
     * @Then /^I verify both products are in the order$/
     */
    public function iVerifyBothProductsInOrder(): void
    {
        $funnel = $this->stateService->get("funnel.current_funnel");
        $confirmationPage = $this->getConfirmationPage();

        Assert::assertTrue(
            $confirmationPage->hasProductCombination(
                $funnel['entry']['product'],
                $funnel['upsell']['product']
            ),
            "Order should contain both initial and upsell products"
        );
    }

    /**
     * @When /^I navigate back in browser$/
     */
    public function iNavigateBackInBrowser(): void
    {
        $this->getSession()->getDriver()->back();
        $this->waitForAjaxToComplete();
    }

    /**
     * @Then /^I verify dietary restriction warnings are displayed$/
     */
    public function iVerifyDietaryRestrictionWarningsAreDisplayed(): void
    {
        $funnel = $this->stateService->get("funnel.current_funnel");
        $productData = $this->stateService->get('currentProduct');

        $upsellPage = $this->pageObjects['upsell'] ?? $this->pageObjectFactory->createPage('UpsellPage');

        if (isset($productData['funnel']['restrictions']['dietary'])) {
            foreach ($productData['funnel']['restrictions']['dietary'] as $warning) {
                Assert::assertTrue(
                    $upsellPage->hasRestrictionWarning($warning),
                    "Dietary restriction warning not found: $warning"
                );
            }
        }
    }

    /**
     * @When /^I click the accept button multiple times$/
     */
    public function iClickTheAcceptButtonMultipleTimes(): void
    {
        $upsellPage = $this->pageObjects['upsell'] ?? $this->pageObjectFactory->createPage('UpsellPage');
        for ($i = 0; $i < 3; $i++) {
            $upsellPage->acceptUpsell();
            usleep(500000);
        }
    }

    /**
     * @Then /^I verify only one upsell product is in the order$/
     */
    public function iVerifyOnlyOneUpsellProductInOrder(): void
    {
        $funnel = $this->stateService->get("funnel.current_funnel");
        $confirmationPage = $this->getConfirmationPage();
        $orderedProducts = $confirmationPage->getOrderedProducts();

        $upsellProductCount = 0;
        foreach ($orderedProducts as $product) {
            if ($product['name'] === $funnel['upsell']['product']) {
                $upsellProductCount++;
            }
        }

        Assert::assertEquals(
            1,
            $upsellProductCount,
            "Expected exactly one upsell product in order, found $upsellProductCount"
        );
    }

    /**
     * @Then /^I verify the shipping cost is "([^"]*)"$/
     */
    public function iVerifyTheShippingCostIsP($expectedCost): void
    {
        $confirmationPage = $this->getConfirmationPage();
        $actualCost = $confirmationPage->getShippingInfo()['cost'];
        Assert::assertEquals(
            $expectedCost,
            $actualCost,
            "Expected shipping cost $expectedCost, got $actualCost"
        );
    }

    /**
     * @Then /^I verify the product instructions contain all warnings$/
     */
    public function iVerifyProductInstructionsContainAllWarnings(): void
    {
        $funnel = $this->stateService->get("funnel.current_funnel");
        $productData = $this->stateService->get('currentProduct');
        $confirmationPage = $this->getConfirmationPage();

        if (isset($productData['funnel']['restrictions'])) {
            foreach ($productData['funnel']['restrictions'] as $type => $warnings) {
                if (is_array($warnings)) {
                    foreach ($warnings as $warning) {
                        Assert::assertTrue(
                            $confirmationPage->hasWarningInInstructions($warning),
                            "Warning not found in instructions: $warning"
                        );
                    }
                }
            }
        }
    }

    protected function logStep(string $step, array $context = []): void
    {
        $message = "[Funnel Test] $step";
        if (!empty($context)) {
            $message .= ": " . json_encode($context);
        }
        error_log($message);
    }

    /**
     * @Then /^I verify browser session is active$/
     */
    public function verifyBrowserSession(): void
    {
        $session = $this->getSession();
        
        if (!$session) {
            throw new RuntimeException('Browser session not found');
        }
        
        try {
            $session->evaluateScript('return true;');
        } catch (\Exception $e) {
            throw new RuntimeException('Browser session is not active: ' . $e->getMessage());
        }
    }

    /**
     * @Then /^I verify BrowserStack session is active$/
     */
    public function verifyBrowserStackSession(): void
    {
        $this->verifyBrowserSession();
    }

    /**
     * @Then /^I should see the PayPal login error$/
     */
    public function iShouldSeeThePayPalLoginError(): void
    {
        $paypalPage = $this->pageObjects['paypal'] ?? $this->pageObjectFactory->createPage('PayPalPage');
        Assert::assertTrue($paypalPage->hasLoginError(),
            'PayPal login error message not found');
    }

    /**
     * @When /^I log in to PayPal sandbox with "([^"]*)" credentials$/
     */
    public function iLogInToPayPalSandboxWithCredentials(string $credentialType): void
    {
        $credentials = $this->testDataContext->getPayPalCredentials(
            $credentialType === 'valid' ? 'paypal_sandbox' : 'paypal_invalid'
        );
        $paypalPage = $this->pageObjects['paypal'] ?? $this->pageObjectFactory->createPage('PayPalPage');
        $paypalPage->login($credentials['username'], $credentials['password']);
    }

    /**
     * @When /^I choose to pay with PayPal$/
     */
    public function iChooseToPayWithPayPal(): void
    {
        $checkoutPage = $this->getCheckoutPage();
        $paypalPage = $this->pageObjects['paypal'] ?? $this->pageObjectFactory->createPage('PayPalPage');
        $checkoutPage->choosePayPalPayment();
        $paypalPage->waitForRedirectToPayPal();
    }

    /**
     * @When /^I am redirected to the PayPal sandbox page$/
     */
    public function iAmRedirectedToThePayPalSandboxPage(): void
    {
        $paypalPage = $this->pageObjects['paypal'] ?? $this->pageObjectFactory->createPage('PayPalPage');
        $paypalPage->waitForRedirectToPayPal();
    }

    /**
     * @Then /^I should see the correct payment amount in PayPal$/
     */
    public function iShouldSeeTheCorrectPaymentAmountInPayPal(): void
    {
        $expectedAmount = $this->stateService->get('orderTotal');
        $paypalPage = $this->pageObjects['paypal'] ?? $this->pageObjectFactory->createPage('PayPalPage');
        $actualAmount = $paypalPage->getDisplayedAmount();
        Assert::assertEquals($expectedAmount, $actualAmount,
            "Expected PayPal amount $expectedAmount, but found $actualAmount");
    }

    /**
     * @When /^I confirm the PayPal payment$/
     */
    public function iConfirmThePayPalPayment(): void
    {
        $paypalPage = $this->pageObjects['paypal'] ?? $this->pageObjectFactory->createPage('PayPalPage');
        $paypalPage->confirmPayment();
    }

    /**
     * @Then /^I should be redirected back to the merchant site$/
     */
    public function iShouldBeRedirectedBackToTheMerchantSite(): void
    {
        $this->waitForUrlContains($this->brandContext->getBaseUrl());
    }

    /**
     * @Then /^the PayPal payment should be successful$/
     */
    public function thePayPalPaymentShouldBeSuccessful(): void
    {
        $this->waitForUrlContains($this->brandContext->getBaseUrl());
        $checkoutPage = $this->getCheckoutPage();
        Assert::assertTrue(
            $checkoutPage->verifyPayPalPaymentSuccess(),
            'PayPal payment was not successful'
        );
    }

    /**
     * @Then /^I should see a PayPal login error message$/
     */
    public function iShouldSeeAPayPalLoginErrorMessage(): void
    {
        $paypalPage = $this->pageObjects['paypal'] ?? $this->pageObjectFactory->createPage('PayPalPage');
        Assert::assertTrue($paypalPage->hasLoginError(),
            'PayPal login error message not found');
    }

    /**
     * @Then /^I should see the 3DS page$/
     */
    public function iShouldSeeThe3DSPage()
    {
        $stripe3DSPage = $this->pageObjects['stripe3ds'] ?? $this->pageObjectFactory->createPage('Stripe3DSPage');
        if (!$stripe3DSPage->is3DSPageDisplayed()) {
            throw new \Exception('3DS page is not displayed');
        }
    }

    /**
     * @When /^I press the "([^"]*)" button$/
     */
    public function iPressTheButton($button)
    {
        $stripe3DSPage = $this->pageObjects['stripe3ds'] ?? $this->pageObjectFactory->createPage('Stripe3DSPage');
        switch (strtolower($button)) {
            case 'complete':
                $stripe3DSPage->clickCompleteButton();
                break;
            case 'fail':
                $stripe3DSPage->clickFailButton();
                break;
            default:
                throw new \Exception(sprintf('Unknown button "%s"', $button));
        }
    }

    /**
     * @When /^I don't complete the order$/
     */
    public function iDontCompleteTheOrder(): void
    {
        $homePage = $this->getHomePage();
        $homePage->load();
        $this->waitForAjaxToComplete();
    }

    /**
     * Waits for AJAX requests to complete
     */
    protected function waitForAjaxToComplete(): void
    {
        $this->getSession()->wait(5000,
            "(typeof jQuery !== 'undefined') && (jQuery.active === 0)"
        );
    }

    /**
     * Waits for URL to contain specific text
     */
    protected function waitForUrlContains(string $text): void
    {
        $this->getSession()->wait(5000,
            sprintf("window.location.href.includes('%s')", $text)
        );
    }

    /**
     * @Given /^I am using the "([^"]*)" brand$/
     */
    public function iAmUsingTheBrand(string $brand): void
    {
        $this->brandContext->switchBrand($brand);
        $this->stateService->set('currentBrand', $brand);
    }

    /**
     * @Given /^I load test data for "([^"]*)"$/
     */
    public function iLoadTestDataFor(string $dataPath): void
    {
        $data = $this->testDataContext->getTestData($dataPath);

        if (!is_array($data)) {
            throw new RuntimeException(sprintf(
                'Invalid test data format for key: %s. Expected array, got %s',
                $dataPath,
                gettype($data)
            ));
        }

        $this->stateService->set('testData', $data);
    }

    /**
     * @Given /^I load product data for "([^"]*)"$/
     * @throws Exception
     */
    public function iLoadProductDataFor(string $productKey): void
    {
        try {
            $productData = $this->testDataContext->getProductData($productKey);
            $this->stateService->set('currentProduct', $productData);
            return;
        } catch (\Exception $e) {
            echo "Warning: Failed to load product data for '$productKey' via TestDataContext. Using mock data instead. Error: " . $e->getMessage() . "\n";
        }

        $productData = [
            'name' => $productKey,
            'slug' => strtolower(str_replace(' ', '-', $productKey)),
            'prices' => [
                'one_time' => [
                    'minimum' => 29.95,
                    'medium' => 39.95,
                    'maximum' => 49.95
                ],
                'subscription' => [
                    'minimum' => 24.95,
                    'medium' => 34.95,
                    'maximum' => 44.95
                ]
            ],
            'options' => [
                'purchase_types' => [
                    'one_time' => 'One-Time Purchase',
                    'subscription' => 'Subscribe & Save'
                ],
                'quantities' => [
                    'minimum' => [
                        'fullName' => '1 Jar',
                        'numberOfItems' => 1
                    ],
                    'medium' => [
                        'fullName' => '3 Jars',
                        'numberOfItems' => 3
                    ],
                    'maximum' => [
                        'fullName' => '6 Jars',
                        'numberOfItems' => 6
                    ]
                ]
            ],
            'content' => [
                'description' => 'Product description goes here',
                'benefits' => ['Benefit 1', 'Benefit 2', 'Benefit 3']
            ]
        ];
        $this->stateService->set('currentProduct', $productData);
    }

    /**
     * Verifies that the page loaded successfully
     * 
     * @Then I should see the page loaded successfully
     */
    public function iShouldSeeThePageLoadedSuccessfully(): void
    {
        $this->verifyBrowserSession();
        
        $page = $this->getSession()->getPage();
        if (!$page) {
            throw new RuntimeException('Page not found');
        }
        
        $body = $page->find('css', 'body');
        if (!$body) {
            throw new RuntimeException('Body element not found on page');
        }

        error_log("Browser verification successful: page loaded with title '" . ($this->getSession()->getPage()->find('css', 'title')?->getText() ?? 'N/A') . "'");
    }

    /**
     * Sets page factory.
     *
     * @param PageObjectFactory $pageObjectFactory
     *
     * @return void
     */
    public function setPageObjectFactory(PageObjectFactory $pageObjectFactory)
    {
        $this->pageObjectFactory = $pageObjectFactory;
        $this->initializePageObjects();
    }
}