# Malaberg Test Automation Framework Documentation

## Overview

This documentation covers the Malaberg test automation framework, which is used for testing a Sylius-based e-commerce
platform. The framework follows the Page Object pattern and uses Behat for BDD-style tests with BrowserStack for
cloud-based browser automation.

## Table of Contents

1. [Test Framework Overview](test_framework_overview.md)
2. [Application Under Test](application_under_test.md)
3. [Critical Workflows](critical_workflows_documentation.md)
4. [Sylius Integration](sylius_integration_documentation.md)
5. [Page Objects](page_objects_documentation.md)
6. [Step Definitions](step_definitions_documentation.md)
7. [Browser Service](browser_service_documentation.md)
8. [Shared State Service](shared_state_service_documentation.md)
9. [Test Data Service](test_data_service_documentation.md)
10. [BrowserStack Integration](browserstack_integration.md)

## Quick Start

### Prerequisites

- PHP 8.1 or higher
- Composer
- BrowserStack account (for browser automation)
- Sylius-based e-commerce platform

### Installation

1. Clone the repository
2. Install dependencies with Composer:
   ```
   composer install
   ```
3. Configure the environment:
   ```
   cp .env.browserstack .env
   ```
4. Edit the `.env` file to set your BrowserStack credentials, base URL, and other configuration options

### Running Tests

Run all tests with BrowserStack:
```
.\run-browserstack-tests.ps1
```

Run a specific feature with BrowserStack:
```
.\run-browserstack-tests.ps1 -Feature "features/purchase.feature"
```

Run tests with a specific tag with BrowserStack:
```
.\run-browserstack-tests.ps1 -Tags "@cart"
```

Run tests in dry-run mode with BrowserStack:
```
.\run-browserstack-tests.ps1 -DryRun
```

### Creating New Tests

1. Create a new feature file in the `features` directory
2. Define scenarios using Gherkin syntax
3. Implement step definitions in the appropriate context class
4. Create or update page objects as needed
5. Run the tests to verify they work as expected

## Framework Architecture

The framework follows a layered architecture:

1. **Feature Layer**: Gherkin feature files that describe the behavior of the application in a human-readable format.
2. **Step Definition Layer**: PHP classes that implement the steps defined in the feature files.
3. **Page Object Layer**: PHP classes that encapsulate the interaction with web pages.
4. **Service Layer**: PHP classes that provide common functionality and services to the other layers.

## Key Components

### Page Objects

Page objects encapsulate the interaction with web pages, providing a higher-level API for tests to use. They abstract away the details of the HTML structure and browser interactions.

Key page objects:

- `BasePage`: Base class for all page objects
- `HomePage`: Represents the home page
- `ProductPage`: Represents the product detail page
- `CartPage`: Represents the shopping cart page
- `CheckoutPage`: Represents the checkout page

### Contexts

Contexts are step definition classes that implement the steps defined in the feature files. They use page objects to interact with the application.

Key contexts:

- `FeatureContext`: Main context class
- `ProductContext`: Handles product-related steps
- `CartContext`: Handles cart-related steps
- `CheckoutContext`: Handles checkout-related steps
- `AbandonedCartContext`: Handles abandoned cart scenarios

### Services

Services provide common functionality to the other layers. They are injected into contexts and page objects as needed.

Key services:

- `BrowserStackBrowserService`: Provides methods for interacting with the browser using BrowserStack
- `ConfigurationService`: Provides access to configuration settings
- `DataService`: Provides access to test data
- `PageFactory`: Creates page objects
- `SharedStateService`: Manages shared state between steps
- `ValidationService`: Validates data

## Best Practices

1. **Single Responsibility**: Each class should have a single responsibility.
2. **Encapsulation**: Page objects should encapsulate the details of the page structure.
3. **Descriptive Step Names**: Step names should be descriptive and follow a consistent pattern.
4. **Error Handling**: All methods should handle errors gracefully and provide meaningful error messages.
5. **State Management**: Use the shared state service to manage state between steps.
6. **Service Injection**: Use dependency injection to access services.
7. **Documentation**: All classes and methods should be well-documented.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests to ensure they pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
