# Page Object Extension Implementation Guide
Version: 2.0 (Migration Update 2024)

## Migration Overview

### Current Status
- Extension: friends-of-behat/page-object-extension
- Version: 0.3.2
- PHP Compatibility: ^7.1 || ^8.0
- Migration Status: In Progress

### Migration Steps

1. **Backup Current Implementation**
```bash
# Create backup directory
mkdir -p backup/page_objects
# Copy current page objects
cp -r features/bootstrap/Page/* backup/page_objects/
```

2. **Remove Current Extension**
```bash
composer remove friends-of-behat/page-object-extension
composer clear-cache
```

3. **Install New Version**
```bash
composer require --dev "friends-of-behat/page-object-extension:^0.3.2"
```

4. **Update Behat Configuration**
```yaml
# behat.yml
default:
  extensions:
    FriendsOfBehat\PageObjectExtension: ~
```

## Implementation Guide

### 1. Basic Setup

#### Directory Structure
```
features/
├── bootstrap/
│   ├── Page/
│   │   ├── BasePage.php
│   │   ├── ProductPage.php
│   │   └── Element/
│   │       └── SearchForm.php
│   └── Context/
│       └── PageObjectContext.php
```

#### Base Page Implementation
```php
namespace Features\Bootstrap\Page;

use FriendsOfBehat\PageObjectExtension\Page\Page;

abstract class BasePage extends Page
{
    protected function verifyPage(): void
    {
        $this->waitForPageToLoad();
    }

    protected function waitForPageToLoad(int $timeout = 5000): void
    {
        $this->getDocument()->waitFor($timeout / 1000, function() {
            return $this->isElementVisible('body');
        });
    }
}
```

### 2. Page Object Creation

#### Product Page Example
```php
namespace Features\Bootstrap\Page;

class ProductPage extends BasePage
{
    protected string $path = '/product/{slug}';
    
    protected array $elements = [
        'Add to Cart' => '.add-to-cart-button',
        'Quantity' => '#quantity-input'
    ];

    public function open(array $urlParameters = []): void
    {
        parent::open($urlParameters);
        $this->waitForPageToLoad();
    }

    protected function verifyPage(): void
    {
        parent::verifyPage();
        $this->getDocument()->waitFor(3, function() {
            return $this->hasElement('Add to Cart');
        });
    }
}
```

#### Element Implementation
```php
namespace Features\Bootstrap\Page\Element;

use FriendsOfBehat\PageObjectExtension\Page\Element;

class SearchForm extends Element
{
    protected string $selector = 'form#search';

    public function search(string $keywords): void
    {
        $this->fillField('q', $keywords);
        $this->pressButton('Search');
    }
}
```

### 3. Context Integration

#### Page Object Context
```php
namespace Features\Bootstrap\Context;

use FriendsOfBehat\PageObjectExtension\Context\PageObjectContext;
use Features\Bootstrap\Page\ProductPage;

class ProductContext extends PageObjectContext
{
    private ProductPage $productPage;

    public function __construct(ProductPage $productPage)
    {
        $this->productPage = $productPage;
    }

    /**
     * @When I view product :slug
     */
    public function iViewProduct(string $slug): void
    {
        $this->productPage->open(['slug' => $slug]);
    }
}
```

### 4. Error Handling

#### Exception Handling
```php
try {
    $this->getElement('Add to Cart')->click();
} catch (ElementNotFoundException $e) {
    throw new RuntimeException(
        sprintf('Add to cart button not found: %s', $e->getMessage())
    );
} catch (Exception $e) {
    throw new RuntimeException(
        sprintf('Failed to add product to cart: %s', $e->getMessage())
    );
}
```

### 5. Migration Verification

#### Checklist
- [ ] All page objects extend correct base class
- [ ] verifyPage() implemented for each page
- [ ] Element selectors updated to use new format
- [ ] Error handling implemented
- [ ] Session management removed
- [ ] Documentation updated

#### Testing Steps
1. Run smoke tests
2. Verify page navigation
3. Check element interactions
4. Validate error handling
5. Test cross-browser functionality

### 6. Best Practices

#### Code Organization
- One page object per file
- Group related elements
- Use type hints
- Document public methods
- Implement proper error handling

#### Element Interaction
```php
// GOOD
public function addToCart(): void
{
    $this->getElement('Add to Cart')->click();
    $this->waitForAjaxToComplete();
}

// BAD
public function addToCart(): void
{
    $this->getSession()->getPage()->find('css', '.add-to-cart')->click();
}
```

#### Wait Conditions
```php
protected function waitForAjaxToComplete(): void
{
    $this->getSession()->wait(5000, 
        "jQuery.active == 0 && document.readyState === 'complete'"
    );
}
```

### 7. Migration Progress Tracking

#### Status Annotations
```php
/**
 * @MigrationStatus: COMPLETED
 * @MigrationVersion: 0.3.2
 * @LastUpdated: 2024-01-20
 * @Verified: true
 */
class ProductPage extends BasePage
```

#### Progress Report
```markdown
## Migration Progress

### Completed
- [ ] Base page object updated
- [ ] Product pages migrated
- [ ] Cart pages migrated
- [ ] Checkout pages migrated

### In Progress
- [ ] Account pages
- [ ] Admin pages

### Pending
- [ ] API pages
- [ ] Report pages
```

### 8. Rollback Procedure

If issues are encountered:

1. Restore backup:
```bash
# Restore page objects
cp -r backup/page_objects/* features/bootstrap/Page/
```

2. Revert composer:
```bash
composer remove friends-of-behat/page-object-extension
composer require --dev "friends-of-behat/page-object-extension:previous-version"
```

3. Restore configuration:
```bash
# Restore behat.yml from backup
cp backup/behat.yml ./
```

### 9. Support and Resources

- [Extension Documentation](https://github.com/FriendsOfBehat/PageObjectExtension)
- [Issue Tracker](https://github.com/FriendsOfBehat/PageObjectExtension/issues)
- [Migration Support Channel](#)