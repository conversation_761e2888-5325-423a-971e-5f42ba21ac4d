# PageObjectExtension Migration Guide

## 1. Overview

### Current Architecture
- Mixed constructor/setter injection for handling:
  - Session timing issues
  - Circular dependencies
  - Runtime initialization

### Target Architecture
- Constructor injection for page objects
- PageObjectExtension factory pattern
- Standardized session management

## 2. Migration Steps

### 2.1 Update Context Classes
```php
use SensioLabs\Behat\PageObjectExtension\Context\PageObjectContext;

class CheckoutContext extends PageObjectContext
{
    private CheckoutPage $checkoutPage;
    private ProductPage $productPage;

    public function __construct(CheckoutPage $checkoutPage, ProductPage $productPage)
    {
        $this->checkoutPage = $checkoutPage;
        $this->productPage = $productPage;
    }
}
```

### 2.2 Update Page Classes
```php
use SensioLabs\Behat\PageObjectExtension\PageObject\Page;

class CheckoutPage extends Page
{
    protected function getUrl(): string
    {
        return '/checkout';
    }
}
```

### 2.3 Update Service Configuration
```yaml
services:
    _defaults:
        public: true
        autowire: true

    Features\Bootstrap\Page\:
        resource: '../features/bootstrap/Page/*'
        parent: FriendsOfBehat\PageObjectExtension\Page\Page
        tags: ['page_object']

    Features\Bootstrap\Context\:
        resource: '../features/bootstrap/Context/*'
        autowire: true
        tags: ['context.service']
```

## 3. Benefits of Migration

1. **Simplified Dependency Management**
   - No more mixed injection patterns
   - Clear constructor dependencies
   - Automated page object creation

2. **Improved Session Handling**
   - Built-in session management
   - Proper initialization timing
   - No manual session injection needed

3. **Better Testing Support**
   - Easier to mock dependencies
   - Clearer component responsibilities
   - More maintainable tests

## 4. Migration Strategy

1. **Phase 1: Infrastructure**
   - Install PageObjectExtension
   - Configure service container
   - Update base classes

2. **Phase 2: Page Objects**
   - Convert page classes one at a time
   - Update to extend Page
   - Remove manual session management

3. **Phase 3: Contexts**
   - Convert contexts to use constructor injection
   - Remove setter injection methods
   - Update service definitions

4. **Phase 4: Validation**
   - Run test suite
   - Verify local WebDriver integration
   - Check all features still work
