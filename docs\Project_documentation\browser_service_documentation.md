# Browser Service Documentation

## Overview

The Browser Service is a key component of the Malaberg test automation framework. It provides a high-level API for
interacting with the browser, abstracting away the details of the underlying browser automation library (BrowserStack).
This document covers the Browser Service interface and implementation.

## BrowserServiceInterface

The `BrowserServiceInterface` defines the contract for browser interaction services.

```php
interface BrowserServiceInterface
{
    /**
     * Visit a URL
     *
     * @param string $url URL to visit
     * @return void
     */
    public function visit(string $url): void;

    /**
     * Get the current session
     *
     * @return Session
     */
    public function getSession(): Session;

    /**
     * Wait for the document to be ready
     *
     * @param int $timeout Timeout in seconds
     * @return bool True if document became ready within timeout
     */
    public function waitForDocumentReady(int $timeout = 30): bool;

    /**
     * Wait for AJAX requests to complete
     *
     * @param int $timeout Timeout in seconds
     * @return bool True if AJAX requests completed within timeout
     */
    public function waitForAjaxToComplete(int $timeout = 30): bool;

    /**
     * Wait for an element to be visible
     *
     * @param string $selector Element selector
     * @param int $timeout Timeout in seconds
     * @return bool True if element became visible within timeout
     */
    public function waitForElementVisible(string $selector, int $timeout = 30): bool;

    /**
     * Click on an element
     *
     * @param string $selector Element selector
     * @return void
     */
    public function clickElement(string $selector): void;

    /**
     * Fill a field with a value
     *
     * @param string $selector Field selector
     * @param string $value Value to fill
     * @return void
     */
    public function fillField(string $selector, string $value): void;

    /**
     * Select an option from a select field
     *
     * @param string $selector Select field selector
     * @param string $value Option value to select
     * @return void
     */
    public function selectOption(string $selector, string $value): void;

    /**
     * Check if an element exists
     *
     * @param string $selector Element selector
     * @return bool
     */
    public function elementExists(string $selector): bool;

    /**
     * Get text from an element
     *
     * @param string $selector Element selector
     * @return string
     */
    public function getElementText(string $selector): string;

    /**
     * Find elements matching a selector
     *
     * @param string $selector Element selector
     * @return NodeElement[]
     */
    public function findElements(string $selector): array;

    /**
     * Take a screenshot
     *
     * @param string|null $name Name for the screenshot
     * @return string Path to the screenshot
     */
    public function takeScreenshot(?string $name = null): string;
}
```

## BrowserStackBrowserService Implementation

The `BrowserStackBrowserService` class implements the `BrowserServiceInterface` and provides browser interaction
functionality using BrowserStack.

```php
class BrowserStackBrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    private int $defaultTimeout = 30;
    private BrowserStackStatusReporter $statusReporter;
    private BrowserStackSessionFactory $sessionFactory;

    /**
     * Constructor
     *
     * @param string $screenshotsDir Screenshots directory path
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        string $screenshotsDir,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);

        $this->screenshotsDir = $screenshotsDir;
        $this->statusReporter = new BrowserStackStatusReporter($logger);
        $this->sessionFactory = new BrowserStackSessionFactory($logger);

        // Create screenshots directory if it doesn't exist
        if (!is_dir($this->screenshotsDir) && !mkdir($this->screenshotsDir, 0777, true)) {
            $this->logError(sprintf("Failed to create screenshots directory: %s", $this->screenshotsDir));
        }

        // Initialize BrowserStack session
        $this->initializeSession();

        $this->logInfo("Initialized BrowserStackBrowserService");
    }

    /**
     * Initialize BrowserStack session
     */
    private function initializeSession(): void
    {
        $this->logInfo("Initializing BrowserStack session");

        // Create capabilities builder
        $capabilitiesBuilder = new BrowserStackCapabilitiesBuilder($this->logger);

        // Load capabilities from environment
        $capabilities = $capabilitiesBuilder
            ->loadFromEnvironment()
            ->build();

        // Create session
        $this->session = $this->sessionFactory->createSession($capabilities);

        $this->logInfo("BrowserStack session initialized successfully");
    }

    /**
     * {@inheritdoc}
     */
    public function getSession(): Session
    {
        return $this->session;
    }

    /**
     * {@inheritdoc}
     */
    public function visit(string $url): void
    {
        $this->logInfo("Visiting URL: {$url}");
        $startTime = microtime(true);
        $maxRetries = 3;

        for ($retry = 0; $retry < $maxRetries; $retry++) {
            try {
                // Check if session is active and start it if needed
                if (!$this->session->isStarted() || !$this->isSessionActive()) {
                    $this->logInfo("Session not active, starting new session...");

                    // Stop existing session if needed
                    if ($this->session->isStarted()) {
                        try {
                            $this->session->stop();
                            $this->logInfo("Stopped existing session");
                        } catch (\Throwable $e) {
                            $this->logWarning("Could not stop existing session: " . $e->getMessage());
                        }
                    }

                    // Start session
                    $this->session->start();
                    $this->logInfo("Session started successfully");
                }

                // Execute the navigation
                $this->logInfo("Navigating to URL: {$url} (attempt " . ($retry + 1) . "/{$maxRetries})");
                $this->session->visit($url);

                // Wait for page to fully load
                $this->waitForPageToLoad();

                $totalElapsedTime = round((microtime(true) - $startTime) * 1000);
                $this->logInfo("Total visit operation completed in {$totalElapsedTime}ms");

                // Navigation succeeded, exit retry loop
                return;

            } catch (\Exception $e) {
                $this->logError("Exception while visiting {$url} (attempt " . ($retry + 1) . "/{$maxRetries}): " . $e->getMessage());
                $this->takeScreenshot('visit_error');

                // Only retry if we haven't reached max retries
                if ($retry < $maxRetries - 1) {
                    $delaySeconds = 5 * ($retry + 1);
                    $this->logInfo("Waiting {$delaySeconds}s before retry");
                    sleep($delaySeconds);
                } else {
                    // Max retries reached, propagate the exception
                    throw new RuntimeException(
                        sprintf('Failed to visit URL after %d attempts: %s', $maxRetries, $url),
                        0,
                        $e
                    );
                }
            }
        }
    }

    /**
     * Mark test as passed in BrowserStack
     *
     * @param string|null $reason Reason for passing
     * @return bool Whether the status was reported successfully
     */
    public function markTestPassed(?string $reason = null): bool
    {
        return $this->statusReporter->markTestPassed($this->session, $reason);
    }

    /**
     * Mark test as failed in BrowserStack
     *
     * @param string|null $reason Reason for failing
     * @return bool Whether the status was reported successfully
     */
    public function markTestFailed(?string $reason = null): bool
    {
        return $this->statusReporter->markTestFailed($this->session, $reason);
    }

    // Additional methods are implemented in the full class
}
```

## Usage in Page Objects

The Browser Service is used by page objects to interact with the browser. It is injected into the page objects through their constructors.

```php
class ProductPage extends BasePage
{
    public function __construct(BrowserServiceInterface $browserService, ?string $baseUrl = null)
    {
        parent::__construct($browserService, $baseUrl);
    }

    public function addToCart(): void
    {
        $this->browserService->clickElement('.add-to-cart-button');
        $this->browserService->waitForAjaxToComplete();
        $this->browserService->waitForElementVisible('.cart-confirmation');

        // Take a screenshot for BrowserStack dashboard
        $this->browserService->takeScreenshot('product_added_to_cart');
    }
}
```

## Usage in Contexts

The Browser Service can also be used directly in contexts for browser interactions that don't fit into the page object model.

```php
class FeatureContext extends BaseContext
{
    private BrowserServiceInterface $browserService;

    public function __construct(BrowserServiceInterface $browserService)
    {
        $this->browserService = $browserService;
    }

    /**
     * @When I take a screenshot named :name
     */
    public function iTakeAScreenshotNamed(string $name): void
    {
        $this->browserService->takeScreenshot($name);
    }

    /**
     * @AfterScenario
     */
    public function afterScenario(AfterScenarioScope $scope): void
    {
        // Report test status to BrowserStack
        if ($scope->getTestResult()->isPassed()) {
            $this->browserService->markTestPassed('Scenario passed successfully');
        } else {
            $this->browserService->markTestFailed($scope->getTestResult()->getMessage());
        }
    }
}
```

## Best Practices for Browser Interaction with BrowserStack

1. **Use Page Objects**: Whenever possible, use page objects to interact with the browser. This provides a clean separation between test logic and browser interaction details.
2. **Wait for Elements**: Always wait for elements to be visible before interacting with them. This helps prevent flaky tests.
3. **Wait for AJAX**: Wait for AJAX requests to complete after actions that trigger them. This ensures that the page is in the expected state before proceeding.
4. **Handle Errors**: Handle errors gracefully and provide meaningful error messages. This helps with debugging when tests fail.
5. **Take Screenshots**: Take screenshots at key points in the test to help with debugging. This is especially useful
   when tests fail and will be available in the BrowserStack dashboard.
6. **Use Descriptive Selectors**: Use descriptive selectors that are unlikely to change. This helps make tests more robust.
7. **Minimize Direct Browser Interaction**: Minimize direct browser interaction in contexts. Instead, use page objects to encapsulate browser interaction details.
8. **Report Test Status**: Always report test status to BrowserStack. This helps with debugging and provides a clear
   overview of test results in the BrowserStack dashboard.
9. **Use BrowserStack Capabilities**: Use BrowserStack capabilities to configure the browser environment. This allows
   you to test on different browsers, operating systems, and device configurations.
10. **Check BrowserStack Dashboard**: Regularly check the BrowserStack dashboard for test results, screenshots, and
    logs. This provides valuable insights into test execution and helps with debugging.
