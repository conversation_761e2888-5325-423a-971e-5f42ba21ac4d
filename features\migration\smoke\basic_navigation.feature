@migration @smoke @navigation
Feature: Basic Navigation - Smoke Tests
  As a test framework maintainer
  I want to verify basic navigation functionality
  So that I can ensure the migration doesn't break core navigation features

  Background:
    Given I am using a desktop device
    And I am on the homepage

  @navigation_methods
  Scenario: Verify basic navigation methods
    When I navigate to the product page
    Then I should be on the product page
    When I navigate back
    Then I should be on the homepage
    When I refresh the page
    Then I should still be on the homepage

  @url_manipulation
  Scenario: Verify URL manipulation
    When I visit "/product"
    Then the current URL should contain "product"
    And I should be able to get the current path
    And I should be able to get URL parameters

  @browser_interaction
  Scenario: Verify browser interaction
    When I resize the window to 1920x1080
    Then the window size should be 1920x1080
    When I maximize the window
    Then the window should be maximized

  @cookie_handling
  Scenario: Verify cookie handling
    When I set a test cookie
    Then I should be able to read the cookie
    When I delete the test cookie
    Then the cookie should not exist

  @screenshot_capture
  Scenario: Verify screenshot functionality
    When I take a screenshot
    Then the screenshot should be saved successfully 