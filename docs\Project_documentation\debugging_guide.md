# Debugging Guide

## Overview

This document provides guidance on debugging issues in the Malaberg test automation framework. It covers common error patterns, debugging techniques, and tools that can help identify and resolve issues.

## Common Error Patterns

### 1. Service Not Found

**Error Message**: `Service "App\Service\SomeService" not found in container`

**Possible Causes**:
- The service is not registered in the service container
- The service interface or implementation is missing
- The service is registered but not public

**Debugging Steps**:
1. Check if the service is registered in the service container configuration
   ```yaml
   # config/services/core.yml
   services:
     App\Service\SomeService:
       arguments:
         $someArg: 'value'
       public: true
   ```
2. Check if the service interface and implementation exist
   ```php
   // src/Service/SomeServiceInterface.php
   namespace App\Service;
   
   interface SomeServiceInterface
   {
       // ...
   }
   
   // src/Service/SomeService.php
   namespace App\Service;
   
   class SomeService implements SomeServiceInterface
   {
       // ...
   }
   ```
3. Make sure the service is public
   ```yaml
   services:
     App\Service\SomeService:
       public: true
   ```

### 2. Page Not Found

**Error Message**: `Page "SomePage" not found`

**Possible Causes**:
- The page class does not exist
- The page class is not in the expected namespace
- The page factory is not configured correctly

**Debugging Steps**:
1. Check if the page class exists in the correct namespace
   ```php
   // src/Page/SomePage.php
   namespace App\Page;
   
   use App\Page\Base\BasePage;
   
   class SomePage extends BasePage
   {
       // ...
   }
   ```
2. Check if the page class is registered in the service container
   ```yaml
   # config/services/pages.yml
   services:
     App\Page\SomePage:
       arguments:
         $browserService: '@App\Service\Browser\BrowserServiceInterface'
         $baseUrl: '%env(TEST_BASE_URL)%'
       public: true
       tags: [ 'page.service' ]
   ```
3. Check if the page factory is correctly configured
   ```yaml
   # config/services/pages.yml
   services:
     App\Service\Page\PageFactoryInterface:
       alias: App\Service\Page\PageFactory
       public: true
   
     App\Service\Page\PageFactory:
       arguments:
         $container: '@service_container'
         $browserService: '@App\Service\Browser\BrowserServiceInterface'
         $baseUrl: '%env(TEST_BASE_URL)%'
       public: true
   ```

### 3. Element Not Found

**Error Message**: `Element with selector ".some-selector" not found`

**Possible Causes**:
- The selector is incorrect
- The element is not present in the page
- The element is not visible
- The page has not finished loading

**Debugging Steps**:
1. Take a screenshot to see the current state of the page
   ```php
   $this->browserService->takeScreenshot('debug');
   ```
2. Check if the selector is correct
   ```php
   $elements = $this->browserService->findElements('.some-selector');
   var_dump(count($elements));
   ```
3. Try waiting for the element to appear
   ```php
   $this->browserService->waitForElementVisible('.some-selector', 10);
   ```
4. Check if the page has finished loading
   ```php
   $this->browserService->waitForPageToLoad();
   $this->browserService->waitForAjaxToComplete();
   ```

### 4. Shared State Key Not Found

**Error Message**: `Undefined index: some_key in SharedStateService.php`

**Possible Causes**:
- The key is not set before trying to get it
- The key is set in a different scope
- The key is reset between steps

**Debugging Steps**:
1. Check if the key is set before trying to get it
   ```php
   if ($this->stateService->has('some_key')) {
       $value = $this->stateService->get('some_key');
   } else {
       // Handle missing key
   }
   ```
2. Provide a default value when getting the key
   ```php
   $value = $this->stateService->get('some_key', 'scenario', 'default_value');
   ```
3. Dump all state in a scope to see what's available
   ```php
   $state = $this->stateService->getAll('scenario');
   var_dump($state);
   ```

### 5. Browser Session Not Active

**Error Message**: `Session is not active`

**Possible Causes**:
- The browser driver is not running
- The browser has crashed
- The session has timed out
- Network issues

**Debugging Steps**:
1. Check if the browser session is started
   ```php
   $isActive = $this->browserService->isSessionActive();
   var_dump($isActive);
   ```
2. Try restarting the browser session
   ```php
   $this->browserService->getSession()->restart();
   ```
3. Check for network issues
   ```php
   $url = $this->browserService->getCurrentUrl();
   echo "Current URL: $url";
   ```

## Debugging Techniques

### 1. Logging

The framework includes a logging system that can be used to track the execution flow and debug issues:

```php
// Log an informational message
$this->logInfo("Navigating to homepage");

// Log a warning message
$this->logWarning("Element not found, retrying");

// Log an error message
$this->logError("Failed to navigate to homepage", $exception);
```

Logs are written to the log file specified in the configuration:

```yaml
# config/services/logging.yml
services:
  logger:
    class: Monolog\Logger
    arguments:
      $name: 'app'
    calls:
      - [pushHandler, ['@logger.file_handler']]
    public: true

  logger.file_handler:
    class: Monolog\Handler\StreamHandler
    arguments:
      $stream: '%app.log_dir%/app.log'
      $level: !php/const Monolog\Logger::DEBUG
```

### 2. Screenshots

Taking screenshots is a powerful way to debug UI issues:

```php
// Take a screenshot with a specific name
$screenshotPath = $this->browserService->takeScreenshot('debug');
echo "Screenshot saved to: $screenshotPath";

// Take a screenshot from a page object
$screenshotPath = $productPage->takeScreenshot('product-page');
echo "Screenshot saved to: $screenshotPath";
```

Screenshots are saved to the directory specified in the configuration:

```yaml
# config/services/core.yml
services:
  App\Service\Browser\BrowserService:
    arguments:
      $screenshotsDir: '%app.project_root%/screenshots'
```

### 3. JavaScript Execution

You can execute JavaScript in the browser to debug issues:

```php
// Get the page title
$title = $this->browserService->executeScript('return document.title;');
echo "Page title: $title";

// Check if an element exists
$exists = $this->browserService->executeScript('return document.querySelector(".some-selector") !== null;');
var_dump($exists);

// Get the HTML of an element
$html = $this->browserService->executeScript('return document.querySelector(".some-selector").outerHTML;');
echo "Element HTML: $html";
```

### 4. Service Container Inspection

You can inspect the service container to debug service-related issues:

```php
// List all services
$serviceIds = $this->container->getServiceIds();
var_dump($serviceIds);

// Check if a service exists
$hasService = $this->container->has('App\Service\Browser\BrowserServiceInterface');
var_dump($hasService);

// Get a service and inspect it
$browserService = $this->container->get('App\Service\Browser\BrowserServiceInterface');
var_dump(get_class($browserService));
```

### 5. Shared State Inspection

You can inspect the shared state to debug state-related issues:

```php
// Dump all state in a scope
$state = $this->stateService->getAll('scenario');
var_dump($state);

// Check if a key exists
$hasKey = $this->stateService->has('product.current', 'scenario');
var_dump($hasKey);

// Get a specific value
$value = $this->stateService->get('product.current', 'scenario');
var_dump($value);
```

## Debugging Tools

### 1. Behat Dry Run

You can use Behat's dry run mode to check if all step definitions are implemented:

```bash
vendor/bin/behat --dry-run
```

This will show you which steps are missing implementations.

### 2. Behat Debug Mode

You can run Behat in debug mode to get more detailed output:

```bash
vendor/bin/behat --format=pretty --colors
```

### 3. Xdebug

You can use Xdebug to step through the code and inspect variables:

1. Install Xdebug
2. Configure your IDE to listen for Xdebug connections
3. Set breakpoints in your code
4. Run Behat with Xdebug enabled:
   ```bash
   XDEBUG_CONFIG="idekey=PHPSTORM" vendor/bin/behat
   ```

### 4. Browser Developer Tools

You can use browser developer tools to inspect the page:

1. Run tests with a visible browser (not headless)
2. Add a step to pause execution:
   ```php
   /**
    * @Then I pause for debugging
    */
   public function iPauseForDebugging(): void
   {
       $this->browserService->executeScript('debugger;');
       $this->browserService->wait(3600); // Wait for an hour or until manually continued
   }
   ```
3. Open browser developer tools when the test pauses
4. Inspect the page, console, network, etc.

## Step-by-Step Debugging Process

When encountering an issue, follow these steps:

1. **Identify the Error**: Look at the error message and stack trace to understand what went wrong.

2. **Reproduce the Issue**: Try to reproduce the issue consistently to make debugging easier.

3. **Isolate the Problem**: Determine which component is causing the issue (service, page object, context, etc.).

4. **Check the Logs**: Look at the logs to see what happened before the error.

5. **Take Screenshots**: Take screenshots to see the state of the page when the error occurred.

6. **Inspect the State**: Check the shared state to see if the expected values are set.

7. **Debug the Component**: Use the appropriate debugging techniques for the component:
   - For services, inspect the service container and service configuration
   - For page objects, check the selectors and page structure
   - For contexts, check the step definitions and service usage

8. **Fix the Issue**: Once you've identified the root cause, fix the issue.

9. **Verify the Fix**: Run the tests again to make sure the issue is resolved.

10. **Document the Issue**: Document the issue and the solution to help others who might encounter the same problem.

## Common Debugging Scenarios

### 1. Test Fails at a Specific Step

If a test fails at a specific step, follow these steps:

1. Look at the error message and stack trace to understand what went wrong.
2. Check the logs to see what happened before the error.
3. Take a screenshot to see the state of the page when the error occurred.
4. Check the step definition to see what it's trying to do.
5. Debug the specific component that's causing the issue.

Example:

```php
/**
 * @When I click the add to cart button
 */
public function iClickTheAddToCartButton(): void
{
    try {
        $productPage = $this->pageFactory->getPage('ProductPage');
        
        // Take a screenshot before clicking
        $productPage->takeScreenshot('before-click');
        
        // Log the current state
        $this->logInfo("About to click add to cart button");
        
        // Try to click the button
        $productPage->addToCart();
        
        // Log success
        $this->logInfo("Successfully clicked add to cart button");
    } catch (\Throwable $e) {
        // Log the error
        $this->logError("Failed to click add to cart button", $e);
        
        // Take a screenshot after the error
        $this->browserService->takeScreenshot('click-error');
        
        // Rethrow the exception
        throw $e;
    }
}
```

### 2. Test Fails Intermittently

If a test fails intermittently, follow these steps:

1. Look for race conditions or timing issues.
2. Add more waiting and synchronization points.
3. Check for external dependencies that might be unreliable.
4. Add more logging to track the execution flow.
5. Run the test multiple times to gather more data.

Example:

```php
/**
 * @When I add the product to cart
 */
public function iAddTheProductToCart(): void
{
    try {
        $productPage = $this->pageFactory->getPage('ProductPage');
        
        // Wait for the page to be fully loaded
        $productPage->waitForPageToLoad();
        $this->browserService->waitForAjaxToComplete();
        
        // Log the current state
        $this->logInfo("Page loaded, about to add product to cart");
        
        // Wait for the add to cart button to be visible
        $this->browserService->waitForElementVisible('.add-to-cart-button');
        
        // Try to add the product to cart
        $productPage->addToCart();
        
        // Wait for the cart to be updated
        $this->browserService->waitForAjaxToComplete();
        $this->browserService->waitForElementVisible('.cart-confirmation');
        
        // Log success
        $this->logInfo("Successfully added product to cart");
    } catch (\Throwable $e) {
        // Log the error
        $this->logError("Failed to add product to cart", $e);
        
        // Take a screenshot after the error
        $this->browserService->takeScreenshot('add-to-cart-error');
        
        // Rethrow the exception
        throw $e;
    }
}
```

### 3. Test Fails in CI but Works Locally

If a test fails in CI but works locally, follow these steps:

1. Check for environment differences (browser version, screen size, etc.).
2. Check for timing issues (CI might be slower or faster).
3. Check for network issues (CI might have different network conditions).
4. Add more logging to track the execution flow.
5. Use screenshots to see the state of the page in CI.

Example:

```php
/**
 * @When I proceed to checkout
 */
public function iProceedToCheckout(): void
{
    try {
        $cartPage = $this->pageFactory->getPage('CartPage');
        
        // Log environment information
        $this->logInfo(sprintf(
            "Environment: Browser=%s, URL=%s",
            $this->browserService->getDriverType(),
            $this->browserService->getCurrentUrl()
        ));
        
        // Take a screenshot before proceeding
        $cartPage->takeScreenshot('before-checkout');
        
        // Wait for the page to be fully loaded
        $cartPage->waitForPageToLoad();
        $this->browserService->waitForAjaxToComplete();
        
        // Log the current state
        $this->logInfo("Page loaded, about to proceed to checkout");
        
        // Wait for the checkout button to be visible
        $this->browserService->waitForElementVisible('.checkout-button');
        
        // Try to proceed to checkout
        $cartPage->proceedToCheckout();
        
        // Wait for the checkout page to load
        $this->browserService->waitForPageToLoad();
        $this->browserService->waitForAjaxToComplete();
        
        // Take a screenshot after proceeding
        $this->browserService->takeScreenshot('after-checkout');
        
        // Log success
        $this->logInfo("Successfully proceeded to checkout");
    } catch (\Throwable $e) {
        // Log the error
        $this->logError("Failed to proceed to checkout", $e);
        
        // Take a screenshot after the error
        $this->browserService->takeScreenshot('checkout-error');
        
        // Rethrow the exception
        throw $e;
    }
}
```

## Cache Debugging

### Using LoggingCacheDecorator

The `LoggingCacheDecorator` provides detailed insights into cache operations and helps diagnose cache-related issues.

#### Setup

1. **Enable Cache Logging**

```yaml
# config/services.yml
services:
    App\Service\Cache\LoggingCacheDecorator:
        decorates: 'App\Service\Cache\SymfonyCacheService'
        arguments:
            $decorated: '@.inner'
            $logger: '@monolog.logger.cache'
```

2. **Configure Logger**

```yaml
# config/packages/monolog.yml
monolog:
    handlers:
        cache:
            type: rotating_file
            path: '%kernel.logs_dir%/cache.log'
            level: debug
            channels: ['cache']
```

#### Log Output Examples

1. **Cache Operations**

```log
[2024-01-25 14:30:15] cache.DEBUG: Cache hit for key: browser.session.12345
[2024-01-25 14:30:16] cache.DEBUG: Cache miss for key: api.response.products
[2024-01-25 14:30:17] cache.DEBUG: Setting cache for key: test_data.brand1.users
```

2. **Cache Invalidation**

```log
[2024-01-25 14:35:20] cache.DEBUG: Deleting cache entries matching pattern: browser.session.*
[2024-01-25 14:35:21] cache.DEBUG: Cache key deleted: config.brand.aeons
```

### Common Cache Issues

1. **Cache Miss Analysis**
    - Unexpected cache misses
    - Incorrect key generation
    - Premature expiration

```php
// Debug cache key generation
$logger->debug('Generated cache key', [
    'key' => $this->buildCacheKey($params),
    'params' => $params
]);
```

2. **Cache Corruption**
    - Serialization errors
    - Type mismatches
    - Incomplete data

```php
// Validate cached data
$logger->debug('Retrieved cached value', [
    'key' => $key,
    'value' => $value,
    'type' => gettype($value)
]);
```

3. **Cache Performance**
    - Slow operations
    - High miss rates
    - Memory issues

```php
// Track operation timing
$start = microtime(true);
$result = $this->cache->get($key);
$duration = microtime(true) - $start;
$logger->debug('Cache operation timing', [
    'operation' => 'get',
    'key' => $key,
    'duration' => $duration,
    'hit' => $result !== null
]);
```

### Debugging Strategies

1. **Cache State Inspection**

```php
public function inspectCacheState(string $pattern): array
{
    $keys = $this->cache->getKeys($pattern);
    $state = [];
    
    foreach ($keys as $key) {
        $state[$key] = [
            'value' => $this->cache->get($key),
            'ttl' => $this->cache->getTtl($key)
        ];
    }
    
    return $state;
}
```

2. **Cache Warmup Verification**

```php
public function verifyCacheWarmup(array $requiredKeys): array
{
    $missing = [];
    foreach ($requiredKeys as $key) {
        if (!$this->cache->has($key)) {
            $missing[] = $key;
        }
    }
    return $missing;
}
```

3. **Cache Consistency Checks**

```php
public function checkCacheConsistency(string $key, mixed $expectedValue): bool
{
    $cachedValue = $this->cache->get($key);
    $this->logger->debug('Cache consistency check', [
        'key' => $key,
        'expected' => $expectedValue,
        'actual' => $cachedValue,
        'matches' => $cachedValue === $expectedValue
    ]);
    return $cachedValue === $expectedValue;
}
```

### Troubleshooting Steps

1. **Cache Configuration**
    - Verify adapter settings
    - Check environment variables
    - Validate service registration

2. **Cache Keys**
    - Review key naming conventions
    - Check key generation logic
    - Verify key uniqueness

3. **Cache Data**
    - Inspect cached values
    - Verify data types
    - Check serialization

4. **Cache Lifetime**
    - Monitor expiration times
    - Check TTL settings
    - Verify cleanup processes

### Debug Commands

```bash
# Clear all cache
bin/console cache:clear

# View cache stats
bin/console cache:stats

# Warm up cache
bin/console cache:warmup

# List cache keys
bin/console cache:keys "pattern:*"
```

## Conclusion

Debugging is an essential part of test automation. By understanding common error patterns, using appropriate debugging techniques, and following a systematic debugging process, you can effectively identify and resolve issues in the Malaberg test automation framework.
