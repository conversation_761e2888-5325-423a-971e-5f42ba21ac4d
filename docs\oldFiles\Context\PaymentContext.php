<?php

namespace Features\Bootstrap\Context;

use Features\Bootstrap\Page\CheckoutPage;
use Features\Bootstrap\Page\PayPalPage;
use Features\Bootstrap\Page\Stripe3DSPage;
use App\Service\State\SharedStateServiceInterface;
use RuntimeException;
use Throwable;

/**
 * Handles payment related operations
 */
class PaymentContext extends BaseContext
{
    private CheckoutPage $checkoutPage;
    private PayPalPage $paypalPage;
    private Stripe3DSPage $stripe3dsPage;

    /**
     * @param CheckoutPage $checkoutPage Checkout page object
     * @param PayPalPage $paypalPage PayPal page object
     * @param Stripe3DSPage $stripe3dsPage Stripe 3DS page object
     * @param SharedDataContext $sharedData Shared data context
     */
    public function __construct(
        CheckoutPage      $checkoutPage,
        PayPalPage        $paypalPage,
        Stripe3DSPage     $stripe3dsPage,
        SharedDataContext $sharedData
    )
    {
        parent::__construct();
        $this->checkoutPage = $checkoutPage;
        $this->paypalPage = $paypalPage;
        $this->stripe3dsPage = $stripe3dsPage;
        $this->sharedData = $sharedData;
        $this->logInfo('PaymentContext initialized');
    }

    /**
     * @When /^I complete payment with PayPal$/
     * @throws RuntimeException
     * @sets payment.method, payment.status
     */
    public function iCompletePaymentWithPayPal(): void
    {
        try {
            $this->paypalPage->login();
            $this->paypalPage->confirmPayment();

            $this->stateService->set('payment.method', 'paypal');
            $this->stateService->set('payment.status', 'completed');

            $this->logInfo('Completed payment with PayPal');
        } catch (Throwable $e) {
            $this->logError('Failed to complete payment with PayPal', $e);
            throw new RuntimeException(
                sprintf('Failed to complete payment with PayPal: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * @When /^I complete 3DS verification$/
     * @throws RuntimeException
     * @sets payment.verified_3ds
     */
    public function iComplete3dsVerification(): void
    {
        try {
            $this->stripe3dsPage->complete3dsVerification();
            $this->stateService->set('payment.verified_3ds', true);

            $this->logInfo('Completed 3DS verification');
        } catch (Throwable $e) {
            $this->logError('Failed to complete 3DS verification', $e);
            throw new RuntimeException(
                sprintf('Failed to complete 3DS verification: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }
}
