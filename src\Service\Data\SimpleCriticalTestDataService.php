<?php

namespace App\Service\Data;

use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;
use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Simple implementation of the test data service for critical tests
 */
class SimpleCriticalTestDataService implements TestDataServiceInterface
{
    private array $testData = [];
    private ?LoggerInterface $logger;
    private ?string $criticalDataFile = null;

    /**
     * Constructor
     *
     * @param array|null $criticalData Optional critical data to use instead of loading from file
     * @param LoggerInterface|null $logger Optional logger
     */
    public function __construct(?array $criticalData = null, ?LoggerInterface $logger = null)
    {
        $this->logger = $logger ?? new NullLogger();

        if ($criticalData !== null) {
            // Use the provided critical data
            $this->testData['_critical_data'] = $criticalData;
            $this->processCriticalData($criticalData);
        } else {
            // Use the file path from the global variable or a default path
            $projectRoot = getenv('APP_PROJECT_ROOT') ?: '/app';
            $this->criticalDataFile = $projectRoot . '/features/fixtures/critical_sales_funnel_data.yml';

            if (!file_exists($this->criticalDataFile)) {
                // Try with realpath as fallback
                $fallbackPath = realpath(__DIR__ . '/../../../features/fixtures/critical_sales_funnel_data.yml');
                if (!$fallbackPath || !file_exists($fallbackPath)) {
                    throw new RuntimeException("Critical test data file not found: {$this->criticalDataFile} or {$fallbackPath}");
                }
                $this->criticalDataFile = $fallbackPath;
            }

            // Load the critical data
            $this->loadCriticalData();
        }
    }

    /**
     * Load critical test data from file
     */
    private function loadCriticalData(): void
    {
        try {
            $criticalData = Yaml::parseFile($this->criticalDataFile);
            $this->processCriticalData($criticalData);
        } catch (\Exception $e) {
            throw new RuntimeException("Error loading critical test data: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Process critical data and register it
     *
     * @param array $criticalData The critical data to process
     */
    private function processCriticalData(array $criticalData): void
    {
        // Store the entire data set for reference
        $this->testData['_critical_data'] = $criticalData;

        // Register funnel data directly
        if (isset($criticalData['funnel_items'])) {
            foreach ($criticalData['funnel_items'] as $key => $item) {
                if (isset($item['entry']['url'])) {
                    $funnelKey = $item['entry']['url'];
                    $this->registerData($funnelKey, $item);
                    $this->registerData('funnel.' . $funnelKey, $item);
                }
            }
        }

        // Register other data types
        $dataTypes = ['products', 'users', 'shipping', 'payment'];
        foreach ($dataTypes as $type) {
            if (isset($criticalData[$type])) {
                foreach ($criticalData[$type] as $key => $item) {
                    $this->registerData($type . '.' . $key, $item);
                }
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array
    {
        // Special handling for funnel data
        if ($type === 'funnel' && $key !== null) {
            if ($this->hasData($key)) {
                return $this->getData($key);
            }

            if ($this->hasData('funnel.' . $key)) {
                return $this->getData('funnel.' . $key);
            }

            // Try to find it in the critical data
            if (isset($this->testData['_critical_data']['funnel_items'])) {
                foreach ($this->testData['_critical_data']['funnel_items'] as $itemKey => $item) {
                    if (isset($item['entry']['url']) && $item['entry']['url'] === $key) {
                        return $item;
                    }
                }
            }
        }

        // For other types, try to find in the critical data
        if (isset($this->testData['_critical_data'][$type])) {
            if ($key && isset($this->testData['_critical_data'][$type][$key])) {
                return $this->testData['_critical_data'][$type][$key];
            } elseif (!$key) {
                return $this->testData['_critical_data'][$type];
            }
        }

        // Try to find by direct key
        $directKey = $type . '.' . ($key ?? '');
        if ($this->hasData($directKey)) {
            return $this->getData($directKey);
        }

        // Not found in critical data
        throw new RuntimeException(
            sprintf('Test data not found for Brand: %s, Type: %s, Key: %s', $brand, $type, $key ?? 'all')
        );
    }

    /**
     * {@inheritdoc}
     */
    public function registerData(string $key, array $data): void
    {
        $this->testData[$key] = $data;
    }

    /**
     * {@inheritdoc}
     */
    public function getData(string $key)
    {
        if (!isset($this->testData[$key])) {
            throw new RuntimeException(
                sprintf('Test data not found for key: %s', $key)
            );
        }

        return $this->testData[$key];
    }

    /**
     * {@inheritdoc}
     */
    public function hasData(string $key): bool
    {
        return isset($this->testData[$key]);
    }
}
