# Data Flow Optimization Documentation v1

## Overview

This document outlines the current data flow architecture in the Malaberg test automation framework, identifies issues with the current implementation, and proposes a comprehensive optimization plan. It provides detailed guidance on what changes are necessary across the existing codebase when implementing the proposed data flow improvements.

## Current Data Flow Architecture

### Data Sources and Structure

The current test automation framework uses multiple sources for test data:

1. **Brand-specific product data**:
   - `features/fixtures/brands/{brand}/products.yml`
   - `features/bootstrap/fixtures/brands/{brand}/products.yml`

2. **Sales funnel test data**:
   - `features/fixtures/sales_funnel_test_data.yml`
   - `features/bootstrap/fixtures/brands/{brand}/test_data.yml` (contains funnel_items)

3. **User and payment data**:
   - `features/fixtures/brands/{brand}/users.yml`
   - Embedded in `features/fixtures/sales_funnel_test_data.yml`

### Current Data Flow Patterns

The framework currently uses several data flow patterns:

#### Pattern 1: Test Data Loading and Validation

```
Test Scenario → TestDataContext → TestDataService → YAML Files → DataValidator → Cache
```

1. Test scenarios request data via step definitions like `Given I load product data`
2. The TestDataContext loads data using TestDataService or TestDataRegistry
3. Data is validated by DataValidator
4. Valid data is cached and registered for later use

#### Pattern 2: Data Sharing Between Contexts

```
TestDataContext → SharedStateService → Other Contexts
```

1. TestDataContext loads and validates data
2. Data is stored in the SharedStateService
3. Other contexts retrieve data from SharedStateService

#### Pattern 3: Sales Funnel Data Flow

```
Funnel Config + Product Data + User Data → Test Execution
```

1. Sales funnel tests load funnel configuration from test_data.yml or sales_funnel_test_data.yml
2. Product data is loaded from products.yml
3. User data is loaded from users.yml or sales_funnel_test_data.yml
4. Data is combined and used to execute test scenarios

### Key Components in Current Implementation

1. **TestDataRegistry**: A central registry for managing test data across contexts
   - Located in `features/bootstrap/Core/TestDataRegistry.php`
   - Loads data from YAML files
   - Validates data structure
   - Caches data for performance
   - Provides methods to register and retrieve data

2. **DataValidator**: Validates test data structure and content
   - Located in `features/bootstrap/Core/DataValidator.php`
   - Ensures required fields are present
   - Validates specific data types (products, users, shipping, payment methods)
   - Provides detailed error messages for validation failures

3. **TestDataService**: Service interface for accessing test data
   - Interface located in `src/Service/Data/TestDataServiceInterface.php`
   - Implementation in `src/Service/Data/TestDataService.php`
   - Loads test data from files
   - Registers data for later use
   - Retrieves registered data

4. **SharedStateService**: Service for sharing state between contexts
   - Interface located in `src/Service/State/SharedStateServiceInterface.php`
   - Implementation in `src/Service/State/SharedStateService.php`
   - Stores data in different scopes (scenario, feature, global)
   - Provides methods to set, get, and check for data
   - Automatically resets state between scenarios and features

## Identified Issues

### 1. Duplicate Data Sources

There are multiple sources for the same type of data:
- Product data exists in both `features/fixtures/brands/{brand}/products.yml` and `features/bootstrap/fixtures/brands/{brand}/products.yml`
- Funnel data exists in both `features/fixtures/sales_funnel_test_data.yml` and `features/bootstrap/fixtures/brands/{brand}/test_data.yml`

This leads to:
- Confusion about which data source is authoritative
- Potential inconsistencies when data is updated in one place but not the other
- Inefficient data loading as the system tries multiple paths

### 2. Inconsistent Data Structures

The data structure varies between files:
- Product data in `features/fixtures/brands/aeons/products.yml` has a different structure than in `features/bootstrap/fixtures/brands/aeons/products.yml`
- Funnel data in `features/fixtures/sales_funnel_test_data.yml` uses a different structure than in `features/bootstrap/fixtures/brands/aeons/test_data.yml`

This leads to:
- Complex validation logic to handle different structures
- Potential errors when accessing data with an unexpected structure
- Difficulty in maintaining and updating test data

### 3. Inefficient Data Loading

The current data loading process is inefficient:
- TestDataRegistry tries multiple paths to find data files
- Data is loaded and validated for each test scenario
- Caching is limited to the current test run

### 4. Complex Data Dependencies

There are complex dependencies between different types of data:
- Funnel tests depend on product data, user data, and payment data
- Product tests depend on product data and user data
- These dependencies are not explicitly defined or managed

### 5. Data Flow Duplication

Several areas where data flow is duplicated:

- **Product Data**: The same product information is duplicated across multiple files
- **Funnel Configuration**: Funnel configuration is duplicated in different files with different structures
- **Test User Data**: User data is duplicated in multiple locations
- **Data Access Patterns**: The same data is accessed through different patterns (TestDataRegistry, TestDataService, SharedStateService)

## Proposed Optimization Plan

### 1. Consolidate Data Sources

**Objective**: Create a single, authoritative source for each type of data.

**Required Changes**:

1. **File Structure Changes**:
   - Create a unified directory structure at `features/fixtures/data/{brand}/{type}.yml`
   - Move all product data to `features/fixtures/data/{brand}/products.yml`
   - Move all funnel data to `features/fixtures/data/{brand}/funnels.yml`
   - Move all user data to `features/fixtures/data/{brand}/users.yml`

2. **Code Changes**:
   - Update `TestDataService.php` to use the new file paths
   - Remove the multiple path lookup in `loadTestData()` method
   - Update any hardcoded paths in test contexts

3. **Data Migration**:
   - Create a data migration script to merge data from multiple sources
   - Ensure all unique data is preserved during migration
   - Validate migrated data against the new schema

### 2. Standardize Data Structures

**Objective**: Define and enforce a consistent data structure for each type of data.

**Required Changes**:

1. **Schema Definition**:
   - Create JSON Schema files for each data type in `features/fixtures/schemas/`
   - Define schemas for products, users, funnels, shipping, and payment methods
   - Document the schema for each data type

2. **Validation Changes**:
   - Update `DataValidator.php` to use JSON Schema for validation
   - Replace custom validation methods with schema validation
   - Add detailed error reporting for schema validation failures

3. **Data Structure Updates**:
   - Update all data files to conform to the standardized structure
   - Create a data transformation script to convert existing data to the new structure
   - Add version information to data files

### 3. Implement Data Relationships

**Objective**: Define explicit relationships between different types of data.

**Required Changes**:

1. **Relationship Definition**:
   - Create a data relationship model in `src/Service/Data/DataRelationshipModel.php`
   - Define relationships between products, funnels, users, etc.
   - Document these relationships in code and documentation

2. **Reference Implementation**:
   - Update data files to use references instead of duplicating data
   - Implement a reference resolver in `TestDataService.php`
   - Add methods to resolve references when loading data

3. **Relationship Validation**:
   - Add validation for data relationships
   - Ensure referenced data exists
   - Report detailed errors for invalid references

### 4. Optimize Data Loading

**Objective**: Implement a more efficient data loading strategy.

**Required Changes**:

1. **Caching Improvements**:
   - Implement a persistent cache in `TestDataService.php`
   - Add cache invalidation based on file modification times
   - Add cache statistics for monitoring

2. **Lazy Loading Implementation**:
   - Add lazy loading for large data sets
   - Implement data pagination for large collections
   - Add methods to load only required data

3. **Bulk Loading**:
   - Add methods to load all data at the beginning of a test run
   - Implement a data preloader in `TestDataService.php`
   - Add configuration options for preloading

### 5. Implement Data Transformation

**Objective**: Add a transformation layer to convert raw data into test-specific formats.

**Required Changes**:

1. **Transformer Implementation**:
   - Create a data transformer interface in `src/Service/Data/Transformer/DataTransformerInterface.php`
   - Implement transformers for each data type
   - Add methods to transform data for specific test scenarios

2. **Dynamic Data Generation**:
   - Add support for dynamic data generation
   - Implement data generators for common test data
   - Add methods to create variations of test data

3. **Integration with Test Contexts**:
   - Update test contexts to use transformers
   - Add helper methods for common transformations
   - Document transformation patterns

### 6. Standardize Data Access Patterns

**Objective**: Standardize how data is accessed across the project.

**Required Changes**:

1. **Unified Data Access Service**:
   - Create a unified data access service in `src/Service/Data/UnifiedDataService.php`
   - Combine functionality from TestDataService and SharedStateService
   - Implement a consistent interface for data access

2. **Context Updates**:
   - Update all contexts to use the unified data access service
   - Replace direct TestDataRegistry and SharedStateService usage
   - Standardize data access patterns across contexts

3. **Documentation and Examples**:
   - Document the standard data access patterns
   - Create examples for common data access scenarios
   - Update existing documentation to reflect the new patterns

## Implementation Plan

### Phase 1: Data Source Consolidation

1. **Create New Directory Structure**
   - Create `features/fixtures/data/` directory
   - Create brand subdirectories
   - Create type subdirectories within each brand

2. **Migrate Existing Data**
   - Create a data migration script
   - Run the script to migrate data to the new structure
   - Validate migrated data

3. **Update TestDataService**
   - Update file path resolution in TestDataService
   - Remove multiple path lookup
   - Add logging for data source access

### Phase 2: Data Structure Standardization

1. **Define JSON Schemas**
   - Create schema files for each data type
   - Document the schemas
   - Implement schema validation

2. **Update Data Validator**
   - Replace custom validation with schema validation
   - Add detailed error reporting
   - Update validation calls in TestDataService

3. **Transform Existing Data**
   - Create a data transformation script
   - Run the script to transform data to the new structure
   - Validate transformed data

### Phase 3: Data Relationship Implementation

1. **Define Data Relationships**
   - Create a data relationship model
   - Document relationships
   - Implement relationship validation

2. **Update Data Files**
   - Replace duplicated data with references
   - Add relationship metadata
   - Validate updated data files

3. **Implement Reference Resolution**
   - Add reference resolution to TestDataService
   - Update data access methods to resolve references
   - Add caching for resolved references

### Phase 4: Data Loading Optimization

1. **Implement Persistent Cache**
   - Add file-based cache for test data
   - Implement cache invalidation
   - Add cache statistics

2. **Add Lazy Loading**
   - Implement lazy loading for large data sets
   - Add data pagination
   - Update data access methods to support lazy loading

3. **Implement Bulk Loading**
   - Add methods for bulk data loading
   - Implement data preloader
   - Add configuration options for preloading

### Phase 5: Data Transformation Implementation

1. **Create Transformer Interface**
   - Define transformer interface
   - Implement transformers for each data type
   - Add transformation methods

2. **Add Dynamic Data Generation**
   - Implement data generators
   - Add methods for creating data variations
   - Integrate generators with transformers

3. **Update Test Contexts**
   - Update contexts to use transformers
   - Add helper methods for common transformations
   - Document transformation patterns

### Phase 6: Data Access Standardization

1. **Create Unified Data Service**
   - Implement unified data access service
   - Combine TestDataService and SharedStateService functionality
   - Add methods for common data access patterns

2. **Update Contexts**
   - Update all contexts to use the unified service
   - Replace direct service usage
   - Standardize data access patterns

3. **Update Documentation**
   - Document the new data access patterns
   - Create examples for common scenarios
   - Update existing documentation

## Required Code Changes

### TestDataService.php

```php
// Current implementation
public function loadTestData(string $brand, string $type, ?string $key = null): array
{
    $cacheKey = sprintf('%s_%s_%s', $brand, $type, $key ?? 'all');
    $this->logInfo(sprintf("Loading test data - Brand: %s, Type: %s, Key: %s", $brand, $type, $key ?? 'all'));

    if (isset($this->cache[$cacheKey])) {
        $this->logInfo(sprintf("Returning cached data for key: %s", $cacheKey));
        return $this->cache[$cacheKey];
    }

    // Try multiple possible paths
    $paths = [
        sprintf('%s/brands/%s/%s.yml', $this->fixturesDir, $brand, $type),
        sprintf('%s/brands/%s/data/%s.yml', $this->fixturesDir, $brand, $type),
        sprintf('%s/brands/%s/fixtures/%s.yml', $this->fixturesDir, $brand, $type),
    ];
    
    // ... rest of the method
}

// Proposed implementation
public function loadTestData(string $brand, string $type, ?string $key = null): array
{
    $cacheKey = sprintf('%s_%s_%s', $brand, $type, $key ?? 'all');
    $this->logInfo(sprintf("Loading test data - Brand: %s, Type: %s, Key: %s", $brand, $type, $key ?? 'all'));

    // Check persistent cache first
    if ($this->persistentCache->has($cacheKey)) {
        $this->logInfo(sprintf("Returning cached data for key: %s", $cacheKey));
        $this->cacheStats->incrementHit($cacheKey);
        return $this->persistentCache->get($cacheKey);
    }

    // Single authoritative path
    $path = sprintf('%s/data/%s/%s.yml', $this->fixturesDir, $brand, $type);
    
    if (!file_exists($path)) {
        $this->logError(sprintf("Test data file not found: %s", $path));
        throw new RuntimeException(sprintf('Test data file not found: %s', $path));
    }
    
    // ... rest of the method with schema validation and reference resolution
}
```

### DataValidator.php

```php
// Current implementation
public function validateProductData(array $data): void
{
    $this->validateRequiredFields($data, [
        'name',
        'slug',
        'prices',
        'options',
        'content'
    ], 'product');

    $this->validatePricing($data['prices']);
    $this->validateOptions($data['options']);
    $this->validateContent($data['content']);
}

// Proposed implementation
public function validateData(array $data, string $type): void
{
    $schemaPath = sprintf('%s/schemas/%s.json', $this->schemaDir, $type);
    
    if (!file_exists($schemaPath)) {
        throw new RuntimeException(sprintf('Schema not found for type: %s', $type));
    }
    
    $schema = json_decode(file_get_contents($schemaPath), true);
    $validator = new JsonSchemaValidator();
    $result = $validator->validate($data, $schema);
    
    if (!$result->isValid()) {
        $errors = $result->getErrors();
        $errorMessages = [];
        
        foreach ($errors as $error) {
            $errorMessages[] = sprintf('%s: %s', $error->getProperty(), $error->getMessage());
        }
        
        throw new RuntimeException(sprintf(
            'Data validation failed for type %s: %s',
            $type,
            implode(', ', $errorMessages)
        ));
    }
}
```

### UnifiedDataService.php (New File)

```php
<?php

namespace App\Service\Data;

use App\Service\AbstractService;
use App\Service\State\SharedStateServiceInterface;
use Psr\Log\LoggerInterface;

/**
 * Unified data service that combines TestDataService and SharedStateService
 */
class UnifiedDataService extends AbstractService implements UnifiedDataServiceInterface
{
    private TestDataServiceInterface $dataService;
    private SharedStateServiceInterface $stateService;
    
    /**
     * Constructor
     *
     * @param TestDataServiceInterface $dataService Test data service
     * @param SharedStateServiceInterface $stateService Shared state service
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        TestDataServiceInterface $dataService,
        SharedStateServiceInterface $stateService,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->dataService = $dataService;
        $this->stateService = $stateService;
        $this->logInfo("Initializing UnifiedDataService");
    }
    
    /**
     * Load and share test data
     *
     * @param string $brand Brand identifier
     * @param string $type Data type
     * @param string|null $key Data key
     * @param string $stateKey Key to use in shared state
     * @param string $scope Shared state scope
     * @return array The loaded data
     */
    public function loadAndShareData(
        string $brand,
        string $type,
        ?string $key = null,
        string $stateKey = null,
        string $scope = 'scenario'
    ): array {
        $data = $this->dataService->loadTestData($brand, $type, $key);
        
        if ($stateKey) {
            $this->stateService->set($stateKey, $data, $scope);
        }
        
        return $data;
    }
    
    // ... additional methods for common data access patterns
}
```

## Test Context Updates

### TestDataContext.php

```php
// Current implementation
public function iLoadTestDataForUser(string $userKey): void
{
    try {
        $brand = $this->configService->getCurrentBrand();
        $data = $this->dataService->loadTestData($brand, 'users', $userKey);

        // Validate user data structure
        $this->validationService->validateUserData($data);

        $this->dataService->registerData('current_user', $data);
        $this->currentTestData['user'] = $data;

        // Store in shared state for other contexts
        $this->stateService->set('user.data', $data);
        $this->stateService->set('user.key', $userKey);

        $this->logInfo(sprintf('Loaded and shared test data for user: %s', $userKey));
    } catch (\Throwable $e) {
        $this->logError(sprintf('Failed to load test data for user: %s', $userKey), $e);
        throw $e;
    }
}

// Proposed implementation
public function iLoadTestDataForUser(string $userKey): void
{
    try {
        $brand = $this->configService->getCurrentBrand();
        
        // Use unified data service
        $data = $this->unifiedDataService->loadAndShareData(
            $brand,
            'users',
            $userKey,
            'user.data',
            'scenario'
        );
        
        // Set additional state
        $this->unifiedDataService->setState('user.key', $userKey);
        
        $this->logInfo(sprintf('Loaded and shared test data for user: %s', $userKey));
    } catch (\Throwable $e) {
        $this->logError(sprintf('Failed to load test data for user: %s', $userKey), $e);
        throw $e;
    }
}
```

## Migration Strategy

To ensure a smooth transition to the new data flow architecture, we recommend the following migration strategy:

1. **Create a parallel implementation** - Implement the new data flow architecture alongside the existing one
2. **Migrate one data type at a time** - Start with product data, then move to users, funnels, etc.
3. **Update contexts incrementally** - Update one context at a time to use the new data flow
4. **Run tests in parallel** - Run tests using both the old and new data flow to ensure compatibility
5. **Deprecate old components** - Once all tests are passing with the new data flow, deprecate the old components
6. **Remove old components** - After a deprecation period, remove the old components

## Conclusion

The proposed data flow optimization plan addresses the identified issues in the current implementation and provides a clear path forward for improving the efficiency, maintainability, and flexibility of the test automation framework. By implementing these changes, the framework will be better equipped to handle the growing complexity of test scenarios and data requirements.

The key benefits of these optimizations include:

1. **Reduced Maintenance Overhead**: With consolidated data sources and standardized structures, maintaining test data becomes simpler
2. **Improved Test Performance**: Efficient data loading and caching will improve test execution speed
3. **Enhanced Test Reliability**: Consistent data access patterns and explicit relationships will reduce errors
4. **Better Scalability**: The optimized data flow will better support adding new brands, products, and test scenarios

## Next Steps

1. Review this optimization plan with the development team
2. Prioritize the implementation phases based on project needs
3. Create detailed implementation tickets for each phase
4. Implement a proof of concept for the highest priority phase
5. Evaluate the results and adjust the plan as needed
