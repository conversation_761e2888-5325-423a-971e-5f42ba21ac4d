<?php

// Load composer autoloader
require_once __DIR__ . '/../../../vendor/autoload.php';
require_once __DIR__ . '/EmailVerificationTool.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

function loadEnvironment(): void
{
    $envFile = __DIR__ . '/.env';
    if (!file_exists($envFile)) {
        throw new RuntimeException("Environment file not found: {$envFile}");
    }

    $vars = parse_ini_file($envFile);
    if ($vars === false) {
        throw new RuntimeException("Failed to parse environment file");
    }

    foreach ($vars as $key => $value) {
        putenv("$key=$value");
    }
}

function displayEmail(array $emailContent): void
{
    echo str_repeat('-', 50) . "\n";
    echo "Email ID: {$emailContent['id']}\n";
    echo "Subject: {$emailContent['subject']}\n";
    echo "From: {$emailContent['from']}\n";
    echo "To: {$emailContent['to']}\n";
    echo "Date: {$emailContent['date']}\n";
    echo "Content Available:\n";
    echo "  - HTML: " . ($emailContent['has_html'] ? 'Yes' : 'No') . "\n";
    echo "  - Text: " . ($emailContent['has_text'] ? 'Yes' : 'No') . "\n";

    if (!empty($emailContent['links'])) {
        echo "Links Found:\n";
        foreach ($emailContent['links'] as $link) {
            echo "  - {$link['text']}: {$link['url']}\n";
        }
    }
    echo str_repeat('-', 50) . "\n\n";
}

try {
    echo "Starting email verification...\n\n";

    loadEnvironment();

    $requiredVars = ['MAILTRAP_TOKEN', 'MAILTRAP_AEONS_INBOX_ID', 'MAILTRAP_ACCOUNT_ID'];
    foreach ($requiredVars as $var) {
        if (!getenv($var)) {
            throw new RuntimeException("Missing required environment variable: {$var}");
        }
    }

    $tool = new Features\Bootstrap\Tools\EmailVerificationTool();

    // Get latest emails
    echo "Fetching latest emails...\n";
    $emails = $tool->getLatestEmails(5);

    if (empty($emails)) {
        echo "No emails found in inbox.\n";
        exit(0);
    }

    echo "Found " . count($emails) . " emails.\n\n";

    // Display formatted emails
    foreach ($emails as $email) {
        $formattedContent = $tool->formatEmailContent($email);
        displayEmail($formattedContent);
    }

    // Example of email verification
    echo "Verifying specific email...\n";
    $criteria = [
        'subject' => 'Test Email',
        'to_email' => '<EMAIL>'
    ];

    $verifiedEmail = $tool->verifyEmailExists($criteria, 300, 3, 10);
    if ($verifiedEmail) {
        echo "Found matching email:\n";
        displayEmail($tool->formatEmailContent($verifiedEmail));
    } else {
        echo "No matching email found.\n";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
} 