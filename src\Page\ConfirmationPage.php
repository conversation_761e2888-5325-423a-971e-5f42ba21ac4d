<?php

namespace App\Page;

use App\Page\Base\BasePage;

/**
 * ConfirmationPage handles actions on the order confirmation page.
 */
class ConfirmationPage extends BasePage
{
    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'ORDER_NUMBER' => '.order-number',
        'ORDER_SUMMARY' => '.order-summary',
        'ORDER_ITEMS' => '.order-items .order-item',
        'ORDER_TOTAL' => '.order-total',
        'CONTINUE_SHOPPING_BUTTON' => '.continue-shopping-button',
        'THANK_YOU_MESSAGE' => '.thank-you-message'
    ];
    /**
     * The path of the confirmation page.
     *
     * @var string
     */
    protected string $path = '/confirmation';

    /**
     * Get the order number
     *
     * @return string
     */
    public function getOrderNumber(): string
    {
        return $this->getElementText(self::SELECTORS['ORDER_NUMBER']);
    }

    /**
     * Get the order items
     *
     * @return array List of order items
     */
    public function getOrderItems(): array
    {
        $items = [];
        $itemElements = $this->browserService->findElements(self::SELECTORS['ORDER_ITEMS']);

        foreach ($itemElements as $element) {
            $items[] = [
                'name' => $element->find('css', '.item-name')->getText(),
                'price' => $element->find('css', '.item-price')->getText(),
                'quantity' => (int)$element->find('css', '.item-quantity')->getText()
            ];
        }

        return $items;
    }

    /**
     * Get the order total
     *
     * @return string
     */
    public function getOrderTotal(): string
    {
        return $this->getElementText(self::SELECTORS['ORDER_TOTAL']);
    }

    /**
     * Continue shopping
     *
     * @return void
     */
    public function continueShopping(): void
    {
        $this->clickElement(self::SELECTORS['CONTINUE_SHOPPING_BUTTON']);
        $this->waitForPageToLoad();
    }

    /**
     * Check if the thank you message is displayed
     *
     * @return bool
     */
    public function hasThankYouMessage(): bool
    {
        return $this->elementExists(self::SELECTORS['THANK_YOU_MESSAGE']);
    }

    /**
     * Get the thank you message
     *
     * @return string
     */
    public function getThankYouMessage(): string
    {
        return $this->getElementText(self::SELECTORS['THANK_YOU_MESSAGE']);
    }

    /**
     * {@inheritdoc}
     */
    protected function verifyPage(): void
    {
        $this->waitForElementVisible(self::SELECTORS['THANK_YOU_MESSAGE']);
    }

    /**
     * Get the number of subscription items in the cart
     *
     * @return int
     */
    public function getSubscriptionItemCount(): int
    {
        // TODO: Implement actual logic to count subscription items
        // For now, return a dummy value
        return 0;
    }

    /**
     * Get the number of one-time purchase items in the cart
     *
     * @return int
     */
    public function getOneTimePurchaseItemCount(): int
    {
        // TODO: Implement actual logic to count one-time purchase items
        // For now, return a dummy value
        return 0;
    }

    /**
     * Get the frequencies of subscription items
     *
     * @return array
     */
    public function getSubscriptionItemFrequencies(): array
    {
        // TODO: Implement actual logic to get subscription frequencies
        // For now, return an empty array
        return [];
    }

    /**
     * Get all items in the cart
     *
     * @return array
     */
    public function getCartItems(): array
    {
        // Reuse the getOrderItems method for compatibility
        return $this->getOrderItems();
    }
}
