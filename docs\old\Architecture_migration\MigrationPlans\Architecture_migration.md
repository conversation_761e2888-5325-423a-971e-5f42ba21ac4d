# E-commerce Test Framework Architecture Migration Plan

## Transitioning to Service-Oriented Test Architecture

**Version:** 1.0
**Last Updated:** 2025-04-10
**Author:** AI Assistant

## Table of Contents

1. [Overview](#1-overview)
2. [Migration Strategy](#2-migration-strategy)
3. [Phase 1: Foundation Setup](#3-phase-1-foundation-setup)
4. [Phase 2: Core Services Implementation](#4-phase-2-core-services-implementation)
5. [Phase 3: Context Migration](#5-phase-3-context-migration)
6. [Phase 4: Page Object Migration](#6-phase-4-page-object-migration)
7. [Phase 5: Test Runner Migration](#7-phase-5-test-runner-migration)
8. [Phase 6: Cleanup and Optimization](#8-phase-6-cleanup-and-optimization)
9. [Testing Strategy](#9-testing-strategy)
10. [Rollback Plan](#10-rollback-plan)
11. [Timeline and Resources](#11-timeline-and-resources)
12. [Appendix: Code Examples](#12-appendix-code-examples)

---

## 1. Overview

### 1.1 Current Architecture Issues

- Singleton pattern overuse (SharedDataContext)
- Inconsistent dependency management
- Duplication and redundancy across contexts
- Lack of clear abstraction layers
- Limited extensibility
- Maintenance challenges

### 1.2 Target Architecture Benefits

- Clear component boundaries with well-defined services
- Explicit dependencies through service container
- Consistent patterns for service usage
- Improved testability and maintainability
- Better separation of concerns
- Enhanced extensibility

### 1.3 Migration Principles

- Incremental changes to maintain functionality
- Backward compatibility where possible
- Comprehensive testing at each step
- Documentation updates in parallel
- Focus on one component at a time

---

## 2. Migration Strategy

### 2.1 Phased Approach

We'll use a phased approach to migrate the architecture while ensuring tests remain functional throughout the process.
Each phase will focus on a specific aspect of the architecture and will include:

1. Implementation of new components
2. Migration of existing functionality
3. Testing to ensure equivalence
4. Documentation updates

### 2.2 Parallel Operation

During migration, we'll maintain both old and new implementations in parallel, with feature flags to switch between
them. This allows for:

- Easy comparison of behavior
- Gradual adoption of new architecture
- Safe rollback if issues are discovered

### 2.3 Dependency Management

We'll use Symfony's Dependency Injection Container for service management, which is already partially implemented in the
project through `FriendsOfBehat\ServiceContainerExtension`.

---

## 3. Phase 1: Foundation Setup

### 3.1 Service Container Configuration

**Objective:** Set up the core service container infrastructure.

#### 3.1.1 Tasks

1. Create a central `services.yml` file with proper structure
2. Define service configuration sections
3. Set up autowiring and autoconfiguration
4. Configure parameter handling

#### 3.1.2 Implementation Details

```yaml
# config/services.yml
parameters:
  # Global parameters
  app.project_root: '%paths.base%'
  app.config_dir: '%app.project_root%/config'
  app.fixtures_dir: '%app.project_root%/features/fixtures'

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false
    bind:
      $projectRoot: '%app.project_root%'
      $configDir: '%app.config_dir%'
      $fixturesDir: '%app.fixtures_dir%'

  # Service registration will go here
```

### 3.2 Interface Definitions

**Objective:** Define interfaces for all core services.

#### 3.2.1 Tasks

1. Create interface definitions for core services
2. Document interface methods thoroughly
3. Define data transfer objects for service communication

#### 3.2.2 Implementation Details

Create the following interfaces:

- `ConfigurationServiceInterface`
- `TestDataServiceInterface`
- `ValidationServiceInterface`
- `BrowserServiceInterface`
- `SharedStateServiceInterface`

### 3.3 Service Base Classes

**Objective:** Create base classes for services and contexts.

#### 3.3.1 Tasks

1. Create `AbstractService` base class
2. Create `ServiceAwareContext` base class
3. Implement service container access methods

#### 3.3.2 Implementation Details

```php
// src/Service/AbstractService.php
namespace App\Service;

abstract class AbstractService
{
    protected function logInfo(string $message, array $context = []): void
    {
        // Logging implementation
    }

    protected function logError(string $message, \Throwable $exception = null, array $context = []): void
    {
        // Error logging implementation
    }
}

// src/Context/Base/ServiceAwareContext.php
namespace App\Context\Base;

use Behat\Behat\Context\Context;
use Symfony\Component\DependencyInjection\ContainerInterface;

abstract class ServiceAwareContext implements Context
{
    protected ContainerInterface $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    protected function getService(string $id)
    {
        return $this->container->get($id);
    }
}
```

---

## 4. Phase 2: Core Services Implementation

### 4.1 Configuration Service

**Objective:** Implement the configuration service to replace ConfigurationManager.

#### 4.1.1 Tasks

1. Create `ConfigurationService` implementing `ConfigurationServiceInterface`
2. Migrate functionality from `ConfigurationManager`
3. Add support for environment variables
4. Implement brand and environment configuration

#### 4.1.2 Implementation Details

```php
// src/Service/Configuration/ConfigurationServiceInterface.php
namespace App\Service\Configuration;

interface ConfigurationServiceInterface
{
    public function getBrandConfig(string $key);
    public function getEnvironmentConfig(string $key);
    public function getCurrentBrand(): string;
    public function getCurrentEnvironment(): string;
    public function setBrand(string $brand): void;
    public function setEnvironment(string $environment): void;
}

// src/Service/Configuration/ConfigurationService.php
namespace App\Service\Configuration;

use App\Service\AbstractService;
use Symfony\Component\Yaml\Yaml;

class ConfigurationService extends AbstractService implements ConfigurationServiceInterface
{
    private string $configDir;
    private string $currentBrand;
    private string $currentEnvironment;
    private array $brandConfigs = [];
    private array $environmentConfigs = [];

    public function __construct(
        string $configDir,
        ?string $brand = null,
        ?string $environment = null
    ) {
        $this->configDir = $configDir;
        $this->currentBrand = $brand ?? getenv('TEST_BRAND') ?? 'aeons';
        $this->currentEnvironment = $environment ?? getenv('TEST_ENV') ?? 'stage';

        $this->loadBrandConfig($this->currentBrand);
        $this->loadEnvironmentConfig($this->currentBrand, $this->currentEnvironment);
    }

    // Implementation of interface methods...
}
```

### 4.2 Test Data Service

**Objective:** Implement the test data service to replace TestDataRegistry.

#### 4.2.1 Tasks

1. Create `TestDataService` implementing `TestDataServiceInterface`
2. Migrate functionality from `TestDataRegistry`
3. Implement caching and validation
4. Add support for different data formats

#### 4.2.2 Implementation Details

```php
// src/Service/Data/TestDataServiceInterface.php
namespace App\Service\Data;

interface TestDataServiceInterface
{
    public function loadTestData(string $brand, string $type, ?string $key = null): array;
    public function registerData(string $key, array $data): void;
    public function getData(string $key);
    public function hasData(string $key): bool;
}

// src/Service/Data/TestDataService.php
namespace App\Service\Data;

use App\Service\AbstractService;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Symfony\Component\Yaml\Yaml;

class TestDataService extends AbstractService implements TestDataServiceInterface
{
    private string $fixturesDir;
    private array $testData = [];
    private array $cache = [];
    private ValidationServiceInterface $validator;
    private ConfigurationServiceInterface $configService;

    public function __construct(
        string $fixturesDir,
        ValidationServiceInterface $validator,
        ConfigurationServiceInterface $configService
    ) {
        $this->fixturesDir = $fixturesDir;
        $this->validator = $validator;
        $this->configService = $configService;
    }

    // Implementation of interface methods...
}
```

### 4.3 Shared State Service

**Objective:** Replace the SharedDataContext singleton with a proper service.

#### 4.3.1 Tasks

1. Create `SharedStateService` implementing `SharedStateServiceInterface`
2. Migrate functionality from `SharedDataContext`
3. Implement scoped state (scenario, feature, global)
4. Add event listeners for state cleanup

#### 4.3.2 Implementation Details

```php
// src/Service/State/SharedStateServiceInterface.php
namespace App\Service\State;

interface SharedStateServiceInterface
{
    public function set(string $key, $value, string $scope = 'scenario'): void;
    public function get(string $key, string $scope = 'scenario');
    public function has(string $key, string $scope = 'scenario'): bool;
    public function getAll(string $scope = 'scenario'): array;
    public function reset(string $scope = 'scenario'): void;
}

// src/Service/State/SharedStateService.php
namespace App\Service\State;

use App\Service\AbstractService;
use Behat\Behat\EventDispatcher\Event\ScenarioTested;
use Behat\Behat\EventDispatcher\Event\FeatureTested;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class SharedStateService extends AbstractService implements SharedStateServiceInterface, EventSubscriberInterface
{
    private array $scenarioState = [];
    private array $featureState = [];
    private array $globalState = [];

    public static function getSubscribedEvents()
    {
        return [
            ScenarioTested::AFTER => ['resetScenarioState'],
            FeatureTested::AFTER => ['resetFeatureState'],
        ];
    }

    // Implementation of interface methods...

    public function resetScenarioState(): void
    {
        $this->scenarioState = [];
    }

    public function resetFeatureState(): void
    {
        $this->featureState = [];
    }
}
```

### 4.4 Validation Service

**Objective:** Implement the validation service to replace DataValidator.

#### 4.4.1 Tasks

1. Create `ValidationService` implementing `ValidationServiceInterface`
2. Migrate functionality from `DataValidator`
3. Add support for schema validation
4. Implement custom validation rules

#### 4.4.2 Implementation Details

```php
// src/Service/Validation/ValidationServiceInterface.php
namespace App\Service\Validation;

interface ValidationServiceInterface
{
    public function validateProductData(array $data): void;
    public function validateUserData(array $data): void;
    public function validateShippingData(array $data): void;
    public function validateSchema(array $data, string $schema): void;
}

// src/Service/Validation/ValidationService.php
namespace App\Service\Validation;

use App\Service\AbstractService;
use RuntimeException;

class ValidationService extends AbstractService implements ValidationServiceInterface
{
    private array $schemas = [];

    public function __construct(string $schemasDir = null)
    {
        if ($schemasDir && is_dir($schemasDir)) {
            $this->loadSchemas($schemasDir);
        }
    }

    // Implementation of interface methods...
}
```

### 4.5 Browser Service

**Objective:** Create a service to manage browser interactions.

#### 4.5.1 Tasks

1. Create `BrowserService` implementing `BrowserServiceInterface`
2. Implement browser session management
3. Add support for different browser drivers
4. Create screenshot and logging capabilities

#### 4.5.2 Implementation Details

```php
// src/Service/Browser/BrowserServiceInterface.php
namespace App\Service\Browser;

use Behat\Mink\Session;

interface BrowserServiceInterface
{
    public function getSession(): Session;
    public function visit(string $url): void;
    public function takeScreenshot(string $name = null): string;
    public function waitForElement(string $selector, int $timeout = 30): void;
    public function executeScript(string $script);
}

// src/Service/Browser/BrowserService.php
namespace App\Service\Browser;

use App\Service\AbstractService;
use Behat\Mink\Session;
use Behat\Mink\Driver\Selenium2Driver;

class BrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;

    public function __construct(Session $session, string $screenshotsDir)
    {
        $this->session = $session;
        $this->screenshotsDir = $screenshotsDir;
    }

    // Implementation of interface methods...
}
```

---

## 5. Phase 3: Context Migration

### 5.1 Base Context

**Objective:** Create a new base context that uses services.

#### 5.1.1 Tasks

1. Create `BaseContext` extending `ServiceAwareContext`
2. Implement common functionality
3. Add service accessor methods
4. Create backward compatibility layer

#### 5.1.2 Implementation Details

```php
// src/Context/Base/BaseContext.php
namespace App\Context\Base;

use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Behat\Behat\Context\Context;
use Symfony\Component\DependencyInjection\ContainerInterface;

abstract class BaseContext extends ServiceAwareContext implements Context
{
    protected function getConfigService(): ConfigurationServiceInterface
    {
        return $this->getService(ConfigurationServiceInterface::class);
    }

    protected function getTestDataService(): TestDataServiceInterface
    {
        return $this->getService(TestDataServiceInterface::class);
    }

    protected function getSharedStateService(): SharedStateServiceInterface
    {
        return $this->getService(SharedStateServiceInterface::class);
    }

    protected function getBrowserService(): BrowserServiceInterface
    {
        return $this->getService(BrowserServiceInterface::class);
    }

    protected function getValidationService(): ValidationServiceInterface
    {
        return $this->getService(ValidationServiceInterface::class);
    }

    protected function logInfo(string $message, array $context = []): void
    {
        // Logging implementation
    }

    protected function logError(string $message, \Throwable $exception = null, array $context = []): void
    {
        // Error logging implementation
    }
}
```

### 5.2 Feature Context

**Objective:** Migrate the main FeatureContext to use services.

#### 5.2.1 Tasks

1. Update `FeatureContext` to extend `BaseContext`
2. Replace direct dependencies with service calls
3. Update step definitions to use services
4. Add backward compatibility for page objects

#### 5.2.2 Implementation Details

```php
// src/Context/FeatureContext.php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Symfony\Component\DependencyInjection\ContainerInterface;

class FeatureContext extends BaseContext
{
    private BrowserServiceInterface $browserService;
    private SharedStateServiceInterface $stateService;

    public function __construct(
        ContainerInterface $container,
        BrowserServiceInterface $browserService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->browserService = $browserService;
        $this->stateService = $stateService;
    }

    /**
     * @BeforeScenario
     */
    public function gatherContexts(BeforeScenarioScope $scope): void
    {
        // Gather other contexts if needed
    }

    // Step definitions using services...
}
```

### 5.3 Brand Context

**Objective:** Migrate BrandContext to use the ConfigurationService.

#### 5.3.1 Tasks

1. Update `BrandContext` to extend `BaseContext`
2. Replace ConfigurationManager with ConfigurationService
3. Update step definitions to use services
4. Ensure backward compatibility

#### 5.3.2 Implementation Details

```php
// src/Context/BrandContext.php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class BrandContext extends BaseContext
{
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;

    public function __construct(
        ContainerInterface $container,
        ConfigurationServiceInterface $configService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->configService = $configService;
        $this->stateService = $stateService;
    }

    /**
     * @Given I am using the :brand brand
     */
    public function iAmUsingTheBrand(string $brand): void
    {
        $this->configService->setBrand($brand);
        $this->stateService->set('brand.current', $brand);
        $this->logInfo(sprintf('Set current brand to: %s', $brand));
    }

    // Other step definitions...
}
```

### 5.4 Other Contexts

**Objective:** Migrate remaining contexts to use services.

#### 5.4.1 Tasks

1. Update each context to extend `BaseContext`
2. Replace direct dependencies with service calls
3. Update step definitions to use services
4. Ensure backward compatibility

#### 5.4.2 Implementation Plan

Migrate contexts in the following order:

1. TestDataContext
2. ProductContext
3. CartContext
4. CheckoutContext
5. PaymentContext
6. EmailContext
7. AdminCommandContext
8. ValidationContext
9. SSHContext
10. Other specialized contexts

---

## 6. Phase 4: Page Object Migration

### 6.1 Base Page

**Objective:** Update the BasePage to work with the BrowserService.

#### 6.1.1 Tasks

1. Create a new `BasePageInterface`
2. Update `BasePage` to use BrowserService
3. Implement common page functionality
4. Ensure backward compatibility

#### 6.1.2 Implementation Details

```php
// src/Page/Base/BasePageInterface.php
namespace App\Page\Base;

interface BasePageInterface
{
    public function open(array $urlParameters = []): void;
    public function getUrl(array $urlParameters = []): string;
    public function isOpen(): bool;
    public function waitForPageToLoad(int $timeout = 30): void;
}

// src/Page/Base/BasePage.php
namespace App\Page\Base;

use App\Service\Browser\BrowserServiceInterface;

abstract class BasePage implements BasePageInterface
{
    protected BrowserServiceInterface $browserService;
    protected string $path = '/';
    protected string $baseUrl;

    public function __construct(BrowserServiceInterface $browserService, string $baseUrl = null)
    {
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL');
    }

    // Implementation of interface methods...
}
```

### 6.2 Page Objects

**Objective:** Migrate page objects to use the BrowserService.

#### 6.2.1 Tasks

1. Update each page object to extend the new BasePage
2. Replace direct Mink dependencies with BrowserService calls
3. Register page objects as services
4. Ensure backward compatibility

#### 6.2.2 Implementation Plan

Migrate page objects in the following order:

1. HomePage
2. ProductPage
3. CartPage
4. CheckoutPage
5. PaymentPage
6. ConfirmationPage
7. Other specialized pages

---

## 7. Phase 5: Test Runner Migration

### 7.1 Service-Based Test Runner

**Objective:** Create a new test runner that uses services.

#### 7.1.1 Tasks

1. Create `TestRunnerService` interface and implementation
2. Migrate functionality from bin/run-tests.php
3. Add support for service container
4. Implement command-line interface

#### 7.1.2 Implementation Details

```php
// src/Service/TestRunner/TestRunnerServiceInterface.php
namespace App\Service\TestRunner;

interface TestRunnerServiceInterface
{
    public function runAllProducts(): int;
    public function runSingleProduct(string $productSlug, bool $dryRun = false): int;
    public function runWithTags(string $tags, bool $dryRun = false): int;
}

// src/Service/TestRunner/TestRunnerService.php
namespace App\Service\TestRunner;

use App\Service\AbstractService;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use Symfony\Component\Process\Process;

class TestRunnerService extends AbstractService implements TestRunnerServiceInterface
{
    private ConfigurationServiceInterface $configService;
    private TestDataServiceInterface $dataService;
    private string $projectRoot;
    private array $baseCmd;

    public function __construct(
        ConfigurationServiceInterface $configService,
        TestDataServiceInterface $dataService,
        string $projectRoot
    ) {
        $this->configService = $configService;
        $this->dataService = $dataService;
        $this->projectRoot = $projectRoot;

        // Initialize base command
        $behatBin = implode(DIRECTORY_SEPARATOR, ['vendor', 'bin', 'behat']);
        if (!file_exists($this->projectRoot . DIRECTORY_SEPARATOR . $behatBin)) {
            throw new \RuntimeException("Behat binary not found at: $behatBin");
        }

        $this->baseCmd = [$behatBin];
    }

    // Implementation of interface methods...
}
```

### 7.2 Command Line Interface

**Objective:** Create a new command-line interface for the test runner.

#### 7.2.1 Tasks

1. Create a new bin/run-tests-new.php script
2. Implement command-line argument parsing
3. Add support for service container initialization
4. Ensure backward compatibility

#### 7.2.2 Implementation Details

```php
#!/usr/bin/env php
<?php
// bin/run-tests-new.php

require __DIR__ . '/../vendor/autoload.php';

use App\Service\TestRunner\TestRunnerServiceInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

// Initialize container
$container = new ContainerBuilder();
$loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/../config'));
$loader->load('services.yml');
$container->compile();

// Parse command line options
$options = getopt('', ['brand::', 'env::', 'tags::', 'product::', 'all-products', 'dry-run']);

// Set environment variables
$brand = $options['brand'] ?? getenv('TEST_BRAND') ?? 'aeons';
$env = $options['env'] ?? getenv('TEST_ENV') ?? 'stage';
putenv("TEST_BRAND=$brand");
putenv("TEST_ENV=$env");

// Get test runner service
$testRunner = $container->get(TestRunnerServiceInterface::class);

// Run tests
try {
    $dryRun = isset($options['dry-run']);

    if (isset($options['all-products'])) {
        exit($testRunner->runAllProducts());
    } elseif (isset($options['tags'])) {
        exit($testRunner->runWithTags($options['tags'], $dryRun));
    } else {
        $product = $options['product'] ?? 'total_harmony';
        exit($testRunner->runSingleProduct($product, $dryRun));
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
```

---

## 8. Phase 6: Cleanup and Optimization

### 8.1 Remove Legacy Code

**Objective:** Remove deprecated code once migration is complete.

#### 8.1.1 Tasks

1. Remove SharedDataContext singleton
2. Remove direct instantiation of services
3. Clean up redundant code
4. Update documentation

### 8.2 Performance Optimization

**Objective:** Optimize the new architecture for performance.

#### 8.2.1 Tasks

1. Implement service caching
2. Add lazy loading for services
3. Optimize container compilation
4. Profile and optimize critical paths

### 8.3 Documentation Update

**Objective:** Update documentation to reflect the new architecture.

#### 8.3.1 Tasks

1. Create architecture diagrams
2. Update README and developer guides
3. Document service interfaces
4. Create examples for common tasks

### 8.4 Resolve Phase 5 Issues

**Objective:** Address issues encountered during the Phase 5 implementation.

#### 8.4.1 Service Container Configuration

1. Fix YAML structure in services.yml to properly support imports
2. Resolve parameter resolution issues (e.g., paths.base parameter)
3. Ensure all service definitions use the correct parameter names

#### 8.4.2 Missing Dependencies

1. Resolve missing class `FriendsOfBehat\PageObjectExtension\Context\PageObjectContext`
2. Ensure all required Behat extensions are properly installed and configured
3. Update composer.json to include all necessary dependencies

#### 8.4.3 Path Configuration

1. Standardize path configuration across all services
2. Create a consistent approach for locating configuration files
3. Implement proper path resolution for different environments

#### 8.4.4 Dependency Injection

1. Replace simplified service implementations with full implementations
2. Resolve circular dependencies in service definitions
3. Implement proper service factory methods where needed

#### 8.4.5 Integration with Behat

1. Ensure TestRunnerService properly integrates with Behat
2. Fix issues with Behat extension loading
3. Implement proper error handling for Behat process execution

---

## 9. Testing Strategy

### 9.1 Unit Testing

- Create unit tests for all service implementations
- Test each service in isolation with mocked dependencies
- Aim for high code coverage (>80%)

### 9.2 Integration Testing

- Test service interactions
- Verify container configuration
- Test context and page object integration

### 9.3 Functional Testing

- Run existing Behat tests with new architecture
- Compare results with previous architecture
- Verify all features still work as expected

### 9.4 Performance Testing

- Measure test execution time before and after migration
- Profile memory usage
- Identify and address bottlenecks

---

## 10. Rollback Plan

### 10.1 Rollback Triggers

- Critical functionality broken
- Significant performance degradation
- Incompatibility with existing tests

### 10.2 Rollback Process

1. Revert to previous architecture version
2. Restore original configuration
3. Run verification tests
4. Document issues for future resolution

---

## 11. Timeline and Resources

### 11.1 Estimated Timeline

- **Phase 1:** 1 hour
- **Phase 2:** 2 hours
- **Phase 3:** 2 hours
- **Phase 4:** 1 hour
- **Phase 5:** 1 hour
- **Phase 6:** 1 hour
- **Total:** 8 hours

### 11.2 Required Resources

- 1-2 AI developers familiar with the codebase
- Test environment for verification
- Documentation resources

---

## 12. Appendix: Code Examples

### 12.1 Service Registration Example

```yaml
# config/services.yml
services:
  # Configuration Service
  App\Service\Configuration\ConfigurationServiceInterface:
    alias: App\Service\Configuration\ConfigurationService

  App\Service\Configuration\ConfigurationService:
    arguments:
      $configDir: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
    public: true

  # Test Data Service
  App\Service\Data\TestDataServiceInterface:
    alias: App\Service\Data\TestDataService

  App\Service\Data\TestDataService:
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
    public: true

  # Shared State Service
  App\Service\State\SharedStateServiceInterface:
    alias: App\Service\State\SharedStateService

  App\Service\State\SharedStateService:
    public: true
    tags:
      - { name: kernel.event_subscriber }
```

### 12.2 Context Service Example

```yaml
# config/services.yml
services:
  # Contexts
  App\Context\FeatureContext:
    arguments:
      $container: '@service_container'
    public: true
    tags: [ 'context.service' ]

  App\Context\BrandContext:
    arguments:
      $container: '@service_container'
    public: true
    tags: [ 'context.service' ]

  App\Context\ProductContext:
    arguments:
      $container: '@service_container'
    public: true
    tags: [ 'context.service' ]
```

### 12.3 Page Object Service Example

```yaml
# config/services.yml
services:
  # Page Objects
  App\Page\HomePage:
    arguments:
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]

  App\Page\ProductPage:
    arguments:
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]
```

### 12.4 Backward Compatibility Layer

```php
// src/Compatibility/SharedDataContextAdapter.php
namespace App\Compatibility;

use App\Service\State\SharedStateServiceInterface;
use Behat\Behat\Context\Context;

/**
 * Adapter to provide backward compatibility with SharedDataContext
 */
class SharedDataContextAdapter implements Context
{
    private static ?self $instance = null;
    private SharedStateServiceInterface $stateService;

    public function __construct(SharedStateServiceInterface $stateService)
    {
        $this->stateService = $stateService;
        self::$instance = $this;
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            throw new \RuntimeException('SharedDataContextAdapter not initialized');
        }
        return self::$instance;
    }

    public function set(string $key, $value): void
    {
        $this->stateService->set($key, $value);
    }

    public function get(string $key)
    {
        return $this->stateService->get($key);
    }

    // Other methods for backward compatibility...
}
```
