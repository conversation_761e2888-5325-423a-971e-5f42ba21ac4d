# Critical Workflows Documentation

This document provides detailed information about the critical workflows tested in the Malaberg test automation framework. These workflows represent the key business processes of the Sylius-based e-commerce platform.

## 1. Product Purchase Flow

The product purchase flow is the core workflow of the e-commerce platform. It covers the entire process from a customer viewing a product to completing a purchase.

### Test Files

- `features/purchase.feature`

### Key Scenarios

1. **Basic Purchase Flow**
   - Customer views a product
   - Customer selects product options (quantity, flavor, etc.)
   - Customer adds the product to the cart
   - Customer proceeds to checkout
   - Customer enters shipping and billing information
   - Customer selects a payment method
   - Customer completes the order
   - Customer receives order confirmation

2. **Purchase with Different Quantities**
   - Verify that the price updates correctly when different quantities are selected
   - Verify that the cart total is calculated correctly based on the selected quantity

3. **Purchase with Different Payment Methods**
   - Verify that the order can be completed with different payment methods (credit card, PayPal, etc.)
   - Verify that the payment processing is handled correctly for each payment method

### Implementation Details

#### Page Objects Used

- `ProductPage`: Handles product selection and adding to cart
- `CartPage`: Handles cart management and proceeding to checkout
- `CheckoutPage`: Handles the checkout process
- `ConfirmationPage`: Handles order confirmation

#### Step Definition Classes Used

- `ProductContext`: Handles product-related steps
- `CartContext`: Handles cart-related steps
- `CheckoutContext`: Handles checkout-related steps
- `PaymentContext`: Handles payment-related steps

#### Example Test

```gherkin
Feature: Product Purchase
  As a customer
  I want to purchase a product
  So that I can receive it at my home

  Scenario: Add product to cart and complete purchase
    Given I am on the homepage
    When I navigate to the product page for "Total Harmony"
    And I select quantity "3"
    And I select purchase type "one_time"
    And I add the product to cart
    Then I should see 1 item in my cart
    And the cart total should be "$39.95"
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I choose the same address for billing
    And I select shipping method "standard"
    And I select payment method "credit_card"
    And I complete the order
    Then I should be on the order confirmation page
    And I should see the order confirmation message
```

## 2. Abandoned Cart Flow

The abandoned cart flow covers the scenario where a customer adds items to the cart but does not complete the purchase. The system sends abandoned cart email reminders to encourage the customer to return and complete the purchase.

### Test Files

- `features/abandoned_cart.feature`
- `features/abandoned_cart_extended.feature`

### Key Scenarios

1. **Basic Abandoned Cart Flow**
   - Customer adds products to the cart
   - Customer begins the checkout process
   - Customer abandons the checkout process
   - System sends abandoned cart email reminders
   - Customer returns to complete the purchase (optional)

2. **Abandoned Cart with Multiple Products**
   - Verify that the abandoned cart email includes all products in the cart
   - Verify that the customer can return and complete the purchase with all products

3. **Abandoned Cart Recovery**
   - Verify that the customer can recover their cart by clicking on the link in the abandoned cart email
   - Verify that the cart contents are preserved when the customer returns

### Implementation Details

#### Page Objects Used

- `ProductPage`: Handles product selection and adding to cart
- `CartPage`: Handles cart management and proceeding to checkout
- `CheckoutPage`: Handles the checkout process
- `EmailPage`: Handles email verification

#### Step Definition Classes Used

- `ProductContext`: Handles product-related steps
- `CartContext`: Handles cart-related steps
- `CheckoutContext`: Handles checkout-related steps
- `AbandonedCartContext`: Handles abandoned cart-related steps
- `EmailContext`: Handles email-related steps

#### Example Test

```gherkin
Feature: Abandoned Cart
  As a customer
  I want to receive a reminder if I abandon my cart
  So that I can return and complete my purchase

  Scenario: Abandon cart and receive reminder email
    Given I am on the homepage
    When I navigate to the product page for "Total Harmony"
    And I select quantity "3"
    And I select purchase type "one_time"
    And I add the product to cart
    And I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I abandon my cart
    Then I should receive an abandoned cart email within "24" hours
    When I click on the recovery link in the abandoned cart email
    Then I should be on the checkout page
    And my cart should contain the previously selected items
```

## 3. Sales Funnel Flow

The sales funnel flow covers the entire customer journey from landing on the homepage to completing a purchase, including upsell offers.

### Test Files

- `features/salesFunnel.feature`

### Key Scenarios

1. **Basic Sales Funnel Flow**
   - Customer lands on the homepage
   - Customer navigates to a product page
   - Customer adds the product to the cart
   - Customer proceeds to checkout
   - Customer completes the purchase
   - Customer is presented with upsell offers
   - Customer accepts or declines upsell offers
   - Customer receives order confirmation

2. **Sales Funnel with Upsell Acceptance**
   - Verify that the order is updated correctly when the customer accepts upsell offers
   - Verify that the upsell products are added to the order

3. **Sales Funnel with Upsell Decline**
   - Verify that the order is processed correctly when the customer declines upsell offers
   - Verify that the customer still receives order confirmation

### Implementation Details

#### Page Objects Used

- `HomePage`: Handles homepage navigation
- `ProductPage`: Handles product selection and adding to cart
- `CartPage`: Handles cart management and proceeding to checkout
- `CheckoutPage`: Handles the checkout process
- `UpsellPage`: Handles upsell offers
- `ConfirmationPage`: Handles order confirmation

#### Step Definition Classes Used

- `ProductContext`: Handles product-related steps
- `CartContext`: Handles cart-related steps
- `CheckoutContext`: Handles checkout-related steps
- `UpsellContext`: Handles upsell-related steps
- `SalesFunnelContext`: Handles sales funnel-related steps

#### Example Test

```gherkin
Feature: Sales Funnel
  As a customer
  I want to be guided through the purchase process
  So that I can find and buy products that meet my needs

  Scenario: Complete sales funnel with upsell acceptance
    Given I am on the homepage
    When I navigate to the product page for "Total Harmony"
    And I select quantity "3"
    And I select purchase type "one_time"
    And I add the product to cart
    And I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I choose the same address for billing
    And I select shipping method "standard"
    And I select payment method "credit_card"
    And I complete the order
    Then I should be on the upsell page
    When I accept the upsell offer
    Then I should be on the order confirmation page
    And I should see the order confirmation message
    And the order should include the upsell products
```

## 4. Checkout Flow

The checkout flow covers the process of a customer proceeding from the cart to completing an order.

### Test Files

- `features/ecommerce_checkout_flows.feature`

### Key Scenarios

1. **Basic Checkout Flow**
   - Customer proceeds to checkout from the cart
   - Customer enters shipping information
   - Customer enters billing information
   - Customer selects a shipping method
   - Customer selects a payment method
   - Customer completes the order
   - Customer receives order confirmation

2. **Checkout with Different Shipping Methods**
   - Verify that the order can be completed with different shipping methods
   - Verify that the shipping cost is calculated correctly for each shipping method

3. **Checkout with Different Payment Methods**
   - Verify that the order can be completed with different payment methods
   - Verify that the payment processing is handled correctly for each payment method

### Implementation Details

#### Page Objects Used

- `CartPage`: Handles cart management and proceeding to checkout
- `CheckoutPage`: Handles the checkout process
- `ConfirmationPage`: Handles order confirmation

#### Step Definition Classes Used

- `CartContext`: Handles cart-related steps
- `CheckoutContext`: Handles checkout-related steps
- `PaymentContext`: Handles payment-related steps

#### Example Test

```gherkin
Feature: Checkout Flow
  As a customer
  I want to complete the checkout process
  So that I can receive my purchased products

  Scenario: Complete checkout with standard shipping and credit card payment
    Given I have items in my cart
    When I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I choose the same address for billing
    And I select shipping method "standard"
    And I select payment method "credit_card"
    And I complete the order
    Then I should be on the order confirmation page
    And I should see the order confirmation message
```

## 5. Subscription Reorder Flow

The subscription reorder flow covers the process of a subscription being automatically reordered based on the selected frequency.

### Test Files

- `features/subscription_reorder.feature`

### Key Scenarios

1. **Basic Subscription Reorder Flow**
   - Customer sets up a subscription
   - System initiates a reorder based on the subscription frequency
   - System processes the payment using the stored payment method
   - System creates a new order
   - System sends order confirmation to the customer

2. **Subscription Reorder with Payment Failure**
   - Verify that the system handles payment failures correctly
   - Verify that the customer is notified of the payment failure
   - Verify that the customer can update the payment method and retry

3. **Subscription Reorder with Address Change**
   - Verify that the system uses the updated shipping address for reorders
   - Verify that the customer can update the shipping address for future reorders

### Implementation Details

#### Page Objects Used

- `ProductPage`: Handles product selection and subscription setup
- `AccountPage`: Handles account management and subscription settings
- `EmailPage`: Handles email verification

#### Step Definition Classes Used

- `ProductContext`: Handles product-related steps
- `SubscriptionContext`: Handles subscription-related steps
- `EmailContext`: Handles email-related steps

#### Example Test

```gherkin
Feature: Subscription Reorder
  As a subscription customer
  I want my orders to be automatically reordered
  So that I never run out of my favorite products

  Scenario: Automatic subscription reorder
    Given I have an active subscription for "Total Harmony"
    When the subscription reorder date is reached
    Then the system should create a new order
    And the system should process the payment using my stored payment method
    And I should receive an order confirmation email
    And the next reorder date should be updated based on my subscription frequency
```

## Best Practices for Testing Critical Workflows

1. **End-to-End Testing**: Test the entire workflow from start to finish to ensure all components work together correctly.
2. **Data Validation**: Verify that data is correctly processed and stored at each step of the workflow.
3. **Error Handling**: Test error scenarios to ensure the system handles errors gracefully.
4. **State Management**: Ensure that state is correctly maintained across all steps of the workflow.
5. **Realistic Test Data**: Use realistic test data that reflects actual user behavior.
6. **Performance Testing**: Test the performance of critical workflows under load.
7. **Cross-Browser Testing**: Test critical workflows across different browsers and devices.
8. **Regression Testing**: Run tests regularly to catch regressions early.
