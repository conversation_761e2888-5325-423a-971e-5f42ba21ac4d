<?php

namespace App\Service\Configuration;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Yaml\Yaml;

/**
 * Service for managing brand and environment configurations
 */
class ConfigurationService extends AbstractService implements ConfigurationServiceInterface
{
    private string $configDir;
    private string $currentBrand;
    private string $currentEnvironment;
    private array $brandConfigs = [];
    private array $environmentConfigs = [];

    /**
     * Constructor
     *
     * @param string $configDir Configuration directory path
     * @param string|null $brand Brand identifier (defaults to TEST_BRAND env var or 'aeons')
     * @param string|null $environment Environment identifier (defaults to TEST_ENV env var or 'stage')
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        string           $configDir,
        ?string          $brand = null,
        ?string          $environment = null,
        ?LoggerInterface $logger = null
    )
    {
        parent::__construct($logger);

        $this->configDir = $configDir;
        $this->currentBrand = $brand ?? getenv('TEST_BRAND') ?? 'aeons';
        $this->currentEnvironment = $environment ?? getenv('TEST_ENV') ?? 'stage';

        $this->logInfo(sprintf(
            "Initializing ConfigurationService with brand '%s' and environment '%s'",
            $this->currentBrand,
            $this->currentEnvironment
        ));

        $this->loadBrandConfig($this->currentBrand);
        $this->loadEnvironmentConfig($this->currentBrand, $this->currentEnvironment);
    }

    /**
     * Load brand configuration from file
     *
     * @param string $brand Brand identifier
     * @throws RuntimeException When brand configuration cannot be loaded
     */
    private function loadBrandConfig(string $brand): void
    {
        $this->logInfo(sprintf("Loading brand configuration for '%s'", $brand));

        // Fix for environment variables not being set correctly
        if ($brand === 'env_dc57ccc979c4a77c_TEST_BRAND_b1fb30bf6f45929cf5d0eb34e7d33e25') {
            $brand = 'aeons';
            $this->currentBrand = 'aeons';
            $this->logInfo("Defaulting to 'aeons' brand due to environment variable issue");
        }

        $pathOptions = [
            sprintf('%s/brands/%s/config.yml', $this->configDir, $brand),
            sprintf('%s/brands/%s/brand.yml', $this->configDir, $brand),
            sprintf('%s/brands/%s.yml', $this->configDir, $brand),
            // Add environment-specific config files
            sprintf('%s/brands/%s/%s.yml', $this->configDir, $brand, $this->currentEnvironment),
        ];

        $loaded = false;
        foreach ($pathOptions as $path) {
            if (file_exists($path)) {
                $this->logInfo(sprintf("Loading brand config from path: %s", $path));

                try {
                    $config = Yaml::parseFile($path);
                    $this->validateBrandConfig($config);
                    $this->brandConfigs[$brand] = $config['brand'];
                    $loaded = true;
                    break;
                } catch (\Exception $e) {
                    $this->logError(
                        sprintf("Error parsing brand config file: %s", $path),
                        $e
                    );
                    throw new RuntimeException(
                        sprintf('Error parsing brand configuration file: %s', $path),
                        0,
                        $e
                    );
                }
            }
        }

        if (!$loaded) {
            $this->logWarning(sprintf(
                "Brand configuration file not found. Tried: %s. Creating default configuration.",
                implode(', ', $pathOptions)
            ));

            // Create a default brand configuration
            $this->brandConfigs[$brand] = [
                'name' => 'Aeons',
                'code' => 'aeons',
                'url' => 'https://aeonstest.info',
                'currency' => 'GBP',
                'shipping_zones' => ['UK', 'US', 'EU'],
                'payment_methods' => ['stripe'],
                'product_types' => ['jars', 'sprays', 'bottles', 'boxes'],
                'features' => ['subscription', 'one_time_purchase', 'abandoned_cart', 'product_reviews'],
                'mailtrap' => [
                    'inbox_id' => '3136083'
                ]
            ];

            // Save the default configuration for future use
            $configDir = sprintf('%s/brands/%s', $this->configDir, $brand);
            if (!is_dir($configDir)) {
                mkdir($configDir, 0777, true);
            }

            $configFile = sprintf('%s/%s.yml', $configDir, $this->currentEnvironment);
            $configContent = Yaml::dump(['brand' => $this->brandConfigs[$brand]], 4);
            file_put_contents($configFile, $configContent);

            $this->logInfo(sprintf("Created default brand configuration at: %s", $configFile));
        }
    }

    /**
     * Validate brand configuration structure
     *
     * @param array $config Configuration to validate
     * @throws RuntimeException When validation fails
     */
    private function validateBrandConfig(array $config): void
    {
        if (!isset($config['brand'])) {
            throw new RuntimeException('Invalid brand configuration: missing "brand" key');
        }

        $requiredKeys = ['name', 'code'];
        foreach ($requiredKeys as $key) {
            if (!isset($config['brand'][$key])) {
                throw new RuntimeException(
                    sprintf('Invalid brand configuration: missing required key "%s"', $key)
                );
            }
        }
    }

    /**
     * Load environment configuration from file
     *
     * @param string $brand Brand identifier
     * @param string $env Environment identifier
     * @throws RuntimeException When environment configuration cannot be loaded
     */
    private function loadEnvironmentConfig(string $brand, string $env): void
    {
        $this->logInfo(sprintf("Loading environment configuration for '%s' in '%s'", $brand, $env));

        // Fix for environment variables not being set correctly
        if ($env === 'env_dc57ccc979c4a77c_TEST_ENV_b1fb30bf6f45929cf5d0eb34e7d33e25') {
            $env = 'stage';
            $this->currentEnvironment = 'stage';
            $this->logInfo("Defaulting to 'stage' environment due to environment variable issue");
        }

        $pathOptions = [
            sprintf('%s/brands/%s/environments/%s.yml', $this->configDir, $brand, $env),
            sprintf('%s/environments/%s/%s.yml', $this->configDir, $brand, $env),
            sprintf('%s/environments/%s.yml', $this->configDir, $env),
            // Add direct environment file in brand directory
            sprintf('%s/brands/%s/%s.yml', $this->configDir, $brand, $env),
        ];

        $loaded = false;
        foreach ($pathOptions as $path) {
            if (file_exists($path)) {
                $this->logInfo(sprintf("Loading environment config from path: %s", $path));

                try {
                    $config = Yaml::parseFile($path);

                    // Handle different file formats
                    if (isset($config['environments'][$env])) {
                        // Format with environments key
                        $this->validateEnvironmentConfig($config['environments'][$env]);
                        $this->environmentConfigs[$brand][$env] = $config['environments'][$env];
                    } elseif (isset($config['brand'])) {
                        // Format with brand key
                        $this->validateEnvironmentConfig($config['brand']);
                        $this->environmentConfigs[$brand][$env] = $config['brand'];
                    } else {
                        // Direct format
                        $this->validateEnvironmentConfig($config);
                        $this->environmentConfigs[$brand][$env] = $config;
                    }

                    $loaded = true;
                    break;
                } catch (\Exception $e) {
                    $this->logError(
                        sprintf("Error parsing environment config file: %s", $path),
                        $e
                    );
                    throw new RuntimeException(
                        sprintf('Error parsing environment configuration file: %s', $path),
                        0,
                        $e
                    );
                }
            }
        }

        if (!$loaded) {
            $this->logWarning(sprintf(
                "Environment configuration file not found. Tried: %s. Creating default configuration.",
                implode(', ', $pathOptions)
            ));

            // Create a default environment configuration
            $this->environmentConfigs[$brand][$env] = [
                'base_url' => 'https://aeonstest.info',
                'api_url' => 'https://api.aeonstest.info',
                'admin_url' => 'https://admin.aeonstest.info',
                'timeout' => 30,
                'retry_attempts' => 3,
                'debug' => true,
                'features' => [
                    'subscription' => true,
                    'one_time_purchase' => true,
                    'abandoned_cart' => true,
                    'product_reviews' => true
                ]
            ];

            // Save the default configuration for future use
            $configDir = sprintf('%s/brands/%s', $this->configDir, $brand);
            if (!is_dir($configDir)) {
                mkdir($configDir, 0777, true);
            }

            $configFile = sprintf('%s/%s.yml', $configDir, $env);
            $configContent = Yaml::dump(['environment' => $this->environmentConfigs[$brand][$env]], 4);
            file_put_contents($configFile, $configContent);

            $this->logInfo(sprintf("Created default environment configuration at: %s", $configFile));
        }
    }

    /**
     * Validate environment configuration structure
     *
     * @param array $config Configuration to validate
     * @throws RuntimeException When validation fails
     */
    private function validateEnvironmentConfig(array $config): void
    {
        $requiredKeys = ['url'];
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key])) {
                throw new RuntimeException(
                    sprintf('Invalid environment configuration: missing required key "%s"', $key)
                );
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getBrandConfig(string $key)
    {
        if (!isset($this->brandConfigs[$this->currentBrand][$key])) {
            throw new RuntimeException(
                sprintf('Brand configuration not found: %s', $key)
            );
        }

        return $this->brandConfigs[$this->currentBrand][$key];
    }

    /**
     * {@inheritdoc}
     */
    public function getEnvironmentConfig(string $key)
    {
        // Special handling for base_url
        if ($key === 'base_url') {
            return $this->environmentConfigs[$this->currentBrand][$this->currentEnvironment]['url'] ?? null;
        }

        if (!isset($this->environmentConfigs[$this->currentBrand][$this->currentEnvironment][$key])) {
            throw new RuntimeException(
                sprintf('Environment configuration not found: %s', $key)
            );
        }

        return $this->environmentConfigs[$this->currentBrand][$this->currentEnvironment][$key];
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentBrand(): string
    {
        return $this->currentBrand;
    }

    /**
     * {@inheritdoc}
     */
    public function getCurrentEnvironment(): string
    {
        return $this->currentEnvironment;
    }

    /**
     * {@inheritdoc}
     */
    public function setBrand(string $brand): void
    {
        $this->logInfo(sprintf("Setting brand to '%s'", $brand));

        if (!isset($this->brandConfigs[$brand])) {
            $this->loadBrandConfig($brand);
        }

        $this->currentBrand = $brand;

        // Ensure environment config is loaded for the new brand
        if (!isset($this->environmentConfigs[$brand][$this->currentEnvironment])) {
            $this->loadEnvironmentConfig($brand, $this->currentEnvironment);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function setEnvironment(string $environment): void
    {
        $this->logInfo(sprintf("Setting environment to '%s'", $environment));

        if (!isset($this->environmentConfigs[$this->currentBrand][$environment])) {
            $this->loadEnvironmentConfig($this->currentBrand, $environment);
        }

        $this->currentEnvironment = $environment;
    }
}
