# Service-Oriented Test Architecture Documentation for AI Agents

## Table of Contents

1. [Architecture Overview](#1-architecture-overview)
2. [Core Architectural Principles](#2-core-architectural-principles)
3. [Component Structure](#3-component-structure)
4. [Service Layer](#4-service-layer)
5. [Context Layer](#5-context-layer)
6. [Page Object Layer](#6-page-object-layer)
7. [Dependency Management](#7-dependency-management)
8. [State Management](#8-state-management)
9. [Common Patterns and Idioms](#9-common-patterns-and-idioms)
10. [Migration from Legacy Architecture](#10-migration-from-legacy-architecture)
11. [AI Agent Guidelines](#11-ai-agent-guidelines)

---

## 1. Architecture Overview

The E-commerce Test Framework uses a service-oriented architecture (SOA) designed to maximize maintainability,
testability, and AI comprehension. This architecture addresses several critical issues in the previous implementation:

- **Excessive context class size and complexity**
- **Difficulties with refactoring**
- **Unclear and duplicated step definitions**
- **Missing necessary step definitions**

The architecture consists of three primary layers:

1. **Service Layer**: Core services that encapsulate business logic and technical operations
2. **Context Layer**: Behat contexts that map Gherkin steps to service calls
3. **Page Object Layer**: Page objects that represent UI elements and interactions

These layers interact through explicit dependencies and clear interfaces, creating a system that is both flexible and
comprehensible.

![Architecture Diagram](../diagrams/service_oriented_architecture.png)

---

## 2. Core Architectural Principles

### 2.1 Separation of Concerns

Each component has a single, well-defined responsibility:

- **Services**: Encapsulate business logic and technical operations
- **Contexts**: Map Gherkin steps to service calls
- **Page Objects**: Represent UI elements and interactions

### 2.2 Explicit Dependencies

Dependencies are explicitly declared through constructor injection:

```php
public function __construct(
    ContainerInterface $container,
    ConfigurationServiceInterface $configService,
    SharedStateServiceInterface $stateService
) {
    parent::__construct($container);
    $this->configService = $configService;
    $this->stateService = $stateService;
}
```

### 2.3 Interface-Based Design

Components depend on interfaces, not concrete implementations:

```php
private ConfigurationServiceInterface $configService;
private SharedStateServiceInterface $stateService;
```

### 2.4 Composition Over Inheritance

Functionality is composed from multiple services rather than inherited from base classes:

```php
// Prefer this:
$productPage = $this->pageFactory->getPage('ProductPage');
$productPage->addToCart();

// Over this:
$this->productPageContext->addToCart();
```

### 2.5 Single Responsibility Principle

Each class has a single reason to change:

- **ConfigurationService**: Manages configuration settings
- **BrowserService**: Handles browser interactions
- **ProductContext**: Implements product-related steps

---

## 3. Component Structure

### 3.1 Directory Structure

```
project_root/
├── config/
│   ├── services.yml          # Main service configuration
│   ├── services/             # Service-specific configurations
│   │   ├── core.yml          # Core service definitions
│   │   ├── contexts.yml      # Context service definitions
│   │   ├── pages.yml         # Page object definitions
│   │   └── compatibility.yml # Legacy compatibility layer
│   ├── brands/               # Brand-specific configurations
│   └── environments/         # Environment-specific configurations
├── src/
│   ├── Service/              # Core services
│   │   ├── AbstractService.php  # Base service class
│   │   ├── Browser/          # Browser interaction services
│   │   ├── Configuration/    # Configuration services
│   │   ├── Data/             # Test data services
│   │   ├── Page/             # Page factory services
│   │   ├── State/            # State management services
│   │   └── Validation/       # Validation services
│   ├── Context/              # Behat contexts
│   │   ├── Base/             # Base context classes
│   │   │   ├── BaseContext.php
│   │   │   └── ServiceAwareContext.php
│   │   ├── BrandContext.php
│   │   ├── CartContext.php
│   │   └── ...
│   ├── Page/                 # Page objects
│   │   ├── Base/             # Base page classes
│   │   │   ├── BasePage.php
│   │   │   └── BasePageInterface.php
│   │   ├── Element/          # Page elements
│   │   ├── HomePage.php
│   │   ├── ProductPage.php
│   │   └── ...
│   └── Compatibility/        # Legacy compatibility adapters
├── features/                 # Behat feature files
│   ├── bootstrap/            # Behat bootstrap
│   │   └── FeatureContext.php
│   └── *.feature             # Feature files
└── tests/                    # Unit tests
    ├── Service/              # Service tests
    ├── Context/              # Context tests
    └── Page/                 # Page object tests
```

### 3.2 Naming Conventions

- **Services**: `{Domain}{Action}Service` (e.g., `ConfigurationService`)
- **Interfaces**: `{ServiceName}Interface` (e.g., `ConfigurationServiceInterface`)
- **Contexts**: `{Domain}Context` (e.g., `ProductContext`)
- **Page Objects**: `{Page}Page` (e.g., `ProductPage`)

---

## 4. Service Layer

The service layer is the foundation of the architecture, providing core functionality that other components use.

### 4.1 Service Types

#### 4.1.1 Configuration Service

Manages configuration settings for brands, environments, and test execution.

```php
interface ConfigurationServiceInterface
{
    public function getBrandConfig(string $key);
    public function getEnvironmentConfig(string $key);
    public function getCurrentBrand(): string;
    public function getCurrentEnvironment(): string;
    public function setBrand(string $brand): void;
    public function setEnvironment(string $environment): void;
}
```

#### 4.1.2 Browser Service

Handles browser interactions, abstracting away the details of Selenium/WebDriver.

```php
interface BrowserServiceInterface
{
    public function getSession(): Session;
    public function visit(string $url): void;
    public function takeScreenshot(string $name = null): string;
    public function waitForElement(string $selector, int $timeout = 30): void;
    public function executeScript(string $script);
}
```

#### 4.1.3 Shared State Service

Manages state sharing between contexts, replacing the singleton pattern with a service.

```php
interface SharedStateServiceInterface
{
    public function set(string $key, $value, string $scope = 'scenario'): void;
    public function get(string $key, string $scope = 'scenario');
    public function has(string $key, string $scope = 'scenario'): bool;
    public function getAll(string $scope = 'scenario'): array;
    public function reset(string $scope = 'scenario'): void;
}
```

#### 4.1.4 Test Data Service

Manages test data loading and access.

```php
interface TestDataServiceInterface
{
    public function loadTestData(string $brand, string $type, ?string $key = null): array;
    public function registerData(string $key, array $data): void;
    public function getData(string $key);
    public function hasData(string $key): bool;
}
```

#### 4.1.5 Validation Service

Validates test data and test results.

```php
interface ValidationServiceInterface
{
    public function validateProductData(array $data): void;
    public function validateUserData(array $data): void;
    public function validateShippingData(array $data): void;
    public function validateSchema(array $data, string $schema): void;
}
```

#### 4.1.6 Page Factory Service

Creates and manages page objects.

```php
interface PageFactoryInterface
{
    public function createPage(string $pageClass, array $parameters = []): BasePageInterface;
    public function getPage(string $pageName, array $parameters = []): BasePageInterface;
    public function hasPage(string $pageName): bool;
}
```

### 4.2 Service Registration

Services are registered in the Symfony Dependency Injection Container through YAML configuration:

```yaml
# config/services/core.yml
services:
  # Configuration Service
  App\Service\Configuration\ConfigurationServiceInterface:
    alias: App\Service\Configuration\ConfigurationService

  App\Service\Configuration\ConfigurationService:
    arguments:
      $configDir: '%app.config_dir%'
      $brand: '%env(TEST_BRAND)%'
      $environment: '%env(TEST_ENV)%'
    public: true

  # Test Data Service
  App\Service\Data\TestDataServiceInterface:
    alias: App\Service\Data\TestDataService

  App\Service\Data\TestDataService:
    arguments:
      $fixturesDir: '%app.fixtures_dir%'
    public: true
```

### 4.3 Service Implementation Pattern

Services follow a consistent implementation pattern:

1. Define an interface in `src/Service/{Domain}/{ServiceName}Interface.php`
2. Implement the interface in `src/Service/{Domain}/{ServiceName}.php`
3. Extend `AbstractService` for common functionality
4. Register the service in `config/services/core.yml`

Example:

```php
namespace App\Service\Configuration;

use App\Service\AbstractService;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Yaml\Yaml;

class ConfigurationService extends AbstractService implements ConfigurationServiceInterface
{
    private string $configDir;
    private string $currentBrand;
    private string $currentEnvironment;
    private array $brandConfigs = [];
    private array $environmentConfigs = [];
    
    public function __construct(
        string $configDir,
        ?string $brand = null,
        ?string $environment = null,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->configDir = $configDir;
        $this->currentBrand = $brand ?? getenv('TEST_BRAND') ?? 'aeons';
        $this->currentEnvironment = $environment ?? getenv('TEST_ENV') ?? 'stage';
        
        $this->loadBrandConfig($this->currentBrand);
        $this->loadEnvironmentConfig($this->currentBrand, $this->currentEnvironment);
    }
    
    // Implementation of interface methods...
}
```

---

## 5. Context Layer

The context layer maps Gherkin steps to service calls, providing a bridge between the feature files and the service
layer.

### 5.1 Context Types

#### 5.1.1 Base Contexts

- **ServiceAwareContext**: Provides access to the service container
- **BaseContext**: Extends ServiceAwareContext with helper methods for accessing specific services

```php
abstract class BaseContext extends ServiceAwareContext implements Context
{
    protected function getConfigService(): ConfigurationServiceInterface
    {
        return $this->getService(ConfigurationServiceInterface::class);
    }
    
    protected function getTestDataService(): TestDataServiceInterface
    {
        return $this->getService(TestDataServiceInterface::class);
    }
    
    // Other helper methods...
}
```

#### 5.1.2 Domain-Specific Contexts

- **BrandContext**: Brand-specific steps
- **ProductContext**: Product-related steps
- **CartContext**: Shopping cart steps
- **CheckoutContext**: Checkout process steps
- **PaymentContext**: Payment processing steps

### 5.2 Context Implementation Pattern

Contexts follow a consistent implementation pattern:

1. Extend `BaseContext`
2. Inject required services through constructor
3. Implement step definitions that use services
4. Register the context in `config/services/contexts.yml`

Example:

```php
namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\State\SharedStateServiceInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class BrandContext extends BaseContext
{
    private ConfigurationServiceInterface $configService;
    private SharedStateServiceInterface $stateService;
    
    public function __construct(
        ContainerInterface $container,
        ConfigurationServiceInterface $configService,
        SharedStateServiceInterface $stateService
    ) {
        parent::__construct($container);
        $this->configService = $configService;
        $this->stateService = $stateService;
    }
    
    /**
     * @Given I am using the :brand brand
     */
    public function iAmUsingTheBrand(string $brand): void
    {
        $this->configService->setBrand($brand);
        $this->stateService->set('brand.current', $brand);
    }
    
    // Other step definitions...
}
```

### 5.3 Context Registration

Contexts are registered in the Symfony Dependency Injection Container through YAML configuration:

```yaml
# config/services/contexts.yml
services:
  # Contexts
  App\Context\:
    resource: '../../src/Context/*'
    exclude: '../../src/Context/{Base}/*'
    public: true
    arguments:
      $container: '@service_container'
    tags: [ 'context.service' ]
```

### 5.4 Step Definition Best Practices

1. **Keep step definitions small and focused**
2. **Delegate implementation to services and page objects**
3. **Use shared state for data sharing between steps**
4. **Handle exceptions and provide meaningful error messages**
5. **Log important actions for debugging**

Example of a well-structured step definition:

```php
/**
 * @When I add the product to cart
 */
public function iAddTheProductToCart(): void
{
    try {
        // Use page object to perform the action
        $productPage = $this->pageFactory->getPage('ProductPage');
        $productPage->addToCart();
        
        // Get current product data from shared state
        $productSlug = $this->stateService->get('product.current');
        $flavor = $this->stateService->get('product.selected_flavor');
        $quantity = $this->stateService->get('product.selected_quantity', 'scenario') ?? 1;
        $purchaseType = $this->stateService->get('product.purchase_type', 'scenario') ?? 'one_time';
        
        // Store cart item in shared state
        $cartItem = [
            'product' => $productSlug,
            'flavor' => $flavor,
            'quantity' => $quantity,
            'purchase_type' => $purchaseType
        ];
        
        // Update cart in shared state
        $cartItems = $this->stateService->get('cart.items', 'scenario') ?? [];
        $cartItems[] = $cartItem;
        $this->stateService->set('cart.items', $cartItems);
        $this->stateService->set('cart.last_added', $cartItem);
        
        $this->logInfo("Added product to cart: " . json_encode($cartItem));
    } catch (\Throwable $e) {
        $this->logError("Failed to add product to cart", $e);
        throw $e;
    }
}
```

---

## 6. Page Object Layer

The page object layer represents UI elements and interactions, providing a high-level API for browser automation.

### 6.1 Page Object Types

#### 6.1.1 Base Page Objects

- **BasePageInterface**: Defines the contract for all page objects
- **BasePage**: Implements BasePageInterface with common functionality

```php
interface BasePageInterface
{
    public function open(array $urlParameters = []): void;
    public function getUrl(array $urlParameters = []): string;
    public function isOpen(): bool;
    public function waitForPageToLoad(int $timeout = 30): void;
}

abstract class BasePage implements BasePageInterface
{
    protected BrowserServiceInterface $browserService;
    protected string $path = '/';
    protected string $baseUrl;
    
    public function __construct(BrowserServiceInterface $browserService, string $baseUrl = null)
    {
        $this->browserService = $browserService;
        $this->baseUrl = $baseUrl ?? getenv('TEST_BASE_URL');
    }
    
    // Implementation of interface methods...
}
```

#### 6.1.2 Domain-Specific Page Objects

- **HomePage**: Main landing page
- **ProductPage**: Product detail page
- **CartPage**: Shopping cart page
- **CheckoutPage**: Checkout process page
- **ConfirmationPage**: Order confirmation page

### 6.2 Page Object Implementation Pattern

Page objects follow a consistent implementation pattern:

1. Extend `BasePage`
2. Define the page path
3. Implement page-specific methods
4. Register the page object in `config/services/pages.yml`

Example:

```php
namespace App\Page;

use App\Page\Base\BasePage;
use App\Service\Browser\BrowserServiceInterface;
use App\Service\Data\TestDataServiceInterface;

class ProductPage extends BasePage
{
    protected string $path = '/product/{slug}';
    private TestDataServiceInterface $dataService;
    
    public function __construct(
        BrowserServiceInterface $browserService,
        TestDataServiceInterface $dataService,
        string $baseUrl = null
    ) {
        parent::__construct($browserService, $baseUrl);
        $this->dataService = $dataService;
    }
    
    public function addToCart(): void
    {
        $this->browserService->waitForElement('.add-to-cart-button');
        $this->browserService->executeScript("document.querySelector('.add-to-cart-button').click()");
        $this->browserService->waitForElement('.cart-confirmation');
    }
    
    // Other page-specific methods...
}
```

### 6.3 Page Object Registration

Page objects are registered in the Symfony Dependency Injection Container through YAML configuration:

```yaml
# config/services/pages.yml
services:
  # Page Objects
  App\Page\:
    resource: '../../src/Page/*'
    exclude: '../../src/Page/{Base,Element}/*'
    public: true
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    tags: [ 'page.service' ]
  
  # Specific Page Objects with Additional Dependencies
  App\Page\ProductPage:
    arguments:
      $browserService: '@App\Service\Browser\BrowserServiceInterface'
      $dataService: '@App\Service\Data\TestDataServiceInterface'
      $baseUrl: '%env(TEST_BASE_URL)%'
    public: true
    tags: [ 'page.service' ]
```

---

## 7. Dependency Management

The architecture uses Symfony's Dependency Injection Container for dependency management, providing a consistent way to
define, configure, and retrieve services.

### 7.1 Service Container Configuration

The service container is configured through YAML files:

```yaml
# config/services.yml
parameters:
  # Global parameters
  app.project_root: '%paths.base%'
  app.config_dir: '%app.project_root%/config'
  app.fixtures_dir: '%app.project_root%/features/fixtures'

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false
    bind:
      $projectRoot: '%app.project_root%'
      $configDir: '%app.config_dir%'
      $fixturesDir: '%app.fixtures_dir%'

  # Import service definitions
  _imports:
    - { resource: 'services/core.yml' }
    - { resource: 'services/compatibility.yml' }
    - { resource: 'services/contexts.yml' }
    - { resource: 'services/pages.yml' }
```

### 7.2 Dependency Injection

Dependencies are injected through constructor injection:

```php
public function __construct(
    ContainerInterface $container,
    PageFactoryInterface $pageFactory,
    TestDataServiceInterface $dataService,
    SharedStateServiceInterface $stateService
) {
    parent::__construct($container);
    $this->pageFactory = $pageFactory;
    $this->dataService = $dataService;
    $this->stateService = $stateService;
}
```

### 7.3 Service Retrieval

Services can be retrieved from the container in two ways:

1. **Direct injection**: Preferred for specific, known dependencies
2. **Container access**: Used for dynamic service retrieval

```php
// Direct injection (preferred)
public function __construct(ConfigurationServiceInterface $configService)
{
    $this->configService = $configService;
}

// Container access (for dynamic service retrieval)
protected function getService(string $id)
{
    return $this->container->get($id);
}
```

---

## 8. State Management

The architecture uses a dedicated service for state management, replacing the singleton pattern with a more testable and
maintainable approach.

### 8.1 Shared State Service

The `SharedStateService` manages state sharing between contexts:

```php
class SharedStateService extends AbstractService implements SharedStateServiceInterface, EventSubscriberInterface
{
    private array $scenarioState = [];
    private array $featureState = [];
    private array $globalState = [];
    
    public static function getSubscribedEvents()
    {
        return [
            ScenarioTested::AFTER => ['resetScenarioState'],
            FeatureTested::AFTER => ['resetFeatureState'],
        ];
    }
    
    public function set(string $key, $value, string $scope = 'scenario'): void
    {
        // Implementation...
    }
    
    public function get(string $key, string $scope = 'scenario')
    {
        // Implementation...
    }
    
    // Other methods...
}
```

### 8.2 State Scopes

The shared state service supports three scopes:

1. **Scenario**: State is reset after each scenario
2. **Feature**: State is reset after each feature
3. **Global**: State persists for the entire test run

### 8.3 State Usage Pattern

```php
// Setting state
$this->stateService->set('product.current', $productSlug);
$this->stateService->set('cart.items', $cartItems, 'feature');

// Getting state
$productSlug = $this->stateService->get('product.current');
$cartItems = $this->stateService->get('cart.items', 'feature') ?? [];

// Checking if state exists
if ($this->stateService->has('product.current')) {
    // Do something...
}
```

---

## 9. Common Patterns and Idioms

### 9.1 Try-Catch-Log Pattern

```php
try {
    // Do something...
    $this->logInfo("Operation succeeded");
} catch (\Throwable $e) {
    $this->logError("Operation failed", $e);
    throw $e;
}
```

### 9.2 Service Access Pattern

```php
protected function getConfigService(): ConfigurationServiceInterface
{
    return $this->getService(ConfigurationServiceInterface::class);
}
```

### 9.3 Page Object Usage Pattern

```php
$productPage = $this->pageFactory->getPage('ProductPage');
$productPage->open(['slug' => $productSlug]);
$productPage->addToCart();
```

### 9.4 State Management Pattern

```php
// Store data
$this->stateService->set('cart.items', $cartItems);

// Retrieve data with fallback
$cartItems = $this->stateService->get('cart.items') ?? [];
```

---

## 10. Migration from Legacy Architecture

The migration from the legacy architecture to the service-oriented architecture is being done in phases:

### 10.1 Migration Phases

1. **Foundation Setup**: Service container, interfaces, base classes
2. **Core Services Implementation**: Configuration, test data, shared state, validation, browser
3. **Context Migration**: Base context, feature context, brand context, other contexts
4. **Page Object Migration**: Base page, page factory, page objects
5. **Test Runner Migration**: Service-based test runner, command-line interface
6. **Cleanup and Optimization**: Remove legacy code, optimize performance

### 10.2 Backward Compatibility

During migration, backward compatibility is maintained through adapters and compatibility layers:

```php
// Legacy service aliases
Features\Bootstrap\Core\ConfigurationManager:
  alias: App\Service\Configuration\ConfigurationService
  public: true

// SharedDataContext adapter
App\Compatibility\SharedDataContextAdapter:
  arguments:
    $stateService: '@App\Service\State\SharedStateService'
  public: true

Features\Bootstrap\SharedDataContext:
  factory: [ '@App\Compatibility\SharedDataContextAdapter', 'getInstance' ]
  public: true
```

---

## 11. AI Agent Guidelines

Guidelines for AI agents working with this architecture:

### 11.1 Understanding the Architecture

1. **Start with interfaces**: Interfaces define the contract for services
2. **Follow the dependency chain**: Understand how components depend on each other
3. **Look at service registration**: Service configuration shows how components are wired together

### 11.2 Making Changes

1. **Identify the right component**: Determine which service, context, or page object needs to change
2. **Follow existing patterns**: Use the established patterns for the component type
3. **Maintain separation of concerns**: Keep business logic in services, step definitions in contexts, UI interactions
   in page objects

### 11.3 Adding New Functionality

1. **Add a service interface**: Define the contract for the new functionality
2. **Implement the service**: Create a concrete implementation of the interface
3. **Register the service**: Add the service to the appropriate configuration file
4. **Use the service**: Inject the service into contexts or page objects that need it

### 11.4 Troubleshooting

1. **Check service registration**: Ensure services are properly registered and configured
2. **Verify dependencies**: Make sure all required dependencies are injected
3. **Look for interface violations**: Ensure implementations follow their interface contracts
4. **Check state management**: Verify state is being properly set and retrieved

### 11.5 Best Practices

1. **Keep contexts small and focused**: Each context should handle a specific domain
2. **Delegate to services and page objects**: Contexts should delegate implementation to services and page objects
3. **Use interfaces for dependencies**: Depend on interfaces, not concrete implementations
4. **Follow naming conventions**: Use consistent naming for services, contexts, and page objects
5. **Document public methods**: Provide clear documentation for public methods

---

## Conclusion

The service-oriented architecture provides a solid foundation for the E-commerce Test Framework, addressing the issues
in the previous implementation and creating a more maintainable, testable, and AI-comprehensible system. By following
the patterns and guidelines in this documentation, AI agents can effectively work with the codebase, making changes and
adding new functionality while maintaining the architectural integrity.
