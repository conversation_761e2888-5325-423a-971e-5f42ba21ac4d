# Step Definition Best Practices

## Context Organization

### 1. Single Responsibility Principle

Each context should have a single, well-defined responsibility:

```php
// GOOD - CartContext handles only cart-related operations
class CartContext implements Context 
{
    public function addToCart(): void
    public function updateQuantity(): void
    public function removeItem(): void
}

// BAD - Mixed responsibilities
class CartContext implements Context 
{
    public function addToCart(): void
    public function fillShippingInfo(): void  // Should be in CheckoutContext
}
```

### 2. Step Definition Placement

Step definitions should be placed in the most specific context possible:

```php
// GOOD - Product-related step in ProductContext
class ProductContext implements Context 
{
    /**
     * @When /^I add "([^"]*)" to cart$/
     */
    public function iAddProductToCart(string $productName): void
    {
        $this->productPage->load($productName);
        $this->productPage->addToCart();
    }
}

// BAD - Product-related step in FeatureContext
class FeatureContext implements Context 
{
    /**
     * @When /^I add "([^"]*)" to cart$/
     */
    public function iAddProductToCart(string $productName): void
    {
        $this->productContext->addToCart($productName); // Unnecessary delegation
    }
}
```

### 3. Context Communication

Use `ContextManager` for context communication instead of direct dependencies:

```php
// GOOD - Using ContextManager
class CheckoutContext implements Context 
{
    private ContextManager $contextManager;

    public function completeCheckout(): void
    {
        $cartData = $this->contextManager
            ->getContext('cart')
            ->getCartData();
        
        // Process checkout with cart data
    }
}

// BAD - Direct context dependency
class CheckoutContext implements Context 
{
    private CartContext $cartContext;

    public function completeCheckout(): void
    {
        $cartData = $this->cartContext->getCartData();
    }
}
```

### 4. Shared Data Management

Use `SharedDataContext` for data that needs to be accessed across contexts:

```php
// GOOD - Using SharedDataContext
class ProductContext implements Context 
{
    public function addToCart(): void
    {
        $productData = $this->productPage->getProductData();
        SharedDataContext::getInstance()->set('currentProduct', $productData);
    }
}

class CartContext implements Context 
{
    public function verifyCart(): void
    {
        $productData = SharedDataContext::getInstance()->get('currentProduct');
        // Verify cart contents
    }
}
```

### 5. Step Definition Naming

Use clear, descriptive names that match the feature file language:

```php
// GOOD - Clear, action-oriented name
/**
 * @When /^I add "([^"]*)" to cart$/
 */
public function iAddSpecificProductToCart(string $productName): void

// BAD - Unclear or technical name
/**
 * @When /^I add "([^"]*)" to cart$/
 */
public function processCartAddition(string $productName): void
```

### 6. Page Object Integration

Interact with pages through page objects, not directly with elements. For complete implementation details,
see [Page Object Rules](page_object_rules.md).

Key principles for step definitions:
1. Use page objects for all UI interactions
2. Handle errors appropriately
3. Store interaction results in SharedDataContext
4. Use clear, descriptive method names

Example:
```php
/**
 * @When /^I add "([^"]*)" to cart with quantity (\d+)$/
 */
public function iAddProductToCartWithQuantity(string $productName, int $quantity): void
{
    try {
        $this->productPage->setQuantity($quantity);
        $this->productPage->addToCart();
        
        // Store for verification steps
        $this->sharedData->set('last_added_product', [
            'name' => $productName,
            'quantity' => $quantity
        ]);
    } catch (ElementNotFoundException $e) {
        throw new RuntimeException(
            sprintf(
                'Failed to add product "%s" with quantity %d: %s',
                $productName,
                $quantity,
                $e->getMessage()
            )
        );
    }
}
```

### 7. Error Handling

Include proper error handling and validation:

```php
// GOOD - Proper error handling
public function addToCart(): void
{
    try {
        $this->productPage->addToCart();
    } catch (ElementNotFoundException $e) {
        throw new RuntimeException(
            'Add to cart button not found: ' . $e->getMessage()
        );
    }
}

// BAD - No error handling
public function addToCart(): void
{
    $this->productPage->addToCart();
}
```

### 8. Documentation

Include clear PHPDoc comments for all step definitions:

```php
/**
 * Adds a specific product to the cart.
 * 
 * @When /^I add "([^"]*)" to cart$/
 * @param string $productName The name of the product to add
 * @throws RuntimeException If the product cannot be added
 */
public function iAddSpecificProductToCart(string $productName): void
```

## Migration Guide

To migrate existing step definitions to the new structure:

1. Identify the most appropriate context for each step definition
2. Move the step definition to its proper context
3. Update any dependencies to use ContextManager
4. Update shared data access to use SharedDataContext
5. Add proper error handling and documentation

Example migration:

```php
// Before (in FeatureContext)
/**
 * @When /^I add "([^"]*)" to cart$/
 */
public function userAddsSpecificProductToTheCart($productName): void
{
    $this->productPage->load($productName);
    $this->productContext->addToCart();
}

// After (in ProductContext)
/**
 * Adds a specific product to the cart.
 * 
 * @When /^I add "([^"]*)" to cart$/
 * @param string $productName The name of the product to add
 * @throws RuntimeException If the product cannot be added
 */
public function iAddSpecificProductToCart(string $productName): void
{
    try {
        $this->productPage->load($productName);
        $this->productPage->addToCart();
        
        // Store product data for other contexts
        SharedDataContext::getInstance()->set(
            'currentProduct',
            $this->productPage->getProductData()
        );
    } catch (ElementNotFoundException $e) {
        throw new RuntimeException(
            sprintf(
                'Failed to add product "%s" to cart: %s',
                $productName,
                $e->getMessage()
            )
        );
    }
}
``` 