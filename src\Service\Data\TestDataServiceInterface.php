<?php

namespace App\Service\Data;

interface TestDataServiceInterface
{
    /**
     * Load test data for a brand
     *
     * @param string $brand Brand identifier
     * @param string $type Data type (products, users, etc)
     * @param string|null $key Optional specific data key
     * @return array The loaded test data
     */
    public function loadTestData(string $brand, string $type, ?string $key = null): array;

    /**
     * Register data for later use
     *
     * @param string $key Data key
     * @param array $data Data to register
     * @return void
     */
    public function registerData(string $key, array $data): void;

    /**
     * Get registered data
     *
     * @param string $key Data key
     * @return mixed Registered data or null if not found
     */
    public function getData(string $key);

    /**
     * Check if data is registered
     *
     * @param string $key Data key
     * @return bool True if data is registered, false otherwise
     */
    public function hasData(string $key): bool;
}
