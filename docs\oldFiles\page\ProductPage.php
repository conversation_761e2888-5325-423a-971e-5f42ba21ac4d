<?php

namespace Features\Bootstrap\Page;

use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\ElementNotFoundException;
use InvalidArgumentException;

/**
 * ProductPage handles actions on the product detail page.
 */
class ProductPage extends BasePage
{
    /**
     * The path of the product page.
     *
     * @var string
     */
    protected $path = '/product/{slug}';

    /**
     * Product name to URL slug mapping
     * @var array<string, string>
     */
    private array $productSlugs = [
        'Total Harmony' => 'aeons-total-harmony',
        // Add other products as needed
    ];

    /**
     * CSS Selectors used throughout the page
     */
    private const SELECTORS = [
        'QUANTITY' => '[data-value="1"]',
        'PURCHASE_TYPE' => '.ratio-title.product-variant-label-info',
        'PRODUCT_NAME' => '.title',
        'PRICE' => '#product-price',
        'RESTRICTIONS' => '.product-restrictions .warning',
        'FLAVOR' => '.flavor'
    ];

    /**
     * Verifies that we're on the expected page.
     */
    protected function verifyPage(): void
    {
        parent::verifyPage();
        $this->waitForElementVisible(self::SELECTORS['PRODUCT_NAME']);
    }

    /**
     * Gets the URL for the product page.
     *
     * @param array $urlParameters Parameters to be replaced in the URL
     * @return string The complete URL
     */
    public function getUrl(array $urlParameters = []): string
    {
        $path = $this->path;
        if (isset($urlParameters['slug'])) {
            $path = str_replace('{slug}', $urlParameters['slug'], $path);
        }
        return $this->baseUrl . $path;
    }

    /**
     * Loads the product page for a specific product.
     *
     * @param string $productName The actual product name (e.g., "Total Harmony")
     * @param ?array $productData Optional product data
     * @throws ElementNotFoundException
     * @throws InvalidArgumentException If product name is not recognized
     */
    public function loadWithName(string $productName, ?array $productData = null): void
    {
        if ($productData) {
            // Verify product name matches
            if ($productData['name'] !== $productName) {
                throw new \InvalidArgumentException(
                    sprintf('Product name mismatch. Expected: %s, Got: %s',
                        $productName,
                        $productData['name']
                    )
                );
            }
            $fullUrl = $this->getBaseUrl() . 'ProductPage.php/' . trim($productData['url_path'], '/');
        } else {
            // Fallback to using slugs if no data provided
            if (!isset($this->productSlugs[$productName])) {
                throw new \InvalidArgumentException("Unknown product: $productName");
            }
            $fullUrl = $this->getBaseUrl() . 'ProductPage.php/' . $this->productSlugs[$productName];
        }
        
        $this->getSession()->visit($fullUrl);
        $this->waitForPageToLoad();
    }

    /**
     * Waits for the page to load completely.
     *
     * @param int $timeout The maximum time to wait in milliseconds.
     * @throws ElementNotFoundException
     */
    public function waitForPageToLoad(int $timeout = 10000): void
    {
        parent::waitForPageToLoad($timeout);
        $this->waitForElementVisible('#sylius-product-adding-to-cart', $timeout);
    }

    /**
     * Retrieves the displayed total sum on the "Add to Cart" button.
     *
     * @return string The displayed total (e.g., "£xx.xx").
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getDisplayedTotal(): string
    {
        $selector = 'button.add-product #product-price';
        $addToCartButton = $this->findElement($selector);
        return $addToCartButton->getText();
    }

    /**
     * Retrieves the selected size option text.
     *
     * @return string The selected size option (e.g., "1 Jar").
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getSelectedSizeOption(): string
    {
        $selector = '.choose-item.active p';
        $selectedSizeOption = $this->findElement($selector);
        return trim($selectedSizeOption->getText());
    }

    /**
     * Retrieves the product quantity selected.
     *
     * @return int The product quantity.
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getProductQuantity(): int
    {
        $selector = 'input#sylius_add_to_cart_cartItem_quantity';
        $productQuantity = $this->findElement($selector);
        return (int)$productQuantity->getValue();
    }

    /**
     * Retrieves the selected pricing mode.
     *
     * @return string The pricing mode (e.g., "One-Time Purchase").
     * @throws ElementNotFoundException If the element is not found.
     */
    public function getPricingMode(): string
    {
        $selector = '.purchase-option.active .product-variant-label-info';
        $pricingMode = $this->findElement($selector);
        return trim($pricingMode->getText());
    }

    /**
     * Opens the product page.
     *
     * @param array $urlParameters
     * @return void
     */
    public function open(array $urlParameters = []): void
    {
        parent::open($urlParameters);
        $this->waitForPageToLoad();
    }

    /**
     * Adds the product to the cart by clicking the 'Add to Cart' button.
     *
     * @throws ElementNotFoundException If the 'Add to Cart' button is not found.
     */
    public function addToCart(): void
    {
        $button = $this->getAddToCartButton();
        $button->click();
    }

    /**
     * Finds and returns the 'Add to Cart' button element.
     *
     * @throws ElementNotFoundException If the button is not found.
     */
    protected function getAddToCartButton()
    {
        $selector = '#sylius-product-adding-to-cart button.add-product';
        return $this->findElement($selector);
    }

    /**
     * Selects the subscription frequency/supply duration.
     *
     * @param string $frequency The frequency in months (e.g., "2 Months")
     * @return int The selected frequency in days
     * @throws ElementNotFoundException If the frequency selector is not found
     * @throws InvalidArgumentException If an invalid frequency is provided
     */
    public function selectSupplyDuration(string $frequency): int
    {
        // Extract the numeric value from the frequency string
        if (!preg_match('/^(\d+)\s*Months?$/i', $frequency, $matches)) {
            throw new InvalidArgumentException(
                sprintf('Invalid frequency format: "%s". Expected format: "X Month(s)"', $frequency)
            );
        }

        $months = $matches[1];
        $days = $months * 30; // Convert months to days as per the select values

        // Find the frequency selector
        $selector = '.product-subscription-frequency';
        $select = $this->findElement($selector);

        // Select the frequency
        $select->selectOption((string)$days);

        // Wait for any potential AJAX updates
        $this->getSession()->wait(1000);

        return $days;
    }

    /**
     * Selects a product size/quantity option by key (minimum, medium, maximum)
     *
     * @param string $quantityKey The quantity key from test data (e.g., "medium")
     * @param array $productData Product data from test configuration
     * @throws ElementNotFoundException
     * @throws InvalidArgumentException
     */
    public function selectSize(string $quantityKey, array $productData): void
    {
        if (!isset($productData['options']['quantities'][$quantityKey])) {
            throw new \InvalidArgumentException(
                sprintf('Invalid quantity key: "%s". Available keys: %s',
                    $quantityKey,
                    implode(', ', array_keys($productData['options']['quantities']))
                )
            );
        }

        $quantityData = $productData['options']['quantities'][$quantityKey];
        
        // Use numberOfItems for the selector
        $selector = sprintf('.set-quantity[data-value="%d"]', $quantityData['numberOfItems']);
        $element = $this->findElement($selector);
        $element->click();

        // Verify selection
        $this->waitForAjaxToComplete();
        
        if (!$this->isQuantitySelected($quantityData['numberOfItems'])) {
            throw new \RuntimeException(
                sprintf('Failed to select quantity: %s (%d items)',
                    $quantityData['fullName'],
                    $quantityData['numberOfItems']
                )
            );
        }
    }

    /**
     * Checks if a specific quantity is currently selected
     *
     * @param int $numberOfItems The number of items to check
     * @return bool True if the quantity is selected, false otherwise
     */
    private function isQuantitySelected(int $numberOfItems): bool
    {
        try {
        $selector = sprintf('.set-quantity[data-value="%d"].active', $numberOfItems);
        $element = $this->findElement($selector);
        return $element->isVisible();
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * @throws ElementNotFoundException
     */
    public function getQuantity(): ?string
    {
        // Placeholder for element selector
        $selector = '#sylius-product-adding-to-cart .active.set-quantity';
        return $this->findElement($selector)->getAttribute("data-value");
    }

    /**
     * Clicks the subscribe button for the product.
     *
     * @throws ElementNotFoundException If the subscribe button is not found.
     */
    public function clickToSubscribe(): void
    {
        $button = $this->getSubscribeButton();
        $this->scrollToElement($button);
        $button->click();
    }

    /**
     * Finds and returns the subscribe button element.
     *
     * @return NodeElement The subscribe button element.
     * @throws ElementNotFoundException If the button is not found.
     */
    protected function getSubscribeButton(): NodeElement
    {
        $selector = ".purchase-option[data-variant-option-subscription='yes']";
        return $this->findElement($selector);
    }

    /**
     * Scrolls to the specified element.
     *
     * @param NodeElement $element The element to scroll to.
     */
    protected function scrollToElement(NodeElement $element): void
    {
        parent::scrollToElement($element);
    }

    /**
     * Gets the text of the FAQ title.
     *
     * @return string The FAQ title text.
     * @throws ElementNotFoundException If the FAQ title element is not found.
     */
    public function getFaqTitle(): string
    {
        $element = $this->getFaqTitleElement();
        return $element->getText();
    }

    /**
     * Finds and returns the FAQ title element.
     *
     * @return NodeElement The FAQ title element.
     * @throws ElementNotFoundException If the element is not found.
     */
    protected function getFaqTitleElement(): NodeElement
    {
        $selector = 'p.h1';
        return $this->findElement($selector);
    }

    /**
     * Clicks on a specific accordion button.
     *
     * @param int $index Zero-based index of the accordion button to click.
     * @throws InvalidArgumentException If the provided index is out of range.
     */
    public function clickAccordionButton(int $index): void
    {
        $buttons = $this->getAccordionButtons();

        if (isset($buttons[$index])) {
            $button = $buttons[$index];
            $this->scrollToElement($button);
            $button->click();
        } else {
            throw new InvalidArgumentException('Invalid accordion button index');
        }
    }

    /**
     * Finds and returns an array of accordion button elements.
     *
     * @return NodeElement[] The accordion button elements.
     */
    protected function getAccordionButtons(): array
    {
        $selector = '.accordion-button';
        return $this->findElements($selector);
    }

    /**
     * Checks if a specific accordion section is expanded.
     *
     * @param int $index Zero-based index of the accordion section to check.
     * @return bool True if the section is expanded, false otherwise.
     */
    public function isSectionExpanded(int $index): bool
    {
        $expandedSections = $this->getExpandedSections();
        return isset($expandedSections[$index]);
    }

    /**
     * Finds and returns an array of expanded accordion sections.
     *
     * @return NodeElement[] The expanded accordion sections.
     */
    protected function getExpandedSections(): array
    {
        $selector = '.accordion-collapse.show';
        return $this->findElements($selector);
    }

    /**
     * Selects a product by its name.
     *
     * @param string $productName Name of the product to select.
     * @throws ElementNotFoundException If the product link is not found.
     */
    public function selectProductByName(string $productName): void
    {
        $productLink = $this->findElementLink($productName);
        $this->scrollToElement($productLink);
        $productLink->click();
    }

    /**
     * Finds an element using a link text locator.
     *
     * @param string $linkText The text of the link to find.
     * @return NodeElement The found link element.
     * @throws ElementNotFoundException If the link is not found.
     */
    protected function findElementLink(string $linkText): NodeElement
    {
        $element = $this->session->getPage()->findLink($linkText);
        if (!$element) {
            throw new ElementNotFoundException($this->session, 'link', 'text', $linkText);
        }
        return $element;
    }

    /**
     * Retrieves the product name from the page.
     *
     * @return string The product name.
     * @throws ElementNotFoundException If the product name element is not found.
     */
    public function getProductName(): string
    {
        return $this->getCompleteText(self::SELECTORS['PRODUCT_NAME'], 'title');
    }

    /**
     * Normalizes the product name to a consistent format.
     *
     * @param string $productName The product name to normalize.
     * @return string The normalized product name.
     */
    private function normalizeProductName(string $productName): string
    {
        // Convert to Title Case for consistent comparison
        return ucwords(strtolower(trim($productName)));
    }

    /**
     * Selects a purchase option based on test data configuration
     *
     * @param string $purchaseOption The purchase option to select
     * @param array $availableOptions Optional array of available options from test data
     * @return string The selected purchase option
     * @throws ElementNotFoundException If selector not found
     */
    public function selectPurchaseOption(string $purchaseOption): string
    {
        // Base selector for purchase options
        $baseSelector = '#sylius-product-adding-to-cart';
        
        // Find all available purchase option elements
        $purchaseOptions = $this->findElement($baseSelector)
            ->findAll('css', '.purchase-option');

        // Find and click the matching option
        foreach ($purchaseOptions as $option) {
            $labelElement = $option->find('css', '.product-variant-label-info');
            if ($labelElement && trim($labelElement->getText()) === $purchaseOption) {
                $radioInput = $option->find('css', 'input[type="radio"]');
                if ($radioInput) {
                    $radioInput->click();
                    return $purchaseOption;
                }
            }
        }
        
        throw new ElementNotFoundException(
            $this->session,
            'purchase option',
            'text',
            $purchaseOption
        );
    }

    /**
     * Gets the currently selected purchase option
     *
     * @return string The selected purchase option text
     * @throws ElementNotFoundException
     */
    public function getSelectedPurchaseOption(): string
    {
        $element = $this->findElement('.purchase-option.active .product-variant-label-info');
        return trim($element->getText());
    }

    public function getCurrentPrice(): string
    {
        return $this->getDisplayedTotal();
    }

    public function getSubscriptionFrequency(): string
    {
        $selector = '.subscription-frequency';
        return $this->findElement($selector)->getText();
    }

    public function getProductSubtitle(): string
    {
        return $this->getCompleteText('.main .note.color', 'title');
    }

    public function getProductDescription(): string
    {
        return $this->getCompleteText('.main .text', 'raw');
    }

    public function getProductBadges(): array
    {
        return $this->getCompleteTextArray('.info-grid .info-item', 'title');
    }

    public function getSubscriptionBenefits(): array
    {
        $selector = '.purchase-option[data-variant-option-subscription="yes"] ul li';
        $benefits = $this->findElements($selector);
        return array_map(function($benefit) {
            return trim($benefit->getText());
        }, $benefits);
    }

    public function getTrustBadges(): array
    {
        return $this->getCompleteTextArray('.info-grid-w-bg .info-item', 'sentence');
    }

    public function getTestimonial(): array
    {
        return [
            'quote' => $this->findElement('.review-box .text-box .text')->getText(),
            'author' => $this->findElement('.review-box .text-box .name-text')->getText()
        ];
    }

    public function getFAQs(): array
    {
        $selector = '.accordion-item';
        $faqs = $this->findElements($selector);
        
        return array_map(function($faq) {
            return [
                'question' => $this->normalizeText($faq->find('css', '.accordion-button')->getText(), 'raw'),
                'answer' => $this->normalizeText($faq->find('css', '.accordion-body')->getText(), 'raw')
            ];
        }, $faqs);
    }

    public function expandFAQQuestion(int $index): void
    {
        $selector = sprintf('.accordion-item:nth-child(%d) .accordion-button', $index + 1);
        $this->clickElement($selector);
        $this->waitForAjaxToComplete();
    }

    /**
     * Retrieves the pricing information for the product.
     *
     * @return array An array containing pricing information for one-time purchase and subscription options.
     * @throws ElementNotFoundException If pricing elements are not found.
     */
    public function getPricing(): array
    {
        $pricing = [];

        // Get one-time purchase prices
        $selector = '.purchase-option[data-variant-option-subscription="no"]';
        $oneTimeOption = $this->findElement($selector);
        $pricing['one_time'] = $this->extractPricingFromOption($oneTimeOption);

        // Get subscription prices
        $selector = '.purchase-option[data-variant-option-subscription="yes"]';
        $subscriptionOption = $this->findElement($selector);
        $pricing['subscription'] = $this->extractPricingFromOption($subscriptionOption);

        return $pricing;
    }

    /**
     * Extracts pricing information from a given option element.
     *
     * @param NodeElement $option The option element to extract pricing from.
     * @return array An array containing pricing information for the given option.
     */
    private function extractPricingFromOption(NodeElement $option): array
    {
        $quantities = $option->findAll('css', '.choose-item');
        $prices = [];

        foreach ($quantities as $quantity) {
            $name = trim($quantity->find('css', 'p:first-child')->getText());
            $priceText = $quantity->find('css', '.qty-price-per-item')->getText();
            $price = (float) str_replace(['£', ','], '', $priceText);

            if ($quantity->hasClass('active')) {
                $prices['selected'] = [
                    'name' => $name,
                    'price' => $price
                ];
            }

            $prices['options'][] = [
                'name' => $name,
                'price' => $price
            ];
        }

        return $prices;
    }

    /**
     * Normalizes the text to a consistent format.
     *
     * @param string $text The text to normalize.
     * @param string $expectedFormat The expected format of the text.
     * @return string The normalized text.
     */
    private function normalizeText(string $text, string $expectedFormat): string
    {
        $text = trim($text);
        
        return match($expectedFormat) {
            'title' => ucwords(strtolower($text)),      // "Apple Cider Vinegar"
            'upper' => strtoupper($text),               // "PRODUCT OF ITALY"
            'sentence' => ucfirst(strtolower($text)),   // "60 day satisfaction guarantee"
            'raw' => $text,                             // Keep original format
        };
    }

    /**
     * Gets complete text content from an element, including nested elements.
     *
     * @param string $selector Main element CSS selector
     * @param string $format Expected format from products.yml ('raw', 'title', 'upper', 'sentence')
     * @return string Normalized complete text
     * @throws ElementNotFoundException
     */
    private function getCompleteText(string $selector, string $format = 'raw'): string 
    {
        $element = $this->findElement($selector);
        // Get all text content, including nested elements
        $fullText = $element->getText();
        return $this->normalizeText($fullText, $format);
    }

    /**
     * Gets complete text content from an element, including nested elements.
     *
     * @param string $selector Main element CSS selector
     * @param string $format Expected format from products.yml ('raw', 'title', 'upper', 'sentence')
     * @return array Normalized complete text
     */
    private function getCompleteTextArray(string $selector, string $format = 'raw'): array 
    {
        $elements = $this->findElements($selector);
        return array_map(
            fn($element) => $this->normalizeText($element->getText(), $format),
            $elements
        );
    }

    //Funnel methods
    /**
     * Get the pre-selected quantity in funnel
     * 
     * @return string|null The selected quantity or null if not found
     */
    public function getFunnelQuantity(): ?string
    {
        try {
            return $this->getElementAttribute(
                self::SELECTORS['QUANTITY'], 
                'data-value'
            );
        } catch (ElementNotFoundException $e) {
            return null;
        }
    }

    /**
     * Verify if product restriction warning is displayed
     * 
     * @param string $warning The warning text to look for
     * @return bool True if warning is found, false otherwise
     */
    public function hasRestrictionWarning(string $warning): bool
    {
        try {
            $warningElements = $this->findElements(self::SELECTORS['RESTRICTIONS']);
            foreach ($warningElements as $element) {
                if (strpos($element->getText(), $warning) !== false) {
                    return true;
                }
            }
            return false;
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Get all displayed product restrictions
     * 
     * @return array Array of restriction warning texts
     */
    public function getProductRestrictions(): array
    {
        $restrictions = [];
        try {
            $elements = $this->findElements(self::SELECTORS['RESTRICTIONS']);
            foreach ($elements as $element) {
                $restrictions[] = trim($element->getText());
            }
        } catch (ElementNotFoundException $e) {
            // No restrictions found
        }
        return $restrictions;
    }

    /**
     * Checks if the product has flavor options available.
     *
     * @return bool True if flavor selection is available
     */
    public function hasFlavorOptions(): bool
    {
        try {
            $this->findElement(self::SELECTORS['FLAVOR'], 0);
            return true;
        } catch (ElementNotFoundException $e) {
            return false;
        }
    }

    /**
     * Selects a flavor option for the product if available.
     *
     * @param string $flavorCode The flavor code (e.g., "classic", "lemon", "truffle")
     * @throws ElementNotFoundException If the flavor option is not found when it should be available
     */
    public function selectFlavor(string $flavorCode): void
    {
        if (!$this->hasFlavorOptions()) {
            return; // Skip flavor selection if not available
        }

        $selector = sprintf('.flavor[data-option-value-code="%s"]', $flavorCode);
        $element = $this->findElement($selector);
        $element->click();

        // Wait for any potential AJAX updates
        $this->getSession()->wait(1000);
    }

    /**
     * Gets the currently selected flavor if available.
     *
     * @return string|null The selected flavor code or null if flavors not available
     */
    public function getSelectedFlavor(): ?string
    {
        if (!$this->hasFlavorOptions()) {
            return null;
        }

        try {
            $selector = '.flavor.selected';
            $element = $this->findElement($selector);
            return $element->getAttribute('data-option-value-code');
        } catch (ElementNotFoundException $e) {
            return null;
        }
    }
}



