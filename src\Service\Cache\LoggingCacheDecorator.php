<?php

namespace App\Service\Cache;

use Psr\Log\LoggerInterface;

/**
 * Decorator that adds logging to cache operations
 */
class LoggingCacheDecorator implements CacheServiceInterface
{
    public function __construct(
        private readonly CacheServiceInterface $decorated,
        private readonly LoggerInterface       $logger
    )
    {
    }

    public function get(string $key): mixed
    {
        $value = $this->decorated->get($key);
        $this->logger->debug('Cache get operation', [
            'key' => $key,
            'hit' => $value !== null
        ]);
        return $value;
    }

    public function set(string $key, mixed $value, ?int $lifetime = null): void
    {
        $this->decorated->set($key, $value, $lifetime);
        $this->logger->debug('Cache set operation', [
            'key' => $key,
            'lifetime' => $lifetime
        ]);
    }

    public function delete(string $key): void
    {
        $this->decorated->delete($key);
        $this->logger->debug('Cache delete operation', [
            'key' => $key
        ]);
    }

    public function deletePattern(string $pattern): void
    {
        $this->decorated->deletePattern($pattern);
        $this->logger->debug('Cache delete pattern operation', [
            'pattern' => $pattern
        ]);
    }

    public function has(string $key): bool
    {
        $exists = $this->decorated->has($key);
        $this->logger->debug('Cache has operation', [
            'key' => $key,
            'exists' => $exists
        ]);
        return $exists;
    }
} 