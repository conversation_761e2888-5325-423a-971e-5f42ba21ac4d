<?php

require_once __DIR__ . '/../../../vendor/autoload.php';

use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Net\SSH2;

try {
    echo "Checking remote server capabilities...\n\n";

    // SSH Connection
    $ssh = new SSH2('18.170.243.171');
    $key = PublicKeyLoader::load("**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");

    if (!$ssh->login('ec2-user', $key)) {
        throw new Exception('SSH login failed');
    }

    echo "SSH connection successful\n\n";

    // Check available commands
    echo "Checking available commands:\n";
    $commands = ['nc', 'netcat', 'socat', 'ssh'];
    foreach ($commands as $cmd) {
        $result = $ssh->exec("which $cmd 2>/dev/null");
        echo "$cmd: " . ($result ? "Available at $result" : "Not available") . "\n";
    }

    // Check if we can directly connect to MySQL
    echo "\nChecking MySQL connectivity:\n";
    $result = $ssh->exec('nc -zv ************* 3306 2>&1');
    echo "MySQL connection test: $result\n";

    // Check if port forwarding is allowed
    echo "\nChecking port forwarding capabilities:\n";
    $result = $ssh->exec('sysctl -a 2>/dev/null | grep forward');
    echo "Port forwarding settings:\n$result\n";

    // Check firewall rules
    echo "\nChecking firewall rules:\n";
    $result = $ssh->exec('sudo iptables -L 2>/dev/null');
    echo $result ? $result : "No access to iptables or no rules\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
