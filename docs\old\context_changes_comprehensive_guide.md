# Comprehensive Guide to Context File Changes

## Table of Contents

1. [Introduction](#introduction)
2. [Core Architectural Changes](#core-architectural-changes)
3. [Migration Steps for Each Context Type](#migration-steps-for-each-context-type)
4. [Dependency Injection Changes](#dependency-injection-changes)
5. [SharedDataContext Integration](#shareddatacontext-integration)
6. [Context Communication Pattern](#context-communication-pattern)
7. [Error Handling Improvements](#error-handling-improvements)
8. [Best Practices & Coding Standards](#best-practices--coding-standards)
9. [Testing & Validation](#testing--validation)
10. [Troubleshooting Guide](#troubleshooting-guide)

## Introduction

This guide provides an exhaustive overview of the required changes to context files as part of the test framework
modernization initiative. Context files are a crucial part of our Behat test infrastructure, serving as the bridge
between feature files and page objects.

### Purpose of This Guide

- Document required changes to align with new architecture
- Provide step-by-step migration instructions for each context type
- Establish best practices for future context development
- Ensure consistent implementation across the test suite

### Current vs. Target Architecture

**Current Architecture Issues:**

- Direct context-to-context communication
- Manual dependency management
- Inconsistent error handling
- Global state management issues

**Target Architecture Benefits:**

- Standardized dependency injection
- Centralized context management
- Enhanced data sharing mechanism
- Improved error reporting and recovery

## Core Architectural Changes

### ContextManager Implementation

The `ContextManager` serves as a central registry for all context instances, providing a consistent way to access
contexts and their functionality.

```php
// ContextManager.php
class ContextManager 
{
    private array $contexts = [];
    
    public function registerContext(string $contextName, Context $context): void
    {
        $this->contexts[$contextName] = $context;
    }
    
    public function getContext(string $contextName): Context
    {
        if (!isset($this->contexts[$contextName])) {
            throw new RuntimeException("Context '{$contextName}' not registered.");
        }
        
        return $this->contexts[$contextName];
    }
}
```

### BaseContext Standardization

All contexts must extend the `BaseContext` class, which provides:

- ContextManager integration
- SharedDataContext access
- Standard logging capabilities
- Consistent exception handling

```php
// BaseContext.php
abstract class BaseContext implements Context
{
    protected ?ContextManager $contextManager = null;
    protected SharedDataContext $sharedData;
    
    public function __construct()
    {
        $this->sharedData = SharedDataContext::getInstance();
    }
    
    public function setContextManager(ContextManager $contextManager): void
    {
        $this->contextManager = $contextManager;
    }
    
    protected function logInfo(string $message): void
    {
        // Standard logging implementation
    }
    
    protected function logError(string $message, ?Throwable $exception = null): void
    {
        // Standard error logging
    }
}
```

## Migration Steps for Each Context Type

### For All Contexts

1. Extend `BaseContext` instead of implementing `Context` directly
2. Add proper constructor with dependency injection
3. Remove direct context property declarations
4. Implement `setContextManager` method
5. Replace global variable usage with SharedDataContext

### For Feature Contexts

**Before:**

```php
class FeatureContext implements Context
{
    private ProductContext $productContext;
    private CartContext $cartContext;
    
    public function __construct()
    {
        $this->productContext = new ProductContext();
        $this->cartContext = new CartContext();
    }
}
```

**After:**

```php
class FeatureContext extends BaseContext
{
    public function __construct(/* injected dependencies only */)
    {
        parent::__construct();
    }
    
    public function setContextManager(ContextManager $contextManager): void
    {
        parent::setContextManager($contextManager);
        // Additional setup if needed
    }
    
    /**
     * @When I perform some action
     */
    public function iPerformSomeAction(): void
    {
        $productContext = $this->contextManager->getContext(ProductContext::class);
        $productContext->someMethod();
    }
}
```

### For Page-Specific Contexts

**Before:**

```php
class ProductContext implements Context
{
    private ProductPage $productPage;
    
    public function __construct()
    {
        $this->productPage = new ProductPage();
    }
    
    public function setProductPage(ProductPage $productPage): void
    {
        $this->productPage = $productPage;
    }
}
```

**After:**

```php
class ProductContext extends BaseContext
{
    private ProductPage $productPage;
    
    public function __construct(ProductPage $productPage)
    {
        parent::__construct();
        $this->productPage = $productPage;
    }
}
```

### For Data Management Contexts

**Before:**

```php
class TestDataContext implements Context
{
    private array $testData = [];
    
    public function loadTestData(string $path): void
    {
        $this->testData = /* load data */;
    }
    
    public function getTestData(string $key)
    {
        return $this->testData[$key] ?? null;
    }
}
```

**After:**

```php
class TestDataContext extends BaseContext
{
    private TestDataRegistry $registry;
    private DataValidator $validator;
    
    public function __construct(TestDataRegistry $registry, DataValidator $validator)
    {
        parent::__construct();
        $this->registry = $registry;
        $this->validator = $validator;
    }
    
    public function getTestData(string $type, string $key): array
    {
        $data = $this->registry->getTestData($type, $key);
        $this->validator->validate($type, $data);
        return $data;
    }
}
```

## Dependency Injection Changes

### Service Definition in services.yml

```yaml
# services.yml
services:
  # Core Services
  Features\Bootstrap\Core\ConfigurationManager:
    arguments: [ ]

  Features\Bootstrap\Core\TestDataRegistry:
    arguments:
      $configManager: '@Features\Bootstrap\Core\ConfigurationManager'

  # Contexts
  Features\Bootstrap\Context\ProductContext:
    parent: FriendsOfBehat\PageObjectExtension\Context\PageObjectContext
    arguments:
      $productPage: '@Features\Bootstrap\Page\ProductPage'
    tags: [ 'context.service' ]

  Features\Bootstrap\Context\CartContext:
    parent: FriendsOfBehat\PageObjectExtension\Context\PageObjectContext
    arguments:
      $cartPage: '@Features\Bootstrap\Page\CartPage'
    tags: [ 'context.service' ]
```

### Context Registration

For proper context registration, implement the `ContextInitializer`:

```php
// ContextInitializer.php
class ContextInitializer implements ContextInitializer
{
    private ContextManager $contextManager;
    
    public function __construct(ContextManager $contextManager)
    {
        $this->contextManager = $contextManager;
    }
    
    public function initializeContext(Context $context): void
    {
        if ($context instanceof BaseContext) {
            $context->setContextManager($this->contextManager);
            $this->contextManager->registerContext(get_class($context), $context);
        }
    }
}
```

## SharedDataContext Integration

### Replacing Static Properties

**Before:**

```php
class SomeContext implements Context
{
    private static $lastOrderId;
    
    public function setOrderId(string $orderId): void
    {
        self::$lastOrderId = $orderId;
    }
    
    public function getOrderId(): string
    {
        return self::$lastOrderId;
    }
}
```

**After:**

```php
class SomeContext extends BaseContext
{
    public function setOrderId(string $orderId): void
    {
        $this->sharedData->set('last_order_id', $orderId);
    }
    
    public function getOrderId(): string
    {
        return $this->sharedData->get('last_order_id');
    }
}
```

### Data Organization Best Practices

1. Use namespaced keys for shared data:
   ```php
   $this->sharedData->set('product.price', $price);
   $this->sharedData->set('product.name', $name);
   ```

2. Use structured data when possible:
   ```php
   $this->sharedData->set('product', [
       'name' => $name,
       'price' => $price,
       'quantity' => $quantity
   ]);
   ```

3. Document shared data dependencies:
   ```php
   /**
    * @When I view product details
    * @depends product.id
    * @sets product.name, product.price
    */
   public function iViewProductDetails(): void
   ```

## Context Communication Pattern

### Accessing Other Contexts

**Before:**

```php
class CheckoutContext implements Context
{
    private CartContext $cartContext;
    
    public function __construct(CartContext $cartContext)
    {
        $this->cartContext = $cartContext;
    }
    
    public function completeCheckout(): void
    {
        $cartItems = $this->cartContext->getCartItems();
        // Process checkout
    }
}
```

**After:**

```php
class CheckoutContext extends BaseContext
{
    public function completeCheckout(): void
    {
        $cartContext = $this->contextManager->getContext(CartContext::class);
        $cartItems = $cartContext->getCartItems();
        // Process checkout
    }
}
```

### Method Access Guidelines

1. Only access public methods of other contexts
2. Document context dependencies
3. Use return values instead of modifying shared state directly
4. Avoid circular dependencies between contexts

## Error Handling Improvements

### Standardized Exception Handling

```php
try {
    $this->productPage->addToCart();
} catch (ElementNotFoundException $e) {
    throw new RuntimeException(
        sprintf(
            'Failed to add product "%s" to cart: %s',
            $this->sharedData->get('product.name'),
            $e->getMessage()
        ),
        0,
        $e
    );
}
```

### Exception Types

1. **RuntimeException**: General runtime errors
2. **InvalidArgumentException**: Invalid input data
3. **AssertionFailedException**: Failed assertions
4. **ElementNotFoundException**: UI element not found
5. **TimeoutException**: Action timeout

### Reporting Context

```php
public function addProductToCart(): void
{
    try {
        // Implementation
    } catch (Throwable $e) {
        $this->logError('Failed to add product to cart', $e);
        throw $e;
    }
}
```

## Best Practices & Coding Standards

### Code Structure

1. Organize methods logically:
    - Constructor and setup methods first
    - Step definition methods grouped by feature
    - Helper methods last

2. Method naming conventions:
    - Step methods: `iDoSomething()`, `thereShouldBeSomething()`
    - Helper methods: `getProductData()`, `processOrder()`
    - Private methods: `extractDataFromElement()`, `formatPrice()`

3. Property organization:
    - Page objects first
    - Service dependencies next
    - Configuration properties last

### Documentation Requirements

1. Class documentation:
   ```php
   /**
    * Handles all product-related step definitions
    */
   class ProductContext extends BaseContext
   ```

2. Method documentation:
   ```php
   /**
    * Adds the specified product to cart
    *
    * @When /^I add "([^"]*)" to cart$/
    * @param string $productName
    * @throws RuntimeException when product cannot be added
    */
   public function iAddProductToCart(string $productName): void
   ```

3. Data dependency documentation:
   ```php
   /**
    * @depends product.id from ProductContext
    * @sets cart.item_count, cart.total
    */
   ```

## Testing & Validation

### Context Testing Strategy

1. Unit test contexts in isolation:
    - Mock dependencies
    - Test helper methods
    - Verify error handling

2. Integration test context interactions:
    - Test with real page objects
    - Verify context communication
    - Test data sharing

3. Test coverage requirements:
    - All public methods
    - Key error paths
    - Edge cases in data processing

### Migration Validation

1. Run existing test suite with old and new implementations
2. Compare test results and performance
3. Verify error reporting and recovery
4. Check for memory leaks and resource usage

## Troubleshooting Guide

### Common Migration Issues

1. **Missing Context Dependencies**:
    - Symptom: "Context not registered" exception
    - Solution: Ensure all contexts are properly registered in service container

2. **SharedData Access Issues**:
    - Symptom: Null values in shared data
    - Solution: Verify data is being set in the right step and with correct key

3. **Page Object Initialization**:
    - Symptom: "Property must not be accessed before initialization"
    - Solution: Verify constructor injection is properly configured

4. **Context Manager Not Set**:
    - Symptom: "Context manager not initialized"
    - Solution: Verify ContextInitializer is correctly registered

5. **Circular Dependencies**:
    - Symptom: Stack overflow or infinite recursion
    - Solution: Refactor contexts to eliminate circular dependencies

### Debugging Techniques

1. Enable detailed logging:
   ```yaml
   # behat.yml
   default:
     suites:
       default:
         formatters:
           pretty: true
         extensions:
           Behat\MinkExtension:
             browser_name: chrome
             base_url: http://localhost
             sessions:
               default:
                 selenium2:
                   wd_host: http://localhost:4444/wd/hub
     extensions:
       Features\Bootstrap\Extension\DebugExtension:
         log_level: debug
   ```

2. Add debugging information to shared data:
   ```php
   $this->sharedData->set('debug.last_action', 'product_add_to_cart');
   $this->sharedData->set('debug.element_id', $elementId);
   ```

3. Use step definition tracing:
   ```bash
   $ vendor/bin/behat --format=pretty --out=std --format=progress --out=behat.log
   ```

4. Implement context state dumping:
   ```php
   /**
    * @When I debug current state
    */
   public function debugCurrentState(): void
   {
       var_dump($this->sharedData->all());
       // Or write to log file
   }
   ```

This guide provides a comprehensive roadmap for migrating existing context files to the new architecture. Follow these
guidelines to ensure consistent implementation and maintain test stability during the transition. 