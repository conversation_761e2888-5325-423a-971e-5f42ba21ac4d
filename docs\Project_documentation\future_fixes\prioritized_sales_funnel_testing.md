# Prioritized Sales Funnel Testing

This document outlines a simple and fast solution to prioritize running critical sales funnel tests without requiring a full data flow migration.

## Current Issue

The `test:sales-funnel:headless` command is intended to run high-priority sales funnel tests, but there are several issues:

1. There's a typo in the composer.json script - it uses `@@high-priority` instead of `@high-priority`
2. The data loading process is inefficient, causing tests to run slower than necessary
3. The critical sales funnel tests depend on multiple data sources with potential inconsistencies
4. The `total-harmony-funnel` profile is stored in `features/bootstrap/fixtures/brands/aeons/test_data.yml` but the test
   expects it to be available directly

## Quick Solution

### 1. Fix the Composer Script

Update the `test:sales-funnel:headless` script in composer.json to use a dedicated runner script:

```json
"test:sales-funnel:headless": "@php bin/run-critical-funnel-tests.php",
```

### 2. Create a Dedicated Test Data File for Sales Funnel Tests

Create a new file `features/fixtures/critical_sales_funnel_data.yml` that contains all the necessary data for the
high-priority sales funnel tests, including the original data structure from
`features/bootstrap/fixtures/brands/aeons/test_data.yml`:

```yaml
# Critical Sales Funnel Test Data
# This file contains all the data needed for high-priority sales funnel tests

brands:
  aeons:
    name: "Aeons"
    base_url: "https://aeonstest.info"
    admin_url: "https://aeonstest.info/admin"

products:
  total_harmony:
    name: "Total Harmony"
    slug: "aeons-total-harmony"
    prices:
      one_time:
        minimum: 49.95
        medium: 134.85
        maximum: 199.8
      subscription:
        minimum: 44.95
        medium: 121.23
        maximum: 179.25
    options:
      purchase_types:
        one_time: "One-Time Purchase"
        subscription: "Subscribe & Save"
      quantities:
        minimum:
          fullName: "1 Jar"
          numberOfItems: 1
        medium:
          fullName: "3 Jars"
          numberOfItems: 3
        maximum:
          fullName: "6 Jars"
          numberOfItems: 6

  ancient_roots:
    name: "Ancient Roots"
    slug: "aeons-ancient-roots-olive-oil"
    prices:
      one_time:
        minimum: 34.95
        medium: 89.95
        maximum: 167.7
      subscription:
        minimum: 31.85
        medium: 80.85
        maximum: 150.9
    options:
      purchase_types:
        one_time: "One-Time Purchase"
        subscription: "Subscribe & Save"
      quantities:
        minimum:
          fullName: "1 Bottle"
          numberOfItems: 1
        medium:
          fullName: "3 Bottles"
          numberOfItems: 3
        maximum:
          fullName: "6 Bottles"
          numberOfItems: 6

# Original test data structure from features/bootstrap/fixtures/brands/aeons/test_data.yml
test_users:
  default:
    email: "<EMAIL>"
    first_name: "Alice"
    last_name: "Johnson"
    phone: "1234567890"
    address: "789 Oak St"
    city: "Manchester"
    postcode: "M1 1AA"
    country: "GB"

product_options:
  ancient_roots:
    flavors:
      classic:
        name: "Classic"
        description: "Our best-seller"
      lemon:
        name: "Lemon"
        description: "Zesty and fresh"
      truffle:
        name: "Truffle"
        description: "Earthy and rich"

payment_methods:
  stripe_valid:
    card_number: "****************"
    expiry: "12/26"
    cvc: "123"

shipping_methods:
  UK:
    method: "Domestic tracked"
    cost: "2.95"

funnel_items:
  total_harmony_basic:
    entry:
      url: "total-harmony-funnel"
      product: "total_harmony"
      quantity: "medium"
      purchase_type: "one_time"
    upsell:
      url: "total-harmony-upsell"
      product: "ancient_roots"
      quantity: "minimum"
      purchase_type: "one_time"
```

### 3. Create a Dedicated Test Runner Script

Create a new script `bin/run-critical-funnel-tests.php` that loads data directly from the dedicated file and also checks
the original file for reference:

```php
#!/usr/bin/env php
<?php

require __DIR__ . '/../vendor/autoload.php';

use Symfony\Component\Yaml\Yaml;

// Set up environment
putenv('TEST_BRAND=aeons');
putenv('TEST_ENV=stage');
putenv('TEST_BASE_URL=https://aeonstest.info');

// Load critical test data
$criticalDataFile = __DIR__ . '/../features/fixtures/critical_sales_funnel_data.yml';
if (!file_exists($criticalDataFile)) {
    echo "Error: Critical test data file not found at $criticalDataFile\n";
    exit(1);
}

echo "Loading critical test data from $criticalDataFile\n";
$criticalData = Yaml::parseFile($criticalDataFile);

// Check if we need to load the original test data file as well
$originalDataFile = __DIR__ . '/../features/bootstrap/fixtures/brands/aeons/test_data.yml';
if (file_exists($originalDataFile)) {
    echo "Loading original test data from $originalDataFile for reference\n";
    $originalData = Yaml::parseFile($originalDataFile);

    // Merge the original funnel items into our critical data if they don't exist
    if (isset($originalData['funnel_items']) && !isset($criticalData['funnel_items'])) {
        $criticalData['funnel_items'] = $originalData['funnel_items'];
        echo "Merged funnel_items from original data\n";
    }
}

// Register data in global state for test access
$GLOBALS['critical_test_data'] = $criticalData;

// Create a debug file to inspect the data
$debugFile = __DIR__ . '/../var/logs/critical_test_data_debug.json';
$debugDir = dirname($debugFile);
if (!is_dir($debugDir)) {
    mkdir($debugDir, 0777, true);
}
file_put_contents($debugFile, json_encode($criticalData, JSON_PRETTY_PRINT));
echo "Debug data written to $debugFile\n";

// Set up command to run high-priority tests
$behatBin = __DIR__ . '/../vendor/bin/behat';
$featureFile = __DIR__ . '/../features/salesFunnel.feature';
$command = "$behatBin $featureFile --tags=@high-priority --format=pretty";

echo "Running command: $command\n";
passthru($command, $returnCode);

exit($returnCode);
```

### 4. Create a Modified Context for Critical Tests

Create a new context class `features/bootstrap/CriticalTestContext.php` that handles the original data structure:

```php
<?php

namespace Features\Bootstrap;

use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;

/**
 * Context for critical tests that need fast data access
 */
class CriticalTestContext implements Context
{
    /**
     * @BeforeScenario @high-priority
     */
    public function setupCriticalTestData(BeforeScenarioScope $scope)
    {
        // Check if critical test data is available
        if (!isset($GLOBALS['critical_test_data'])) {
            echo "Warning: Critical test data not found in globals. Using standard data loading.\n";
            return;
        }

        // Get all contexts
        $environment = $scope->getEnvironment();

        // Share data with other contexts
        $testDataContext = $environment->getContext('Features\\Bootstrap\\TestDataContext');
        if ($testDataContext) {
            // Register critical test data
            $criticalData = $GLOBALS['critical_test_data'];

            // Register brand data
            if (isset($criticalData['brands']['aeons'])) {
                $testDataContext->registerTestData('brand', 'aeons', $criticalData['brands']['aeons']);
            }

            // Register product data
            if (isset($criticalData['products'])) {
                foreach ($criticalData['products'] as $key => $product) {
                    $testDataContext->registerTestData('product', $key, $product);
                }
            }

            // Register test user data
            if (isset($criticalData['test_users'])) {
                foreach ($criticalData['test_users'] as $key => $user) {
                    $testDataContext->registerTestData('user', $key, $user);
                }
            }

            // Register product options
            if (isset($criticalData['product_options'])) {
                foreach ($criticalData['product_options'] as $key => $options) {
                    $testDataContext->registerTestData('product_options', $key, $options);
                }
            }

            // Register payment method data
            if (isset($criticalData['payment_methods'])) {
                foreach ($criticalData['payment_methods'] as $key => $method) {
                    $testDataContext->registerTestData('payment_method', $key, $method);
                }
            }

            // Register shipping method data
            if (isset($criticalData['shipping_methods'])) {
                foreach ($criticalData['shipping_methods'] as $key => $method) {
                    $testDataContext->registerTestData('shipping_method', $key, $method);
                }
            }

            // Register funnel configurations
            if (isset($criticalData['funnel_configurations'])) {
                foreach ($criticalData['funnel_configurations'] as $key => $config) {
                    $testDataContext->registerTestData('funnel_configuration', $key, $config);
                }
            }

            // Register funnel items - this is the key for total-harmony-funnel
            if (isset($criticalData['funnel_items'])) {
                foreach ($criticalData['funnel_items'] as $key => $item) {
                    $testDataContext->registerTestData('funnel_item', $key, $item);

                    // Special handling for total-harmony-funnel
                    if ($key === 'total_harmony_basic' && isset($item['entry']['url']) && $item['entry']['url'] === 'total-harmony-funnel') {
                        // Create a direct mapping for the URL as a key
                        $testDataContext->registerTestData('funnel', 'total-harmony-funnel', $item);
                    }
                }
            }
        }
    }
}
```

### 5. Add the Context to behat.yml

Add the new context to the behat.yml file:

```yaml
default:
  suites:
    default:
      contexts:
        - Features\Bootstrap\CriticalTestContext
        # Other contexts...
```

## Benefits of This Approach

1. **Speed**: By loading all critical test data from a single file, we eliminate the need to search multiple paths and validate complex data structures
2. **Simplicity**: The solution doesn't require changing the existing data flow architecture
3. **Isolation**: Critical tests have their own dedicated data source, reducing the risk of changes to other data files affecting critical tests
4. **Minimal Changes**: Only a few new files are added, with no modifications to existing code
5. **Future-Proof**: This approach can be used as a stepping stone toward the full data flow optimization
6. **Compatibility**: The solution maintains compatibility with the original data structure in
   `features/bootstrap/fixtures/brands/aeons/test_data.yml`

## Implementation Steps

1. Fix the typo in the composer.json script
2. Create the dedicated test data file with all necessary data for high-priority tests
3. Create the dedicated test runner script
4. Update the CriticalTestDataService to properly load and register critical test data
5. Update the composer script to use the new runner

## Files Created for Critical Testing Flow

The following files have been created or modified to support the critical sales funnel testing flow:

### New Files

1. `features/fixtures/critical_sales_funnel_data.yml` - Contains all the necessary data for high-priority sales funnel
   tests
2. `bin/run-critical-funnel-tests.php` - Script to run critical funnel tests with optimized data loading

### Modified Files

1. `src/Service/Data/CriticalTestDataService.php` - Enhanced to better handle critical test data
2. `src/Context/SalesFunnelContext.php` - Updated to use the CriticalTestDataService
3. `composer.json` - Updated script for running critical tests

### Generated Files

1. `var/logs/critical_test_data_debug.json` - Debug file with the loaded critical test data (created at runtime)

## Simplified Approach

After investigating various approaches, we've determined that the most effective way to implement critical sales funnel
testing is to:

1. Use the existing Behat configuration and context structure
2. Enhance the CriticalTestDataService to efficiently load and register critical test data
3. Update the SalesFunnelContext to check for critical test data first
4. Use environment variables to control when critical test data should be used

This approach avoids creating a parallel testing infrastructure that would be difficult to maintain and instead
leverages the existing architecture while optimizing the critical paths.

## Debugging

If you encounter issues with the test not finding the `total-harmony-funnel` profile, check the following:

1. Verify that the `critical_test_data_debug.json` file in `var/logs` contains the correct funnel items data
2. Ensure that the `CriticalTestContext` is properly registering the funnel data with the correct key
3. Check if the test is looking for the funnel data under a different key or structure

The special handling in `CriticalTestContext` should ensure that the `total-harmony-funnel` profile is available to the
test, even though it's defined as a URL in the `funnel_items.total_harmony_basic.entry.url` field in the original data
structure.

This solution allows you to run critical sales funnel tests more efficiently without spending time on a full data flow migration and without disrupting the existing codebase.
