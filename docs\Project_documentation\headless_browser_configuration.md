# Headless Browser Configuration with <PERSON>

This document provides detailed information about the headless browser configuration used in the Malaberg test automation framework.

## Overview

The framework uses Symfony Panther with Google Chrome in headless mode for automated testing. Panther is a browser
testing and web scraping library for PHP that provides a convenient API to control Chrome or Firefox. Headless mode
allows browsers to run without a visible UI, which is ideal for automated testing, especially in CI/CD environments.

## Panther Configuration

### Panther Driver

The framework uses the `Robertfausk\Mink\Driver\PantherDriver` to interact with Chrome or Firefox. Panther manages
browser drivers internally, eliminating the need for separate WebDriver installations.

### Configuration in Service Container

The Panther configuration is defined in the service container configuration file `config/services/panther.yml`:

```yaml
# Panther service configuration
services:
  # Default configuration for all services
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Panther Browser Service
  App\Service\Browser\BrowserServiceInterface:
    class: App\Service\Browser\PantherBrowserService
    arguments:
      $session: '@mink.session'
      $screenshotsDir: '%app.project_root%/screenshots'
      $logger: '@logger'
    public: true

  # Legacy service alias for backward compatibility
  browser.service:
    alias: App\Service\Browser\BrowserServiceInterface
    public: true
```

### Behat Extension Configuration

The Panther extension is configured in `behat.yml`:

```yaml
extensions:
  Robertfausk\Behat\PantherExtension: ~

  FriendsOfBehat\MinkExtension:
    base_url: '%env(TEST_BASE_URL)%'
    default_session: panther
    browser_name: chrome
    sessions:
      panther:
        panther:
          options:
            browser: 'chrome'
            webServerDir: '%paths.base%/public'
          manager_options:
            capabilities:
              goog:chromeOptions:
                args:
                  - "--no-sandbox"
                  - "--disable-gpu"
                  - "--window-size=1920,1080"
                  - "--ignore-certificate-errors"
                  - "--disable-dev-shm-usage"
                  - "--headless"
```

```

## How Panther Works

Panther manages browser instances automatically, so you don't need to start Chrome or Firefox manually. When you run your tests, Panther will:

1. Start a Chrome or Firefox browser instance with the specified options
2. Create a WebDriver session to control the browser
3. Execute your tests against the browser
4. Clean up the browser instance when tests are complete

This eliminates the need for separate scripts to start browsers or manage WebDriver servers.

## Chrome Command-Line Arguments

The following command-line arguments are used for headless Chrome with Panther:

- `--disable-gpu`: Disables GPU hardware acceleration (required for headless mode on some platforms)
- `--headless`: Runs Chrome in headless mode
- `--no-sandbox`: Disables the sandbox (used in CI environments)
- `--disable-dev-shm-usage`: Disables shared memory usage (used in CI environments)
- `--window-size=1920,1080`: Sets the window size to 1920x1080
- `--ignore-certificate-errors`: Ignores SSL certificate errors
- `--disable-popup-blocking`: Prevents Chrome from blocking popups
- `--disable-extensions`: Disables Chrome extensions

## Docker Configuration

The Docker configuration for Panther is defined in `docker-compose.yml`:

```yaml
version: "3.8"

services:
  # PHP/Behat Test Runner Service with Panther
  behat_runner:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: malaberg_behat_runner
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./reports:/app/reports
      - ./screenshots:/app/screenshots
      - ./downloads:/app/downloads
    environment:
      - TEST_BRAND=${TEST_BRAND:-aeons}
      - TEST_ENV=${TEST_ENV:-stage}
      - TEST_BASE_URL=${TEST_BASE_URL:-https://aeonstest.info}
      - TEST_PRODUCT=${TEST_PRODUCT:-}
      - PANTHER_NO_SANDBOX=1
      - PANTHER_CHROME_ARGUMENTS=--disable-dev-shm-usage --disable-gpu --window-size=1920,1080 --ignore-certificate-errors
      - DEBUG=${DEBUG:-false}
      - PANTHER_ERROR_SCREENSHOT_DIR=/app/screenshots
    networks:
      - test_network
```

## PantherBrowserService

The `PantherBrowserService` class is responsible for interacting with the browser. It uses the Mink session with Panther
driver:

```php
<?php

namespace App\Service\Browser;

use App\Service\AbstractService;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Session;
use Psr\Log\LoggerInterface;
use RuntimeException;

class PantherBrowserService extends AbstractService implements BrowserServiceInterface
{
    private Session $session;
    private string $screenshotsDir;
    private int $defaultTimeout = 30;

    public function __construct(
        Session $session,
        string $screenshotsDir,
        ?LoggerInterface $logger = null
    ) {
        parent::__construct($logger);
        $this->session = $session;
        $this->screenshotsDir = $screenshotsDir;

        // Create screenshots directory if it doesn't exist
        if (!is_dir($this->screenshotsDir) && !mkdir($this->screenshotsDir, 0777, true)) {
            $this->logError(sprintf("Failed to create screenshots directory: %s", $this->screenshotsDir));
        }

        $this->logInfo("Initializing PantherBrowserService");
    }

    // Implementation of interface methods...
}
```

## Taking Screenshots with Panther

The `PantherBrowserService` includes an improved method for taking screenshots:

```php
public function takeScreenshot(?string $name = null): string
{
    $name = $name ?? date('YmdHis');
    $filename = sprintf('%s/%s.png', $this->screenshotsDir, $name);

    try {
        $this->logger->debug("Taking screenshot: {$filename}");
        file_put_contents($filename, $this->session->getScreenshot());
        $this->logger->debug("Screenshot saved: {$filename}");
        return $filename;
    } catch (\Exception $e) {
        $this->logger->error("Failed to take screenshot: " . $e->getMessage());
        throw new RuntimeException("Failed to take screenshot: " . $e->getMessage(), 0, $e);
    }
}
```

## Troubleshooting

### Browser Issues

If Panther fails to start the browser, check the following:

1. Ensure Chrome or Firefox is installed in the container or local environment
2. Check if the browser executable is in the expected location or PATH
3. Verify that you have sufficient permissions to start the browser
4. Check if the `PANTHER_NO_SANDBOX=1` environment variable is set in Docker environments

### Connection Issues

If Panther cannot connect to the browser, check the following:

1. Ensure the browser is compatible with the version of Panther you're using
2. Check if a firewall is blocking the connection
3. Verify that the browser capabilities in behat.yml are correctly configured

### Screenshot Issues

If screenshots are not working, check the following:

1. Ensure the screenshots directory exists and is writable
2. Set the `PANTHER_ERROR_SCREENSHOT_DIR` environment variable to specify where screenshots should be saved
3. Check if the `PantherBrowserService` is correctly configured with the screenshots directory

## Performance Considerations

Panther with headless browsers is generally faster than running browsers with a visible UI, but there are some
additional optimizations you can make:

1. **Disable Unnecessary Features**: Add additional command-line arguments to disable features you don't need:
   ```
   --disable-extensions
   --disable-default-apps
   --disable-translate
   ```

2. **Limit Resource Usage**: Add command-line arguments to limit resource usage:
   ```
   --js-flags="--max_old_space_size=2048"
   --memory-pressure-off
   ```

3. **Parallel Execution**: Run tests in parallel to improve overall throughput.

4. **Use the Built-in Web Server**: Panther includes a built-in web server that can serve your application during tests,
   which can be faster than accessing external URLs.

## Advantages of Panther

1. **Simplified Setup**: No need for separate Selenium or WebDriver servers
2. **Automatic Driver Management**: Panther manages browser drivers internally
3. **PHP Native**: Built on Symfony components and integrates well with PHP applications
4. **Dual Mode**: Can run in real browser mode or pure PHP mode using Symfony's DomCrawler
5. **Screenshot Support**: Built-in support for taking screenshots during tests
6. **Modern Browser Support**: Works with the latest versions of Chrome and Firefox

## Conclusion

The Panther-based browser configuration in the Malaberg test automation framework provides a robust foundation for
automated testing. By leveraging Panther's capabilities, you can ensure reliable and efficient test execution with
minimal configuration and maintenance overhead.
