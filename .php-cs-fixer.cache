{"php": "8.3.12", "version": "3.75.0", "indent": "    ", "lineEnding": "\n", "rules": {"blank_line_after_namespace": true, "braces_position": true, "class_definition": true, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"attribute_placement": "ignore", "on_multiline": "ensure_fully_multiline"}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": {"elements": ["property"]}, "single_import_per_statement": true, "single_line_after_imports": true, "single_space_around_construct": {"constructs_followed_by_a_single_space": ["abstract", "as", "case", "catch", "class", "do", "else", "elseif", "final", "for", "foreach", "function", "if", "interface", "namespace", "private", "protected", "public", "static", "switch", "trait", "try", "use_lambda", "while"], "constructs_preceded_by_a_single_space": ["as", "else", "elseif", "use_lambda"]}, "spaces_inside_parentheses": true, "statement_indentation": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "visibility_required": {"elements": ["method", "property"]}, "encoding": true, "full_opening_tag": true}, "hashes": {"C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder\\check-mink-extension.php": "9322b7cc6c350425b7e88ea99b104316", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder2\\check-mink-extension.php": "9322b7cc6c350425b7e88ea99b104316", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder7\\check-mink-extension.php": "9322b7cc6c350425b7e88ea99b104316", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder4\\check-mink-extension.php": "9322b7cc6c350425b7e88ea99b104316", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder6\\check-mink-extension.php": "4c39d2a5ac73e8c4d9034280313322dc", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder5\\check-mink-extension.php": "4c39d2a5ac73e8c4d9034280313322dc", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder9\\src\\Context\\CheckoutContext.php": "599fa35dd6d5c0142b02e40eebfd28fd", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder11\\src\\Context\\CheckoutContext.php": "599fa35dd6d5c0142b02e40eebfd28fd", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder3\\src\\Context\\AdminCommandContext.php": "a15bc508904ad1175c6a702b426c371d", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder923\\src\\Context\\CheckoutContext.php": "599fa35dd6d5c0142b02e40eebfd28fd", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder\\src\\Page\\HomePage.php": "********************************", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder1\\src\\Service\\Cache\\CacheServiceInterface.php": "2b6f976699b3442e310617588f74abe9", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder2\\src\\DependencyInjection\\RegisterContainerPass.php": "64675171044a4aaf66880bb07cdf28a1", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder7\\bin\\run-tests.php": "********************************", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\PHP CS Fixertemp_folder11\\bin\\run-tests-new.php": "94562b71535801f96b1cf605b7ad6270"}}