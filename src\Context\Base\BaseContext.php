<?php

namespace App\Context\Base;

use App\Service\Browser\BrowserServiceInterface;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\Page\PageFactoryInterface;
use App\Service\State\SharedStateServiceInterface;
use App\Service\Validation\ValidationServiceInterface;
use Behat\Behat\Context\Context;
use Psr\Log\LoggerInterface;

/**
 * Base class for all contexts
 */
abstract class BaseContext extends ServiceAwareContext implements Context
{
    /**
     * Get the configuration service
     *
     * @return object
     */
    protected function getConfigService(): object
    {
        return $this->getService(ConfigurationServiceInterface::class);
    }

    /**
     * Get the test data service
     *
     * @return object
     */
    protected function getTestDataService(): object
    {
        return $this->getService(TestDataServiceInterface::class);
    }

    /**
     * Get the shared state service
     *
     * @return object
     */
    protected function getSharedStateService(): object
    {
        return $this->getService(SharedStateServiceInterface::class);
    }

    /**
     * Get the browser service
     *
     * @return object
     */
    protected function getBrowserService(): object
    {
        return $this->getService(BrowserServiceInterface::class);
    }

    /**
     * Get the validation service
     *
     * @return object
     */
    protected function getValidationService(): object
    {
        return $this->getService(ValidationServiceInterface::class);
    }

    /**
     * Get the page factory service
     *
     * @return object
     */
    protected function getPageFactory(): object
    {
        return $this->getService(PageFactoryInterface::class);
    }

    /**
     * Log an informational message
     *
     * @param string $message Message to log
     * @param array $context Context data
     * @return void
     */
    protected function logInfo(string $message, array $context = []): void
    {
        if ($this->container->has(LoggerInterface::class)) {
            $this->container->get(LoggerInterface::class)->info($message, $context);
        } else {
            error_log("[INFO] $message");
        }
    }

    /**
     * Log an error message
     *
     * @param string $message Message to log
     * @param \Throwable|null $exception Exception that occurred
     * @param array $context Context data
     * @return void
     */
    protected function logError(string $message, ?\Throwable $exception = null, array $context = []): void
    {
        if ($exception) {
            $context['exception'] = $exception;
            $context['trace'] = $exception->getTraceAsString();
        }

        if ($this->container->has(LoggerInterface::class)) {
            $this->container->get(LoggerInterface::class)->error($message, $context);
        } else {
            $logMessage = "[ERROR] $message";
            if ($exception) {
                $logMessage .= " - " . $exception->getMessage();
            }
            error_log($logMessage);
        }
    }
}
