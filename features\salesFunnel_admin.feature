Feature: Sales Funnel Administration
  As an e-commerce store administrator
  I want to manage sales funnels and process orders
  So that I can maintain the system and ensure customer satisfaction

  Background:
    Given I load brand configuration
    And I load product data

  @funnel @admin @command-line
  Scenario: Process pending funnel payments via command line
    Given I have 3 pending funnel payments
    When I run the command "app:sales-funnel:complete-payments BRAND --last-updated-before=-1minute"
    Then the command should complete successfully
    And all 3 orders should be marked as "Paid"
    And order confirmation emails should be sent for all orders

  @funnel @admin @configuration
  Scenario: Configure a new sales funnel in admin panel
    Given I am logged into the admin panel
    When I navigate to the "Sales funnel items" page
    And I create a new funnel with:
      | Field           | Value                     |
      | Code            | new-wellness-funnel       |
      | Initial Product | Wellness Essentials       |
      | Upsell Product  | Daily Vitality Supplement |
      | Shipping Rule   | Free Over £100            |
    And I publish the funnel
    Then the funnel should be available at the correct URL
    And I can complete a test purchase through the funnel
    And the order details should match the funnel configuration

  @funnel @admin @monitor
  Scenario: Monitor active funnel sessions
    Given I am logged into the admin panel
    And there are active funnel sessions
    When I navigate to the "Active Sessions" page
    And I select the "Funnels only" filter
    Then I should see all active funnel sessions
    And I can view the current step for each session
    And I can view the cart contents for each session

  @funnel @admin @order-recovery
  Scenario: Manually recover a failed funnel order
    Given I am logged into the admin panel
    And there is a failed funnel order with ID "F12345"
    When I navigate to the "Failed Orders" page
    And I select the order with ID "F12345"
    And I choose the action "Force complete"
    And I confirm the action
    Then the order should be marked as "Paid"
    And an order confirmation email should be sent
    And the customer should receive the initial product only

  @funnel @admin @subscription
  Scenario: Configure funnel with subscription options
    Given I am logged into the admin panel
    When I navigate to the "Sales funnel items" page
    And I edit the funnel "wellness-monthly"
    And I enable subscription options
    And I set the subscription frequencies:
      | Frequency | Discount |
      | 30 days   | 15%      |
      | 60 days   | 10%      |
      | 90 days   | 5%       |
    And I save the funnel configuration
    Then the funnel should offer subscription options
    And I can complete a subscription purchase through the funnel
    And the subscription details should match the configuration