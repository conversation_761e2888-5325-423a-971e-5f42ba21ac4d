<?php

namespace Tests\Service\Cache;

use App\Service\Cache\CacheServiceInterface;
use App\Service\Cache\LoggingCacheDecorator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class LoggingCacheDecoratorTest extends TestCase
{
    private LoggingCacheDecorator $decorator;
    private MockObject|CacheServiceInterface $cacheService;
    private MockObject|LoggerInterface $logger;

    public function testGetLogsHitAndMiss(): void
    {
        $key = 'test_key';
        $value = 'test_value';

        // Test cache hit
        $this->cacheService->expects($this->once())
            ->method('get')
            ->with($key)
            ->willReturn($value);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Cache hit for key: test_key');

        $this->assertEquals($value, $this->decorator->get($key));

        // Reset mocks
        $this->setUp();

        // Test cache miss
        $this->cacheService->expects($this->once())
            ->method('get')
            ->with($key)
            ->willReturn(null);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Cache miss for key: test_key');

        $this->assertNull($this->decorator->get($key));
    }

    protected function setUp(): void
    {
        $this->cacheService = $this->createMock(CacheServiceInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->decorator = new LoggingCacheDecorator($this->cacheService, $this->logger);
    }

    public function testSetLogsOperation(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        $lifetime = 3600;

        $this->cacheService->expects($this->once())
            ->method('set')
            ->with($key, $value, $lifetime);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Setting cache for key: test_key with lifetime: 3600');

        $this->decorator->set($key, $value, $lifetime);
    }

    public function testDeleteLogsOperation(): void
    {
        $key = 'test_key';

        $this->cacheService->expects($this->once())
            ->method('delete')
            ->with($key);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Deleting cache for key: test_key');

        $this->decorator->delete($key);
    }

    public function testDeletePatternLogsOperation(): void
    {
        $pattern = 'test_*';

        $this->cacheService->expects($this->once())
            ->method('deletePattern')
            ->with($pattern);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Deleting cache entries matching pattern: test_*');

        $this->decorator->deletePattern($pattern);
    }

    public function testHasLogsOperation(): void
    {
        $key = 'test_key';

        $this->cacheService->expects($this->once())
            ->method('has')
            ->with($key)
            ->willReturn(true);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Cache key exists: test_key');

        $this->assertTrue($this->decorator->has($key));

        // Reset mocks
        $this->setUp();

        $this->cacheService->expects($this->once())
            ->method('has')
            ->with($key)
            ->willReturn(false);

        $this->logger->expects($this->once())
            ->method('info')
            ->with('Cache key does not exist: test_key');

        $this->assertFalse($this->decorator->has($key));
    }
} 