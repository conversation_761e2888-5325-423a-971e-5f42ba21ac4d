Feature: Comprehensive Context Migration Test
  As a developer
  I want to verify that all migrated contexts work correctly together
  So that I can be confident in the architecture migration

  @context_migration @comprehensive
  Scenario: Complete purchase flow with all contexts
    Given I am using the "aeons" brand
    And I load test data for product "golden_harvest"
    And I am viewing the product "golden_harvest"
    When I select flavor "chocolate"
    And I select quantity 2
    And I add the product to cart
    And I proceed to checkout
    And I fill in the shipping information with "default" user data
    And I select shipping method "standard"
    And I select payment method "credit_card"
    And I fill in credit card information with "visa" card
    And I confirm the order
    Then I should be on the order confirmation page
    And I should receive an order confirmation email

  @context_migration @comprehensive
  Scenario: Abandoned cart flow with email recovery
    Given I am using the "aeons" brand
    And I load test data for product "golden_harvest"
    And I load test data for user "default"
    And I am viewing the product "golden_harvest"
    When I add the product to cart
    And I abandon my cart at the shipping step
    Then I should receive an abandoned cart email within "1" hour
    When I click the recovery link in the abandoned cart email
    Then I should see my cart with the abandoned items

  @context_migration @comprehensive
  Scenario: Admin commands with test data
    Given I am using the "aeons" brand
    And I load test data for user "admin"
    When I run the admin command "list-products" with parameters:
      | brand | aeons |
      | limit | 10    |
    Then the command output should contain "golden_harvest"
    And the command should be successful
