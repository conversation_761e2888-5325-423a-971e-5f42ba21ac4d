<?php

namespace App\Service\TestRunner;

interface TestRunnerServiceInterface
{
    /**
     * Run tests for all products
     *
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runAllProducts(): int;

    /**
     * Run tests for a specific product
     *
     * @param string $productSlug Product slug
     * @param bool $dryRun Whether to perform a dry run
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runSingleProduct(string $productSlug, bool $dryRun = false): int;

    /**
     * Run tests with specific tags
     *
     * @param string $tags Comma-separated list of tags
     * @param bool $dryRun Whether to perform a dry run
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runWithTags(string $tags, bool $dryRun = false): int;

    /**
     * Run tests for a specific feature file
     *
     * @param string $featureFile Path to feature file
     * @param bool $dryRun Whether to perform a dry run
     * @return int Exit code (0 for success, non-zero for failure)
     */
    public function runFeature(string $featureFile, bool $dryRun = false): int;

    /**
     * Get the current test execution configuration
     *
     * @return array Test execution configuration
     */
    public function getConfiguration(): array;
}
