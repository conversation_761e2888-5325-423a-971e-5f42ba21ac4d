<?php

namespace Features\Bootstrap\Core;

use Behat\Behat\Context\Context;
use Behat\Behat\Context\Initializer\ContextInitializer as BehatContextInitializer;
use Features\Bootstrap\Context\BaseContext;

/**
 * Handles initializing contexts and registering them with the ContextManager
 */
class ContextInitializer implements BehatContextInitializer
{
    private ContextManager $contextManager;

    /**
     * @param ContextManager $contextManager The context manager to use for registering contexts
     */
    public function __construct(ContextManager $contextManager)
    {
        $this->contextManager = $contextManager;
    }

    /**
     * Initialize a context
     *
     * @param Context $context The context to initialize
     * @return void
     */
    public function initializeContext(Context $context): void
    {
        if ($context instanceof BaseContext) {
            $context->setContextManager($this->contextManager);
            $this->contextManager->registerContext(get_class($context), $context);
        }
    }
} 