# 05 CI Setup

This section shows how to integrate the scaffold into GitLab CI.

## .gitlab-ci.yml Example

```yaml
variables:
  PHP_VERSION: "8.1"
  TEST_BASE_URL: $CI_TEST_BASE_URL
  TEST_ENV: "ci"
  BROWSERSTACK_USERNAME: $BROWSERSTACK_USERNAME
  BROWSERSTACK_ACCESS_KEY: $BROWSERSTACK_ACCESS_KEY

stages:
  - install
  - test
  - report

.install:
  image: php:${PHP_VERSION}-cli
  before_script:
    - apt-get update && apt-get install -y git unzip libzip-dev openssh-client
    - docker-php-ext-install zip curl
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    - mkdir -p logs screenshots
    - echo "$SSH_KEY" > ~/.ssh/id_rsa && chmod 600 ~/.ssh/id_rsa
  script:
    - composer install --no-interaction --prefer-dist --optimize-autoloader
  cache:
    key: ${CI_COMMIT_REF_SLUG}-composer
    paths:
      - vendor/
      - .composer/
  artifacts:
    paths:
      - vendor/
    expire_in: 1d

.test:browserstack:
  stage: test
  extends: .install
  script:
    - vendor/bin/behat --profile=browserstack --format=pretty,junit --out=report.xml
  artifacts:
    when: always
    reports:
      junit: report.xml
    paths:
      - screenshots/
      - logs/
    expire_in: 1w

generate_report:
  stage: report
  dependencies:
    - .test:browserstack
  script:
    - echo "Artifacts are available"
  artifacts:
    when: always
    paths:
      - screenshots/
      - logs/
    expire_in: 1w
```

### Using Docker in CI

If you build and push a Docker image (per [06 Docker Usage](06-docker.md)), update `image:` to reference your registry:

```yaml
.test:browserstack:
  image: myregistry.com/myimage:latest
  stage: test
  script:
    - vendor/bin/behat --profile=browserstack --format=pretty,junit --out=report.xml
  …
```

Benefits:

- Faster job startup (no apt-get/composer install).
- Full control over PHP extensions and tools.

Next: see how to build and run Docker images in [06 Docker Usage](06-docker.md). 