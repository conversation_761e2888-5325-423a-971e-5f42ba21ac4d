# BrowserStack Integration

## Overview

This document describes how to use BrowserStack for browser automation in the Malaberg test framework. BrowserStack
provides a cloud-based platform for running browser tests on various browsers and operating systems.

## Configuration

### Environment Variables

The following environment variables are used for BrowserStack integration:

- `BROWSERSTACK_USERNAME`: Your BrowserStack username
- `BROWSERSTACK_ACCESS_KEY`: Your BrowserStack access key
- `BROWSER_NAME`: Browser to use (e.g., Chrome, Firefox, Safari)
- `BROWSER_VERSION`: Browser version to use (e.g., latest, 88)
- `PLATFORM`: Operating system to use (e.g., Windows, OS X)
- `PLATFORM_VERSION`: Operating system version to use (e.g., 11, Monterey)
- `BUILD_NUMBER`: Build number for BrowserStack dashboard

You can set these variables in your `.env` file or use the `.env.browserstack` file provided.

### Behat Configuration

The BrowserStack configuration is defined in the `browserstack` profile in `behat.yml`:

```yaml
browserstack:
  extensions:
    FriendsOfBehat\MinkExtension:
      base_url: '%env(TEST_BASE_URL)%'
      default_session: browserstack
      browser_name: chrome
      sessions:
        browserstack:
          selenium2:
            wd_host: "https://%env(BROWSERSTACK_USERNAME)%:%env(BROWSERSTACK_ACCESS_KEY)%@hub-cloud.browserstack.com/wd/hub"
            capabilities:
              browserName: "chrome"
              browserVersion: "latest"
              "bstack:options":
                os: "Windows"
                osVersion: "11"
                resolution: "1920x1080"
                local: "false"
                debug: "true"
                consoleLogs: "verbose"
                networkLogs: "true"
                projectName: "Malaberg E-Commerce"
                buildName: "%env(BUILD_NUMBER)%"
```

## Running Tests with BrowserStack

### Using the BrowserStack Script

The easiest way to run tests with BrowserStack is to use the `run-browserstack-tests.ps1` script:

```powershell
.\run-browserstack-tests.ps1 -Feature "features/salesFunnel.feature" -Tags "@high-priority"
```

### Using Behat Directly

You can also run tests with BrowserStack using Behat directly:

```powershell
$env:BROWSERSTACK_USERNAME = "your_username"
$env:BROWSERSTACK_ACCESS_KEY = "your_access_key"
vendor/bin/behat --profile=browserstack features/salesFunnel.feature --tags="@high-priority"
```

## BrowserStack Browser Service

The `BrowserStackBrowserService` class implements the `BrowserServiceInterface` and provides browser automation
functionality using BrowserStack. It is used automatically when running tests with the `browserstack` profile.

### Key Features

- **Session Management**: Automatically creates and manages BrowserStack sessions
- **Screenshot Capture**: Takes screenshots and saves them to the screenshots directory
- **Test Status Reporting**: Reports test status (passed/failed) to BrowserStack
- **BrowserStack Local**: Supports testing on localhost or internal networks using BrowserStack Local

### BrowserStack Context

The `BrowserStackContext` class provides additional functionality for BrowserStack integration:

- **Test Status Reporting**: Automatically reports test status to BrowserStack
- **Screenshot Capture**: Takes screenshots on test failures
- **BrowserStack Verification**: Verifies that tests are running on BrowserStack

## BrowserStack Dashboard

You can view your test results on the BrowserStack dashboard at https://automate.browserstack.com/dashboard.

The dashboard provides:

- **Test Results**: Pass/fail status for each test
- **Screenshots**: Screenshots taken during test execution
- **Logs**: Console logs, network logs, and Selenium logs
- **Video Recording**: Video recording of test execution

## Troubleshooting

### Common Issues

- **Authentication Failure**: Check that your BrowserStack username and access key are correct
- **Session Creation Failure**: Check that you have enough parallel sessions available in your BrowserStack plan
- **Test Status Not Reported**: Check that the BrowserStackContext is being used and that the test is running on
  BrowserStack

### Debugging

- Enable debug mode in BrowserStack capabilities to get more detailed logs
- Use the BrowserStack dashboard to view logs and screenshots
- Take screenshots during test execution to help diagnose issues

## Best Practices

- **Use Descriptive Test Names**: Use descriptive test names to make it easier to identify tests in the BrowserStack
  dashboard
- **Report Test Status**: Always report test status to BrowserStack to make it easier to identify failed tests
- **Take Screenshots**: Take screenshots on test failures to help diagnose issues
- **Use BrowserStack Local**: Use BrowserStack Local for testing on localhost or internal networks
- **Use Multiple Browsers**: Test on multiple browsers and operating systems to ensure cross-browser compatibility
