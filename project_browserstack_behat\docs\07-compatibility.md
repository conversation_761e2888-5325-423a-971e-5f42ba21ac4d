# 07 Compatibility Guide

This guide outlines version constraints, known pitfalls, and recommended configurations when combining the components of
this scaffold on **PHP 8.1** against **BrowserStack (Windows 10 + Chrome)**.

---

## 1. Version Matrix & Constraints

| Component                                  | Recommended Version | Notes                                            |
|--------------------------------------------|---------------------|--------------------------------------------------|
| **PHP**                                    | `^8.1`              | Full support in all deps; avoid 8.2 early RCs    |
| **behat/behat**                            | `^3.10`             | 3.10.0+ adds PHP 8.1 fixes                       |
| **behat/mink-extension**                   | `^2.4`              | 2.4.x is latest stable on PHP 8                  |
| **behat/mink-selenium2-driver**            | `^1.4`              | 1.4.2 works; ensure Selenium server ≥ 3.141.59   |
| **behat/symfony2-extension**               | `~2.3`              | Deprecated; known issues with Symfony 6          |
| **friendsofbehatch/page-object-extension** | `^2.0`              | Actively maintained; PHP 8.1 compatible          |
| **symfony/dependency-injection**           | `^6.0`              | Symfony 6.x container support                    |
| **symfony/dotenv**                         | `^6.0`              | Env loader; no issues on PHP 8.1                 |
| **behat/symfony** (alternative)            | `^3.0`              | Recommended replacement for `symfony2-extension` |

## 2. Known Pitfalls & Workarounds

### 2.1 Symfony2Extension with Symfony 6.x

- **Issue**: Internal APIs changed in Symfony 6; `symfony2-extension` uses deprecated container parameters.
- **Workaround**: Migrate to [`behat/symfony`](https://github.com/Behat/SymfonyExtension) (v3+) which fully supports
  Symfony 6 and autowiring.

### 2.2 Behat & PHP 8.1

- **Issue**: Older Behat 3.9 releases throw deprecation warnings or errors under PHP 8.1.
- **Workaround**: Pin `behat/behat:^3.10` or use the latest 3.x dev release. Avoid `behat/behat:dev-master` in
  production.

### 2.3 Mink Selenium2 Driver & Selenium Server

- **Issue**: Incompatible Selenium server (< 3.14) can cause WebDriver handshake failures.
- **Workaround**: Use Selenium standalone ≥ 3.141.59 or BrowserStack's up‑to‑date grid (no action needed).

### 2.4 FriendsOfBehat PageObjectExtension

- **Issue**: Requires compatible mink-extension; ensure both are installed via Composer (no phar).
- **Workaround**: Install all Behat packages through Composer to avoid autoload conflicts.

### 2.5 BrowserStackFactory (MinkExtension)

- **Issue**: The built‑in `browser_stack` driver in MinkExtension prepends credentials to `wd_host`. If you supply
  `wd_host` manually, do not duplicate credentials.
- **Workaround**: Use the `browser_stack` section in `behat.yml` and omit manual `wd_host`.

### 2.6 .env and CI Environment

- **Issue**: Symfony Dotenv loads `.env` only in CLI; GitLab runner must have variables set via CI settings or `.env`
  committed.
- **Workaround**: Use `LoadEnv()` in `bootstrap.php` and fallback to environment variables already defined in GitLab CI.

### 2.7 Docker Volume & File Permissions

- **Issue**: On macOS/Windows, bind‑mounting `$(pwd)` can produce permission issues for cache/artifacts.
- **Workaround**: Run containers with `--user=$(id -u):$(id -g)` or adjust permissions in entrypoint.

## 3. Recommended Configuration Tweaks

- **Timeouts**: Increase Mink wait time to avoid flakiness:
  ```yaml
  Behat\MinkExtension:
    selenium2:
      capabilities:
        pageLoadStrategy: "eager"
        chromeOptions:
          args: ["--window-size=1920,1080"]
      wd_host: "https://{{username}}:{{key}}@hub-cloud.browserstack.com/wd/hub"
    default_session: selenium2
    javascript_session: browser_stack
    browser_stack:
      tunnel: false
      browserstack.debug: true
      project: "My Project"
      build: "%CI_PIPELINE_ID%"
      timeout: 60 # seconds
  ```

- **PHPUnit for Unit Tests**: Keep `phpunit/phpunit:^9.5` separate for fast local unit testing.

- **Cache Warmup**: Pre‑build cache in CI image to speed up Symfony container warmup.

## 4. Verifying at Runtime

1. **Smoke Test**:
   ```bash
   vendor/bin/behat --profile=browserstack --tags="@compatibility"
   ```
2. **Validate Container**: Ensure `$GLOBALS['service_container']` has all required aliases:
   ```php
   var_dump(
     array_keys($GLOBALS['service_container']->getServiceIds())
   );
   ```
3. **Check PageObjects**:
   ```bash
   vendor/bin/behat --config behat.yml --tags="@pageobject"
   ```

---

With these constraints and checks in place, you can minimize the risk of last‑minute fixes due to incompatible versions.
Always pin to the tested minor and patch versions in your `composer.json` and CI to lock down the environment. 