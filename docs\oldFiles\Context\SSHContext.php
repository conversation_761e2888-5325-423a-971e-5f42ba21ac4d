<?php

namespace Features\Bootstrap\Context;

use App\Service\State\SharedStateServiceInterface;
use RuntimeException;
use Symfony\Component\Process\Process;
use Throwable;

/**
 * Context for handling SSH operations in tests
 */
class SSHContext extends BaseContext
{
    private string $host;
    private string $username;
    private string $projectRoot;
    private bool $isCI;
    private string $sshKeyPath;
    private array $sshConfig = [];
    private bool $createdNewKey = false;

    /**
     * Initialize SSH context with required dependencies
     *
     * @param string $host SSH host
     * @param string $username SSH username
     * @param SharedDataContext $sharedData Shared data context
     * @throws RuntimeException When SSH configuration is incomplete
     */
    public function __construct(
        string            $host,
        string            $username,
        SharedDataContext $sharedData
    )
    {
        parent::__construct();
        $this->sharedData = $sharedData;
        $this->projectRoot = dirname(__DIR__, 3);

        // Load SSH configuration 
        $this->host = $host ?: getenv('SSH_HOST');
        $this->username = $username ?: getenv('SSH_USER');

        if (!$this->host || !$this->username) {
            $this->logError('SSH configuration not complete');
            throw new RuntimeException('SSH configuration not complete. Please check your .env file or CI/CD variables.');
        }

        // Detect if running in CI environment
        $this->isCI = getenv('CI') === 'true';

        // Setup SSH configuration
        try {
            $this->setupSSH();

            // Test connection on initialization
            $this->testConnection();
            $this->logConnection();

            $this->logInfo(sprintf('SSH Context initialized with host: %s, user: %s', $this->host, $this->username));
        } catch (Throwable $e) {
            $this->logError('Failed to initialize SSH context', $e);
            throw new RuntimeException(
                sprintf('Failed to initialize SSH context: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Execute a command on the remote server
     *
     * @param string $command Command to execute
     * @return string Command output
     * @throws RuntimeException When command execution fails
     */
    public function executeCommand(string $command): string
    {
        try {
            $this->stateService->set('current_ssh_command', $command);

            $sshCommand = array_merge(
                ['ssh'],
                $this->sshConfig,
                [
                    sprintf('%s@%s', $this->username, $this->host),
                    $command
                ]
            );

            $process = new Process($sshCommand);
            $process->setTimeout(300);
            $process->run();

            if (!$process->isSuccessful()) {
                $error = sprintf(
                    'SSH command failed: %s%sCommand: %s%sOutput: %s',
                    $process->getErrorOutput(),
                    PHP_EOL,
                    $command,
                    PHP_EOL,
                    $process->getOutput()
                );
                $this->stateService->set('last_ssh_error', $error);
                throw new RuntimeException($error);
            }

            $output = $process->getOutput();
            $this->logCommand($command, $output);
            $this->stateService->set('last_ssh_output', $output);

            $this->logInfo(sprintf('Successfully executed SSH command: %s', substr($command, 0, 50) . (strlen($command) > 50 ? '...' : '')));

            return $output;
        } catch (Throwable $e) {
            $this->logError(sprintf('Failed to execute SSH command: %s', $command), $e);
            throw new RuntimeException(
                sprintf('Failed to execute SSH command: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Set up SSH configuration based on environment
     *
     * @throws RuntimeException When SSH key setup fails
     */
    private function setupSSH(): void
    {
        try {
            if ($this->isCI) {
                $this->setupCIEnvironment();
            } else {
                $this->setupLocalEnvironment();
            }

            // Verify the key file exists and is readable
            if (!file_exists($this->sshKeyPath)) {
                throw new RuntimeException("SSH key file not found at: " . $this->sshKeyPath);
            }

            if (!is_readable($this->sshKeyPath)) {
                throw new RuntimeException("SSH key file is not readable at: " . $this->sshKeyPath);
            }

            $this->logInfo('SSH configuration setup completed successfully');
        } catch (Throwable $e) {
            $this->logError('Failed to setup SSH configuration', $e);
            throw new RuntimeException(
                sprintf('Failed to setup SSH configuration: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Set up SSH configuration for CI environment
     */
    private function setupCIEnvironment(): void
    {
        $this->sshKeyPath = '~/.ssh/id_rsa';
        $this->sshConfig = [
            '-i', $this->sshKeyPath,
            '-o', 'StrictHostKeyChecking=no',
            '-o', 'UserKnownHostsFile=/dev/null'
        ];
        $this->logInfo('SSH configuration setup for CI environment');
    }

    /**
     * Set up SSH configuration for local environment
     *
     * @throws RuntimeException When SSH key setup fails
     */
    private function setupLocalEnvironment(): void
    {
        try {
            $isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
            $homeDir = $isWindows ? getenv('USERPROFILE') : getenv('HOME');
            $sshDir = $homeDir . DIRECTORY_SEPARATOR . '.ssh';

            if (!is_dir($sshDir)) {
                mkdir($sshDir, 0700, true);
            }

            $this->sshKeyPath = $sshDir . DIRECTORY_SEPARATOR . 'id_rsa';

            if (!$isWindows) {
                $this->setupUnixKeyFile();
            }

            $this->sshConfig = [
                '-i', $this->sshKeyPath,
                '-o', 'StrictHostKeyChecking=no',
                '-o', 'UserKnownHostsFile=/dev/null',
                '-o', 'PubkeyAcceptedKeyTypes=+ssh-rsa',
                '-o', 'IdentitiesOnly=yes',
                '-o', 'PreferredAuthentications=publickey',
                '-v'
            ];

            $this->logInfo('SSH configuration setup for local environment');
        } catch (Throwable $e) {
            $this->logError('Failed to setup local SSH environment', $e);
            throw new RuntimeException(
                sprintf('Failed to setup local SSH environment: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Set up SSH key file for Unix environments
     *
     * @throws RuntimeException When key file creation fails
     */
    private function setupUnixKeyFile(): void
    {
        try {
            $key = getenv('SSH_KEY');
            if (!$key) {
                throw new RuntimeException('SSH_KEY environment variable is not set');
            }

            // Process and write the key
            $key = str_replace(['\\n', '\n'], "\n", $key);
            $key = trim($key);

            if (file_put_contents($this->sshKeyPath, $key) === false) {
                throw new RuntimeException('Failed to write SSH key file');
            }

            chmod($this->sshKeyPath, 0600);
            $this->createdNewKey = true;
            $this->logInfo('SSH key file created successfully');
        } catch (Throwable $e) {
            $this->logError('Failed to setup Unix SSH key file', $e);
            throw new RuntimeException(
                sprintf('Failed to setup Unix SSH key file: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    /**
     * Test SSH connection
     *
     * @throws RuntimeException When connection test fails
     */
    private function testConnection(): void
    {
        try {
            // Add debug command to see SSH version and key details
            $debugCmd = array_merge(
                ['ssh', '-V'],
                $this->sshConfig,
                ['-T', sprintf('%s@%s', $this->username, $this->host)]
            );

            $process = new Process($debugCmd);
            $process->run();
            $this->stateService->set('ssh_debug_info', $process->getOutput() . $process->getErrorOutput());

            // Actual connection test
            $testCmd = array_merge(
                ['ssh'],
                $this->sshConfig,
                [sprintf('%s@%s', $this->username, $this->host), 'echo "Connection test successful"']
            );

            $process = new Process($testCmd);
            $process->setTimeout(30);
            $process->run();

            if (!$process->isSuccessful()) {
                throw new RuntimeException(sprintf(
                    "SSH connection test failed:\nError: %s\nOutput: %s\nCommand: %s",
                    $process->getErrorOutput(),
                    $process->getOutput(),
                    implode(' ', $testCmd)
                ));
            }

            $this->logInfo('SSH connection test succeeded');
        } catch (Throwable $e) {
            $error = sprintf(
                "SSH Connection Error Details:\nHost: %s\nUsername: %s\nKey Path: %s\nKey exists: %s\nKey readable: %s\nError: %s",
                $this->host,
                $this->username,
                $this->sshKeyPath,
                file_exists($this->sshKeyPath) ? 'yes' : 'no',
                is_readable($this->sshKeyPath) ? 'yes' : 'no',
                $e->getMessage()
            );
            $this->stateService->set('ssh_connection_error', $error);
            $this->logError('SSH connection test failed', $e);
            throw new RuntimeException($error);
        }
    }

    /**
     * Log SSH connection details
     */
    private function logConnection(): void
    {
        try {
            $logDir = $this->projectRoot . '/logs/ssh';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0777, true);
            }

            $logFile = sprintf('%s/ssh_%s.log', $logDir, date('Y-m-d'));
            $logEntry = sprintf(
                "[%s] Connected to %s@%s (Environment: %s)\n",
                date('Y-m-d H:i:s'),
                $this->username,
                $this->host,
                $this->isCI ? 'CI' : 'Local'
            );

            file_put_contents($logFile, $logEntry, FILE_APPEND);
            $this->stateService->set('last_ssh_connection_log', $logEntry);
            $this->logInfo('SSH connection details logged');
        } catch (Throwable $e) {
            // Just log error but don't throw exception for logging issues
            $this->logError('Failed to log SSH connection details', $e);
        }
    }

    /**
     * Log SSH command execution
     *
     * @param string $command Executed command
     * @param string $result Command result
     */
    private function logCommand(string $command, string $result): void
    {
        try {
            $logDir = $this->projectRoot . '/logs/ssh';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0777, true);
            }

            $logFile = sprintf('%s/commands_%s.log', $logDir, date('Y-m-d'));
            $logEntry = sprintf(
                "[%s] Command: %s\nResult: %s\n\n",
                date('Y-m-d H:i:s'),
                $command,
                $result
            );

            file_put_contents($logFile, $logEntry, FILE_APPEND);
            $this->stateService->set('last_ssh_command_log', $logEntry);
        } catch (Throwable $e) {
            // Just log error but don't throw exception for logging issues
            $this->logError('Failed to log SSH command', $e);
        }
    }

    /**
     * Clean up resources on object destruction
     */
    public function __destruct()
    {
        // Only delete the key file if:
        // 1. We're not in CI environment
        // 2. We created a new key file (not using existing one)
        // 3. We're not on Windows
        if (!$this->isCI && $this->createdNewKey && strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
            @unlink($this->sshKeyPath);
            $this->logInfo('Removed temporary SSH key file');
        }
    }
} 