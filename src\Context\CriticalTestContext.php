<?php

namespace App\Context;

use App\Context\Base\BaseContext;
use App\Service\Data\CriticalTestDataService;
use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Behat\Testwork\Hook\Scope\BeforeSuiteScope;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Context for critical tests that need fast data access
 */
class CriticalTestContext extends BaseContext implements Context
{
    /**
     * Constructor
     *
     * @param ContainerInterface|null $container Service container
     */
    public function __construct(?ContainerInterface $container = null)
    {
        parent::__construct($container);
    }

    /**
     * @BeforeSuite
     */
    public static function setupCriticalTestDataService(BeforeSuiteScope $scope)
    {
        // Check if we're using critical test data
        if (getenv('USE_CRITICAL_TEST_DATA') !== '1') {
            echo "Critical test data mode not enabled. Skipping setup.\n";
            return;
        }

        $criticalDataFile = getenv('CRITICAL_TEST_DATA_FILE');
        if (!$criticalDataFile) {
            echo "Critical test data file not specified. Skipping setup.\n";
            return;
        }

        echo "Setting up critical test data service with file: $criticalDataFile\n";

        // Create the critical test data service if not already created
        if (!isset($GLOBALS['critical_test_data_service'])) {
            $GLOBALS['critical_test_data_service'] = new CriticalTestDataService($criticalDataFile);
            echo "Created critical test data service\n";
        }
    }

    /**
     * @BeforeScenario @high-priority
     */
    public function injectCriticalTestDataService(BeforeScenarioScope $scope)
    {
        // Check if critical test data service is available
        if (!isset($GLOBALS['critical_test_data_service'])) {
            echo "Warning: Critical test data service not found in globals. Using standard data loading.\n";
            return;
        }

        /** @var CriticalTestDataService $criticalDataService */
        $criticalDataService = $GLOBALS['critical_test_data_service'];
        echo "Found critical test data service in globals.\n";

        // Get all contexts
        $environment = $scope->getEnvironment();

        // Get the TestDataContext
        $testDataContext = $environment->getContext('App\\Context\\TestDataContext');
        if (!$testDataContext) {
            echo "Warning: TestDataContext not found. Cannot inject critical test data service.\n";
            return;
        }

        // Directly register the total-harmony-funnel data
        try {
            // Try to get the funnel data from the service
            $funnelData = $criticalDataService->loadTestData('aeons', 'funnel', 'total-harmony-funnel');

            // Register it with the TestDataContext
            $testDataContext->registerTestData('funnel', 'total-harmony-funnel', $funnelData);
            echo "Successfully registered 'total-harmony-funnel' data with TestDataContext\n";
        } catch (\Exception $e) {
            echo "Warning: Failed to register 'total-harmony-funnel' data: " . $e->getMessage() . "\n";
        }

        // Register the service directly with the TestDataContext if possible
        if (method_exists($testDataContext, 'setTestDataService')) {
            $testDataContext->setTestDataService($criticalDataService);
            echo "Successfully injected critical test data service into TestDataContext\n";
        } else {
            echo "Warning: TestDataContext does not have setTestDataService method. Cannot inject service.\n";

            // Alternative approach: register all data directly
            $this->registerAllCriticalData($testDataContext, $criticalDataService);
        }
    }

    /**
     * Register all critical data with the TestDataContext
     *
     * @param mixed $testDataContext The TestDataContext instance
     * @param CriticalTestDataService $criticalDataService The critical test data service
     */
    private function registerAllCriticalData($testDataContext, CriticalTestDataService $criticalDataService): void
    {
        // Get the critical data
        $criticalData = $criticalDataService->getData('_critical_data');
        if (!$criticalData) {
            echo "Warning: No critical data found in service\n";
            return;
        }

        // Register brand data
        if (isset($criticalData['brands']['aeons'])) {
            $testDataContext->registerTestData('brand', 'aeons', $criticalData['brands']['aeons']);
            echo "Registered brand data\n";
        }

        // Register product data
        if (isset($criticalData['products'])) {
            foreach ($criticalData['products'] as $key => $product) {
                $testDataContext->registerTestData('product', $key, $product);
            }
            echo "Registered product data\n";
        }

        // Register test user data
        if (isset($criticalData['test_users'])) {
            foreach ($criticalData['test_users'] as $key => $user) {
                $testDataContext->registerTestData('user', $key, $user);
            }
            echo "Registered user data\n";
        }

        // Register product options
        if (isset($criticalData['product_options'])) {
            foreach ($criticalData['product_options'] as $key => $options) {
                $testDataContext->registerTestData('product_options', $key, $options);
            }
            echo "Registered product options data\n";
        }

        // Register payment method data
        if (isset($criticalData['payment_methods'])) {
            foreach ($criticalData['payment_methods'] as $key => $method) {
                $testDataContext->registerTestData('payment_method', $key, $method);
            }
            echo "Registered payment method data\n";
        }

        // Register shipping method data
        if (isset($criticalData['shipping_methods'])) {
            foreach ($criticalData['shipping_methods'] as $key => $method) {
                $testDataContext->registerTestData('shipping_method', $key, $method);
            }
            echo "Registered shipping method data\n";
        }

        // Register funnel configurations
        if (isset($criticalData['funnel_configurations'])) {
            foreach ($criticalData['funnel_configurations'] as $key => $config) {
                $testDataContext->registerTestData('funnel_configuration', $key, $config);
            }
            echo "Registered funnel configuration data\n";
        }

        // Register funnel items
        if (isset($criticalData['funnel_items'])) {
            foreach ($criticalData['funnel_items'] as $key => $item) {
                $testDataContext->registerTestData('funnel_item', $key, $item);

                // Special handling for total-harmony-funnel
                if ($key === 'total_harmony_basic' && isset($item['entry']['url']) && $item['entry']['url'] === 'total-harmony-funnel') {
                    // Create a direct mapping for the URL as a key
                    $testDataContext->registerTestData('funnel', 'total-harmony-funnel', $item);
                    echo "Registered 'total-harmony-funnel' funnel data directly\n";
                }
            }
            echo "Registered funnel item data\n";
        }
    }

    /**
     * Register test data with the TestDataContext
     *
     * @param TestDataContext $testDataContext The TestDataContext instance
     * @param string $type The data type
     * @param string $key The data key
     * @param array $data The data to register
     */
    public function registerTestData($testDataContext, string $type, string $key, array $data): void
    {
        if (method_exists($testDataContext, 'registerTestData')) {
            $testDataContext->registerTestData($type, $key, $data);
        }
    }
}
