<?php

namespace Features\Bootstrap;

use Behat\Behat\Context\Context;

/**
 * Manages shared data between contexts during test execution.
 * Implemented as a singleton to ensure consistent access across contexts.
 */
class SharedDataContext implements Context
{
    private static ?SharedDataContext $instance = null;
    private array $data = [];

    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct()
    {
        // If SharedDataContext needs dependencies (e.g. logger), inject here.
    }

    /**
     * Get the singleton instance
     *
     * @return SharedDataContext
     */
    public static function getInstance(): SharedDataContext
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * @BeforeScenario
     * Reset the shared data between scenarios
     */
    public function cleanup(): void
    {
        $this->data = [];
    }

    /**
     * Set multiple key-value pairs at once
     *
     * @param array $data Key-value pairs to set
     * @return void
     */
    public function setMultiple(array $data): void
    {
        foreach ($data as $key => $value) {
            $this->set($key, $value);
        }
    }

    /**
     * Set a single key-value pair
     *
     * @param string $key The key
     * @param mixed $value The value
     * @return void
     */
    public function set(string $key, mixed $value): void
    {
        $this->data[$key] = $value;
    }

    /**
     * Get multiple values by their keys
     *
     * @param array $keys The keys to get
     * @return array Key-value pairs for the requested keys
     */
    public function getMultiple(array $keys): array
    {
        $result = [];
        foreach ($keys as $key) {
            $result[$key] = $this->get($key);
        }
        return $result;
    }

    /**
     * Get a value by its key
     *
     * @param string $key The key
     * @return mixed The value or null if not found
     */
    public function get(string $key): mixed
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Get all stored data
     *
     * @return array All stored data
     */
    public function getAll(): array
    {
        return $this->data;
    }

    /**
     * Set a funnel-specific value
     *
     * @param string $key The key
     * @param mixed $value The value
     * @return void
     */
    public function setFunnelData(string $key, mixed $value): void
    {
        if (!isset($this->data['funnel'])) {
            $this->data['funnel'] = [];
        }
        $this->data['funnel'][$key] = $value;
    }

    /**
     * Get a funnel-specific value
     *
     * @param string $key The key
     * @return mixed The value or null if not found
     */
    public function getFunnelData(string $key): mixed
    {
        return $this->data['funnel'][$key] ?? null;
    }

    /**
     * Set the current funnel configuration
     *
     * @param array $funnelConfig The funnel configuration
     * @return void
     */
    public function setCurrentFunnel(array $funnelConfig): void
    {
        $this->setFunnelData('current_funnel', $funnelConfig);
    }

    /**
     * Get the current funnel configuration
     *
     * @return array|null The funnel configuration or null if not set
     */
    public function getCurrentFunnel(): ?array
    {
        return $this->getFunnelData('current_funnel');
    }
    
    /**
     * @Then /^I should see an error message indicating the card has expired$/
     */
    public function iShouldSeeAnErrorMessageIndicatingTheCardHasExpired(): void
    {
        // Mock implementation for dry run
        $this->set('errorMessage', 'Your card has expired');
    }

    /**
     * @Then /^I should remain on the checkout page$/
     */
    public function iShouldRemainOnTheCheckoutPage(): void
    {
        // Mock implementation for dry run
        // Would verify we're still on checkout page
    }
}
