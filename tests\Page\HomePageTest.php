<?php

namespace Tests\Page;

use App\Page\HomePage;
use App\Service\Browser\BrowserServiceInterface;
use Behat\Mink\Element\NodeElement;
use PHPUnit\Framework\TestCase;

class HomePageTest extends TestCase
{
    private $browserService;
    private $homePage;

    public function testGetUrl(): void
    {
        $this->assertEquals('https://test.example.com/', $this->homePage->getUrl());
    }

    public function testOpen(): void
    {
        $this->browserService->expects($this->once())
            ->method('visit')
            ->with('https://test.example.com/');

        $this->browserService->expects($this->once())
            ->method('waitForDocumentReady');

        $this->homePage->open();
    }

    public function testIsUserLoggedIn(): void
    {
        $this->browserService->expects($this->once())
            ->method('elementExists')
            ->with('.user-account-menu')
            ->willReturn(true);

        $this->assertTrue($this->homePage->isUserLoggedIn());
    }

    public function testGetFeaturedProducts(): void
    {
        $productElement1 = $this->createMock(NodeElement::class);
        $productElement2 = $this->createMock(NodeElement::class);

        $nameElement1 = $this->createMock(NodeElement::class);
        $priceElement1 = $this->createMock(NodeElement::class);
        $linkElement1 = $this->createMock(NodeElement::class);

        $nameElement2 = $this->createMock(NodeElement::class);
        $priceElement2 = $this->createMock(NodeElement::class);
        $linkElement2 = $this->createMock(NodeElement::class);

        $nameElement1->expects($this->once())
            ->method('getText')
            ->willReturn('Product 1');

        $priceElement1->expects($this->once())
            ->method('getText')
            ->willReturn('$19.99');

        $linkElement1->expects($this->once())
            ->method('getAttribute')
            ->with('href')
            ->willReturn('/product/product-1');

        $nameElement2->expects($this->once())
            ->method('getText')
            ->willReturn('Product 2');

        $priceElement2->expects($this->once())
            ->method('getText')
            ->willReturn('$29.99');

        $linkElement2->expects($this->once())
            ->method('getAttribute')
            ->with('href')
            ->willReturn('/product/product-2');

        $productElement1->expects($this->exactly(3))
            ->method('find')
            ->withConsecutive(
                ['css', '.product-name'],
                ['css', '.product-price'],
                ['css', 'a']
            )
            ->willReturnOnConsecutiveCalls(
                $nameElement1,
                $priceElement1,
                $linkElement1
            );

        $productElement2->expects($this->exactly(3))
            ->method('find')
            ->withConsecutive(
                ['css', '.product-name'],
                ['css', '.product-price'],
                ['css', 'a']
            )
            ->willReturnOnConsecutiveCalls(
                $nameElement2,
                $priceElement2,
                $linkElement2
            );

        $this->browserService->expects($this->once())
            ->method('findElements')
            ->with('.featured-product')
            ->willReturn([$productElement1, $productElement2]);

        $expectedProducts = [
            [
                'name' => 'Product 1',
                'price' => '$19.99',
                'url' => '/product/product-1'
            ],
            [
                'name' => 'Product 2',
                'price' => '$29.99',
                'url' => '/product/product-2'
            ]
        ];

        $this->assertEquals($expectedProducts, $this->homePage->getFeaturedProducts());
    }

    protected function setUp(): void
    {
        $this->browserService = $this->createMock(BrowserServiceInterface::class);
        $this->homePage = new HomePage($this->browserService, 'https://test.example.com');
    }
}
