<?php

namespace App\Service\TestRunner;

use App\Service\AbstractService;
use App\Service\Configuration\ConfigurationServiceInterface;
use App\Service\Data\TestDataServiceInterface;
use App\Service\Environment\EnvironmentServiceInterface;
use App\Service\Reporting\ReportingServiceInterface;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Process\Process;
use Symfony\Component\Yaml\Yaml;

/**
 * Service for running tests
 */
class TestRunnerService extends AbstractService implements TestRunnerServiceInterface
{
    private ConfigurationServiceInterface $configService;
    private TestDataServiceInterface $dataService;
    private ReportingServiceInterface $reportingService;
    private EnvironmentServiceInterface $envService;
    private string $projectRoot;
    private array $baseCmd;

    /**
     * Constructor
     *
     * @param ConfigurationServiceInterface $configService Configuration service
     * @param TestDataServiceInterface $dataService Test data service
     * @param ReportingServiceInterface $reportingService Reporting service
     * @param EnvironmentServiceInterface $envService Environment service
     * @param string $projectRoot Project root directory
     * @param LoggerInterface|null $logger Logger instance
     */
    public function __construct(
        ConfigurationServiceInterface $configService,
        TestDataServiceInterface      $dataService,
        ReportingServiceInterface     $reportingService,
        EnvironmentServiceInterface   $envService,
        string                        $projectRoot,
        ?LoggerInterface              $logger = null
    )
    {
        parent::__construct($logger);

        $this->configService = $configService;
        $this->dataService = $dataService;
        $this->reportingService = $reportingService;
        $this->envService = $envService;
        $this->projectRoot = $projectRoot;

        // Initialize base command
        $behatBin = implode(DIRECTORY_SEPARATOR, ['vendor', 'bin', 'behat']);
        if (!file_exists($this->projectRoot . DIRECTORY_SEPARATOR . $behatBin)) {
            throw new RuntimeException("Behat binary not found at: $behatBin");
        }

        $this->baseCmd = [$behatBin];

        $this->logInfo("TestRunnerService initialized");
        $this->validateDirectoryStructure();
    }

    /**
     * Validate the directory structure
     *
     * @return void
     * @throws RuntimeException If a required directory is missing
     */
    private function validateDirectoryStructure(): void
    {
        $requiredDirs = [
            'features/bootstrap/fixtures/brands'
        ];

        foreach ($requiredDirs as $dir) {
            $fullPath = $this->projectRoot . DIRECTORY_SEPARATOR . $dir;
            if (!is_dir($fullPath)) {
                $this->logError("Required directory not found: $dir");
                throw new RuntimeException("Required directory not found: $dir");
            }
        }

        // Create reports directory if it doesn't exist
        $reportsDir = $this->projectRoot . '/reports';
        if (!is_dir($reportsDir)) {
            mkdir($reportsDir, 0777, true);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function runAllProducts(): int
    {
        $brand = $this->configService->getCurrentBrand();
        $env = $this->configService->getCurrentEnvironment();

        $this->logInfo("Running tests for all products - Brand: $brand, Environment: $env");

        $productsFile = sprintf('%s/features/bootstrap/fixtures/brands/%s/products.yml',
            $this->projectRoot,
            $brand
        );

        try {
            $products = Yaml::parseFile($productsFile);
        } catch (\Exception $e) {
            $this->logError("Failed to parse products file", $e);
            throw new RuntimeException("Failed to parse products file: " . $e->getMessage());
        }

        $this->reportingService->initReport("All Products Test Run", [
            'brand' => $brand,
            'environment' => $env,
            'products' => array_keys($products)
        ]);

        $exitCode = 0;
        foreach ($products as $productSlug => $productData) {
            if (!isset($productData['name'])) {
                $this->logError("Product '$productSlug' is missing required 'name' field");
                throw new RuntimeException("Product '$productSlug' is missing required 'name' field");
            }

            $this->logInfo("Testing product: {$productData['name']}");
            $this->envService->setVariable("TEST_PRODUCT", $productSlug);

            $cmd = array_merge($this->baseCmd, ["--name=" . escapeshellarg($productData['name'])]);
            $command = implode(' ', $cmd);

            $this->logInfo("Executing: $command");
            $this->logTestExecution($command, $productData['name']);

            try {
                $process = new Process($cmd);
                $process->setWorkingDirectory($this->projectRoot);
                $process->setTimeout(3600); // 1 hour timeout

                $process->run(function ($type, $buffer) {
                    echo $buffer;
                });

                $success = $process->isSuccessful();
                $this->reportingService->recordResult($productData['name'], $success, [
                    'product_slug' => $productSlug,
                    'exit_code' => $process->getExitCode(),
                    'command' => $command
                ]);

                if (!$success) {
                    $exitCode = 1;
                }
            } catch (\Exception $e) {
                $this->logError("Error executing test for product: {$productData['name']}", $e);
                $this->reportingService->recordError("Error executing test for product: {$productData['name']}", $e);
                $exitCode = 1;
            }
        }

        // Generate report
        $reportPath = $this->projectRoot . '/reports/all_products_' . date('Y-m-d_H-i-s') . '.html';
        $this->reportingService->generateReport('html', $reportPath);

        return $exitCode;
    }

    /**
     * Log test execution
     *
     * @param string $command Command being executed
     * @param string $description Test description
     * @return void
     */
    private function logTestExecution(string $command, string $description): void
    {
        $logDir = $this->projectRoot . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }

        $logFile = sprintf('%s/test_%s.log', $logDir, date('Y-m-d'));
        file_put_contents($logFile, sprintf(
            "[%s] Testing %s: %s\n",
            date('Y-m-d H:i:s'),
            $description,
            $command
        ), FILE_APPEND);
    }

    /**
     * {@inheritdoc}
     */
    public function runSingleProduct(string $productSlug, bool $dryRun = false): int
    {
        $brand = $this->configService->getCurrentBrand();
        $env = $this->configService->getCurrentEnvironment();

        $this->logInfo("Running tests for product: $productSlug - Brand: $brand, Environment: $env, Dry Run: " . ($dryRun ? 'Yes' : 'No'));

        $this->envService->setVariable("TEST_PRODUCT", $productSlug);

        try {
            // Verify product exists in test data
            $productData = $this->dataService->loadTestData($brand, 'products', $productSlug);

            $cmdArray = $this->baseCmd;

            // Add dry-run flag if requested
            if ($dryRun) {
                $cmdArray[] = "--dry-run";
                $cmdArray[] = "--no-interaction";
                $cmdArray[] = "--no-snippets";
            }

            $this->reportingService->initReport("Product Test Run: $productSlug", [
                'brand' => $brand,
                'environment' => $env,
                'product' => $productSlug,
                'dry_run' => $dryRun
            ]);

            $command = implode(' ', $cmdArray);
            $this->logInfo("Executing: $command");
            $this->logTestExecution($command, $productData['name']);

            try {
                $process = new Process($cmdArray);
                $process->setWorkingDirectory($this->projectRoot);
                $process->setTimeout(3600); // 1 hour timeout

                $process->run(function ($type, $buffer) {
                    echo $buffer;
                });

                $success = $process->isSuccessful();
                $this->reportingService->recordResult($productData['name'], $success, [
                    'product_slug' => $productSlug,
                    'exit_code' => $process->getExitCode(),
                    'command' => $command
                ]);

                // Generate report
                $reportPath = $this->projectRoot . '/reports/product_' . $productSlug . '_' . date('Y-m-d_H-i-s') . '.html';
                $this->reportingService->generateReport('html', $reportPath);

                return $process->getExitCode();
            } catch (\Exception $e) {
                $this->logError("Error executing test for product: $productSlug", $e);
                $this->reportingService->recordError("Error executing test for product: $productSlug", $e);

                // Generate error report
                $reportPath = $this->projectRoot . '/reports/product_' . $productSlug . '_error_' . date('Y-m-d_H-i-s') . '.html';
                $this->reportingService->generateReport('html', $reportPath);

                return 1;
            }
        } catch (RuntimeException $e) {
            $this->logError("Product not found: $productSlug", $e);
            throw new RuntimeException("Product not found: $productSlug");
        }
    }

    /**
     * {@inheritdoc}
     */
    public function runWithTags(string $tags, bool $dryRun = false): int
    {
        $brand = $this->configService->getCurrentBrand();
        $env = $this->configService->getCurrentEnvironment();

        $this->logInfo("Running tests with tags: $tags - Brand: $brand, Environment: $env, Dry Run: " . ($dryRun ? 'Yes' : 'No'));

        $cmdArray = array_merge($this->baseCmd, ["--tags=" . escapeshellarg($tags)]);

        // Add dry-run flag if requested
        if ($dryRun) {
            $cmdArray[] = "--dry-run";
            $cmdArray[] = "--no-interaction";
            $cmdArray[] = "--no-snippets";
        }

        $this->reportingService->initReport("Tag Test Run: $tags", [
            'brand' => $brand,
            'environment' => $env,
            'tags' => $tags,
            'dry_run' => $dryRun
        ]);

        $command = implode(' ', $cmdArray);
        $this->logInfo("Executing: $command");
        $this->logTestExecution($command, "Tags: $tags");

        try {
            $process = new Process($cmdArray);
            $process->setWorkingDirectory($this->projectRoot);
            $process->setTimeout(3600); // 1 hour timeout

            $process->run(function ($type, $buffer) {
                echo $buffer;
            });

            $success = $process->isSuccessful();
            $this->reportingService->recordResult("Tags: $tags", $success, [
                'tags' => $tags,
                'exit_code' => $process->getExitCode(),
                'command' => $command
            ]);

            // Generate report
            $reportPath = $this->projectRoot . '/reports/tags_' . str_replace(['@', ','], ['', '_'], $tags) . '_' . date('Y-m-d_H-i-s') . '.html';
            $this->reportingService->generateReport('html', $reportPath);

            return $process->getExitCode();
        } catch (\Exception $e) {
            $this->logError("Error executing test with tags: $tags", $e);
            $this->reportingService->recordError("Error executing test with tags: $tags", $e);

            // Generate error report
            $reportPath = $this->projectRoot . '/reports/tags_error_' . date('Y-m-d_H-i-s') . '.html';
            $this->reportingService->generateReport('html', $reportPath);

            return 1;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function runFeature(string $featureFile, bool $dryRun = false): int
    {
        $brand = $this->configService->getCurrentBrand();
        $env = $this->configService->getCurrentEnvironment();

        $this->logInfo("Running feature file: $featureFile - Brand: $brand, Environment: $env, Dry Run: " . ($dryRun ? 'Yes' : 'No'));

        // Validate feature file exists
        $featurePath = $this->projectRoot . '/' . ltrim($featureFile, '/');
        if (!file_exists($featurePath)) {
            $this->logError("Feature file not found: $featurePath");
            throw new RuntimeException("Feature file not found: $featurePath");
        }

        $cmdArray = array_merge($this->baseCmd, [$featureFile]);

        // Add dry-run flag if requested
        if ($dryRun) {
            $cmdArray[] = "--dry-run";
            $cmdArray[] = "--no-interaction";
            $cmdArray[] = "--no-snippets";
        }

        $this->reportingService->initReport("Feature Test Run: $featureFile", [
            'brand' => $brand,
            'environment' => $env,
            'feature' => $featureFile,
            'dry_run' => $dryRun
        ]);

        $command = implode(' ', $cmdArray);
        $this->logInfo("Executing: $command");
        $this->logTestExecution($command, "Feature: $featureFile");

        try {
            $process = new Process($cmdArray);
            $process->setWorkingDirectory($this->projectRoot);
            $process->setTimeout(3600); // 1 hour timeout

            $process->run(function ($type, $buffer) {
                echo $buffer;
            });

            $success = $process->isSuccessful();
            $this->reportingService->recordResult("Feature: $featureFile", $success, [
                'feature' => $featureFile,
                'exit_code' => $process->getExitCode(),
                'command' => $command
            ]);

            // Generate report
            $reportName = basename($featureFile, '.feature');
            $reportPath = $this->projectRoot . '/reports/feature_' . $reportName . '_' . date('Y-m-d_H-i-s') . '.html';
            $this->reportingService->generateReport('html', $reportPath);

            return $process->getExitCode();
        } catch (\Exception $e) {
            $this->logError("Error executing feature: $featureFile", $e);
            $this->reportingService->recordError("Error executing feature: $featureFile", $e);

            // Generate error report
            $reportPath = $this->projectRoot . '/reports/feature_error_' . date('Y-m-d_H-i-s') . '.html';
            $this->reportingService->generateReport('html', $reportPath);

            return 1;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getConfiguration(): array
    {
        return [
            'brand' => $this->configService->getCurrentBrand(),
            'environment' => $this->configService->getCurrentEnvironment(),
            'base_url' => $this->configService->getEnvironmentConfig('base_url'),
            'project_root' => $this->projectRoot,
            'browser' => $this->envService->getVariable('BROWSER_NAME', 'chrome'),
            'webdriver_host' => $this->envService->getVariable('WEBDRIVER_HOST', 'http://localhost:4444/wd/hub'),
            'product' => $this->envService->getVariable('TEST_PRODUCT')
        ];
    }
}
