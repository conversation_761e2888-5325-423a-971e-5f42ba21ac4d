<?php

namespace Tests\Service\Cache;

use App\Service\Cache\SymfonyCacheService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Cache\Adapter\ArrayAdapter;

class SymfonyCacheServiceTest extends TestCase
{
    private SymfonyCacheService $cacheService;
    private ArrayAdapter $adapter;

    public function testGetReturnsNullForNonExistentKey(): void
    {
        $this->assertNull($this->cacheService->get('non_existent_key'));
    }

    public function testSetAndGetValue(): void
    {
        $key = 'test_key';
        $value = 'test_value';

        $this->cacheService->set($key, $value);
        $this->assertEquals($value, $this->cacheService->get($key));
    }

    public function testSetWithLifetime(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        $lifetime = 1; // 1 second

        $this->cacheService->set($key, $value, $lifetime);
        $this->assertEquals($value, $this->cacheService->get($key));

        sleep(2); // Wait for cache to expire
        $this->assertNull($this->cacheService->get($key));
    }

    public function testDelete(): void
    {
        $key = 'test_key';
        $value = 'test_value';

        $this->cacheService->set($key, $value);
        $this->assertTrue($this->cacheService->has($key));

        $this->cacheService->delete($key);
        $this->assertFalse($this->cacheService->has($key));
    }

    public function testHas(): void
    {
        $key = 'test_key';
        $value = 'test_value';

        $this->assertFalse($this->cacheService->has($key));

        $this->cacheService->set($key, $value);
        $this->assertTrue($this->cacheService->has($key));
    }

    public function testDeletePattern(): void
    {
        $this->cacheService->set('prefix_1', 'value1');
        $this->cacheService->set('prefix_2', 'value2');
        $this->cacheService->set('other', 'value3');

        $this->cacheService->deletePattern('prefix_*');

        $this->assertFalse($this->cacheService->has('prefix_1'));
        $this->assertFalse($this->cacheService->has('prefix_2'));
        $this->assertTrue($this->cacheService->has('other'));
    }

    protected function setUp(): void
    {
        $this->adapter = new ArrayAdapter();
        $this->cacheService = new SymfonyCacheService($this->adapter);
    }
} 